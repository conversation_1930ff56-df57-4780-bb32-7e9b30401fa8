# 合约参数比较工具重构需求文档

## 介绍

重构现有的合约参数比较工具，简化代码结构，优化输出格式，将多个分散的功能整合到一个统一的工具中，并改进数据展示方式。

## 需求

### 需求 1: 代码整合和简化

**用户故事:** 作为开发者，我希望将分散的功能整合到一个文件中，以便更容易维护和使用。

#### 验收标准

1. WHEN 运行重构后的工具 THEN 系统应该将 `check_perpetuals.py` 的永续合约检查功能完全整合到 `compare_contract_params.py` 中
2. WHEN 整合完成后 THEN 系统应该删除不再需要的 `check_perpetuals.py` 和 `analyze_differences.py` 文件
3. WHEN 用户运行主程序 THEN 系统应该提供所有原有功能而无需运行多个脚本

### 需求 2: 输出格式优化

**用户故事:** 作为数据分析师，我希望只获得 Excel 格式的输出文件，以便更好地进行数据分析和可视化。

#### 验收标准

1. WHEN 程序运行完成 THEN 系统应该只生成 Excel 文件作为最终输出
2. WHEN 生成输出文件 THEN 系统不应该生成任何 JSON 格式的结果文件（保留原始数据 JSON 文件）
3. WHEN 删除 JSON 输出 THEN 系统应该移除所有相关的 JSON 生成代码逻辑

### 需求 3: Excel 表格结构增强

**用户故事:** 作为用户，我希望在 Excel 中看到完整的交易对对比信息，包括只在单个交易所存在的交易对。

#### 验收标准

1. WHEN 生成 Excel 文件 THEN 系统应该包含一个新的 sheet 显示所有交易对的完整对比
2. WHEN 显示交易对对比 THEN 表格应该包含列：标准化交易对、Binance交易对、OKX交易对
3. WHEN 某个交易对只在一个交易所存在 THEN 对应的另一个交易所列应该显示为空
4. WHEN 生成完整对比表 THEN 系统应该按标准化交易对名称排序显示

### 需求 4: 数据完整性保证

**用户故事:** 作为用户，我希望重构后的工具能够保持所有原有的数据分析功能。

#### 验收标准

1. WHEN 运行重构后的工具 THEN 系统应该保留所有原有的合约参数比较功能
2. WHEN 进行参数比较 THEN 系统应该保持相同的比较逻辑和精度
3. WHEN 生成统计信息 THEN 系统应该提供与原工具相同的统计数据
4. WHEN 处理盘口数据 THEN 系统应该保持相同的价格计算逻辑

### 需求 5: 用户体验改进

**用户故事:** 作为用户，我希望工具运行更加简洁高效，减少不必要的文件和输出。

#### 验收标准

1. WHEN 程序运行 THEN 系统应该提供清晰的进度提示信息
2. WHEN 处理完成 THEN 系统应该只保留必要的输出文件
3. WHEN 发生错误 THEN 系统应该提供有用的错误信息和建议
4. WHEN 工具完成运行 THEN 用户应该能够立即使用生成的 Excel 文件进行分析