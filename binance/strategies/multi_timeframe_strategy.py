from copy import copy

from vnpy.event import Event
from vnpy_ctastrategy import (
    CtaTemplate,
    StopOrder,
    TickData,
    BarData,
    TradeData,
    OrderData,
    BarGenerator,
    ArrayManager,
)
from datetime import datetime, time
from vnpy.trader.constant import Interval
from vnpy_ctastrategy.base import EngineType
from vnpy.trader.database import ZoneInfo

UTC_TZ = ZoneInfo("UTC")

from prod.event import EVENT_BAR, EVENT_MINIBAR, EVENT_BAR_AGG


class MultiTimeframeStrategy(CtaTemplate):
    """"""
    author = "用Python的交易员"

    rsi_signal = 20
    rsi_window = 14
    fast_window = 5
    slow_window = 20
    fixed_size = 1

    rsi_value = 0
    rsi_long = 0
    rsi_short = 0
    fast_ma = 0
    slow_ma = 0
    ma_trend = 0

    parameters = ["rsi_signal", "rsi_window",
                  "fast_window", "slow_window",
                  "fixed_size"]

    variables = ["rsi_value", "rsi_long", "rsi_short",
                 "fast_ma", "slow_ma", "ma_trend"]

    def __init__(self, cta_engine, strategy_name, vt_symbol, setting):
        """"""
        super().__init__(cta_engine, strategy_name, vt_symbol, setting)

        self.rsi_long = 50 + self.rsi_signal
        self.rsi_short = 50 - self.rsi_signal

        # Get origin from setting
        self.origin = setting.get("origin", None)
        if isinstance(self.origin, str):
            self.origin = datetime.fromisoformat(self.origin).astimezone(UTC_TZ)

        self.minibg4 = BarGenerator(self.on_minibar, 4, self.on_4sec_bar, interval=Interval.SECOND_2, ignore_volume_zero=True)
        self.miniam4 = ArrayManager(1) # 用1使inited更快

        self.minibg6 = BarGenerator(self.on_minibar, 6, self.on_6sec_bar, interval=Interval.SECOND_2, ignore_volume_zero=True)
        self.miniam6 = ArrayManager(1)

        self.bg2 = BarGenerator(self.on_bar, 2, self.on_2min_bar, origin=self.origin, ignore_volume_zero=True)
        self.am2 = ArrayManager(1)

        self.bg_3d = BarGenerator(self.on_bar, 60*24*3, self.on_3d_bar, origin=self.origin, ignore_volume_zero=True)
        self.am_3d = ArrayManager(1)

        # 添加周线配置
        self.bg_week = BarGenerator(self.on_bar, 1, self.on_weekly_bar, interval=Interval.WEEKLY, ignore_volume_zero=True)
        self.am_week = ArrayManager(1)

        # 添加月线配置
        self.bg_month = BarGenerator(self.on_bar, 1, self.on_monthly_bar, interval=Interval.MONTHLY, ignore_volume_zero=True)
        self.am_month = ArrayManager(1)

        self.register_event()

    def register_event(self):
        """
        注册事件监听
        """
        self.cta_engine.event_engine.register(EVENT_BAR + self.vt_symbol, self.process_bar_event)
        self.cta_engine.event_engine.register(EVENT_MINIBAR + self.vt_symbol, self.process_minibar_event)

    def on_init(self):
        """
        Callback when strategy is inited.
        """
        self.write_log("策略初始化")
        self.load_bar(60)

    def on_start(self):
        """
        Callback when strategy is started.
        """
        self.write_log("策略启动")

    def on_stop(self):
        """
        Callback when strategy is stopped.
        """
        self.write_log("策略停止")

    def on_tick(self, tick: TickData):
        """
        Callback of new tick data update.
        """
        # self.bg5.update_tick(tick) # 不用订阅的tick更新bar和触发on_bar
        pass

    def process_minibar_event(self, event: Event):
        """
        处理minibar（2s bar）事件
        """
        if self.inited:
            self.on_minibar(event.data)

    def process_bar_event(self, event: Event):
        """
        直接订阅bar，触发on_bar
        """
        if self.inited:
            self.on_bar(event.data)

    def on_minibar(self, bar: BarData):
        """
        处理minibar（2s bar）数据
        """
        self.minibg4.update_bar(bar)
        self.minibg6.update_bar(bar)

    def on_bar(self, bar: BarData):
        """
        Callback of new bar data update.
        """
        self.bg2.update_bar(bar)
        self.bg_3d.update_bar(bar)
        self.bg_week.update_bar(bar)  # 更新周线数据
        self.bg_month.update_bar(bar)  # 更新月线数据

    def on_4sec_bar(self, bar: BarData):
        """
        处理4秒K线数据
        """
        self.miniam4.update_bar(bar)
        if not self.miniam4.inited:
            return

        # 只在回测时推送聚合K线事件
        # if self.get_engine_type() == EngineType.BACKTESTING:
        bar.interval = Interval.SECOND_4
        event = Event(EVENT_BAR_AGG, copy(bar))
        self.cta_engine.event_engine.put(event)

        self.put_event()

    def on_6sec_bar(self, bar: BarData):
        """
        处理6秒K线数据
        """
        self.miniam6.update_bar(bar)
        if not self.miniam6.inited:
            return

        # 只在回测时推送聚合K线事件
        # if self.get_engine_type() == EngineType.BACKTESTING:
        bar.interval = Interval.SECOND_6
        event = Event(EVENT_BAR_AGG, copy(bar))
        self.cta_engine.event_engine.put(event)

        self.put_event()

    def on_2min_bar(self, bar: BarData):
        """"""
        self.cancel_all()

        self.am2.update_bar(bar)
        if not self.am2.inited:
            return

        if not self.ma_trend:
            return

        self.rsi_value = self.am2.rsi(self.rsi_window)

        if self.pos == 0:
            if self.ma_trend > 0 and self.rsi_value >= self.rsi_long:
                self.buy(bar.close_price + 5, self.fixed_size)
            elif self.ma_trend < 0 and self.rsi_value <= self.rsi_short:
                self.short(bar.close_price - 5, self.fixed_size)

        elif self.pos > 0:
            if self.ma_trend < 0 or self.rsi_value < 50:
                self.sell(bar.close_price - 5, abs(self.pos))

        elif self.pos < 0:
            if self.ma_trend > 0 or self.rsi_value > 50:
                self.cover(bar.close_price + 5, abs(self.pos))

        # 只在回测时推送聚合K线事件
        # if self.get_engine_type() == EngineType.BACKTESTING:
        bar.interval = Interval.MINUTE_2
        event = Event(EVENT_BAR_AGG, copy(bar))
        self.cta_engine.event_engine.put(event)

        self.put_event()

    def on_3d_bar(self, bar: BarData):
        """"""
        self.am_3d.update_bar(bar)
        if not self.am_3d.inited:
            return

        self.fast_ma = self.am_3d.sma(self.fast_window)
        self.slow_ma = self.am_3d.sma(self.slow_window)

        if self.fast_ma > self.slow_ma:
            self.ma_trend = 1
        else:
            self.ma_trend = -1

        # 只在回测时推送聚合K线事件
        # if self.get_engine_type() == EngineType.BACKTESTING:
        bar.interval = Interval.DAILY_3
        event = Event(EVENT_BAR_AGG, copy(bar))
        self.cta_engine.event_engine.put(event)

    def on_weekly_bar(self, bar: BarData):
        """
        周线数据回调
        """
        self.am_week.update_bar(bar)
        if not self.am_week.inited:
            return

        # 只在回测时推送聚合K线事件
        # if self.get_engine_type() == EngineType.BACKTESTING:
        bar.interval = Interval.WEEKLY
        event = Event(EVENT_BAR_AGG, copy(bar))
        self.cta_engine.event_engine.put(event)

        self.put_event()

    def on_monthly_bar(self, bar: BarData):
        """
        月线数据回调
        """
        self.am_month.update_bar(bar)
        if not self.am_month.inited:
            return

        # 只在回测时推送聚合K线事件
        # if self.get_engine_type() == EngineType.BACKTESTING:
        bar.interval = Interval.MONTHLY
        event = Event(EVENT_BAR_AGG, copy(bar))
        self.cta_engine.event_engine.put(event)

        self.put_event()

    def on_order(self, order: OrderData):
        """
        Callback of new order data update.
        """
        pass

    def on_trade(self, trade: TradeData):
        """
        Callback of new trade data update.
        """
        self.put_event()

    def on_stop_order(self, stop_order: StopOrder):
        """
        Callback of stop order update.
        """
        pass
