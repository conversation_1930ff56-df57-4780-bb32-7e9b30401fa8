import traceback
import uuid
import sys
from typing import Optional
from collections import defaultdict
from datetime import datetime

from vnpy.event import Event, EventEngine, EVENT_TIMER
from vnpy.trader.database import DB_TZ
from vnpy.trader.engine import BaseEngine, MainEngine
from vnpy.trader.utility import load_json, save_json, extract_vt_symbol
from vnpy.trader.object import LogData, TickData, BarData, SubscribeRequest
from vnpy.trader.event import EVENT_LOG, EVENT_TICK

from prod.event import EVENT_BAR
from ..rpc import RpcServer
from prod.db_status_manager import OrderError
from prod.wecom_alert import WecomAlertManager

APP_NAME = "RpcService"
EVENT_RPC_LOG = "eRpcLog"


class RpcEngine(BaseEngine):
    """
    VeighNa的rpc服务引擎，支持主备切换和监控告警
    """
    setting_filename: str = "rpc_service_setting2.json"

    default_setting: dict = {
        "rep_address": "tcp://*:2014",
        "pub_address": "tcp://*:4102",
        "monitor_vt_symbols": ["BTCUSDT.BINANCE", "ETHUSDT.BINANCE"]
    }

    def __init__(self, main_engine: MainEngine, event_engine: EventEngine) -> None:
        """构造函数"""
        super().__init__(main_engine, event_engine, APP_NAME)

        self.rep_address: str = self.default_setting["rep_address"]
        self.pub_address: str = self.default_setting["pub_address"]
        self.monitor_vt_symbols: list = self.default_setting["monitor_vt_symbols"]

        self.server: Optional[RpcServer] = None
        self.gateway = None

        # 订阅管理 - 客户端集合模式
        self.symbol_clients = defaultdict(set)  # {vt_symbol: {client_ids}}
        self.unsubscribe_time = {}  # {vt_symbol: datetime}

        # 客户端管理
        self.clients = set()  # 客户端ID集合

        # 监控告警相关
        self.counter = 0
        self.last_normal_alert_time = datetime.now(DB_TZ)
        self.normal_alert_interval = 30 * 60  # 30分钟间隔（秒）

        # 初始化企业微信告警管理器
        self.wecom_manager = WecomAlertManager()

        self.init_server()
        self.load_setting()
        self.register_event()

    def init_server(self) -> None:
        """初始化服务器"""
        self.server = RpcServer()

        # 注册主引擎方法
        self.server.register(self.main_engine.send_order)
        self.server.register(self.main_engine.cancel_order)
        self.server.register(self.main_engine.query_history)

        self.server.register(self.main_engine.get_tick)
        self.server.register(self.main_engine.get_order)
        self.server.register(self.main_engine.get_trade)
        self.server.register(self.main_engine.get_position)
        self.server.register(self.main_engine.get_account)
        self.server.register(self.main_engine.get_contract)
        self.server.register(self.main_engine.get_all_ticks)
        self.server.register(self.main_engine.get_all_orders)
        self.server.register(self.main_engine.get_all_trades)
        self.server.register(self.main_engine.get_all_positions)
        self.server.register(self.main_engine.get_all_accounts)
        self.server.register(self.main_engine.get_all_contracts)
        self.server.register(self.main_engine.get_all_active_orders)

        # 注册引擎自定义方法
        self.server.register(self.subscribe)
        self.server.register(self.unsubscribe)
        self.server.register(self.get_client_id)
        self.server.register(self.client_disconnect)

    def load_setting(self) -> None:
        """读取配置文件"""
        setting: dict = load_json(self.setting_filename)
        if not setting:
            setting = self.default_setting
            save_json(self.setting_filename, setting)

        self.rep_address = setting.get("rep_address", self.default_setting["rep_address"])
        self.pub_address = setting.get("pub_address", self.default_setting["pub_address"])
        self.monitor_vt_symbols = setting.get("monitor_vt_symbols", self.default_setting["monitor_vt_symbols"])

    def save_setting(self) -> None:
        """保存配置文件"""
        setting: dict = {
            "rep_address": self.rep_address,
            "pub_address": self.pub_address,
            "monitor_vt_symbols": self.monitor_vt_symbols
        }
        save_json(self.setting_filename, setting)

    def set_gateway(self, gateway) -> None:
        """设置网关"""
        self.gateway = gateway

    def get_client_id(self) -> str:
        """分配新的客户端ID - 使用UUID，确保去重"""
        while True:
            client_id = str(uuid.uuid4())
            if client_id not in self.clients:
                self.clients.add(client_id)
                break
        self.write_log(f'分配客户端ID: {client_id}')
        return client_id

    def subscribe(self, req: SubscribeRequest, gateway_name: str, client_id: str = None) -> None:
        """订阅行情 - 客户端集合管理"""
        if not client_id or not self.gateway:
            return

        vt_symbol = f"{req.symbol}.{req.exchange.value}"

        # 添加客户端ID到集合中
        self.clients.add(client_id)

        # 添加到客户端集合
        was_empty = len(self.symbol_clients[vt_symbol]) == 0
        self.symbol_clients[vt_symbol].add(client_id)

        # 清除该标的的退订时间（如果存在）
        if vt_symbol in self.unsubscribe_time:
            del self.unsubscribe_time[vt_symbol]

        # 如果是第一个客户端，发送订阅请求
        if was_empty:
            self.gateway.subscribe(req)
            self.write_log(f'订阅行情: {vt_symbol} - 客户端: {client_id}')
        else:
            self.write_log(f'增加订阅客户端: {vt_symbol} - 客户端: {client_id}, 总数: {len(self.symbol_clients[vt_symbol])}')

    def unsubscribe(self, req: SubscribeRequest, gateway_name: str, client_id: str = None) -> None:
        """退订行情 - 忽略监控标的的退订"""
        if not client_id:
            return

        vt_symbol = f"{req.symbol}.{req.exchange.value}"

        # 忽略监控标的的退订请求
        if vt_symbol in self.monitor_vt_symbols:
            self.write_log(f'忽略监控标的退订请求: {vt_symbol} - 客户端: {client_id}')
            return

        # 从客户端集合中移除
        if vt_symbol in self.symbol_clients:
            self.symbol_clients[vt_symbol].discard(client_id)
            remaining = len(self.symbol_clients[vt_symbol])

            self.write_log(f'移除订阅客户端: {vt_symbol} - 客户端: {client_id}, 剩余: {remaining}')

            # 如果没有客户端了，记录退订时间
            if remaining == 0:
                self.unsubscribe_time[vt_symbol] = datetime.now(DB_TZ)

    def client_disconnect(self, client_id: str) -> None:
        """客户端断开连接"""
        disconnected_symbols = []

        self.clients.discard(client_id)

        # 清理该客户端的所有订阅
        for vt_symbol, clients in list(self.symbol_clients.items()):
            if client_id in clients:
                clients.discard(client_id)
                disconnected_symbols.append(vt_symbol)

                # 如果没有客户端订阅了且不是监控标的，记录退订时间
                if len(clients) == 0 and vt_symbol not in self.monitor_vt_symbols:
                    self.unsubscribe_time[vt_symbol] = datetime.now(DB_TZ)

        if disconnected_symbols:
            self.write_log(f'客户端{client_id}断开，清理订阅: {disconnected_symbols}')
        else:
            self.write_log(f'客户端{client_id}断开，无订阅需要清理')

    def subscribe_monitor_symbols(self) -> None:
        """订阅监控标的"""
        if not self.gateway:
            return

        for vt_symbol in self.monitor_vt_symbols:
            symbol, exchange = extract_vt_symbol(vt_symbol)
            req = SubscribeRequest(symbol=symbol, exchange=exchange)

            # 直接订阅
            self.gateway.subscribe(req)

            # 添加到symbol_clients中，使用特殊的监控客户端ID
            monitor_client_id = f"MONITOR_{symbol}"
            self.symbol_clients[vt_symbol].add(monitor_client_id)

            self.write_log(f'订阅监控标的: {vt_symbol}')

    def start(self, rep_address: str, pub_address: str) -> bool:
        """启动rpc服务"""
        if self.server.is_active():
            self.write_log("RPC服务运行中")
            return False

        self.rep_address = rep_address
        self.pub_address = pub_address

        try:
            self.server.start(rep_address, pub_address)
        except:  # noqa
            msg: str = traceback.format_exc()
            self.write_log(f"RPC服务启动失败：{msg}")
            return False

        # 启动企业微信告警管理器
        self.wecom_manager.start()
        self.write_log("企业微信告警管理器已启动")

        self.save_setting()
        self.write_log("RPC服务启动成功")
        return True

    def stop(self) -> bool:
        """停止rpc服务"""
        if not self.server.is_active():
            self.write_log("RPC服务未启动")
            return False

        self.wecom_manager.stop()
        self.server.stop()
        self.server.join()
        self.write_log("RPC服务已停止")
        return True

    def close(self) -> None:
        """关闭rpc服务"""
        self.stop()

    def register_event(self) -> None:
        """注册事件"""
        self.event_engine.register_general(self.process_event)
        self.event_engine.register(EVENT_TIMER, self.process_timer_event)

    def process_event(self, event: Event) -> None:
        """调用事件"""
        if self.server.is_active():
            # if not any(keyword in event.type.lower() for keyword in ['log', 'timer', 'rpc', 'contract','account','position','order','trade']):
            #     self.write_log(f"Received event: type={event.type}, data_type={type(event.data)}")

            # if event.type == EVENT_TIMER:
            # if event.type in {EVENT_TIMER, EVENT_LOG, EVENT_RPC_LOG}:
            #     return

            if isinstance(event.data, (TickData, BarData)) and event.type in (EVENT_TICK, EVENT_BAR):
                self.server.publish("", event)

    def process_timer_event(self, event: Event) -> None:
        """定时事件处理"""
        self.counter += 1

        # 检查每个标的的退订缓存
        now = datetime.now(DB_TZ)

        # 清理过期的退订标的
        for vt_symbol, clients in list(self.symbol_clients.items()):
            if len(clients) == 0:
                last_unsub_time = self.unsubscribe_time.get(vt_symbol)
                if last_unsub_time and (now - last_unsub_time).total_seconds() >= 10:
                    if self.gateway:
                        req = SubscribeRequest(*extract_vt_symbol(vt_symbol))
                        self.gateway.unsubscribe(req)
                        self.write_log(f"退订行情: {req.vt_symbol}")

                    # 清理缓存
                    del self.symbol_clients[vt_symbol]
                    del self.unsubscribe_time[vt_symbol]

        # 每30秒检查一次订阅的标的行情状态
        if self.counter >= 30:
            self.counter = 0
            self._check_tick_status(now)

    def _check_tick_status(self, now: datetime) -> None:
        """检查tick状态"""
        if not self.gateway:
            return

        # 检查所有标的的订阅状态
        has_active = False
        active_str = "活跃订阅: {"
        timeout_symbols = []  # 记录超时的标的
        latest_tick_time = None  # 记录所有标的中最新的tick时间

        # 检查所有订阅标的
        for vt_symbol, clients in self.symbol_clients.items():
            client_count = len(clients)
            if client_count > 0:
                has_active = True
                symbol, exchange = extract_vt_symbol(vt_symbol)
                active_str += f"'{symbol}': {client_count}, "

                # 检查tick数据
                tick = self.gateway.market_ws_api.ticks.get(symbol.lower())

                if tick and hasattr(tick, "localtime") and tick.localtime:
                    # 更新最新tick时间
                    if latest_tick_time is None or tick.localtime > latest_tick_time:
                        latest_tick_time = tick.localtime

                    # 检查是否超时
                    if (now - tick.localtime).total_seconds() >= 30:
                        timeout_symbols.append(symbol)
                        # 重新订阅超时的标的
                        req = SubscribeRequest(symbol=symbol, exchange=exchange)
                        self.gateway.subscribe(req)
                        self.write_log(f"重新订阅行情: {req.vt_symbol}, 当前订阅客户端数: {client_count}")
                elif not tick:
                    # 没有tick数据，也算超时
                    timeout_symbols.append(symbol)
                    req = SubscribeRequest(symbol=symbol, exchange=exchange)
                    self.gateway.subscribe(req)
                    self.write_log(f"重新订阅行情: {req.vt_symbol}, 当前订阅客户端数: {client_count}")

        if has_active:
            active_str = active_str.rstrip(", ") + "}"
            self.write_log(active_str)

            # 检查整体tick超时情况
            if latest_tick_time and (now - latest_tick_time).total_seconds() >= 30:
                # 发送企业微信告警
                alert_msg = f"RPC行情服务告警：所有标的tick数据超时30秒，最新tick时间：{latest_tick_time}，当前时间：{now}，超时标的：{timeout_symbols}"
                self.wecom_manager.add_message(alert_msg)

                # 记录告警到数据库
                try:
                    OrderError.create(
                        symbol="RPC_TICK_TIMEOUT",
                        exchange="BINANCE",
                        error_code=11,
                        error_msg=f"所有标的tick数据超时30秒，超时标的：{timeout_symbols}",
                        username="RPC_ENGINE",
                        create_date=now
                    )
                    self.write_log("tick超时告警已入库")
                except Exception as e:
                    self.write_log(f"入库告警信息失败：{str(e)}")

            # 每30分钟发送正常通知
            elif (now - self.last_normal_alert_time).total_seconds() >= self.normal_alert_interval:
                try:
                    OrderError.create(
                        symbol="RPC_NORMAL_STATUS",
                        exchange="BINANCE",
                        error_code=10,
                        error_msg=f"RPC行情服务正常运行，活跃订阅数：{len([v for v in self.symbol_clients.values() if len(v) > 0])}",
                        username="RPC_ENGINE",
                        create_date=now
                    )
                    self.last_normal_alert_time = now
                    self.write_log("RPC服务正常状态已入库")
                except Exception as e:
                    self.write_log(f"入库正常状态失败：{str(e)}")

    def write_log(self, msg: str) -> None:
        """输出日志"""
        frame = sys._getframe(1)
        func_name = frame.f_code.co_name
        line_no = frame.f_lineno
        class_name = self.__class__.__name__
        formatted_msg = f"[{class_name}.{func_name}:{line_no}] {msg}"
        log: LogData = LogData(msg=formatted_msg, gateway_name=APP_NAME)
        event: Event = Event(EVENT_RPC_LOG, log)
        self.event_engine.put(event)
