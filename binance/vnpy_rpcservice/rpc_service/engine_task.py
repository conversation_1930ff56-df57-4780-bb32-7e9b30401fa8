import traceback
import random
from typing import Optional
from time import sleep
from datetime import datetime

from vnpy.event import Event, EventEngine
from ..rpc import RpcServer
from vnpy.trader.engine import BaseEngine, MainEngine
from vnpy.trader.utility import load_json, save_json
from vnpy.trader.object import LogData
from vnpy.trader.event import EVENT_TIMER
import sys

APP_NAME = "RpcTaskPub"
EVENT_RPC_TASK_LOG = "eRpcTaskLog"

class RpcTaskPubEngine(BaseEngine):
    """
    RPC任务发布引擎
    """
    # 测试配置
    TEST_SYMBOLS = ["ETHUSDT.BINANCE"]  # 测试用的交易对
    TEST_PRICE_RANGE = (2800, 3000)     # 测试价格范围
    TEST_VOLUME_RANGE = (0.008, 0.02)   # 测试数量范围
    TIMER_INTERVAL = 1                   # 测试任务生成间隔(秒)

    setting_filename: str = "rpc_task_pub_setting.json"

    def __init__(self, main_engine: MainEngine, event_engine: EventEngine) -> None:
        """构造函数"""
        super().__init__(main_engine, event_engine, APP_NAME)

        self.rep_address: str = "tcp://*:2014"
        self.pub_address: str = "tcp://*:4102"
        self.server: Optional[RpcServer] = None
        self.task_count: int = 0
        self.timer_count: int = 0
        self.simple_mode: bool = False

        self.init_server()
        self.load_setting()
        self.register_event()

    def init_server(self) -> None:
        """初始化服务器"""
        self.server = RpcServer()
        # 注册任务完成回调函数
        self.server.register(self.on_task_finished)

    def load_setting(self) -> None:
        """读取配置文件"""
        setting: dict = load_json(self.setting_filename)
        if setting:
            self.rep_address = setting.get("rep_address", self.rep_address)
            self.pub_address = setting.get("pub_address", self.pub_address)

    def save_setting(self) -> None:
        """保存配置文件"""
        setting: dict = {
            "rep_address": self.rep_address,
            "pub_address": self.pub_address
        }
        save_json(self.setting_filename, setting)

    def start(self, rep_address: str, pub_address: str, simple_mode: bool = False) -> bool:
        """启动服务"""
        if self.server.is_active():
            self.write_log("RPC任务发布服务运行中")
            return False

        self.rep_address = rep_address
        self.pub_address = pub_address
        self.simple_mode = simple_mode

        try:
            self.server.start(rep_address, pub_address)
        except:  # noqa
            msg: str = traceback.format_exc()
            self.write_log(f"RPC任务发布服务启动失败：{msg}")
            return False

        self.save_setting()
        self.write_log("RPC任务发布服务启动成功")
        return True

    def stop(self) -> bool:
        """停止服务"""
        if not self.server.is_active():
            self.write_log("RPC任务发布服务未启动")
            return False

        self.server.stop()
        self.server.join()
        self.write_log("RPC任务发布服务已停止")
        return True

    def close(self) -> None:
        """关闭服务"""
        self.stop()

    def register_event(self) -> None:
        """注册事件监听"""
        self.event_engine.register(EVENT_TIMER, self.process_timer_event)

    def process_timer_event(self, event: Event) -> None:
        """处理定时事件"""
        if self.server.is_active():
            if self.timer_count == 0:
                self.generate_test_task()
            self.timer_count += 1
            if self.timer_count >= self.TIMER_INTERVAL:
                self.timer_count = 0

    def generate_test_task(self) -> None:
        """生成测试任务"""
        # 随机选择用户
        user = f"user{random.randint(1, 3)}"
        
        if self.simple_mode:
            # 简单模式：直接发送字符串
            self.server.publish(user, "ok")
            self.write_log(f"向{user}发送简单消息：ok")
        else:
            # 完整模式：发送任务数据
            # 随机选择交易对
            vt_symbol = random.choice(self.TEST_SYMBOLS)
            
            # 随机生成价格和数量
            price = random.uniform(*self.TEST_PRICE_RANGE)
            volume = random.uniform(*self.TEST_VOLUME_RANGE)
            
            # 生成任务数据
            self.task_count += 1
            task_data = {
                "task_id": f"task_{self.task_count}",
                "vt_symbol": vt_symbol,
                "price": price,
                "volume": volume,
                "datetime": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
            # 发布任务
            event = Event(type="task", data=task_data)
            self.server.publish(user, event)
            self.write_log(f"向{user}发布任务：{task_data}")

    def on_task_finished(self, user: str, task_id: str) -> None:
        """处理任务完成回调"""
        self.write_log(f"收到{user}完成任务通知：{task_id}")

    def write_log(self, msg: str) -> None:
        """输出日志"""
        frame = sys._getframe(1)
        func_name = frame.f_code.co_name
        line_no = frame.f_lineno
        class_name = self.__class__.__name__
        formatted_msg = f"[{class_name}.{func_name}:{line_no}] {msg}"
        log: LogData = LogData(msg=formatted_msg, gateway_name=APP_NAME)
        event: Event = Event(EVENT_RPC_TASK_LOG, log)
        self.event_engine.put(event)