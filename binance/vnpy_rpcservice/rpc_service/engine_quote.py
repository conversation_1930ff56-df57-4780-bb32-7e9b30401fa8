import traceback
import sys
from typing import Optional
from collections import defaultdict
from datetime import datetime

from vnpy.event import Event, EventEngine, EVENT_TIMER
from vnpy.trader.database import DB_TZ
from vnpy.trader.engine import BaseEngine, MainEngine
from vnpy.trader.utility import load_json, save_json, extract_vt_symbol
from vnpy.trader.object import LogData, TickData, BarData, SubscribeRequest
from vnpy.trader.event import EVENT_LOG

from ..rpc import RpcServer

APP_NAME = "RpcService"
EVENT_RPC_LOG = "eRpcLog"


class RpcEngine(BaseEngine):
    """
    VeighNa的rpc服务引擎，支持订阅管理和监控。
    """
    setting_filename: str = "rpc_service_setting.json"

    def __init__(self, main_engine: MainEngine, event_engine: EventEngine) -> None:
        """构造函数"""
        super().__init__(main_engine, event_engine, APP_NAME)

        self.rep_address: str = "tcp://*:2014"
        self.pub_address: str = "tcp://*:4102"

        self.server: Optional[RpcServer] = None
        self.gateway = None

        # 订阅缓存结构
        self._subscribe_cache = {
            "counts": defaultdict(int),  # 记录每个标的的订阅计数
            "last_unsub_time": {},  # 记录每个标的的最后退订时间
        }

        self.counter = 0

        self.init_server()
        self.load_setting()
        self.register_event()

    def init_server(self) -> None:
        """初始化服务器"""
        self.server = RpcServer()

        # 注册主引擎方法
        self.server.register(self.main_engine.send_order)
        self.server.register(self.main_engine.cancel_order)
        self.server.register(self.main_engine.query_history)

        self.server.register(self.main_engine.get_tick)
        self.server.register(self.main_engine.get_order)
        self.server.register(self.main_engine.get_trade)
        self.server.register(self.main_engine.get_position)
        self.server.register(self.main_engine.get_account)
        self.server.register(self.main_engine.get_contract)
        self.server.register(self.main_engine.get_all_ticks)
        self.server.register(self.main_engine.get_all_orders)
        self.server.register(self.main_engine.get_all_trades)
        self.server.register(self.main_engine.get_all_positions)
        self.server.register(self.main_engine.get_all_accounts)
        self.server.register(self.main_engine.get_all_contracts)
        self.server.register(self.main_engine.get_all_active_orders)

        # 注册引擎自定义方法
        self.server.register(self.subscribe)
        self.server.register(self.unsubscribe)

    def load_setting(self) -> None:
        """读取配置文件"""
        setting: dict[str, str] = load_json(self.setting_filename)
        if not setting:
            setting = {
                "rep_address": "tcp://*:2014",
                "pub_address": "tcp://*:4102"
            }
            save_json(self.setting_filename, setting)
        self.rep_address = setting.get("rep_address", self.rep_address)
        self.pub_address = setting.get("pub_address", self.pub_address)

    def save_setting(self) -> None:
        """保存配置文件"""
        setting: dict[str, str] = {
            "rep_address": self.rep_address,
            "pub_address": self.pub_address
        }
        save_json(self.setting_filename, setting)

    def start(self, rep_address: str, pub_address: str) -> bool:
        """启动rpc服务"""
        if self.server.is_active():
            self.write_log("RPC服务运行中")
            return False

        self.rep_address = rep_address
        self.pub_address = pub_address

        try:
            self.server.start(rep_address, pub_address)
        except:  # noqa
            msg: str = traceback.format_exc()
            self.write_log(f"RPC服务启动失败：{msg}")
            return False

        self.save_setting()
        self.write_log("RPC服务启动成功")
        return True

    def stop(self) -> bool:
        """停止rpc服务"""
        if not self.server.is_active():
            self.write_log("RPC服务未启动")
            return False

        self.server.stop()
        self.server.join()
        self.write_log("RPC服务已停止")
        return True

    def close(self) -> None:
        """关闭rpc服务"""
        self.stop()

    def set_gateway(self, gateway) -> None:
        """设置网关"""
        self.gateway = gateway

    def subscribe(self, req: SubscribeRequest, gateway_name: str) -> None:
        """订阅行情
        如果标的已经在订阅缓存中且count>0，则只增加计数
        否则发送订阅请求并增加计数
        """
        if not self.gateway:
            return

        vt_symbol = f"{req.symbol}.{req.exchange.value}"

        # 增加订阅计数
        self._subscribe_cache["counts"][vt_symbol] += 1

        # 如果订阅计数为1，说明是第一次订阅，需要发送订阅请求
        if self._subscribe_cache["counts"][vt_symbol] == 1:
            self.gateway.subscribe(req)
            self.write_log(f'订阅行情: {vt_symbol} - {gateway_name}')

    def unsubscribe(self, req: SubscribeRequest, gateway_name: str) -> None:
        """退订行情
        减少订阅计数，并记录退订时间
        实际的退订操作在process_timer_event中处理
        """
        vt_symbol = f"{req.symbol}.{req.exchange.value}"

        # 减少订阅计数
        if vt_symbol in self._subscribe_cache["counts"]:
            self._subscribe_cache["counts"][vt_symbol] = max(0, self._subscribe_cache["counts"][vt_symbol] - 1)

        # 更新最后退订时间
        self._subscribe_cache["last_unsub_time"][vt_symbol] = datetime.now(DB_TZ)
        self.write_log(f'更新退订缓存: {vt_symbol} - {gateway_name}')

    def register_event(self) -> None:
        """注册事件"""
        self.event_engine.register_general(self.process_event)
        self.event_engine.register(EVENT_TIMER, self.process_timer_event)

    def process_event(self, event: Event) -> None:
        """调用事件"""
        if self.server.is_active():
            # if not any(keyword in event.type.lower() for keyword in ['log', 'timer', 'rpc', 'contract','account','position','order','trade']):
            #     self.write_log(f"Received event: type={event.type}, data_type={type(event.data)}")

            # if event.type == EVENT_TIMER:
            # if event.type in {EVENT_TIMER, EVENT_LOG, EVENT_RPC_LOG}:
            #     return

            if isinstance(event.data, (TickData, BarData)):
                self.server.publish("", event)
                # if event.type == "eTick.":
                #     self.write_log(f'{event.data}')

    def process_timer_event(self, event: Event) -> None:
        """定时事件处理"""
        self.counter += 1

        # 检查每个标的的退订缓存
        now = datetime.now(DB_TZ)

        # 清理过期的退订标的
        for vt_symbol, count in list(self._subscribe_cache["counts"].items()):
            if count == 0:  # 如果订阅计数为0
                last_unsub_time = self._subscribe_cache["last_unsub_time"].get(vt_symbol)
                if last_unsub_time and (now - last_unsub_time).total_seconds() >= 10:
                    req = SubscribeRequest(*extract_vt_symbol(vt_symbol))
                    self.gateway.unsubscribe(req)
                    self.write_log(f"退订行情: {req.vt_symbol}")

                    # 清理缓存
                    del self._subscribe_cache["counts"][vt_symbol]
                    del self._subscribe_cache["last_unsub_time"][vt_symbol]

        # 每60秒检查一次订阅的标的行情状态
        if self.counter >= 60:
            self.counter = 0
            if not self.gateway:
                return

            # 检查所有标的的订阅状态
            has_active = False
            active_str = "活跃订阅: {"

            for vt_symbol, count in self._subscribe_cache["counts"].items():
                if count > 0:
                    has_active = True
                    symbol, exchange = extract_vt_symbol(vt_symbol)
                    active_str += f"'{symbol}': {count}, "

                    # 检查tick数据
                    tick = self.gateway.market_ws_api.ticks.get(symbol.lower())

                    if not tick or getattr(tick, "localtime") and (now - tick.localtime).total_seconds() >= 60:
                        # 如果没有tick或者tick时间超过1分钟，重新订阅
                        req = SubscribeRequest(symbol=symbol, exchange=exchange)
                        self.gateway.subscribe(req)
                        self.write_log(f"重新订阅行情: {req.vt_symbol}, 当前订阅计数: {count}")

            if has_active:
                active_str = active_str.rstrip(", ") + "}"
                self.write_log(active_str)

    def write_log(self, msg: str) -> None:
        """输出日志"""
        frame = sys._getframe(1)
        func_name = frame.f_code.co_name
        line_no = frame.f_lineno
        class_name = self.__class__.__name__
        formatted_msg = f"[{class_name}.{func_name}:{line_no}] {msg}"
        log: LogData = LogData(msg=formatted_msg, gateway_name=APP_NAME)
        event: Event = Event(EVENT_RPC_LOG, log)
        self.event_engine.put(event)
