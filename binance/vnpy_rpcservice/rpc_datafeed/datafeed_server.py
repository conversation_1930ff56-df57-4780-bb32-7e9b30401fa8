from vnpy.event import EventEngine
from vnpy.trader.setting import SETTINGS
SETTINGS["datafeed.name"] = "xt"

from rpc_datafeed import DatafeedServer
from time import sleep

REP_ADDRESS = "tcp://*:6001"
PUB_ADDRESS = "tcp://*:6002"

# 客户端实际使用时，请将这三行配置代码注释掉，并写入vt_setting.json
# SETTINGS["datafeed.name"] = "rpcservice"
# SETTINGS["datafeed.username"] = "tcp://*************:6001"
# SETTINGS["datafeed.password"] = "tcp://*************:6002"

def main() -> None:
    """"""
    event_engine = EventEngine()
    event_engine.start()

    server = DatafeedServer(event_engine)
    server.start(REP_ADDRESS, PUB_ADDRESS)

    while True:
        sleep(1)

    event_engine.stop()
    server.stop()


if __name__ == "__main__":
    main()
