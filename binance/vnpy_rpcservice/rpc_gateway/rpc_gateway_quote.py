from vnpy.event import Event
from ..rpc import RpcClient
from vnpy.trader.gateway import BaseGateway
from vnpy.trader.object import (
    SubscribeRequest,
    HistoryRequest,
    CancelRequest,
    OrderRequest, TickData
)
from vnpy.trader.constant import Exchange
from vnpy.trader.object import (
    BarData,
    ContractData,
    AccountData,
    PositionData,
    OrderData,
    TradeData
)


class RpcGateway(BaseGateway):
    """
    VeighNa用于连接rpc服务的接口。
    """

    default_name: str = "RPC"

    default_setting: dict[str, str] = {
        "主动请求地址": "tcp://127.0.0.1:2014",
        "推送订阅地址": "tcp://127.0.0.1:4102"
    }

    exchanges: list[Exchange] = list(Exchange)

    def __init__(self, event_engine, gateway_name: str) -> None:
        """构造函数"""
        super().__init__(event_engine, gateway_name)

        self.client: "RpcClient" = RpcClient()
        self.client.callback = self.client_callback

    def connect(self, setting: dict) -> None:
        """连接交易接口"""
        req_address: str = setting["主动请求地址"]
        pub_address: str = setting["推送订阅地址"]

        self.client.subscribe_topic("")
        self.client.start(req_address, pub_address)

    def subscribe(self, req: SubscribeRequest) -> None:
        """订阅行情"""
        self.client.subscribe(req, self.gateway_name)

    def unsubscribe(self, req: SubscribeRequest) -> None:
        """退订行情"""
        self.client.unsubscribe(req, self.gateway_name)

    def send_order(self, req: OrderRequest) -> str:
        """委托下单"""
        pass

    def cancel_order(self, req: CancelRequest) -> None:
        """委托撤单"""
        pass

    def query_account(self) -> None:
        """查询资金"""
        pass

    def query_position(self) -> None:
        """查询持仓"""
        pass

    def query_history(self, req: HistoryRequest) -> list[BarData]:
        """查询历史数据"""
        return self.client.query_history(req, self.gateway_name)

    def close(self) -> None:
        """关闭连接"""
        self.client.stop()
        self.client.join()

    def client_callback(self, topic: str, event: Event) -> None:
        """回调函数"""
        if event is None:
            return

        data: object = event.data

        # 只处理行情数据，提高性能
        if not isinstance(data, (TickData, BarData)):
            return

        if hasattr(data, "gateway_name"):
            data.gateway_name = self.gateway_name

        self.event_engine.put(event)
