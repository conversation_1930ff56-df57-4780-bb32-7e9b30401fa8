import threading
from collections import defaultdict
from copy import copy
from datetime import datetime, timedelta
from functools import partial

from vnpy.event import Event
from vnpy.trader.constant import Exchange
from vnpy.trader.event import EVENT_TIMER, EVENT_TICK
from vnpy.trader.gateway import BaseGateway
from vnpy.trader.object import (BarData)
from vnpy.trader.object import (SubscribeRequest, HistoryRequest, CancelRequest, OrderRequest, TickData)
from vnpy.trader.utility import extract_vt_symbol

from prod.event import EVENT_BAR
from ..rpc import RpcClient


class RpcGateway(BaseGateway):
    """
    VeighNa用于连接rpc服务的接口，支持主备切换
    """

    default_name: str = "RPC"

    default_setting: dict = {
        "servers": [
            {
                "主动请求地址": "tcp://127.0.0.1:2014",
                "推送订阅地址": "tcp://127.0.0.1:4102",
            },
            {
                "主动请求地址": "tcp://127.0.0.1:2015",
                "推送订阅地址": "tcp://127.0.0.1:5102",
            }
        ],
        "health_check_interval": 5,
        "data_timeout": 5,
        "monitor_vt_symbols": ["BTCUSDT.BINANCE", "ETHUSDT.BINANCE"]
    }

    exchanges: list[Exchange] = list(Exchange)

    def __init__(self, event_engine, gateway_name: str) -> None:
        """构造函数"""
        super().__init__(event_engine, gateway_name)

        # 连接管理 - 所有服务器始终连接
        self.servers: list = []
        self.current_server_index: int = 0
        self.clients: dict = {}  # {server_index: RpcClient} - 所有服务器连接
        self.active_client: RpcClient = None   # 当前活跃连接
        self.client_ids: dict = {}  # {server_index: client_id}

        # 健康检测配置
        self.health_check_interval: int = self.default_setting['health_check_interval'] // event_engine._interval
        self.data_timeout: int = self.default_setting['data_timeout']
        self.monitor_vt_symbols: list = self.default_setting['monitor_vt_symbols']

        # 状态管理
        self.last_data_time: dict = defaultdict(lambda: defaultdict(lambda: datetime.now()))  # {server_index: {vt_symbol: datetime}}
        self.subscribed_symbols: set = set()
        self.health_check_counter: int = 0
        self.last_switch_time: datetime = datetime.now()

        # 时间顺序检查
        self.last_tick_time: dict = {}  # {vt_symbol: datetime}
        self.last_bars: dict = {}       # {vt_symbol: BarData} - 缓存上一根bar

        # 线程安全锁
        self.data_lock = threading.Lock()  # 保护数据处理的线程安全
        
        # 连接状态跟踪
        self.server_connection_status = {}  # {server_index: bool} - 连接状态

    def connect(self, setting: dict) -> None:
        """连接交易接口，支持主备服务器"""
        # 加载配置
        self.servers = setting.get('servers', self.default_setting['servers'])
        self.health_check_interval = setting.get('health_check_interval', self.default_setting['health_check_interval']) // self.event_engine._interval
        self.data_timeout = setting.get('data_timeout', self.default_setting['data_timeout'])
        self.monitor_vt_symbols = setting.get('monitor_vt_symbols', self.default_setting['monitor_vt_symbols'])

        # 连接所有服务器（始终保持连接）
        self._connect_all_servers()

        # 设置当前活跃服务器为主服务器（索引0）
        if 0 in self.clients:
            self.current_server_index = 0
            self.active_client = self.clients[0]

        # 注册定时器事件
        self.event_engine.register(EVENT_TIMER, self.process_timer_event)

    def _connect_all_servers(self) -> None:
        """连接所有服务器（始终保持连接）"""
        for i, server in enumerate(self.servers):
            try:
                client = RpcClient()
                client.callback = partial(self.client_callback_with_index, client_index=i)
                # 重写连接断开回调
                client.on_disconnected = partial(self.on_client_disconnected, client_index=i)

                req_address = server["主动请求地址"]
                pub_address = server["推送订阅地址"]

                client.subscribe_topic("")
                client.start(req_address, pub_address)

                try:
                    client_id = client.get_client_id(timeout=500)  # 0.5秒超时
                    self.client_ids[i] = client_id
                    self.clients[i] = client
                    
                    # 初始化连接状态
                    self.server_connection_status[i] = True
                    
                    server_name = server.get("主动请求地址", f'服务器{i}')
                    self.write_log(f"从{server_name}获得客户端ID: {client_id}")
                except Exception as e:
                    server_name = server.get("主动请求地址", f'服务器{i}')
                    self.write_log(f"从{server_name}获取客户端ID失败: {str(e)}")
                    client.stop()
                    continue

            except Exception as e:
                server_name = server.get("主动请求地址", f'服务器{i}')
                self.write_log(f"连接{server_name}失败: {str(e)}")

    def _switch_to_server(self, server_index: int) -> bool:
        """切换到指定服务器"""
        if server_index not in self.clients:
            self.write_log(f"服务器{server_index}不存在或未连接")
            return False

        # 退订当前服务器的业务标的
        self._unsubscribe_all()

        # 使用锁保护切换过程，避免与数据处理冲突
        with self.data_lock:
            # 切换活跃客户端
            self.current_server_index = server_index
            self.active_client = self.clients[server_index]
            self.last_switch_time = datetime.now()

        server_name = self.servers[server_index].get("主动请求地址", f'服务器{server_index}')
        self.write_log(f"已切换到{server_name}")

        # 重新订阅所有标的
        self._resubscribe_all()

        return True

    def subscribe(self, req: SubscribeRequest, update_symbols: bool = True) -> None:
        """订阅行情"""
        client_id = self.client_ids.get(self.current_server_index)
        if self.active_client and client_id is not None:
            try:
                self.active_client.subscribe(req, self.gateway_name, client_id, timeout=500)
            except Exception as e:
                self.write_log(f"订阅{req.vt_symbol}失败: {str(e)}")
                # 订阅失败可能表示服务器有问题，触发健康检查
                self._check_current_server_health()
            if update_symbols:
                self.subscribed_symbols.add(req.vt_symbol)

    def unsubscribe(self, req: SubscribeRequest, update_symbols: bool = True) -> None:
        """退订行情"""
        client_id = self.client_ids.get(self.current_server_index)
        if self.active_client and client_id is not None:
            try:
                self.active_client.unsubscribe(req, self.gateway_name, client_id, timeout=500)
            except Exception as e:
                self.write_log(f"退订{req.vt_symbol}失败: {str(e)}")
            if update_symbols:
                self.subscribed_symbols.discard(req.vt_symbol)

    def send_order(self, req: OrderRequest) -> str:
        """委托下单"""
        pass

    def cancel_order(self, req: CancelRequest) -> None:
        """委托撤单"""
        pass

    def query_account(self) -> None:
        """查询资金"""
        pass

    def query_position(self) -> None:
        """查询持仓"""
        pass

    def query_history(self, req: HistoryRequest) -> list[BarData]:
        """查询历史数据"""
        if self.active_client:
            return self.active_client.query_history(req, self.gateway_name)
        return []

    def close(self) -> None:
        """关闭连接"""
        # 注销定时器事件
        self.event_engine.unregister(EVENT_TIMER, self.process_timer_event)

        # 通知服务端客户端断开并关闭所有连接
        for i, client in self.clients.items():
            try:
                client_id = self.client_ids.get(i)
                if client_id:
                    try:
                        client.client_disconnect(client_id, timeout=500)
                        self.write_log(f"通知服务器{i}客户端{client_id}断开")
                    except Exception as e:
                        self.write_log(f"通知服务器{i}断开失败: {str(e)}")
                client.stop()
                client.join()
            except Exception as e:
                self.write_log(f"关闭服务器{i}连接失败: {str(e)}")

    def client_callback_with_index(self, topic: str, event: Event, client_index: int) -> None:
        """多服务器模式回调函数"""
        if event is None:
            return

        data: object = event.data

        # 只处理行情数据，提高性能
        # type判断，防止BarData在EVENT_BAR EVENT_BAR+bar.vt_symbol等多种事件类型下影响去重判断
        if not isinstance(data, (TickData, BarData)) or event.type not in (EVENT_TICK, EVENT_BAR):
            return

        if hasattr(data, "gateway_name"):
            data.gateway_name = self.gateway_name

        # 如果之前标记为断开，现在收到数据说明恢复了
        if not self.server_connection_status.get(client_index, False):
            self.server_connection_status[client_index] = True
            server_name = self.servers[client_index].get("主动请求地址", f'服务器{client_index}')
            self.write_log(f"检测到服务器{client_index}({server_name})连接恢复")
        
        # 只记录监控标的的数据时间
        if data.vt_symbol in self.monitor_vt_symbols:
            self.last_data_time[client_index][data.vt_symbol] = datetime.now()

        # 只有当前活跃客户端的数据才推送到事件引擎
        # 使用锁保证线程安全，避免多线程乱序问题
        with self.data_lock:
            if client_index == self.current_server_index:
                # 检查时间顺序并处理数据
                if isinstance(data, TickData):
                    self._process_tick_data(data, event)
                elif isinstance(data, BarData):
                    self._process_bar_data(data, event)

    def _process_tick_data(self, tick: TickData, event: Event) -> None:
        """处理tick数据，检查时间顺序"""
        vt_symbol = tick.vt_symbol
        current_time = tick.datetime

        # 检查时间是否倒序
        last_time = self.last_tick_time.get(vt_symbol)
        if last_time and current_time <= last_time:
            # self.write_log(f"忽略倒序tick数据: {vt_symbol}, 当前时间: {current_time}, 上次时间: {last_time}")
            return

        # 更新最后时间并推送事件
        self.last_tick_time[vt_symbol] = current_time
        self.event_engine.put(event)

    def _process_bar_data(self, bar: BarData, event: Event) -> None:
        """处理bar数据，检查时间顺序并补充缺失的bar - 参考binance_linear_gateway2.py"""
        vt_symbol = bar.vt_symbol

        # 先检查时间差
        last_bar = self.last_bars.get(vt_symbol)
        if last_bar:
            # 检查时间是否倒序或重复
            if bar.datetime <= last_bar.datetime:
                # self.write_log(f"忽略倒序或重复bar数据: {vt_symbol}, 当前时间: {bar.datetime}, 上次时间: {last_bar.datetime}")
                return

            minutes_diff = int((bar.datetime - last_bar.datetime).total_seconds() / 60)
            if minutes_diff > 1:
                # 设置固定属性 - 使用last_bar的close_price
                last_bar.volume = 0
                last_bar.turnover = 0
                last_bar.open_price = last_bar.close_price
                last_bar.high_price = last_bar.close_price
                last_bar.low_price = last_bar.close_price

                # 生成并推送缺失的bar
                for _ in range(minutes_diff-1):
                    last_bar.datetime = last_bar.datetime + timedelta(minutes=1)

                    self.write_log(f"补充缺失的bar: {vt_symbol}, 时间: {last_bar.datetime}, 价格: {last_bar.close_price}")

                    # 发送事件时复制bar对象
                    bar_copy = copy(last_bar)
                    fill_event = Event(event.type, bar_copy)
                    self.event_engine.put(fill_event)

        # 更新last_bar
        self.last_bars[vt_symbol] = bar

        # 发送当前bar的事件
        self.event_engine.put(event)

    def process_timer_event(self, event: Event) -> None:
        """处理定时器事件"""
        self.health_check_counter += 1

        # 健康检查
        if self.health_check_counter >= self.health_check_interval:
            self.health_check_counter = 0
            self._check_current_server_health()
            self._check_server_recovery()

    def _check_current_server_health(self) -> None:
        """检查当前服务器健康状态"""
        # 检查当前服务器：所有监控标的都超时才触发切换
        is_healthy = self._is_server_healthy(self.current_server_index)

        # 如果服务器不健康且有备用服务器，尝试切换
        if not is_healthy and len(self.servers) > 1:
            timeout_symbols = [vt_symbol.split(".")[0] for vt_symbol in self.monitor_vt_symbols]
            self.write_log(f"检测到所有监控标的数据超时: {timeout_symbols}，尝试切换服务器")
            self._try_switch_server()

    def _check_server_recovery(self) -> None:
        """检查主服务器是否恢复，如果恢复则切回主服务器"""
        # 如果当前不是主服务器，检查主服务器是否恢复
        if self.current_server_index != 0 and len(self.servers) > 1:
            now = datetime.now()

            # 检查主服务器：所有监控标的都必须正常才能切回
            primary_healthy = self._is_server_healthy(0)

            # 如果主服务器健康且切换已超过5分钟，尝试切回主服务器
            if primary_healthy and (now - self.last_switch_time).total_seconds() > 300:  # 5分钟后才考虑切回
                self.write_log("检测到主服务器恢复且切换时间超过5分钟，尝试切回主服务器")
                if self._switch_to_server(0):
                    self.write_log("已成功切回主服务器")

    def _is_server_healthy(self, server_index: int) -> bool:
        """检查指定服务器是否健康"""
        if server_index not in self.clients:
            return False

        # 首先检查连接状态
        if not self.server_connection_status.get(server_index, False):
            return False

        # 然后检查监控标的数据
        now = datetime.now()
        server_data = self.last_data_time.get(server_index, {})

        for vt_symbol in self.monitor_vt_symbols:
            if vt_symbol in server_data:
                last_time = server_data[vt_symbol]
                if (now - last_time).total_seconds() > self.data_timeout:
                    return False
            else:
                return False

        # 所有检查都通过才算健康
        return True

    def  _try_switch_server(self) -> bool:
        """尝试切换到备用服务器"""
        # 尝试切换到下一个健康的可用服务器
        for i in range(1, len(self.servers)):
            next_index = (self.current_server_index + i) % len(self.servers)
            if next_index in self.clients:
                is_healthy = self._is_server_healthy(next_index)
                if is_healthy and self._switch_to_server(next_index):
                    return True

        self.write_log("所有备用服务器均不可用或数据异常")
        return False

    def _unsubscribe_all(self) -> None:
        """退订当前服务器的所有标的"""
        # 退订所有标的，不更新subscribed_symbols集合
        for vt_symbol in self.subscribed_symbols.copy():
            try:
                symbol, exchange = extract_vt_symbol(vt_symbol)
                req = SubscribeRequest(symbol=symbol, exchange=exchange)
                self.unsubscribe(req, update_symbols=False)
                self.write_log(f"退订标的: {vt_symbol}")
            except Exception as e:
                self.write_log(f"退订标的{vt_symbol}失败: {str(e)}")

    def _resubscribe_all(self) -> None:
        """重新订阅所有标的到当前服务器"""
        # 重新订阅所有标的，不更新subscribed_symbols集合
        for vt_symbol in self.subscribed_symbols.copy():
            try:
                symbol, exchange = extract_vt_symbol(vt_symbol)
                req = SubscribeRequest(symbol=symbol, exchange=exchange)
                self.subscribe(req, update_symbols=False)
                self.write_log(f"重新订阅标的: {vt_symbol}")
            except Exception as e:
                self.write_log(f"重新订阅{vt_symbol}失败: {str(e)}")

    def on_client_disconnected(self, client_index: int) -> None:
        """客户端连接断开回调"""
        self.server_connection_status[client_index] = False
        server_name = self.servers[client_index].get("主动请求地址", f'服务器{client_index}')
        self.write_log(f"检测到服务器{client_index}({server_name})连接断开")
        
        # 如果是当前活跃服务器断开，立即尝试切换
        if client_index == self.current_server_index:
            self.write_log(f"当前活跃服务器{client_index}断开，立即尝试切换")
            self._try_switch_server()

