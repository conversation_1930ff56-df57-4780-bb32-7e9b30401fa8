from typing import Dict, Any
from datetime import datetime

from vnpy.event import Event
from ..rpc import RpcClient
from vnpy.trader.gateway import BaseGateway
from vnpy.trader.object import (
    LogData, 
    SubscribeRequest,
    OrderRequest,
    CancelRequest
)
from vnpy.trader.event import EVENT_LOG


class RpcTaskSubGateway(BaseGateway):
    """
    RPC任务订阅网关
    """

    default_name: str = "RPC_TASK"

    default_setting: dict[str, str] = {
        "主动请求地址": "tcp://127.0.0.1:2014",
        "推送订阅地址": "tcp://127.0.0.1:4102",
        "用户名": "user1",
        "简单模式": "否"
    }

    def __init__(self, event_engine, gateway_name: str) -> None:
        """构造函数"""
        super().__init__(event_engine, gateway_name)

        self.client: RpcClient = RpcClient()
        self.client.callback = self.client_callback
        
        self.username: str = ""
        self.tasks: Dict[str, Any] = {}
        self.task_count: int = 0
        self.simple_mode: bool = False

    def connect(self, setting: dict) -> None:
        """连接服务器"""
        self.username = setting["用户名"]
        req_address: str = setting["主动请求地址"]
        pub_address: str = setting["推送订阅地址"]
        self.simple_mode = setting.get("简单模式", "否") == "是"

        # 订阅频道
        self.client.subscribe_topic("")
        self.client.start(req_address, pub_address)

        self.write_log(
            f"服务器连接成功，主动请求地址：{req_address}，"
            f"推送订阅地址：{pub_address}，"
            f"简单模式：{'开启' if self.simple_mode else '关闭'}"
        )

    def subscribe(self, req: SubscribeRequest) -> None:
        """订阅行情"""
        pass

    def send_order(self, req: OrderRequest) -> str:
        """委托下单"""
        return ""

    def cancel_order(self, req: CancelRequest) -> None:
        """委托撤单"""
        pass

    def query_account(self) -> None:
        """查询资金"""
        pass

    def query_position(self) -> None:
        """查询持仓"""
        pass

    def close(self) -> None:
        """关闭连接"""
        self.client.stop()
        self.client.join()

    def client_callback(self, topic: str, data: Any) -> None:
        """回调函数"""
        if topic != self.username:
            return
            
        if self.simple_mode:
            # 简单模式：直接处理字符串消息
            if data == "ok":
                self.write_log(f"收到简单消息：{data}")
                self.task_count += 1
                self.write_log(f"{self.username}已完成任务数: {self.task_count}")
        else:
            # 完整模式：处理Event对象
            if not isinstance(data, Event):
                return
                
            if data.type == "task":
                task_data = data.data
                task_id = task_data["task_id"]
                
                self.write_log(
                    f"收到任务：{task_data['vt_symbol']}, "
                    f"价格: {task_data['price']:.2f}, "
                    f"数量: {task_data['volume']:.4f}"
                )
                
                self.tasks[task_id] = task_data
                
                # 处理任务
                self.process_task(task_id)
            
    def process_task(self, task_id: str) -> None:
        """处理任务"""
        task = self.tasks.get(task_id)
        if not task:
            return
            
        # 这里只是简单返回任务完成通知
        self.write_log(
            f"处理任务[{task_id}]: {task['vt_symbol']}, "
            f"价格: {task['price']:.2f}, "
            f"数量: {task['volume']:.4f}"
        )
        
        # 发送任务完成通知
        self.client.on_task_finished(self.username, task_id)
        
        # 删除已完成的任务
        self.tasks.pop(task_id)
        
        # 更新任务计数
        self.task_count += 1
        self.write_log(f"{self.username}已完成任务数: {self.task_count}")
