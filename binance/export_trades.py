import os
import time
import requests
import pandas as pd
from io import StringIO, BytesIO
from datetime import datetime
import zipfile

from vnpy_evo.event import EventEngine
from vnpy_evo.trader.engine import MainEngine
from vnpy_evo.trader.utility import load_json

from prod.binance_linear_gateway import BinanceLinearGateway

def main():
    """主入口函数"""
    event_engine = EventEngine()
    main_engine = MainEngine(event_engine)
    
    # 添加币安网关
    main_engine.add_gateway(BinanceLinearGateway)
    
    # 加载连接设置
    setting = load_json("connect_binance.json")
    api_key = setting["API Key"]
    proxy_host = setting["Proxy Host"]
    proxy_port = setting["Proxy Port"]
    sheet_name = api_key[:10]  # 使用API Key前10位作为sheet名称
    
    # 连接到币安
    main_engine.connect(setting, "BINANCE_LINEAR")
    gateway = main_engine.get_gateway(BinanceLinearGateway.default_name)
    
    # 请求下载ID并等待URL就绪
    gateway.rest_api.query_trade_history_id()
    
    # 等待URL就绪
    timeout = 60*1000  # 超时时间
    start_time = time.time()
    while not gateway.trade_history_ready:
        if time.time() - start_time > timeout:
            main_engine.write_log("获取下载链接超时")
            main_engine.close()
            return
        time.sleep(1)
        
    # 下载文件
    try:
        response = requests.get(
            gateway.trade_history_url,
            proxies={
                "http": f"http://{proxy_host}:{proxy_port}",
                "https": f"http://{proxy_host}:{proxy_port}"
            }
        )
        
        if response.status_code != 200:
            main_engine.write_log(f"下载失败，状态码：{response.status_code}")
            main_engine.write_log(f"响应内容：{response.text}")
            main_engine.close()
            return
            
        # 处理zip文件
        zip_file = zipfile.ZipFile(BytesIO(response.content))
        main_engine.write_log(f"ZIP文件内容列表: {zip_file.namelist()}")
        
        # 读取zip中的csv文件
        csv_filename = zip_file.namelist()[0]  # 获取第一个文件名
        csv_content = zip_file.read(csv_filename).decode('utf-8')
        main_engine.write_log(f"CSV内容预览：{csv_content[:1000]}")
        
        # 检查CSV是否为空
        if not csv_content.strip():
            main_engine.write_log("警告：CSV文件内容为空")
            main_engine.close()
            return
            
        # 保存为Excel
        df = pd.read_csv(StringIO(csv_content))
        
        # 检查数据帧是否为空
        if df.empty:
            main_engine.write_log("警告：没有交易记录")
            main_engine.close()
            return
            
        filename = f"trades_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        with pd.ExcelWriter(filename) as writer:
            df.to_excel(writer, sheet_name=sheet_name, index=False)
        
        main_engine.write_log(f"交易历史已保存至: {filename}")
        main_engine.write_log(f"数据行数: {len(df)}")
        
    except Exception as e:
        main_engine.write_log(f"处理数据时发生错误: {str(e)}")
        if 'response' in locals():
            if hasattr(response, 'content'):
                main_engine.write_log(f"响应内容类型: {type(response.content)}")
                main_engine.write_log(f"响应内容大小: {len(response.content)} bytes")
            if hasattr(response, 'headers'):
                main_engine.write_log(f"响应头: {dict(response.headers)}")
    finally:
        main_engine.close()

if __name__ == "__main__":
    main() 