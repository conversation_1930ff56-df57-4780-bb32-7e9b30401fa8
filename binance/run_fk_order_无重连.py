import sys
from datetime import datetime
from typing import Dict, Optional, Set
import time
from decimal import Decimal
import yaml
import json
from pathlib import Path
import traceback
from dataclasses import dataclass
from queue import Queue
from threading import Thread, Lock

from peewee import (
    Model, CharField, DoubleField, DateTimeField, 
    IntegerField, CompositeKey, MySQLDatabase,
    SQL, fn, TextField, BigIntegerField
)
from playhouse.shortcuts import ReconnectMixin

from vnpy_evo.event import Event, EventEngine
from vnpy_evo.trader.engine import MainEngine
from vnpy_evo.trader.object import (
    OrderRequest, Direction, Offset, OrderType,
    Exchange, Status, AccountData, PositionData,
    TickData, OrderData, TradeData, SubscribeRequest
)
from vnpy_evo.trader.utility import round_to
from vnpy_evo.trader.event import EVENT_ORDER, EVENT_TRADE, EVENT_TIMER, EVENT_POSITION
from prod.binance_linear_gateway import BinanceLinearGateway

class EnumId:
    def __init__(self, *args):
        self.idx2name = {}
        for (idx, name) in enumerate(args):
            setattr(self, name, idx)
            self.idx2name[idx] = name

    def to_str(self, idx):
        return self.idx2name.get(idx, "NOTFOUND")

# 状态定义
OrderStatusEnum = EnumId(
    "INIT",          # 0: 初始状态
    "RECEIVED",      # 1: 任务接收
    "FINISHED",      # 2: 完成
    "ORDERING",      # 3: 订单处理中
    "NOTFOUND5",
    "ERROR"          # 5: 异常
)

# 添加重连支持的MySQL数据库类
class ReconnectMySQLDatabase(ReconnectMixin, MySQLDatabase):
    """带有重连机制的MySQL数据库类"""
    pass

@dataclass
class UserTaskStatus:
    """用户任务状态"""
    status: int = OrderStatusEnum.INIT  # 状态
    error_msg: str = ""  # 错误信息

@dataclass
class DbUpdateTask:
    """数据库更新任务"""
    task_id: int
    status: int
    remarks: str

class DbUpdateQueue:
    """数据库更新队列管理器"""
    def __init__(self):
        self._queue: Dict[int, DbUpdateTask] = {}  # task_id -> DbUpdateTask
        self._lock = Lock()
        self._thread: Optional[Thread] = None
        self._active = False
        self._updating = False  # 标记是否正在更新数据库

    def start(self):
        """启动数据库更新线程"""
        self._active = True
        self._thread = Thread(target=self._run, daemon=True)
        self._thread.start()

    def stop(self):
        """停止数据库更新线程"""
        self._active = False
        if self._thread:
            self._thread.join()

    def put(self, task_id: int, status: int, remarks: str):
        """放入更新任务，相同task_id的新任务会替换旧任务"""
        with self._lock:
            self._queue[task_id] = DbUpdateTask(task_id, status, remarks)

    def is_empty(self) -> bool:
        """检查队列是否为空且没有正在进行的更新"""
        with self._lock:
            return len(self._queue) == 0 and not self._updating

    def _run(self):
        """数据库更新线程"""
        while self._active:
            tasks_to_update = []
            
            # 仅在获取任务时持有锁
            with self._lock:
                if self._queue:
                    tasks_to_update = list(self._queue.values())
                    self._queue.clear()
                    self._updating = True  # 标记开始更新

            # 更新数据库（不持有锁）
            if tasks_to_update:
                try:
                    with FkOrder._meta.database.atomic():
                        for task in tasks_to_update:
                            FkOrder.update(
                                status=task.status,
                                remarks=task.remarks
                            ).where(FkOrder.id == task.task_id).execute()
                except Exception as e:
                    print(f"数据库更新错误: {str(e)}\n{traceback.format_exc()}")
                finally:
                    # 标记更新完成
                    with self._lock:
                        self._updating = False

            # 每秒检查一次
            time.sleep(1)

class TaskStatus:
    """任务状态管理器"""
    def __init__(self, task_id: int, app: "FkOrderApp"):
        self.task_id = task_id
        self.app = app
        self._user_states: Dict[str, UserTaskStatus] = {}

    def __getitem__(self, username: str) -> UserTaskStatus:
        if username not in self._user_states:
            self._user_states[username] = UserTaskStatus()
        return self._user_states[username]

    def __setitem__(self, username: str, state: UserTaskStatus) -> None:
        """设置用户状态时自动更新数据库"""
        self._user_states[username] = state
        self._update_task_status()

    def set_user_status(self, username: str, status: int, error_msg: str = "") -> None:
        """设置用户状态的便捷方法"""
        self[username] = UserTaskStatus(status, error_msg)

    def _update_task_status(self) -> None:
        """
        根据所有用户的状态，更新任务的整体状态
        
        规则：
        1. 如果所有用户都是FINISHED -> 任务状态为FINISHED
        2. 如果有用户是ORDERING -> 任务状态为ORDERING
        3. 如果有用户是ERROR -> 任务状态为ERROR
        4. 其他情况为RECEIVED
        """
        if not self._user_states:
            return

        user_states = self._user_states.values()
        
        # 确定任务状态
        if all(state.status == OrderStatusEnum.FINISHED for state in user_states):
            new_status = OrderStatusEnum.FINISHED
        elif any(state.status == OrderStatusEnum.ORDERING for state in user_states):
            new_status = OrderStatusEnum.ORDERING
        elif any(state.status == OrderStatusEnum.ERROR for state in user_states):
            new_status = OrderStatusEnum.ERROR
        else:
            new_status = OrderStatusEnum.RECEIVED

        # 将更新任务放入队列
        self.app.db_update_queue.put(
            self.task_id,
            new_status,
            self._generate_remarks()
        )

    def _generate_remarks(self) -> str:
        """生成任务备注"""
        remarks_list = []
        for username, state in self._user_states.items():
            status_str = f"{username}:{state.status}"
            if state.error_msg:
                status_str += f"({state.error_msg})"
            remarks_list.append(status_str)
        return "; ".join(remarks_list)


class FkOrder(Model):
    """平仓任务表"""
    id = BigIntegerField(primary_key=True)
    symbol = CharField(max_length=63)
    ping_volume = DoubleField()
    user = CharField(max_length=63)
    create_date = DateTimeField()
    update_date = DateTimeField(constraints=[SQL('DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')])
    status = IntegerField(constraints=[SQL('DEFAULT 0')])  # 默认为初始状态0
    remarks = TextField(null=True)
    del_flag = IntegerField(constraints=[SQL('DEFAULT 0')])

    class Meta:
        database = None  # 将在运行时设置
        table_name = 'fk_order'
        indexes = (
            (('symbol', 'status'), False),
        )

class FkUser(Model):
    """用户目录表"""
    id = IntegerField(primary_key=True)
    user = CharField(unique=True)

    class Meta:
        database = None  # 将在运行时设置
        table_name = 'fk_user'

'''
CREATE TABLE `fk_order` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `symbol` VARCHAR(63) NOT NULL COMMENT '交易对(包含BINANCE等后缀)',
    `ping_volume` DOUBLE NOT NULL DEFAULT 0 COMMENT '平仓量',
    `user` VARCHAR(63) NOT NULL COMMENT '用户名',
    `create_date` DATETIME NOT NULL COMMENT '任务创建时间',
    `update_date` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `status` TINYINT NOT NULL DEFAULT 0 COMMENT '任务状态:0初始,1已接收,2完成,5异常',
    `remarks` TEXT COMMENT '备注说明',
    `del_flag` TINYINT NOT NULL DEFAULT 0 COMMENT '删除标记:0存在,1删除',
    PRIMARY KEY (`id`),
    INDEX `idx_symbol_status` (`symbol`, `status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='平仓任务表';

-- 创建用户目录表
CREATE TABLE `fk_user` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user` VARCHAR(63) NOT NULL COMMENT '用户名',
    PRIMARY KEY (`id`),
    UNIQUE INDEX `idx_user` (`user`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户目录表';

-- 插入用户数据
INSERT INTO `fk_user` (`user`) VALUES 
('all'),
('zhmain'),
('Liub'),
('Gaoy'),
('Zhangl'),
('Wangxy'),
('Xiey'),
('Zhangqf'),
('Zouyw'),
('Liuy'),
('zhuser1');
'''

class UserGateway(BinanceLinearGateway):
    """每个用户的网关"""        
    def write_log(self, msg: str) -> None:
        """重写日志方法，添加用户标识"""
        super().write_log(f"[{self.gateway_name}] {msg}")

class PositionManager:
    """持仓管理器"""
    
    def __init__(self, event_engine: EventEngine) -> None:
        """构造函数"""
        self.event_engine = event_engine
        self.positions: Dict[str, Dict[str, float]] = {}  # username: {vt_symbol: volume}
        
        self.register_event()
        
    def register_event(self) -> None:
        """注册事件监听"""
        self.event_engine.register(EVENT_POSITION, self.process_position_event)
        
    def process_position_event(self, event: Event) -> None:
        """处理持仓事件"""
        position: PositionData = event.data
        username = position.gateway_name.split(".")[0]  # 从gateway_name中提取用户名
        
        if username not in self.positions:
            self.positions[username] = {}
            
        self.positions[username][position.vt_symbol] = position.volume
        
    def get_position(self, username: str, vt_symbol: str) -> float:
        """获取指定用户指定合约的持仓数量"""
        if username not in self.positions:
            return 0.0
        return self.positions[username].get(vt_symbol, 0.0)

class FkOrderApp:
    """平仓任务执行应用"""
    
    def __init__(self):
        """构造函数"""
        self.event_engine = EventEngine()
        self.main_engine = MainEngine(self.event_engine)
        
        self.user_gateways: Dict[str, UserGateway] = {}  # 用户交易网关字典
        self.quote_gateway: Optional[UserGateway] = None  # 行情订阅专用网关
        self.db = None
        
        # 订单管理相关
        self.active_orders: Dict[str, Dict] = {}  # vt_orderid: order_info
        self.order_task_map: Dict[str, int] = {}  # vt_orderid: task_id
        self.task_order_map: Dict[int, Set[str]] = {}  # task_id: set of vt_orderid
        
        # 任务执行状态缓存
        self.task_states: Dict[int, TaskStatus] = {} # task_id: TaskStatus
        
        # 行情订阅管理
        self.subscribed_symbols: Set[str] = set()  # 已订阅的合约集合
        self.symbol_tasks: Dict[str, Set[int]] = {}  # symbol -> set of task_ids that need this symbol
        
        # 持仓管理
        self.position_manager = PositionManager(self.event_engine)
        
        # 数据库更新队列
        self.db_update_queue = DbUpdateQueue()
        
        self.load_config()
        self.setup_database()
        self.init_gateway()
        self.register_event()

        self.timer_count = 0
        
        self.db_update_queue.start()

    def register_event(self) -> None:
        """注册事件监听"""
        self.event_engine.register(EVENT_ORDER, self.process_order_event)
        self.event_engine.register(EVENT_TIMER, self.process_timer_event)

    def process_order_event(self, event: Event) -> None:
        """处理委托事件"""
        order: OrderData = event.data
        if order.vt_orderid not in self.active_orders:
            return
            
        # 更新活动订单状态
        self.active_orders[order.vt_orderid] = order
        
        # 获取任务信息
        task_id = self.order_task_map.get(order.vt_orderid)
        if not task_id:
            return
        
        task_status = self.task_states.get(task_id)
        if not task_status:
            return
        
        username = order.gateway_name.split(".")[0]
        
        # 如果订单被拒绝，立即更新用户状态
        if order.status == Status.REJECTED:
            task_status.set_user_status(
                username,
                OrderStatusEnum.ERROR,
                f"订单被拒绝: {getattr(order, 'rejected_reason', '')}"
            )
            self._clean_task_cache_if_done(task_id)
            return
        
        # 如果订单已经完成，更新用户状态
        if not order.is_active():
            if order.traded != order.volume:
                task_status.set_user_status(
                    username,
                    OrderStatusEnum.ERROR,
                    f"订单部分成交: {order.traded}/{order.volume}"
                )
            else:
                task_status.set_user_status(
                    username,
                    OrderStatusEnum.FINISHED
                )
            self._clean_task_cache_if_done(task_id)

    def _clean_task_cache_if_done(self, task_id: int) -> None:
        """检查任务是否完成，如果完成则清理相关缓存"""
        try:
            # 获取任务相关的所有订单
            vt_orderids = self.task_order_map.get(task_id, set())
            
            # 检查是否有活跃订单
            has_active_orders = False
            for vt_orderid in vt_orderids:
                order = self.active_orders.get(vt_orderid)
                if order and order.is_active():
                    has_active_orders = True
                    break
            
            # 如果没有活跃订单，且所有用户状态都已经稳定（FINISHED或ERROR），则清理缓存
            task_status = self.task_states.get(task_id)
            if not has_active_orders and task_status:
                all_users_done = True
                for user_state in task_status._user_states.values():
                    if user_state.status not in {OrderStatusEnum.FINISHED, OrderStatusEnum.ERROR}:
                        all_users_done = False
                        break
                
                if all_users_done:
                    # 清理订单相关缓存
                    for vt_orderid in vt_orderids:
                        self.active_orders.pop(vt_orderid, None)
                        self.order_task_map.pop(vt_orderid, None)
                    self.task_order_map.pop(task_id, None)
                    
                    # 获取任务信息用于处理行情订阅
                    task = FkOrder.get_or_none(FkOrder.id == task_id)
                    if task:
                        self.unsubscribe_symbol(task.symbol, task_id)
                    
                    # 清理任务状态缓存
                    self.task_states.pop(task_id, None)
                
        except Exception as e:
            self.write_log(f"清理任务缓存时发生错误: {str(e)}\n{traceback.format_exc()}")

    def process_timer_event(self, event: Event) -> None:
        """处理定时事件"""
        self.timer_count += 1
        if self.timer_count < 10:
            return
        self.timer_count = 0
        
        # 如果有待更新的数据库任务，跳过查询新任务
        if not self.db_update_queue.is_empty():
            return
            
        try:
            # 查询已删除的任务
            deleted_tasks = (FkOrder
                    .select()
                    .where(
                        (FkOrder.del_flag == 1) & 
                        (FkOrder.id.in_([task_id for task_id in self.task_states.keys()]))
                    ))
            
            # 处理已删除的任务
            for task in deleted_tasks:
                self.write_log(f"处理已删除的任务：{task.id}")
                self.process_deleted_task(task)
            
            # 查询待执行的任务
            tasks = (FkOrder
                    .select()
                    .where(
                        (FkOrder.status.in_([OrderStatusEnum.INIT, OrderStatusEnum.RECEIVED])) & 
                        (FkOrder.del_flag == 0)
                    )
                    .order_by(FkOrder.create_date))
            
            for task in tasks:
                self.write_log(f"开始处理任务：{task.id}")
                self.process_order(task)
            
        except Exception as e:
            self.write_log(f"任务扫描过程中发生错误: {str(e)}\n{traceback.format_exc()}")

    def process_deleted_task(self, task: FkOrder) -> None:
        """处理已删除的任务"""
        try:
            task_id = task.id
            task_status = self.task_states.get(task_id)
            if not task_status:
                return
                
            # 获取该任务的所有活动订单
            active_orderids = set()
            if task_id in self.task_order_map:
                active_orderids = self.task_order_map[task_id]
            
            # 撤销所有活动订单
            for vt_orderid in list(active_orderids):
                order = self.main_engine.get_order(vt_orderid)
                if order and order.is_active():
                    req = order.create_cancel_request()
                    self.main_engine.cancel_order(req, order.gateway_name)
                    self.write_log(f"撤销已删除任务的订单：{vt_orderid}")
            
            # 更新任务状态为ERROR
            for username in task_status._user_states.keys():
                if task_status[username].status == OrderStatusEnum.ORDERING:
                    task_status.set_user_status(
                        username,
                        OrderStatusEnum.ERROR,
                        "任务已被删除"
                    )
            
            # 清理相关缓存
            self._clean_task_cache_if_done(task_id)
            
            # 取消行情订阅
            if task.symbol in self.symbol_tasks:
                self.unsubscribe_symbol(task.symbol, task_id)
            
            self.write_log(f"已删除任务处理完成：{task_id}")
            
        except Exception as e:
            self.write_log(f"处理已删除任务时发生错误: {str(e)}\n{traceback.format_exc()}")

    def subscribe_symbol(self, symbol: str, task_id: int) -> bool:
        """订阅合约
        
        Args:
            symbol: 合约代码
            task_id: 需要此行情的任务ID
        
        Returns:
            bool: 订阅是否成功
        """
        # 记录任务对该合约的需求
        if symbol not in self.symbol_tasks:
            self.symbol_tasks[symbol] = set()
        self.symbol_tasks[symbol].add(task_id)
            
        # 如果已经订阅了，不需要重复订阅
        if symbol in self.subscribed_symbols:
            return True
            
        # 使用专用的行情订阅网关
        if not self.quote_gateway:
            self.write_log("未设置行情订阅网关，无法订阅行情")
            return False
            
        contract = self.main_engine.get_contract(symbol)
        if not contract:
            self.write_log(f"未找到合约信息，无法订阅行情: {symbol}")
            return False
            
        req = SubscribeRequest(
            symbol=contract.symbol,
            exchange=contract.exchange
        )
        self.quote_gateway.subscribe(req)
        
        self.subscribed_symbols.add(symbol)
        self.write_log(f"订阅行情: {symbol}")
        return True

    def unsubscribe_symbol(self, symbol: str, task_id: int) -> None:
        """退订合约
        
        Args:
            symbol: 合约代码
            task_id: 不再需要此行情的任务ID
        """
        # 移除任务对该合约的需求
        if symbol in self.symbol_tasks:
            self.symbol_tasks[symbol].discard(task_id)
            
            # 如果没有任何任务需要此合约的行情，则退订
            if not self.symbol_tasks[symbol]:
                if symbol in self.subscribed_symbols:
                    # 使用专用的行情订阅网关
                    if not self.quote_gateway:
                        self.write_log("未设置行情订阅网关，无法退订行情")
                        return
                        
                    contract = self.main_engine.get_contract(symbol)
                    if not contract:
                        return
                        
                    req = SubscribeRequest(
                        symbol=contract.symbol,
                        exchange=contract.exchange
                    )
                    self.quote_gateway.unsubscribe(req)
                    
                    self.subscribed_symbols.remove(symbol)
                    self.write_log(f"退订行情: {symbol}")
                
                # 清理缓存
                self.symbol_tasks.pop(symbol)

    def write_log(self, msg: str) -> None:
        """
        Write a log event from gateway with caller information.
        """
        frame = sys._getframe(1)
        func_name = frame.f_code.co_name
        line_no = frame.f_lineno
        class_name = self.__class__.__name__
        formatted_msg = f"[{class_name}.{func_name}:{line_no}] {msg}"
        self.main_engine.write_log(formatted_msg)

    def load_config(self, config_path: str = "fk_order_config.yaml"):
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)

            # 提取数据库配置
            if "database" not in config:
                raise ValueError("配置文件中缺少数据库配置")
            self.db_config = config["database"]

            # 加载用户配置
            self.users_config = config.get("users", {})

        except Exception as e:
            self.write_log(f"加载配置文件失败: {str(e)}\n{traceback.format_exc()}")
            raise

    def setup_database(self):
        """初始化数据库"""
        try:
            # 从配置中获取数据库设置
            db_config = self.db_config
            
            # 创建数据库连接
            self.db = ReconnectMySQLDatabase(
                database=db_config["database"],
                user=db_config["user"],
                password=db_config["password"],
                host=db_config["host"],
                port=db_config["port"]
            )
            
            # 设置数据库连接
            FkOrder._meta.database = self.db
            FkUser._meta.database = self.db
            
            # 连接数据库
            self.db.connect()
            self.write_log("数据库连接成功")
            
        except Exception as e:
            self.write_log(f"数据库连接失败: {str(e)}\n{traceback.format_exc()}")
            raise

    def init_gateway(self):
        """初始化交易接口"""
        try:
            # 连接所有配置的用户
            for username, settings in self.users_config.items():
                work_dir = settings["work_dir"]
                connect_file = Path(work_dir) / ".vntrader" / "connect_binance_linear.json"
                
                if not connect_file.exists():
                    self.write_log(f"用户 {username} 的连接配置文件不存在")
                    continue

                # 读取连接配置
                with open(connect_file, 'r', encoding='utf-8') as f:
                    connect_config = json.load(f)

                # 创建用户网关
                gateway = UserGateway(self.event_engine, f"{username}.BINANCE_LINEAR")
                gateway.connect(connect_config)
                self.user_gateways[username] = gateway
                
                # 设置第一个连接的用户网关为行情订阅网关
                if self.quote_gateway is None:
                    self.quote_gateway = gateway
                    self.write_log(f"设置用户 {username} 的网关为行情订阅网关")
                
                self.write_log(f"用户 {username} 的交易接口初始化成功")
                
                # 每个用户连接之间添加1秒间隔
                time.sleep(1)

        except Exception as e:
            self.write_log(f"初始化交易接口失败: {str(e)}\n{traceback.format_exc()}")
            raise

    def process_order(self, task: FkOrder):
        """处理单个平仓任务"""
        try:
            # 初始化任务状态
            task_status = TaskStatus(task.id, self)
            self.task_states[task.id] = task_status

            # 获取需要执行平仓的用户列表
            if task.user == "all":
                users = [user.user for user in FkUser.select().where(FkUser.user != "all")]
            else:
                user = FkUser.get_or_none(FkUser.user == task.user)
                if not user:
                    task_status.set_user_status(
                        task.user, 
                        OrderStatusEnum.ERROR,
                        f"未找到用户: {task.user}"
                    )
                    self._clean_task_cache_if_done(task.id)
                    return
                users = [task.user]

            # 订阅行情
            if not self.subscribe_symbol(task.symbol, task.id):
                self.write_log(f"订阅行情失败，等待下次重试: {task.symbol}")
                return
            
            # 获取合约信息
            contract = self.main_engine.get_contract(task.symbol)
            if not contract:
                error_msg = f"未找到合约信息: {task.symbol}"
                self.write_log(error_msg)
                return
            
            # 获取行情数据
            tick = self.main_engine.get_tick(task.symbol)
            if not tick:
                error_msg = f"未获取到行情数据: {task.symbol}"
                self.write_log(error_msg)
                return

            # 获取到行情数据后，将所有用户的状态设置为RECEIVED（如果是INIT状态）
            for username in users:
                if task.status == OrderStatusEnum.INIT:  # 只有INIT状态才设置为RECEIVED
                    task_status.set_user_status(username, OrderStatusEnum.RECEIVED)
            
            # 初始化任务订单集合
            self.task_order_map[task.id] = set()
            
            # 为每个用户执行平仓
            for username in users:
                # 如果用户状态已经是ERROR，跳过处理
                if task_status[username].status == OrderStatusEnum.ERROR:
                    continue
                    
                if username not in self.user_gateways:
                    task_status.set_user_status(
                        username,
                        OrderStatusEnum.ERROR,
                        f"未找到用户 {username} 的交易接口"
                    )
                    continue
                
                gateway = self.user_gateways[username]
                
                try:
                    # 获取持仓
                    position_volume = self.position_manager.get_position(username, task.symbol)
                    
                    # 计算平仓量
                    if task.ping_volume == 0:  # ping_volume为0表示全平
                        volume = abs(position_volume)
                    else:
                        volume = min(abs(position_volume), abs(task.ping_volume))
                        
                    if volume <= 0:
                        task_status.set_user_status(
                            username,
                            OrderStatusEnum.FINISHED,
                            f"用户 {username} 无需平仓(无持仓)"
                        )
                        gateway.write_log(f"用户 {username} 无需平仓(无持仓)")
                        continue
                    
                    # 处理数量精度
                    volume = round_to(volume, contract.min_volume)
                    if volume <= 0:  # 如果精度处理后变为0，则跳过
                        task_status.set_user_status(
                            username,
                            OrderStatusEnum.FINISHED,
                            f"用户 {username} 平仓量({volume})小于最小交易量({contract.min_volume})"
                        )
                        gateway.write_log(f"用户 {username} 平仓量过小，无需平仓")
                        continue
                    
                    # 计算委托价格
                    price_add_percent = 0.02  # 超价比例2%
                    if position_volume > 0:  # 多头持仓，需要卖出平仓
                        order_price = tick.last_price * (1 - price_add_percent)
                        if tick.limit_down:  # 检查是否有跌停价
                            order_price = max(order_price, tick.limit_down)
                    else:  # 空头持仓，需要买入平仓
                        order_price = tick.last_price * (1 + price_add_percent)
                        if tick.limit_up:  # 检查是否有涨停价
                            order_price = min(order_price, tick.limit_up)
                            
                    # 处理价格精度
                    order_price = round_to(order_price, contract.pricetick)
                    
                    # 创建平仓请求
                    req = OrderRequest(
                        symbol=contract.symbol,
                        exchange=contract.exchange,
                        direction=Direction.SHORT if position_volume > 0 else Direction.LONG,
                        offset=Offset.CLOSE,
                        type=OrderType.LIMIT,
                        volume=volume,
                        price=order_price
                    )
                    
                    # 发送订单
                    vt_orderid = gateway.send_order(req)
                    if not vt_orderid:
                        task_status.set_user_status(
                            username,
                            OrderStatusEnum.ERROR,
                            f"用户 {username} 发送平仓订单失败"
                        )
                        gateway.write_log(f"发送平仓订单失败")
                        continue
                    
                    # 记录订单信息
                    self.active_orders[vt_orderid] = None  # 等待订单回报更新
                    self.order_task_map[vt_orderid] = task.id
                    self.task_order_map[task.id].add(vt_orderid)
                    
                    # 更新用户任务状态为ORDERING
                    task_status.set_user_status(username, OrderStatusEnum.ORDERING)
                    gateway.write_log(f"发送平仓订单成功，订单号：{vt_orderid}")
                    
                except Exception as e:
                    error_msg = f"处理用户 {username} 订单时发生错误: {str(e)}"
                    task_status.set_user_status(username, OrderStatusEnum.ERROR, error_msg)
                    gateway.write_log(error_msg)
            
            # 如果没有发出任何订单，检查是否需要清理缓存
            self._clean_task_cache_if_done(task.id)
            
        except Exception as e:
            error_msg = f"处理平仓任务失败: {str(e)}\n{traceback.format_exc()}"
            self.write_log(error_msg)
            if task.user == "all":
                for username in users:
                    task_status.set_user_status(username, OrderStatusEnum.ERROR, error_msg)
            else:
                task_status.set_user_status(task.user, OrderStatusEnum.ERROR, error_msg)
            self._clean_task_cache_if_done(task.id)

    def run(self):
        """运行应用"""            
        try:
            self.write_log("平仓任务执行应用已启动")
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            self.write_log("应用正在关闭...")
        finally:
            self.close()

    def close(self):
        """关闭应用"""
        self.db_update_queue.stop()
        if self.db:
            self.db.close()
        self.main_engine.close()

def main():
    """主函数"""
    app = FkOrderApp()
    app.run()

if __name__ == "__main__":
    main() 