from time import sleep
import sys
import signal

from vnpy.event import EventEngine
from vnpy.trader.engine import MainEngine
from vnpy.trader.utility import load_json, save_json

from vnpy_rpcservice.rpc_service.engine_task import RpcTaskPubEngine, EVENT_RPC_TASK_LOG

class RpcTaskPubApp:
    """RPC任务发布应用"""
    setting_filename: str = "rpc_task_pub_setting.json"
    
    def __init__(self):
        """构造函数"""
        self.event_engine = EventEngine()
        self.main_engine = MainEngine(self.event_engine)
        
        self.load_setting()
        self.register_event()
        self.init_engines()
        
        self._active = True  # 添加运行状态标记
        
    def load_setting(self) -> None:
        """加载配置"""
        self.setting = load_json(self.setting_filename)
        if not self.setting:
            self.setting = {
                "rep_address": "tcp://*:20014",
                "pub_address": "tcp://*:41002"
            }
            save_json(self.setting_filename, self.setting)
        
    def register_event(self) -> None:
        """注册事件监听"""
        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        # 在Windows平台上signal模块可能不完全支持
        if sys.platform != 'win32':
            # 忽略SIGTSTP信号(Ctrl+Z)，避免进程被挂起
            signal.signal(signal.SIGTSTP, signal.SIG_IGN)

        # 注册日志事件监听
        log_engine = self.main_engine.get_engine("log")
        self.event_engine.register(EVENT_RPC_TASK_LOG, log_engine.process_log_event)

            
    def _signal_handler(self, signum, frame):
        """信号处理函数"""
        sig_name = signal.Signals(signum).name
        self.main_engine.write_log(f"收到信号: {sig_name}，准备关闭应用...")
        self._active = False
        
    def init_engines(self) -> None:
        """初始化引擎"""
        # 添加RPC任务发布引擎
        self.rpc_engine = self.main_engine.add_engine(RpcTaskPubEngine)
        
        
    def run(self, simple_mode: bool = False) -> None:
        """运行应用"""
        self.main_engine.write_log("RPC任务发布应用启动")
        
        # 尝试启动服务
        success = self.rpc_engine.start(
            self.setting["rep_address"],
            self.setting["pub_address"],
            simple_mode
        )
        
        if not success:
            self.main_engine.write_log("RPC服务启动失败，程序退出")
            return
        
        self.main_engine.write_log(
            f"RPC服务已启动 - 请求地址：{self.setting['rep_address']}，"
            f"推送地址：{self.setting['pub_address']}，"
            f"运行模式：{'简单模式' if simple_mode else '正常模式'}"
        )
        
        while self._active:
            sleep(1)
            
        self.close()

    def close(self) -> None:
        """关闭应用"""
        self.main_engine.write_log("应用正在关闭...")
        self.rpc_engine.stop()
        self.main_engine.close()

def main() -> None:
    """主函数"""
    # 从命令行参数获取运行模式
    simple_mode = len(sys.argv) > 1 and sys.argv[1].lower() == "simple"
    app = RpcTaskPubApp()
    app.run(simple_mode)

if __name__ == "__main__":
    main()
