from vnpy.trader.setting import SETTINGS
from vnpy.trader.utility import load_json, round_to, extract_vt_symbol
from vnpy_evo.trader.event import EVENT_ACCOUNT, EVENT_POSITION, EVENT_CONTRACT, EVENT_TICK, EVENT_ORDER, EVENT_TRADE
from vnpy_evo.event import Event, EventEngine
from vnpy_evo.trader.engine import BaseEngine,MainEngine
from vnpy_evo.trader.object import (
    OrderRequest, Direction, Offset, OrderType, 
    Exchange, Status, SubscribeRequest, ContractData
)
from datetime import datetime
import time

import json
from prod.recorder_engine import RecorderEngine,OrderErrorData

from vnpy_riskmanager.engine import RiskEngine
from vnpy_riskmanager.plugin.order_value_limit import OrderValueLimit
from vnpy_riskmanager.plugin.symbol_restriction import SymbolRestriction

EVENT_ORDER_ERROR_RECORD = 'eOrderErrorRec'

class OrderTestApp():
    """订单测试应用"""
    
    def __init__(self):
        """"""
        self.event_engine = EventEngine()
        self.main_engine = MainEngine(self.event_engine)
        # self.test_vt_symbol = "ETHUSDT.BINANCE"
        self.test_vt_symbol ="BTCUSDT.BINANCE"  # 测试标的
        self.test_symbol, self.test_exchange = extract_vt_symbol(self.test_vt_symbol)
        
        self.tick = None  # 最新行情
        self.contract = None  # 合约信息
        self.test_finished = False  # 测试是否完成
        self.current_order = None  # 当前测试阶段
        self.position_volume = 0  # 持仓数量
        
        self.register_event()
        self.init_engines()

    def init_engines(self) -> None:
        """初始化引擎"""
        # 添加币安网关
        from prod.binance_linear_gateway import BinanceLinearGateway
        self.main_engine.add_gateway(BinanceLinearGateway)
        setting = load_json("connect_binance_testnet.json")
        self.main_engine.connect(setting, "BINANCE_LINEAR")
        self.main_engine.write_log("接口添加成功")

        from prod.barGen_redis_engine import BarGenEngine
        self.bar_gen_engine = self.main_engine.add_engine(BarGenEngine)
        self.bar_gen_engine.start()
        self.recorder_engine = self.main_engine.add_engine(RecorderEngine)
        self.risk_engine = self.main_engine.add_engine(RiskEngine)
        # self.order_value_limit_engine = self.main_engine.add_engine(OrderValueLimit)
        # self.symbol_restriction = self.main_engine.add_engine(SymbolRestriction)

    def register_event(self):
        """注册事件监听"""
        self.event_engine.register(EVENT_TICK, self.process_tick_event)
        self.event_engine.register(EVENT_CONTRACT, self.process_contract_event)
        self.event_engine.register(EVENT_ORDER, self.process_order_event)
        self.event_engine.register(EVENT_TRADE, self.process_trade_event)
        self.event_engine.register(EVENT_POSITION, self.process_position_event)

    def process_tick_event(self, event: Event):
        """处理TICK事件"""
        tick = event.data
        if tick.vt_symbol == self.test_vt_symbol:
            self.tick = tick
            
    def process_contract_event(self, event: Event):
        """处理合约事件"""
        contract = event.data
        if contract.vt_symbol == self.test_vt_symbol:
            self.contract = contract
            # 订阅行情
            req = SubscribeRequest(
                symbol=contract.symbol,
                exchange=contract.exchange
            )
            self.main_engine.subscribe(req, "BINANCE_LINEAR")
            self.main_engine.write_log(f"订阅{self.test_vt_symbol}行情数据")
            
    def process_position_event(self, event: Event):
        """处理持仓事件"""
        position = event.data
        if position.vt_symbol == self.test_vt_symbol:
            self.position_volume = abs(position.volume)


    def process_order_event(self, event: Event):
        """处理委托事件"""
        order = event.data
        if order.vt_symbol != self.test_vt_symbol:
            return
            
        msg = (f"收到委托回报 {order.vt_symbol} {order.direction} {order.offset} "
               f"价格={order.price} 数量={order.volume} "
               f"状态={order.status} "
               f"id={order.orderid}")
        self.main_engine.write_log(msg)
        
        # 如果订单被拒绝或者全部成交，进入下一阶段测试
        if order.status in [Status.REJECTED, Status.ALLTRADED]:
            self.test_finished = True

    def process_trade_event(self, event: Event):
        """处理成交事件"""
        trade = event.data
        if trade.vt_symbol != self.test_vt_symbol:
            return
            
        msg = (f"收到成交回报 {trade.vt_symbol} {trade.direction} {trade.offset} "
               f"价格={trade.price} 数量={trade.volume}")
        self.main_engine.write_log(msg)
        
    def send_order(
        self,
        direction: Direction,
        offset: Offset,
        price: float,
        volume: float,
        stop: bool = False,
        lock: bool = False,
        net: bool = False,
        test_stage: str = ""
    ) -> str:
        """发送订单"""
        # 等待tick数据和合约信息
        while not self.tick or not self.contract:
            time.sleep(1)
            
        # 创建测试订单
        req = OrderRequest(
            symbol=self.test_symbol,
            exchange=self.test_exchange,
            direction=direction,
            offset=offset,
            type=OrderType.LIMIT,
            price=price,
            volume=volume
        )
        
        # 发送订单
        vt_orderid = self.main_engine.send_order(req, "BINANCE_LINEAR")
        
        # 记录订单信息
        notional = price * volume
        if test_stage:
            self.main_engine.write_log(f"\n{test_stage}")
        self.main_engine.write_log(f"发送测试订单: {self.test_vt_symbol}")
        self.main_engine.write_log(f"方向: {direction} {offset}")
        self.main_engine.write_log(f"价格: {price:.8f}")
        self.main_engine.write_log(f"数量: {volume}")
        self.main_engine.write_log(f"名义价值: {notional:.8f} USDT")
        self.main_engine.write_log(f"订单号: {vt_orderid}")
        
        # 等待订单完成
        self.test_finished = False
        while not self.test_finished:
            time.sleep(5)
            if not vt_orderid:
                self.test_finished = True
            
        # 等待1秒让持仓数据更新
        time.sleep(1)
        
        return vt_orderid

    def buy(self, price: float, volume: float, stop: bool = False, lock: bool = False, net: bool = False, test_stage: str = "") -> str:
        """
        Send buy order to open a long position.
        """
        return self.send_order(Direction.LONG, Offset.OPEN, price, volume, stop, lock, net, test_stage)

    def sell(self, price: float, volume: float, stop: bool = False, lock: bool = False, net: bool = False, test_stage: str = "") -> str:
        """
        Send sell order to close a long position.
        """
        return self.send_order(Direction.SHORT, Offset.CLOSE, price, volume, stop, lock, net, test_stage)

    def short(self, price: float, volume: float, stop: bool = False, lock: bool = False, net: bool = False, test_stage: str = "") -> str:
        """
        Send short order to open as short position.
        """
        return self.send_order(Direction.SHORT, Offset.OPEN, price, volume, stop, lock, net, test_stage)

    def cover(self, price: float, volume: float, stop: bool = False, lock: bool = False, net: bool = False, test_stage: str = "") -> str:
        """
        Send cover order to close a short position.
        """
        return self.send_order(Direction.LONG, Offset.CLOSE, price, volume, stop, lock, net, test_stage)

    def run_test(self):
        """运行测试"""
        # 等待合约信息
        while not self.contract:
            self.main_engine.write_log(f'等待合约信息...')
            time.sleep(5)
            
        # 获取最小名义价值
        min_notional = self.contract.extra.get("min_notional", 0)
        if not min_notional:
            self.main_engine.write_log("无法获取最小名义价值")
            return
            
        self.main_engine.write_log(f"最小名义价值: {min_notional} USDT")
        
        # 等待有效的行情数据
        while not self.tick or not self.tick.ask_price_1:
            self.main_engine.write_log(f'等待行情数据...')
            time.sleep(5)

        # 计算开仓数量 (110% 最小名义价值)
        price = self.tick.ask_price_1
        if not price:
            self.main_engine.write_log("无效的行情价格")
            return
            
        volume = (min_notional * 1.1) / price
        volume = round_to(volume, self.contract.min_volume)
        
        if volume <= 0:
            self.main_engine.write_log(f"计算得到的数量无效: {volume}")
            return
            
        # 开仓 110% 最小名义价值
        self.short(
            price=self.tick.bid_price_5,
            volume=volume,
            test_stage="阶段1: 开仓 (110% 最小名义价值)"
        )
        
        # 第一次平仓 (60% 持仓)/////////
        close_volume1 = round_to(self.position_volume * 0.6, self.contract.min_volume)
        if close_volume1 > 0:
            self.cover(
                price=self.tick.ask_price_5,
                volume=close_volume1,
                test_stage="阶段2: 第一次平仓 (60% 持仓)"
            )
            
        # 尝试使用多开平仓 (预期会被交易所拒单)，看是否会被记录至数据库
        close_volume2 = round_to(self.position_volume, self.contract.min_volume)
        if close_volume2 > 0:
            self.buy(
                price=self.tick.ask_price_5,
                volume=close_volume2,
                test_stage="阶段3: 尝试使用多开平仓 (预期被拒单)"
            )

        # 尝试使用多开平仓 (预期会被交易所拒单)，看是否会被记录至数据库
        if close_volume2 > 0:
            self.buy(
                price=self.tick.ask_price_5,
                volume=close_volume2,
                test_stage="阶段4: 尝试使用多开平仓 (预期被拒单)"
            )
        
        # 最后平仓 (剩余持仓)
        if close_volume2 > 0:
            self.cover(
                price=self.tick.ask_price_5,
                volume=close_volume2,
                test_stage="阶段5: 最后平仓 (剩余持仓)"
            )


        volume1 = 10000 / price
        volume1 = round_to(volume1, self.contract.min_volume)
        # 开仓 100 USDT 预计被order_value_limit拒单liao
        self.short(
            price=self.tick.bid_price_5,
            volume=volume1,
            test_stage="阶段6: 100 USDT 预计被order_value_limit拒单"
        )
        time.sleep(120)
        # 开仓 100 USDT 预计被order_value_limit拒单
        self.buy(
            price=self.tick.ask_price_5,
            volume=volume1,
            test_stage="阶段7: 100 USDT 预计被order_value_limit拒单"
        )

        self.buy(
            price=self.tick.ask_price_5,
            volume=volume,
            test_stage="阶段8: 开仓 (110% 最小名义价值)"
        )        


        self.buy(
            price=self.tick.ask_price_5,
            volume=volume,
            test_stage="阶段9: 110% 最小名义价值 预计被order_flow_limit拒单, 因为委托限制设置为8，前面已委托8笔 预计被order_flow_limit拒单"
        )
        self.buy(
            price=self.tick.ask_price_5,
            volume=volume,
            test_stage="阶段10: 110% 最小名义价值 预计被order_flow_limit拒单, 因为委托限制设置为8，前面已委托8笔 预计被order_flow_limit拒单"
        )

def main():
    """主函数"""
    app = OrderTestApp()
    
    # 等待5秒，确保接口已连接
    time.sleep(15)
    
    # 运行测试
    app.run_test()
    
    return app

if __name__ == "__main__":
    app = main() 