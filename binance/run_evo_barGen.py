from vnpy_evo.trader.object import SubscribeRequest
from vnpy_evo.event import EVENT_TIMER
from vnpy.trader.setting import SETTINGS
from vnpy.trader.utility import load_json
from vnpy_evo.trader.event import EVENT_ACCOUNT, EVENT_POSITION, EVENT_CONTRACT, EVENT_TICK

from vnpy_evo.event import Event
from time import sleep
import signal
import sys

from vnpy_evo.event import EventEngine
from vnpy_evo.trader.engine import MainEngine

from prod.barGen_redis_engine import BarGenEngine
# from prod.binance_linear_gateway import BinanceLinearGateway, F_WEBSOCKET_TRADE_HOST, F_TESTNET_WEBSOCKET_TRADE_HOST
from prod.binance_linear_gateway2 import BinanceLinearGateway, F_WEBSOCKET_TRADE_HOST, F_TESTNET_WEBSOCKET_TRADE_HOST
from prod.recorder_engine import RecorderEngine
from vnpy_ctastrategy.engine import CtaEngine

class BinanceApp:
    """币安应用"""
    
    def __init__(self):
        """构造函数"""
        self.event_engine = EventEngine()
        self.main_engine = MainEngine(self.event_engine)

        self.register_event()
        self.init_engines()

        self.timer_count = 0
        self.health_check_count = 0
        self.health_check_interval = 10  # 每60秒检查一次网关状态
        self._active = True  # 添加运行状态标记
        
    def init_engines(self) -> None:
        """初始化引擎"""
        # 添加币安网关
        self.gateway = self.main_engine.add_gateway(BinanceLinearGateway)
        self.gateway.market_ws_api.enable_log_mode()
        setting = load_json("connect_binance_read.json")
        self.main_engine.connect(setting, "BINANCE_LINEAR")
        
        # 添加K线生成引擎
        self.bar_gen_engine = self.main_engine.add_engine(BarGenEngine)
        self.bar_gen_engine.start()
        # 添加K线记录引擎
        # self.recorder_engine = self.main_engine.add_engine(RecorderEngine)

    def register_event(self):
        """注册事件监听"""
        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        # 在Windows平台上signal模块可能不完全支持
        if sys.platform != 'win32':
            # 忽略SIGTSTP信号(Ctrl+Z)，避免进程被挂起
            signal.signal(signal.SIGTSTP, signal.SIG_IGN)

        self.event_engine.register(EVENT_ACCOUNT, self.process_event)
        self.event_engine.register(EVENT_POSITION, self.process_event)
        # self.event_engine.register(EVENT_CONTRACT, self.process_event)
        # self.event_engine.register(EVENT_TICK+"BTCUSDT.BINANCE", self.process_event)
        self.event_engine.register(EVENT_TIMER, self.process_timer_event)

    def process_event(self, event: Event):
        """处理账户事件"""
        self.main_engine.write_log(f"{event.type} Update: {event.data}")

    def process_timer_event(self, event: Event):
        """处理定时器事件"""
        # 增加健康检查计数
        self.health_check_count += 1
        
        # 每隔health_check_interval秒检查一次网关状态
        if self.health_check_count >= self.health_check_interval:
            self.health_check_count = 0
            
            # 检查网关连接状态
            # if not self.gateway.is_connected():
            #     self.main_engine.write_log("检测到网关连接异常，准备重启")
            #     self.restart_gateway()
            #     return
        
        # # 第50s退订行情
        # self.timer_count += 1
        # if self.timer_count == 50:
        #     self.main_engine.write_log("开始退订行情")
        #     # 获取所有需要退订的合约
        #     reqs = []
        #     for vt_symbol in self.bar_gen_engine.bar_recordings:
        #         try:
        #             contract = self.main_engine.get_contract(vt_symbol)
        #             if not contract:
        #                 self.main_engine.write_log(f"找不到合约：{vt_symbol}")
        #                 continue
                    
        #             req = SubscribeRequest(
        #                 symbol=contract.symbol,
        #                 exchange=contract.exchange
        #             )
        #             reqs.append(req)
        #         except Exception as e:
        #             self.main_engine.write_log(f"创建退订请求异常: {vt_symbol}, 错误: {str(e)}")
        #             continue
            
        #     # 批量退订
        #     if reqs:
        #         gateway_name = "BINANCE_LINEAR"
        #         gateway = self.main_engine.get_gateway(gateway_name)
        #         gateway.unsubscribe_reqs(reqs)
        #         self.main_engine.write_log(f"已退订 {len(reqs)} 个合约的行情")
            
        #     # 清空bar_recordings集合
        #     self.bar_gen_engine.bar_recordings.clear()
        #     self.main_engine.write_log("退订行情完成")

    # def restart_gateway(self):
    #     """重启网关"""
    #     # 关闭旧的网关
    #     if self.gateway:
    #         self.gateway.close()
    #
    #     # 等待5秒
    #     sleep(5)
    #
    #     # 重新初始化网关
    #     setting = load_json("connect_binance_read.json")
    #     self.main_engine.connect(setting, "BINANCE_LINEAR")
    #     if not self.gateway.trade_ws_api._ws and not self.gateway.trade_ws_api._connecting and self.gateway.rest_api.user_stream_key:
    #         if self.gateway.rest_api.server == "REAL":
    #             url = F_WEBSOCKET_TRADE_HOST + self.gateway.rest_api.user_stream_key
    #         else:
    #             url = F_TESTNET_WEBSOCKET_TRADE_HOST + self.gateway.rest_api.user_stream_key
    #         self.gateway.trade_ws_api.connect(url, self.gateway.rest_api.proxy_host, self.gateway.rest_api.proxy_port)
    #
    #     self.main_engine.write_log("网关重启完成")

    def _signal_handler(self, signum, frame):
        """
        信号处理函数
        """
        sig_name = signal.Signals(signum).name
        self.main_engine.write_log(f"收到信号: {sig_name}，准备关闭应用...")
        self._active = False

    def run(self):
        """运行应用"""
        self.main_engine.write_log("K线生成应用已启动")
        while self._active:
            sleep(1)
        self.main_engine.write_log("应用正在关闭...")
        self.close()

    def close(self):
        """关闭应用"""
        self.main_engine.close()

def main():
    """主函数"""
    app = BinanceApp()
    app.run()

if __name__ == "__main__":
    main()