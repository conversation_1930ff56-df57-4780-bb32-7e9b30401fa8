from time import sleep
import sys
import signal

from vnpy.event import EventEngine, Event
from vnpy.trader.engine import MainEngine
from vnpy.trader.utility import load_json, save_json
from vnpy.trader.event import EVENT_LOG, EVENT_TICK
from vnpy.trader.object import SubscribeRequest, Exchange, TickData

from vnpy_rpcservice.rpc_gateway import RpcGateway


class RpcGatewayApp:
    """RPC网关应用"""
    setting_filename: str = "rpc_client_setting.json"
    
    def __init__(self, username: str = "user1"):
        """构造函数"""
        self.event_engine = EventEngine()
        self.main_engine = MainEngine(self.event_engine)
        self.username = username
        self.gateway_name = f"{self.username}.RPC"
        self.load_setting()        
        self.register_event()

        self.init_engines()
        self.subscribed_symbols = set()
        
        self._active = True  # 添加运行状态标记
        
    def load_setting(self) -> dict:
        self.setting = load_json(self.setting_filename)
        if not self.setting:
            self.setting = {
                "主动请求地址": "tcp://127.0.0.1:20014",
                "推送订阅地址": "tcp://127.0.0.1:41002"
            }
            save_json(self.setting_filename, self.setting)

    def register_event(self) -> None:
        """注册事件监听"""
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        # 在Windows平台上signal模块可能不完全支持
        if sys.platform != 'win32':
            # 忽略SIGTSTP信号(Ctrl+Z)，避免进程被挂起
            signal.signal(signal.SIGTSTP, signal.SIG_IGN)

        self.event_engine.register(EVENT_TICK, self.process_tick_event)
            
    def _signal_handler(self, signum, frame):
        """信号处理函数"""
        sig_name = signal.Signals(signum).name
        self.main_engine.write_log(f"收到信号: {sig_name}，准备关闭应用...")
        self._active = False
        
    def init_engines(self) -> None:
        """初始化网关"""
        # 添加RPC网关，添加用户标识到gateway_name中
        self.gateway = self.main_engine.add_gateway(RpcGateway, self.gateway_name)
        
    def process_tick_event(self, event: Event) -> None:
        """处理Tick事件"""
        tick = event.data
        self.main_engine.write_log(f"{tick.localtime} 收到行情: {tick.symbol} last_price:{tick.last_price} datetime:{tick.datetime} ask_price_1:{tick.ask_price_1} extra:{tick.extra}")
        
    def subscribe(self, symbol: str) -> None:
        """订阅行情"""
        if symbol in self.subscribed_symbols:
            self.main_engine.write_log(f"已经订阅过 {symbol}")
            return
            
        # 创建订阅请求
        req = SubscribeRequest(
            symbol=symbol,
            exchange=Exchange.BINANCE
        )
        
        # 发送订阅请求
        self.gateway.subscribe(req)
        self.subscribed_symbols.add(symbol)
        self.main_engine.write_log(f"发送订阅请求: {symbol}")
        
    def unsubscribe(self, symbol: str) -> None:
        """退订行情"""
        if symbol not in self.subscribed_symbols:
            self.main_engine.write_log(f"未订阅 {symbol}")
            return
            
        # 创建订阅请求
        req = SubscribeRequest(
            symbol=symbol,
            exchange=Exchange.BINANCE
        )
        
        # 发送退订请求
        self.gateway.unsubscribe(req)
        self.subscribed_symbols.remove(symbol)
        self.main_engine.write_log(f"发送退订请求: {symbol}")
        
    def run(self) -> None:
        """运行应用"""
        self.main_engine.write_log(f"RPC网关应用[{self.gateway_name}]启动")
        
        # 连接RPC网关
        self.gateway.connect(self.setting)
        self.main_engine.write_log(f"RPC网关[{self.gateway_name}]已连接 - 正在接收行情和交易数据")
        
        # 等待3秒后订阅BTCUSDT
        sleep(3)
        self.subscribe("BTCUSDT")
        
        # 再等待10秒后退订BTCUSDT
        sleep(10)
        self.unsubscribe("BTCUSDT")

        while self._active:
            sleep(1)
            
        self.close()

    def close(self) -> None:
        """关闭应用"""
        self.main_engine.write_log("应用正在关闭...")
        self.gateway.close()
        self.main_engine.close()


def main() -> None:
    """主函数"""
    # 从命令行参数获取用户名
    username = sys.argv[1] if len(sys.argv) > 1 else "user1"
    app = RpcGatewayApp(username)
    app.run()


if __name__ == "__main__":
    main()
