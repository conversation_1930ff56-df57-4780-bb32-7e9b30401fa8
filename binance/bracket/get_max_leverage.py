import requests
import pandas as pd
import urllib.request

def get_system_proxy():
    """获取系统代理设置"""
    proxy_handler = urllib.request.ProxyHandler()
    proxies = {}
    
    # 从系统获取代理设置
    for protocol in ['http', 'https']:
        if proxy := proxy_handler.proxies.get(protocol):
            proxies[protocol] = proxy
            
    return proxies

def get_brackets():
    """获取币安合约杠杆数据"""
    url = "https://www.binance.com/bapi/futures/v1/friendly/future/common/brackets" # U本位合约
    # https://www.binance.com/bapi/futures/v1/friendly/delivery/common/brackets # 币本位合约
    headers = { "content-type": "application/json" }
    
    try:
        response = requests.post(url, headers=headers, data='{}', 
                               proxies=get_system_proxy(), timeout=10)
        return response.json() if response.status_code == 200 else None
    except Exception as e:
        print(f"请求错误: {e}")
        return None

def main():
    # 获取并处理数据
    if not (data := get_brackets()):
        return
        
    # 读取Excel并更新数据
    try:
        df = pd.read_excel('20250122.xlsx')
        
        # 获取每个合约的最高杠杆
        leverage_data = {
            contract['symbol']: max(b['maxOpenPosLeverage'] for b in contract['riskBrackets'])
            for contract in data['data']['brackets']
        }
        
        # 更新最高杠杆列
        df['最高杠杆'] = df['合约'].map(leverage_data)
        
        # 保存结果
        df[['稳定ID', '合约', '最高杠杆']].to_excel('20250122.xlsx', index=False)
        print(f"已更新 {len(df)} 个合约的最高杠杆数据")
            
    except FileNotFoundError:
        print("错误：找不到 20250122.xlsx")

if __name__ == "__main__":
    main()