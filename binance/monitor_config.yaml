# 数据库配置
database:
  host: localhost
  port: 3306
  user: root
  password: p0o9i8u7
  database: scout

price_database:
  host: *************
  port: 3308
  user: zh
  password: zhP@55word
  database: vnpy_crypto_agg

# Redis配置
redis:
  host: localhost
  port: 6380
  password: p0o9i8u7
  db: 0

# 用户配置
users:
#  trader1:
#    work_dir: S:/OneDrive - lancely/芷瀚同步/开发/币/binance/vnpy_evo测试

  # Windows路径示例:
  # trader3:
  #   work_dir: C:/Users/<USER>/vnpy_work
#  ly_real:
#    work_dir: S:\OneDrive - lancely\芷瀚同步\开发\币\binance\vnpy_evo测试\users\testnet2
#  ly_real:
#    work_dir: S:\OneDrive - lancely\芷瀚同步\开发\币\binance\vnpy_evo测试\users\test
  ly_test:
    work_dir: S:\OneDrive - lancely\芷瀚同步\开发\币\binance\vnpy_evo测试\users\testnet
  hxw:
    work_dir: S:\OneDrive - lancely\芷瀚同步\开发\币\binance\vnpy_evo测试\users\testnet_hua