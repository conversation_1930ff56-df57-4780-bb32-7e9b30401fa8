from vnpy.event import Event
from vnpy.trader.event import EVENT_TICK
from vnpy.trader.utility import save_json
from vnpy_evo.event import EventEngine
from vnpy_evo.trader.engine import MainEngine
from vnpy_evo.trader.utility import load_json
import time
import signal
import sys
import typer

from vnpy_algotrading.base import EVENT_ALGO_LOG
from vnpy_algotrading.dispatch_algo import get_dispatcher, logger
from prod.recorder_engine import RecorderEngine
from vnpy_rpcservice.rpc_gateway.rpc_gateway_quote import RpcGateway
from prod.binance_linear_gateway2 import BinanceLinearGateway

app = typer.Typer()
class AlgoApp:
    setting_filename: str = "rpc_client_setting.json"
    def __init__(self, test_mode=False):
        self.test_mode = test_mode
        self.event_engine = EventEngine(interval=0.3)
        self.main_engine = MainEngine(self.event_engine)

        self.trade_gateway = None
        self.quote_gateway = None  # 行情订阅专用网关
        self.algo_engine = None
        self.dispatcher = None

        self.load_setting()
        self.register_event()

        self.init_engines()

        self._active = True  # 添加运行状态标记

    def load_setting(self) -> None:
        """加载配置"""
        self.setting = load_json(self.setting_filename)
        if not self.setting:
            self.setting = {
                "主动请求地址": "tcp://127.0.0.1:2014",
                "推送订阅地址": "tcp://127.0.0.1:4102"
            }
            save_json(self.setting_filename, self.setting)

    def register_event(self) -> None:
        """注册事件监听"""
        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

        # 在Windows平台上signal模块可能不完全支持
        if sys.platform != 'win32':
            # 忽略SIGTSTP信号(Ctrl+Z)，避免进程被挂起
            signal.signal(signal.SIGTSTP, signal.SIG_IGN)
            signal.signal(signal.SIGHUP, self.reload_setting)
            # signal.signal(signal.SIGHUP, lambda signum, frame: self.algo_engine.load_setting() if self.algo_engine else None)

        # self.event_engine.register(EVENT_TICK, self.process_tick_event)

    def reload_setting(self, signum, frame):
        """重新加载配置"""
        if self.algo_engine:
            self.algo_engine.load_setting()

    def _signal_handler(self, signum, frame):
        """信号处理函数"""
        sig_name = signal.Signals(signum).name
        self.main_engine.write_log(f"收到信号: {sig_name}，准备关闭应用...")
        self._active = False

    def init_engines(self):
        """初始化引擎"""
        # 初始化订单分发器(测试模式)
        if self.test_mode:
            self.dispatcher = get_dispatcher()
            self.dispatcher.start(test_enabled=self.test_mode)
            logger.info(f"订单分发器已启动，测试模式：{'开启' if self.test_mode else '关闭'}")

        # 添加币安交易网关
        self.trade_gateway = self.main_engine.add_gateway(BinanceLinearGateway)
        # self.trade_gateway.market_ws_api.enable_log_mode()
        setting = load_json("connect_binance_testnet.json")
        self.trade_gateway.connect(setting)
        self.main_engine.write_log("交易接口添加成功")

        # 添加RPC行情网关
        self.quote_gateway = self.main_engine.add_gateway(RpcGateway)
        self.quote_gateway.connect(self.setting)
        self.main_engine.write_log("行情接口添加成功")

        # 添加K线生成引擎
        # from prod.barGen_redis_engine import BarGenEngine
        # bar_gen_engine = self.main_engine.add_engine(BarGenEngine)
        # bar_gen_engine.start()
        # self.main_engine.write_log("添加Bar生成引擎")
        # 添加K线记录引擎
        self.recorder_engine = self.main_engine.add_engine(RecorderEngine)

        # 记录算法引擎日志
        log_engine = self.main_engine.get_engine('log')
        self.event_engine.register(EVENT_ALGO_LOG, log_engine.process_log_event)
        
        # 添加算法交易引擎
        from vnpy_algotrading import AlgoTradingApp
        self.algo_engine = self.main_engine.add_app(AlgoTradingApp)
        self.algo_engine.start(
            test_enabled=self.test_mode,  # 根据命令行参数设置测试模式
            allow_multiple_algos=True,  # 默认True
            # risk_enabled=True  # 默认True
        )  # 启动算法交易引擎，传入测试标志
        self.main_engine.write_log("算法交易引擎创建成功")

    def process_tick_event(self, event: Event) -> None:
        """处理Tick事件"""
        tick = event.data
        self.main_engine.write_log(
            f"{tick.localtime} 收到行情: {tick.symbol} last_price:{tick.last_price} datetime:{tick.datetime} ask_price_1:{tick.ask_price_1} extra:{tick.extra}")

    def run(self):
        """运行应用"""
        self.main_engine.write_log("算法交易应用已启动")
        while self._active:
            time.sleep(1)
        self.main_engine.write_log("应用正在关闭...")
        self.close()

    def close(self):
        """关闭应用"""
        if self.algo_engine:
            self.algo_engine.close()

        if self.trade_gateway:
            self.trade_gateway.cancel_all()

        if self.dispatcher:
            self.dispatcher.stop()

        time.sleep(2)
        self.quote_gateway.close()
        time.sleep(2)

        self.main_engine.close()

@app.command()
def main(
    test: bool = typer.Option(False, "--test", "-t", help="启用测试模式")
):
    """启动算法交易应用"""
    algo_app = AlgoApp(test_mode=test)
    algo_app.run()

if __name__ == "__main__":
    app()