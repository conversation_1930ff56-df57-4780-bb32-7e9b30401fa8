import time
import argparse
import signal
import sys

# 从 dispatch_algo 模块导入 logger和分发器
# 这样可以复用已配置好的 logger实例
from vnpy_algotrading.dispatch_algo import get_dispatcher, logger

# 全局变量
TEST_ENABLED = True

class DispatchApp:
    def __init__(self, test_dispatch=False):
        """初始化应用"""
        self.test_dispatch = test_dispatch
        self.dispatcher = get_dispatcher()
        self._active = False
        
        # 设置信号处理，以便优雅地关闭应用
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

        # 在Windows平台上signal模块可能不完全支持
        if sys.platform != 'win32':
            # 忽略SIGTSTP信号(Ctrl+Z)，避免进程被挂起
            signal.signal(signal.SIGTSTP, signal.SIG_IGN)
    
    def _signal_handler(self, signum, frame):
        """处理终止信号"""
        sig_name = signal.Signals(signum).name
        logger.info(f"收到信号: {sig_name}，准备关闭应用...")
        self._active = False

    def run(self):
        """运行应用"""
        # 启动订单分发器
        self.dispatcher.start(test_enabled=self.test_dispatch)
        logger.info(f"订单分发应用已启动，测试模式：{'开启' if self.test_dispatch else '关闭'}")
        
        # 主应用循环（注意：分发器已经有自己的循环，这里只是为了保持应用运行）
        self._active = True
        while self._active:
            time.sleep(1)

        logger.info("应用正在关闭...")
        self.close()

    def close(self):
        """关闭应用"""
        # 关闭订单分发器
        self.dispatcher.stop()
        logger.info("订单分发器已关闭")

def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="启动订单分发应用")
    parser.add_argument("--test", action="store_true", help="启用测试模式")
    args = parser.parse_args()

    logger.info("正在启动订单分发应用...")
    
    # 创建并运行应用
    app = DispatchApp(test_dispatch=args.test)
    app.run()

    logger.info("订单分发应用已退出")
    sys.exit(0)


if __name__ == "__main__":
    main()