import signal
import sys
import time

from vnpy.event import Event, EventEngine
from vnpy.trader.engine import MainEngine
from vnpy.trader.event import EVENT_TIMER
from vnpy.trader.utility import load_json, save_json
from peewee import fn

from prod.recorder_engine import OrderError
from prod.wecom_alert import report_we_alert


class ErrorMonitor:
    """订单错误监控"""
    
    setting_filename: str = "error_monitor_setting.json"
    
    def __init__(self):
        """构造函数"""
        self.event_engine = EventEngine()
        self.main_engine = MainEngine(self.event_engine)
        
        # 加载配置
        self.load_setting()
        
        # 缓存上次查询的最大ID
        # 开始运行前错误也报警
        # self.last_error_id = 0
        # 开始运行前错误不报警
        self.last_error_id = self.get_last_error_id()
        
        # Timer计数器
        self.timer_count = 0
        
        # 运行状态标记
        self._active = True
        
        # 注册事件监听
        self.register_event()
        
        self.main_engine.write_log("错误监控已启动")
            
    def register_event(self) -> None:
        """注册事件监听"""
        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        # 在Windows平台上signal模块可能不完全支持
        if sys.platform != 'win32':
            # 忽略SIGTSTP信号(Ctrl+Z)，避免进程被挂起
            signal.signal(signal.SIGTSTP, signal.SIG_IGN)
            
        self.event_engine.register(EVENT_TIMER, self.process_timer_event)
        
    def _signal_handler(self, signum, frame):
        """信号处理函数"""
        sig_name = signal.Signals(signum).name
        self.main_engine.write_log(f"收到信号: {sig_name}，准备关闭监控...")
        self._active = False
    
    def load_setting(self) -> None:
        """加载配置"""
        self.setting = load_json(self.setting_filename)
        
        if not self.setting:
            self.main_engine.write_log("配置文件为空,使用默认配置")
            self.setting = {
                "error_codes": [-2027],      # 需要监控的错误码列表
                "msg_keywords": [],          # 需要监控的错误信息关键词列表
                "wecom_key": "b54436b7-2384-4b54-863f-caebc34309a4",  # 企业微信机器人key
                "timer_interval": 60         # 定时器间隔(秒)
            }
            save_json(self.setting_filename, self.setting)
        
    def get_last_error_id(self) -> int:
        """获取当前最大错误ID"""
        return OrderError.select(fn.MAX(OrderError.id)).scalar() or 0
            
    def process_timer_event(self, event: Event) -> None:
        """处理定时事件"""
        self.timer_count += 1
        if self.timer_count < self.setting["timer_interval"]:
            return
        self.timer_count = 0

        self.main_engine.write_log("检查新的错误记录")
        self.check_new_errors()
            
    def check_new_errors(self) -> None:
        """检查新的错误记录"""
        # 先获取最新id
        latest_id = self.get_last_error_id()
        
        # 执行基础查询(id范围)
        query = (
            OrderError.select()
            .where(
                (OrderError.id > self.last_error_id) 
                & (OrderError.id <= latest_id)
            )
        )
        
        # 构建需要监控的条件(OR关系)
        filter_conditions = []
        
        # 添加错误码条件
        if self.setting["error_codes"]:
            filter_conditions.append(OrderError.error_code.in_(self.setting["error_codes"]))
            
        # 添加错误信息关键词条件
        if self.setting["msg_keywords"]:
            for keyword in self.setting["msg_keywords"]:
                filter_conditions.append(OrderError.error_msg.contains(keyword))
        
        # 组合过滤条件(任一条件满足即可)
        if filter_conditions:
            combined = filter_conditions[0]
            for cond in filter_conditions[1:]:
                combined = combined | cond
            query = query.where(combined)
        
        # 添加排序
        new_errors = query.order_by(OrderError.id)
        
        errors = list(new_errors)
        if not errors:
            self.last_error_id = latest_id  # 更新最后处理的id
            return
            
        # 构建合并告警消息
        msg = "[订单错误告警汇总]\n\n"
        for error in errors:
            # 基本错误信息
            msg += f"合约: {error.symbol}.{error.exchange}\n"
            msg += f"订单号: {error.orderid}\n"
            msg += f"错误码: {error.error_code}\n"
            msg += f"错误信息: {error.error_msg}\n"
            msg += f"创建时间: {error.create_date}\n"
            
            # 可选字段,只有非空时才添加
            if error.username:
                msg += f"用户: {error.username}\n"
            if error.todo_id:
                msg += f"todo_id: {error.todo_id}\n"
            if error.fix_date:
                msg += f"修复时间: {error.fix_date}\n"
            if error.remarks:
                msg += f"备注: {error.remarks}\n"
            if error.ext1:
                msg += f"扩展信息1: {error.ext1}\n"
            if error.ext2:
                msg += f"扩展信息2: {error.ext2}\n"
                
            msg += "------------------------\n"
            
        success, send_msg = report_we_alert(text=msg, key=self.setting["wecom_key"])
        
        if success:
            self.main_engine.write_log(f"告警发送成功: {len(errors)}条错误记录")
        else:
            self.main_engine.write_log(f"告警发送失败: {send_msg}")
            
        # 处理完成后更新id
        self.last_error_id = latest_id
            
    def close(self):
        """关闭应用"""
        self.main_engine.write_log("监控正在关闭...")
        self.main_engine.close()
            
    def run(self):
        """运行监控"""
        # 等待退出
        while self._active:
            time.sleep(1)
            
        self.close()

def main():
    """主函数"""    
    monitor = ErrorMonitor()
    monitor.run()
    
if __name__ == "__main__":
    main()