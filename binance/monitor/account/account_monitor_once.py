import json
import time
from datetime import datetime
from pathlib import Path
from typing import Dict
import yaml
from dataclasses import dataclass
from peewee import (
    Model, CharField, DoubleField, DateTimeField,
    CompositeKey, MySQLDatabase
)
from playhouse.shortcuts import ReconnectMixin

from vnpy_binance import BinanceLinearGateway
from vnpy_evo.event import Event, EventEngine
from vnpy_evo.trader.engine import MainEngine
from vnpy_evo.trader.object import AccountData
from vnpy_evo.trader.event import EVENT_ACCOUNT, EVENT_TIMER
from peewee import Model, CharField,  DateTimeField, fn
import inspect

# Constants
UPDATE_INTERVAL = 10  # seconds
# 启动一次执行一次，每24小时执行一次，外部调度
# 添加重连支持的MySQL数据库类
class ReconnectMySQLDatabase(ReconnectMixin, MySQLDatabase):
    """带有重连机制的MySQL数据库类"""
    pass

class AccountBalance(Model):
    """账户余额记录"""
    username = CharField()                # 用户名
    balance = DoubleField()              # 账户余额
    frozen = DoubleField()               # 冻结金额
    timestamp = DateTimeField()          # 记录时间
    base = DoubleField()                 # 新增字段：base
    pnl = DoubleField()                  # 新增字段：pnl
    in_out = DoubleField()               # 新增字段：in_out

    class Meta:
        database = None  # 将在运行时设置
        primary_key = CompositeKey('username', 'timestamp')

class UserGateway(BinanceLinearGateway):
    """每个用户的网关"""
    def write_log(self, msg: str) -> None:
        """重写日志方法，添加用户标识"""
        super().write_log(f"[{self.gateway_name}] {msg}")

    def on_account(self, account: AccountData) -> None:
        """重写on_account方法，忽略WebSocket推送的账户数据

        inspect.stack() 返回一个 FrameInfo 对象列表，包含当前的调用栈信息，从上到下依次是：
        - 最内层调用(当前函数 on_account)在第一个元素[0]
        - 调用当前函数的函数在第二个元素[1]
        - 调用链一直往上，直到程序入口

        每个 FrameInfo 对象包含以下属性：
        - frame: 当前帧对象，包含局部变量等信息
        - filename: 文件名
        - lineno: 行号
        - function: 函数名(字符串类型)
        - code_context: 源代码上下文
        - index: 代码行在上下文中的索引
        """
        # 获取完整的调用栈，从内到外的所有调用层级
        stack = inspect.stack()

        # 遍历调用栈中的每一帧
        for frame in stack:
            # frame.function 返回字符串类型的函数名
            # frame.frame.f_locals 返回当前帧的局部变量字典
            # f_locals.get('self', '') 获取局部变量中的self对象，如果不存在返回空字符串
            if ('on_packet' in frame.function and  # 检查是否在 on_packet 函数中
                'BinanceLinearTradeWebsocketApi' in str(frame.frame.f_locals.get('self', ''))  # 检查是否是 WebSocket API 的实例
            ):
                # 发现是来自 WebSocket 的推送，直接返回不处理
                self.write_log(f"来自 WebSocket 的推送，直接返回不处理")
                return

        # 如果不是来自WebSocket的推送(比如来自REST API的查询结果)，则正常处理
        super().on_account(account)

class AccountMonitor:
    def __init__(self, config_path: str = "monitor_config.yaml"):
        """初始化账户监控器"""
        self.event_engine = EventEngine()
        self.main_engine = MainEngine(self.event_engine)
        self.users: Dict[str, UserGateway] = {}
        self.query_count = 0
        self.leverage = 0 # 控制leverage 每天只做一次就好

        self.load_config(config_path)  # 先加载配置
        self.setup_database()  # 后初始化数据库
        self.register_handlers()

    def setup_database(self):
        """初始化数据库"""
        try:
            # 从配置中获取数据库设置
            db_config = self.db_config

            # 创建数据库连接
            self.db = ReconnectMySQLDatabase(
                database=db_config["database"],
                user=db_config["user"],
                password=db_config["password"],
                host=db_config["host"],
                port=db_config["port"]
            )

            # 设置数据库连接
            AccountBalance._meta.database = self.db

            # 连接并创建表
            self.db.connect()
            self.db.create_tables([AccountBalance])
            self.db.close()

            self.main_engine.write_log("数据库连接成功")
        except Exception as e:
            self.main_engine.write_log(f"数据库连接失败: {str(e)}")
            raise

    def load_config(self, config_path: str):
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)

            # 提取数据库配置
            if "database" not in config:
                raise ValueError("配置文件中缺少数据库配置")
            self.db_config = config["database"]

            # 加载用户配置
            for username, settings in config["users"].items():
                work_dir = settings["work_dir"]
                connect_file = Path(work_dir) / ".vntrader" / "connect_binance_linear.json"

                if not connect_file.exists():
                    continue

                with open(connect_file, 'r', encoding='utf-8') as f:
                    connect_config = json.load(f)

                # 创建用户网关
                gateway = UserGateway(self.event_engine, username)
                gateway.connect(connect_config)
                self.users[username] = gateway

        except Exception as e:
            print(f"加载配置文件失败: {str(e)}")
            raise

    def register_handlers(self):
        """注册事件处理器"""
        self.event_engine.register(EVENT_ACCOUNT, self.handle_account_event)
        self.event_engine.register(EVENT_TIMER, self.handle_timer_event)

    def handle_account_event(self, event: Event):
        """处理账户数据事件"""
        account: AccountData = event.data
        # 从vt_accountid中获取gateway_name，即用户名
        username = account.gateway_name

        # 只处理 USDT 资产
        if username in self.users and account.accountid == "USDT":
            self.print_current_balance(username, account)
            self.record_balance(username, account)

    def handle_timer_event(self, event: Event):
        """处理定时器事件"""
        self.query_count += 1

        # 定时查询账户信息
        if self.query_count >= UPDATE_INTERVAL:
            self.query_count = 0
            for gateway in self.users.values():
                gateway.query_account()
            if not self.leverage:
                self.set_leverage()
                self.leverage = 1

    def print_current_balance(self, username: str, account: AccountData):
        """打印当前余额信息"""
        if float(account.balance) > 0:
            self.users[username].write_log(f"余额: {account.balance}, 冻结: {account.frozen}")

    def record_balance(self, username: str, account: AccountData):
        try:
            # 获取今天的日期
            today = datetime.now().date()

            # 检查是否已经有记录
            existing_record = AccountBalance.select().where(
                AccountBalance.username == username,
                (fn.DATE(AccountBalance.timestamp) == today)
            ).exists()

            if existing_record:
                self.users[username].write_log(f"今天的余额已经记录过了。")
                return

            # 获取同名用户的最新记录中的 base
            latest_record = AccountBalance.select().where(
                AccountBalance.username == username
            ).order_by(AccountBalance.timestamp.desc()).first()

            # 如果存在最新记录，则使用其 base；否则 base 默认为 0
            base = latest_record.base if latest_record else 0.0

            # 计算 pnl
            pnl = float(account.balance) - base

            # 创建新记录
            with self.db.atomic():
                AccountBalance.create(
                    username=username,
                    balance=float(account.balance),
                    frozen=float(account.frozen),
                    base=base,  # 设置 base
                    pnl=pnl,    # 设置 pnl
                    in_out=0.0,  # 根据需求设置 in_out，这里默认为 0
                    timestamp=datetime.now()
                )

        except Exception as e:
            self.users[username].write_log(f"记录余额失败: {str(e)}")

    def close(self):
        """关闭监控"""
        try:
            if hasattr(self, 'db'):
                self.db.close()
        except Exception as e:
            self.main_engine.write_log(f"关闭数据库连接失败: {str(e)}")
        finally:
            self.main_engine.close()

    def set_leverage(self):
        """为所有用户设置 杠杆倍数为5倍"""
        SYMBOL = "FTMUSDT"
        LEVERAGE = 5

        for username, gateway in self.users.items():
            gateway.rest_api.set_leverage(SYMBOL, LEVERAGE, extra={"username": username})
            gateway.write_log(f"设置 {username} 的 {SYMBOL} 杠杆倍数为 {LEVERAGE}倍")

    def run(self):
        """运行监控"""
        try:
            self.main_engine.write_log("账户监控已启动")
            time.sleep(200) # 只跑一次
            self.main_engine.close()
            print(f"记录完成，退出")
        except KeyboardInterrupt:
            self.main_engine.write_log("账户监控已停止")
        finally:
            self.close()

def run_child():
    """子进程运行函数"""
    monitor = AccountMonitor()
    monitor.run()

if __name__ == "__main__":
    run_child()