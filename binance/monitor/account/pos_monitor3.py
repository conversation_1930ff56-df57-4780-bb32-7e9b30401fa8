import json
import signal
import sys
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import yaml
from dataclasses import dataclass
from peewee import (
    Model, CharField, DoubleField, DateTimeField, 
    CompositeKey, MySQLDatabase, SqliteDatabase, chunked
)
from playhouse.shortcuts import ReconnectMixin
from queue import Queue, Empty
import threading

from vnpy.trader.utility import extract_vt_symbol

from prod.binance_linear_gateway2 import BinanceLinearGateway
from vnpy_evo.event import Event, EventEngine
from vnpy_evo.trader.engine import MainEngine
from vnpy_evo.trader.object import AccountData, PositionData, ContractData
from vnpy_evo.trader.event import EVENT_ACCOUNT, EVENT_TIMER, EVENT_POSITION
from vnpy_evo.trader.constant import Exchange
from peewee import Model, CharField, DoubleField, DateTimeField, fn
import inspect
import pandas as pd
import redis
from sqlalchemy import create_engine, text
from urllib.parse import quote_plus as urlquote
import traceback
from functools import reduce

# Constants
POSITION_UPDATE_INTERVAL = 60*60*3  # 3小时查询一次持仓
CONTRACT_UPDATE_INTERVAL = 300  # 5分钟查询一次合约信息

# 添加重连支持的MySQL数据库类
class ReconnectMySQLDatabase(ReconnectMixin, MySQLDatabase):
    """带有重连机制的MySQL数据库类"""
    pass

'''
SET GLOBAL time_zone = 'Asia/Shanghai';
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for position_monitor
-- ----------------------------
DROP TABLE IF EXISTS `position_monitor`;
CREATE TABLE `position_monitor`  (
  `username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `symbol` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `exchange` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `market_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `leverage` double NOT NULL,
  `position` double NOT NULL,
  `current_price` double NOT NULL,
  `position_value` double NOT NULL,
  `max_notional_value` double NOT NULL,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`username`, `symbol`, `exchange`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;

'''
class PositionMonitor(Model):
    """实时持仓监控表"""
    username = CharField()                # 用户名
    symbol = CharField()                 # 持仓合约
    exchange = CharField()               # 交易所
    market_type = CharField()            # 市场类型
    leverage = DoubleField()             # 目前杠杆率
    position = DoubleField()             # 目前持仓量
    current_price = DoubleField()        # 实时价格
    position_value = DoubleField()       # 持仓价值(持仓量*实时价格)
    max_notional_value = DoubleField()   # 目前杠杆允许的最大持仓额
    update_time = DateTimeField(default=datetime.now)  # 更新时间

    class Meta:
        table_name = 'position_monitor'
        database = None  # 将在运行时设置
        primary_key = CompositeKey('username', 'symbol', 'exchange')

class UserGateway(BinanceLinearGateway):
    """每个用户的网关"""        
    def write_log(self, msg: str) -> None:
        """重写日志方法，添加用户标识"""
        super().write_log(f"[{self.gateway_name}] {msg}")

    def on_account(self, account: AccountData) -> None:
        """重写on_account方法，忽略WebSocket推送的账户数据

        inspect.stack() 返回一个 FrameInfo 对象列表，包含当前的调用栈信息，从上到下依次是：
        - 最内层调用(当前函数 on_account)在第一个元素[0]
        - 调用当前函数的函数在第二个元素[1]
        - 调用链一直往上，直到程序入口

        每个 FrameInfo 对象包含以下属性：
        - frame: 当前帧对象，包含局部变量等信息
        - filename: 文件名
        - lineno: 行号
        - function: 函数名(字符串类型)
        - code_context: 源代码上下文
        - index: 代码行在上下文中的索引
        """
        # 获取完整的调用栈，从内到外的所有调用层级
        stack = inspect.stack()

        # 遍历调用栈中的每一帧
        for frame in stack:
            # frame.function 返回字符串类型的函数名
            # frame.frame.f_locals 返回当前帧的局部变量字典
            # f_locals.get('self', '') 获取局部变量中的self对象，如果不存在返回空字符串
            if ('on_packet' in frame.function and  # 检查是否在 on_packet 函数中
                'BinanceLinearTradeWebsocketApi' in str(frame.frame.f_locals.get('self', ''))  # 检查是否是 WebSocket API 的实例
            ):
                # 发现是来自 WebSocket 的推送，直接返回不处理
                self.write_log(f"来自 WebSocket 的推送，直接返回不处理")
                return

        # 如果不是来自WebSocket的推送(比如来自REST API的查询结果)，则正常处理
        super().on_account(account)

@dataclass
class PositionData2DB:
    """用于数据库操作的持仓数据类"""
    username: str
    symbol: str
    exchange: str
    market_type: str
    leverage: float
    position: float
    current_price: float
    position_value: float
    max_notional_value: float
    is_delete: bool = False  # True表示需要删除该记录

@dataclass
class QueryRequest:
    """查询请求数据类"""
    type: str  # "position" 或 "contract"
    clear_db: bool  # 是否需要清空数据库

class AccountMonitor:
    def __init__(self, config_path: str = "monitor_config.yaml"):
        """初始化账户监控器"""
        self.event_engine = EventEngine()
        self.main_engine = MainEngine(self.event_engine)

        self._active = True  # 添加运行状态标记

        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

        # 在Windows平台上signal模块可能不完全支持
        if sys.platform != 'win32':
            # 忽略SIGTSTP信号(Ctrl+Z)，避免进程被挂起
            signal.signal(signal.SIGTSTP, signal.SIG_IGN)

        self.users: Dict[str, UserGateway] = {}
        self.position_query_count = 0
        self.contract_query_count = 0
        self.leverage = 0 # 控制leverage 每天只做一次就好
        
        # 缓存合约信息
        self.contract_cache = {}
        
        # 添加数据库操作队列和线程控制
        self.db_queue = Queue()
        self.db_thread = None
        self.db_thread_running = False
        
        # 添加数据请求队列和线程控制
        self.query_queue = Queue()
        self.query_thread = None
        self.query_thread_running = False
        
        # 添加数据库清理计数器
        self.cleanup_count = 0
        self.cleanup_interval = 6  # 6 * 10秒 = 1分钟
        
        self.load_config(config_path)  # 先加载配置
        self.setup_database()  # 数据库连接
        self.setup_redis()     # Redis连接
        self.setup_price_db()  # 价格数据库连接
        self.register_handlers()  # 注册事件处理器
        
        # 启动数据库处理线程
        self.start_db_thread()
        # 启动数据请求处理线程
        self.start_query_thread()

    def _signal_handler(self, signum, frame):
        """
        信号处理函数
        """
        sig_name = signal.Signals(signum).name
        self.main_engine.write_log(f"收到信号: {sig_name}，准备关闭应用...")
        self._active = False

    def setup_database(self):
        """初始化数据库"""
        try:
            # 从配置中获取数据库设置
            db_config = self.db_config
            
            # 创建数据库连接
            self.db = ReconnectMySQLDatabase(
                db_config["database"],
                host=db_config["host"],
                port=db_config["port"],
                user=db_config["user"],
                password=db_config["password"]
            )
            
            # 设置数据库连接
            PositionMonitor._meta.database = self.db
            
            # 创建表（如果不存在）
            self.db.create_tables([PositionMonitor], safe=True)
            
            # 创建视图（如果不存在）
            self.create_leverage_monitor_view()
            
            self.main_engine.write_log("数据库连接成功")
        except Exception as e:
            self.main_engine.write_log(f"数据库连接失败: {str(e)}")
            raise
            
    def setup_redis(self):
        """初始化Redis连接"""
        try:
            # 从配置中获取Redis设置
            redis_config = self.redis_config
            
            # 创建Redis连接
            self.redis_client = redis.Redis(
                host=redis_config["host"],
                port=redis_config["port"],
                password=redis_config["password"],
                db=redis_config["db"],
                decode_responses=False  # 不自动解码为字符串
            )
            
            self.main_engine.write_log("Redis连接成功")
        except Exception as e:
            self.main_engine.write_log(f"Redis连接失败: {str(e)}")
            self.redis_client = None
            
    def setup_price_db(self):
        """初始化价格数据库连接"""
        try:
            # 从配置中获取价格数据库设置
            price_db_config = self.price_db_config
            
            # 创建数据库连接字符串，对密码进行URL编码
            connection_string = f"mysql+pymysql://{price_db_config['user']}:{urlquote(price_db_config['password'])}@{price_db_config['host']}:{price_db_config['port']}/{price_db_config['database']}?charset=utf8"
            
            # 创建 SQLAlchemy 引擎，并添加连接池参数
            self.price_db_engine = create_engine(
                connection_string,
                max_overflow=50,  # 超过连接池大小外最多创建的连接
                pool_size=50,     # 连接池大小
                pool_timeout=5,   # 池中没有线程最多等待的时间，否则报错
                pool_recycle=-1,  # 多久之后对线程池中的线程进行一次连接的回收（重置）
                # encoding='utf-8',
                echo=False
            )
            
            self.main_engine.write_log("价格数据库连接成功")
        except Exception as e:
            self.main_engine.write_log(f"价格数据库连接失败: {str(e)}")
            self.price_db_engine = None

    def load_config(self, config_path: str):
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)

            # 提取数据库配置
            if "database" not in config or "price_database" not in config:
                raise ValueError("配置文件中缺少数据库配置")
            self.db_config = config["database"]            
            self.price_db_config = config["price_database"]
            
            # 提取Redis配置
            if "redis" not in config:
                self.main_engine.write_log("配置文件中缺少Redis配置，将使用默认设置")
                self.redis_config = {
                    "host": "localhost",
                    "port": 6379,
                    "password": "",
                    "db": 0
                }
            else:
                self.redis_config = config["redis"]

            # 加载用户配置
            for username, settings in config["users"].items():
                work_dir = settings["work_dir"]
                connect_file = Path(work_dir) / ".vntrader" / "connect_binance_linear.json"
                
                if not connect_file.exists():
                    continue

                with open(connect_file, 'r', encoding='utf-8') as f:
                    connect_config = json.load(f)

                # 创建用户网关
                gateway = UserGateway(self.event_engine, username)
                gateway.connect(connect_config)
                self.users[username] = gateway

        except Exception as e:
            print(f"加载配置文件失败: {str(e)}")
            raise

    def register_handlers(self):
        """注册事件处理器"""
        self.event_engine.register(EVENT_TIMER, self.handle_timer_event)
        self.event_engine.register(EVENT_POSITION, self.handle_position_event)

    def handle_timer_event(self, event: Event):
        """处理定时器事件"""
        # 更新合约查询计数
        self.contract_query_count += 1
        if self.contract_query_count >= CONTRACT_UPDATE_INTERVAL:
            self.main_engine.write_log("开始查询合约信息")
            # 向请求队列提交合约查询请求
            self.query_queue.put(QueryRequest(type="contract", clear_db=False))
            self.contract_query_count = 0

        # 更新持仓查询计数
        # self.position_query_count += 1
        # if self.position_query_count >= POSITION_UPDATE_INTERVAL:
        #     self.main_engine.write_log("开始查询持仓信息")
        #     # 向请求队列提交持仓查询请求
        #     self.query_queue.put(QueryRequest(type="position", clear_db=True))
        #     self.position_query_count = 0            

    def handle_position_event(self, event: Event):
        """处理持仓数据事件"""
        position: PositionData = event.data
        username = position.gateway_name
        
        if username in self.users:
            self.record_position(username, position)
    
    def get_extra(self, username: str, vt_symbol: str) -> Optional[Tuple[float, float]]:
        """获取交易对的杠杆和最大名义价值信息
        返回: 成功时返回(leverage, max_notional_value)元组，失败时返回None
        """
        # 通过main_engine获取合约信息
        contract: ContractData = self.main_engine.get_contract(vt_symbol)
        
        if contract and contract.extra:
            # 检查是否同时存在leverage和max_notional_value信息
            has_leverage = "leverage" in contract.extra and username in contract.extra["leverage"]
            has_max_value = "max_notional_value" in contract.extra and username in contract.extra["max_notional_value"]
            
            # 只有同时存在两个值时才返回
            if has_leverage and has_max_value:
                leverage = float(contract.extra["leverage"][username])
                max_notional_value = float(contract.extra["max_notional_value"][username])
                return leverage, max_notional_value
            else:
                self.users[username].write_log(f"合约{vt_symbol}缺少必要的额外信息: {contract.extra}")
        else:
            self.users[username].write_log(f"无法获取合约{vt_symbol}的额外信息")
            
        return None
            
    def start_db_thread(self):
        """启动数据库处理线程"""
        if not self.db_thread_running:
            self.db_thread_running = True
            self.db_thread = threading.Thread(target=self.process_db_queue)
            self.db_thread.daemon = True  # 设置为守护线程
            self.db_thread.start()
            self.main_engine.write_log("数据库处理线程已启动")

    def stop_db_thread(self):
        """停止数据库处理线程"""
        self.db_thread_running = False
        if self.db_thread:
            self.db_thread.join()
            self.main_engine.write_log("数据库处理线程已停止")

    def process_db_queue(self):
        """处理数据库操作队列的线程函数"""
        while self.db_thread_running:
            try:
                # 每10秒处理一次队列中的数据
                time.sleep(10)
                
                # 更新清理计数器
                self.cleanup_count += 1
                
                # 每隔cleanup_interval（3分钟）触发一次清理
                if self.cleanup_count >= self.cleanup_interval:
                    self.cleanup_old_positions()
                    self.cleanup_count = 0  # 重置计数器
                    
                # 获取队列中的所有数据
                queue_data = []
                while not self.db_queue.empty():
                    queue_data.append(self.db_queue.get())
                
                if not queue_data:
                    continue
                
                # 使用字典来保存最新的数据，键为(username, symbol, exchange)
                latest_data = {}
                
                # 遍历所有数据，总是保留最新的记录
                for data in queue_data:
                    key = (data.username, data.symbol, data.exchange)
                    latest_data[key] = data
                
                # 分离需要删除和需要更新的数据
                delete_data = []
                update_data = []
                need_clear_db = False
                
                for data in latest_data.values():
                    # 检查是否是清空数据库的特殊请求
                    if data.username == "" and data.symbol == "":
                        need_clear_db = True
                        # 清空已收集的数据
                        delete_data = []
                        update_data = []
                        continue
                        
                    if data.is_delete:
                        delete_data.append((data.username, data.symbol, data.exchange))
                    else:
                        update_data.append({
                            'username': data.username,
                            'symbol': data.symbol,
                            'exchange': data.exchange,
                            'market_type': data.market_type,
                            'leverage': data.leverage,
                            'position': data.position,
                            'current_price': data.current_price,
                            'position_value': data.position_value,
                            'max_notional_value': data.max_notional_value
                        })
                
                # 批量处理数据
                with self.db.atomic():
                    # 如果需要清空数据库
                    if need_clear_db:
                        PositionMonitor.delete().execute()
                        self.main_engine.write_log("数据库已清空")
                    
                    # 批量删除数据
                    if delete_data:
                        for chunk in chunked(delete_data, 50):
                            conditions = [(PositionMonitor.username == username) & 
                                       (PositionMonitor.symbol == symbol) & 
                                       (PositionMonitor.exchange == exchange) 
                                       for username, symbol, exchange in chunk]
                            delete_query = PositionMonitor.delete().where(conditions[0] if len(conditions) == 1 else reduce(lambda x, y: x | y, conditions))
                            delete_query.execute()
                            self.main_engine.write_log(f"成功删除 {len(chunk)} 条持仓记录")
                    
                    # 批量更新/插入数据
                    if update_data:
                        for chunk in chunked(update_data, 50):
                            PositionMonitor.insert_many(chunk).on_conflict_replace().execute()
                            self.main_engine.write_log(f"成功更新 {len(chunk)} 条持仓记录")
                
                self.main_engine.write_log(f"成功处理 {len(latest_data)} 条持仓数据，原始数据 {len(queue_data)} 条")
                
            except Empty:
                continue
            except Exception as e:
                self.main_engine.write_log(f"处理数据库队列时发生错误: {str(e)}\n{traceback.format_exc()}")

    def cleanup_old_positions(self):
        """清理PositionMonitor表中旧的记录"""
        try:
            # 计算10分钟前的时间
            cutoff_time = datetime.now() - timedelta(minutes=10)
            
            # 执行删除操作
            delete_query = PositionMonitor.delete().where(PositionMonitor.update_time < cutoff_time)
            deleted_count = delete_query.execute()
            
            if deleted_count > 0:
                self.main_engine.write_log(f"成功清理 {deleted_count} 条旧的持仓记录 (早于 {cutoff_time.strftime('%Y-%m-%d %H:%M:%S')})")
        except Exception as e:
            self.main_engine.write_log(f"清理旧持仓记录时发生错误: {str(e)}\n{traceback.format_exc()}")

    def record_position(self, username: str, position: PositionData):
        """记录持仓信息"""
        try:
            if not position.volume:  # 如果持仓量为0，则删除记录
                symbol, exchange = extract_vt_symbol(position.vt_symbol)
                
                # 创建删除记录的数据对象
                db_data = PositionData2DB(
                    username=username,
                    symbol=symbol,
                    exchange=exchange.value,
                    market_type="",
                    leverage=0,
                    position=0,
                    current_price=0,
                    position_value=0,
                    max_notional_value=0,
                    is_delete=True
                )
                
                # 将数据放入队列
                self.db_queue.put(db_data)
                return

            symbol, exchange = extract_vt_symbol(position.vt_symbol)
            
            # 获取市场类型
            market_type = "币"  # 当前默认均为数字货币交易
            
            # 获取杠杆信息和最大名义价值
            extra_info = self.get_extra(username, position.vt_symbol)
            if not extra_info:
                # self.users[username].write_log(f"无法获取合约{position.vt_symbol}的额外信息，跳过记录")
                # return
                leverage, max_notional_value = 0, 0
            else:
                leverage, max_notional_value = extra_info
            
            # 获取实时价格
            # current_price, _ = self.get_realtime_price2(position.vt_symbol)
            current_price, _ = self.get_realtime_price(position.vt_symbol)
            if current_price <= 0:  # 如果无法获取实时价格，则使用开仓价格
                current_price = position.price
                
            # 计算持仓价值
            position_value = abs(float(position.volume)) * current_price
            
            # 创建数据对象
            db_data = PositionData2DB(
                username=username,
                symbol=symbol,
                exchange=exchange.value,
                market_type=market_type,
                leverage=leverage,
                position=float(position.volume),
                current_price=current_price,
                position_value=position_value,
                max_notional_value=max_notional_value
            )
            
            # 将数据放入队列
            self.db_queue.put(db_data)
            
            self.users[username].write_log(f"持仓数据已加入队列 - {symbol}: 持仓量={position.volume}, 杠杆={leverage}, 当前价格={current_price}, 持仓价值={position_value}, 最大名义价值={max_notional_value}")
            
        except Exception as e:
            self.users[username].write_log(f"处理持仓数据失败: {str(e)}\n{traceback.format_exc()}")

    def get_realtime_price(self, vt_symbol: str) -> tuple[float, float]:
        ret = (0,0)
        symbol, exchange_str = vt_symbol.split(".")
        query = f"select close_price,datetime from dbbardata d where d.symbol = '{symbol}' and exchange='{exchange_str}' and d.interval='1m' order by d.datetime desc limit 1"
        # print(f"query {query}")
        try:
            with self.price_db_engine.connect() as conn:
                ret = pd.read_sql_query(sql=text(query), con=conn)
                if len(ret) > 0:
                    ret = (ret.iloc[0].close_price, ret.iloc[0].datetime)
        except Exception as e:
            self.main_engine.write_log(f"获取实时价格失败: {str(e)}")
        return ret

    # get realtime price from redis
    def get_realtime_price2(self, vt_symbol: str) -> tuple[float, float]:
        ret = (0,0)
        (realtime_price, limit_up, limit_down) = (0, 0, 0)
        data = self.redis_client.hgetall(vt_symbol)
        # self.main_engine.write_log(f"get_redis_price {vt_symbol} ret: {data}")
        if b'price' in data.keys() and b'datetime' in data.keys():
            realtime_price = float(data[b'price'].decode())
            datetime = float(data[b'datetime'].decode())
        #     if realtime_price == 0:
        #         (realtime_price, datetime) = self.get_realtime_price(vt_symbol)
        #     return (realtime_price, datetime)
        # else:
        #     return ret
            if realtime_price:
                return (realtime_price, datetime)

        (realtime_price, datetime) = self.get_realtime_price(vt_symbol)
        if realtime_price:
            return (realtime_price, datetime)
        else:
            return (0, 0)

    def create_leverage_monitor_view(self):
        """创建杠杆监控视图"""
        try:
            # 检查视图是否已存在
            cursor = self.db.execute_sql("SHOW TABLES LIKE 'leverage_monitor'")
            if cursor.fetchone():
                self.main_engine.write_log("视图leverage_monitor已存在，无需创建")
                return
                
            # 创建视图SQL
            create_view_sql = """
            CREATE VIEW leverage_monitor AS 
            SELECT username, symbol, exchange, leverage, max_notional_value, position_value, update_time 
            FROM position_monitor
            """
            
            # 执行创建视图
            self.db.execute_sql(create_view_sql)
            self.main_engine.write_log("成功创建视图leverage_monitor")
        except Exception as e:
            self.main_engine.write_log(f"创建视图失败: {str(e)}")
            self.main_engine.write_log(traceback.format_exc())
    
    def start_query_thread(self):
        """启动数据请求处理线程"""
        if not self.query_thread_running:
            self.query_thread_running = True
            self.query_thread = threading.Thread(target=self.process_query_queue)
            self.query_thread.daemon = True  # 设置为守护线程
            self.query_thread.start()
            self.main_engine.write_log("数据请求处理线程已启动")

    def stop_query_thread(self):
        """停止数据请求处理线程"""
        self.query_thread_running = False
        if self.query_thread:
            self.query_thread.join()
            self.main_engine.write_log("数据请求处理线程已停止")

    def process_query_queue(self):
        """处理数据请求队列的线程函数"""
        while self.query_thread_running:
            try:
                time.sleep(1)
                # 等待新的请求
                request: QueryRequest = self.query_queue.get(timeout=1)
                
                # 如果需要清空数据库，发送清空请求
                if request.clear_db:
                    clear_request = PositionData2DB(
                        username="",  # 特殊标记：空用户名
                        symbol="",    # 特殊标记：空标的
                        exchange="",
                        market_type="",
                        leverage=0,
                        position=0,
                        current_price=0,
                        position_value=0,
                        max_notional_value=0,
                        is_delete=False  # 使用特殊标记而不是删除标记
                    )
                    self.db_queue.put(clear_request)
                
                # 处理查询请求
                for username, gateway in self.users.items():
                    if request.type == "contract":
                        gateway.rest_api.query_contract()
                        time.sleep(1)
                        gateway.rest_api.query_symbol_config()  # 查询交易对配置
                        time.sleep(1)
                    elif request.type == "position":
                        gateway.query_position()  # 查询持仓
                        time.sleep(1)
                    
            except Empty:
                continue
            except Exception as e:
                self.main_engine.write_log(f"处理数据请求时发生错误: {str(e)}\n{traceback.format_exc()}")

    def close(self):
        """关闭监控"""
        try:
            # 停止数据库处理线程
            self.stop_db_thread()
            # 停止数据请求处理线程
            self.stop_query_thread()
            
            if hasattr(self, 'db'):
                self.db.close()
        except Exception as e:
            self.main_engine.write_log(f"关闭数据库连接失败: {str(e)}")
        finally:
            self.main_engine.close()

    def run(self):
        """运行应用"""
        self.main_engine.write_log("实时持仓监控已启动")
        while self._active:
            time.sleep(1)
        self.main_engine.write_log("应用正在关闭...")
        self.close()

def main():
    """主函数"""
    app = AccountMonitor()
    app.run()

if __name__ == "__main__":
    main() 