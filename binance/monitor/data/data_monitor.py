import signal
import sys
import time
import traceback
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Tuple
from collections import defaultdict
import math

from vnpy.event import Event, EventEngine
from vnpy.trader.engine import MainEngine, EmailEngine
from vnpy.trader.event import EVENT_TIMER
from vnpy.trader.constant import Exchange, Interval
from vnpy.trader.setting import SETTINGS
from vnpy_mysql.mysql_database import MysqlDatabase, DbBarOverview
from vnpy.trader.utility import load_json, save_json

try:
    from vnpy.usertools.zh_notify_tool import report_we_alert
    from vnpy.usertools.db_status_manager import Status
except:
    from prod.wecom_alert import report_we_alert
    from prod.db_status_manager import Status


class BarOverviewCache:
    """K线数据汇总缓存"""
    
    def __init__(self):
        """初始化缓存"""
        # 使用defaultdict优化字典结构
        self.overviews = defaultdict(dict)  # 格式: {vt_symbol: overview_dict}
        
    def update(self, overview: DbBarOverview) -> None:
        """更新缓存"""
        vt_symbol = f"{overview.symbol}.{overview.exchange}"
        
        self.overviews[vt_symbol] = {
            "count": overview.count,
            "start": overview.start,
            "end": overview.end
        }
        
    def get(self, symbol: str, exchange: str) -> Dict:
        """获取缓存数据"""
        vt_symbol = f"{symbol}.{exchange}"
        return self.overviews.get(vt_symbol)


class DataMonitor:
    """数据监控器"""
    
    setting_filename: str = "data_monitor_setting.json"
    
    def __init__(self):
        """初始化监控器"""
        self.event_engine = EventEngine()
        self.main_engine = MainEngine(self.event_engine)
        self.database = MysqlDatabase()
        self.cache = BarOverviewCache()
        
        # 告警配置
        self.max_delay_threshold = 24 * 60   # 数据滞后超过24小时不再由本系统处理
        self.base_delay_cooldown = 180       # 基础滞后告警冷却时间：3分钟
        self.max_delay_cooldown = 1800       # 最大滞后告警冷却时间：30分钟
        
        # 初始化时间
        now = datetime.now()
        cooldown_time = now - timedelta(seconds=self.base_delay_cooldown)  # 设置为基础冷却时间前，避免启动时立即告警
        
        # 异常记录
        self.anomalies = defaultdict(lambda: {
            "missing_count": 0,              # 缺失数据计数
            "delay_minutes": 0,              # 数据滞后分钟数
            "consecutive_delay_alerts": 0,   # 连续滞后告警次数
            "last_delay_alert": cooldown_time, # 上次滞后告警时间
            "missing_details": {}            # 缺失数据详情
        })
        
        # 监控的交易对列表
        self.symbol_list = []
        self._last_reload_time = now
        self._reload_interval = timedelta(minutes=30)  # 设置重新加载间隔为30分钟
        
        # 正常状态通知跟踪
        self._last_normal_alert_hour = now.hour  # 上次发送正常状态通知的小时
        self._normal_alert_sent = True    # 当前小时是否已发送正常状态通知
        
        # 加载配置
        self.load_setting()
        
        # 初始化缓存
        self.init_cache()
        
        # 注册事件处理器
        self.register_event()
        
        # 计时器
        self.timer_count = 0
        self.check_interval = 60  # 每60秒检查一次

        self._active = True  # 添加运行状态标记

    def load_setting(self) -> None:
        """加载配置"""
        self.refill_setting()
        self.setting = load_json(self.setting_filename)
        if not self.setting:
            self.setting = {
                "symbol_list": ["ETHUSDT.BINANCE", "BTCUSDT.BINANCE", "BNBUSDT.BINANCE", "ADAUSDT.BINANCE", "DOGEUSDT.BINANCE"],  # 需要监控的交易对列表
                "wecom_key": "b54436b7-2384-4b54-863f-caebc34309a4",  # 企业微信机器人key
                "delay_threshold": 3  # 数据滞后超过3分钟触发告警
            }
            save_json(self.setting_filename, self.setting)
            
        # 更新监控列表和延迟阈值
        self.symbol_list = self.setting["symbol_list"]
        self.delay_threshold = self.setting["delay_threshold"]
        self.write_log(f"配置加载完成，监控列表：{self.symbol_list}")

    def _signal_handler(self, signum, frame):
        """
        信号处理函数
        """
        sig_name = signal.Signals(signum).name
        self.main_engine.write_log(f"收到信号: {sig_name}，准备关闭应用...")
        self._active = False
        
    def init_cache(self) -> None:
        """初始化缓存"""
        # 直接使用ORM查询分钟线数据
        minute_overviews = DbBarOverview.select().where(
            DbBarOverview.interval == Interval.MINUTE.value
        )
        
        for overview in minute_overviews:
            self.cache.update(overview)
                
        self.write_log(f"成功加载 {minute_overviews.count()} 条分钟线汇总数据")
            
    def register_event(self) -> None:
        """注册事件处理器"""
        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

        # 在Windows平台上signal模块可能不完全支持
        if sys.platform != 'win32':
            # 忽略SIGTSTP信号(Ctrl+Z)，避免进程被挂起
            signal.signal(signal.SIGTSTP, signal.SIG_IGN)
            # 注册SIGHUP信号处理器，用于重新加载配置
            signal.signal(signal.SIGHUP, self.reload_setting)

        self.event_engine.register(EVENT_TIMER, self.process_timer_event)
        
    def reload_setting(self, signum=None, frame=None) -> None:
        """重新加载配置"""
        self.load_setting()
        self.write_log("配置重新加载完成")

    def refill_setting(self, signum=None, frame=None) -> None:
        """重新加载配置"""
        self.setting = load_json(self.setting_filename)
        if not self.setting:
            self.setting = {
                "symbol_list": ["ETHUSDT.BINANCE", "BTCUSDT.BINANCE", "BNBUSDT.BINANCE", "ADAUSDT.BINANCE", "DOGEUSDT.BINANCE"],  # 需要监控的交易对列表
                "wecom_key": "b54436b7-2384-4b54-863f-caebc34309a4",  # 企业微信机器人key
                "delay_threshold": 5  # 数据滞后超过3分钟触发告警
            }
        else:
            query = Status.select(Status.content).where(Status.running_status == 2)
            symbols = set()  # 使用集合自动去重
            for status in query:
                parts = status.content.split("_")
                if len(parts) > 1:
                    symbols.add(parts[0])

            # 转换为列表并更新设置
            symbol_list = list(symbols)
            self.setting["symbol_list"] = symbol_list
        save_json(self.setting_filename, self.setting)
        self.write_log("symbol refill finished")

    def process_timer_event(self, event: Event) -> None:
        """处理定时器事件"""
        self.timer_count += 1
        if self.timer_count < self.check_interval:
            return
        self.timer_count = 0
        
        # 检查是否需要重新加载配置
        now = datetime.now()
        if (now - self._last_reload_time) > self._reload_interval:
            # re-fill setting
            self.refill_setting()
            self.reload_setting()
            self._last_reload_time = now
        
        self.write_log("开始检查数据完整性")
        self.check_data_integrity()
        
    def check_data_integrity(self) -> None:
        """检查数据完整性"""
        # 直接使用ORM查询最新的分钟线数据
        current_overviews = DbBarOverview.select().where(
            DbBarOverview.interval == Interval.MINUTE.value
        )
        
        now = datetime.now()
        current_hour = now.hour
        current_minute = now.minute
        
        # 检查是否需要重置正常状态通知标志
        if current_hour != self._last_normal_alert_hour:
            self._normal_alert_sent = False
            self._last_normal_alert_hour = current_hour
        
        # 记录需要告警的信息
        missing_symbols = []  # 数据缺失的合约
        delay_symbols = []    # 数据滞后的合约
        
        for overview in current_overviews:
            vt_symbol = f"{overview.symbol}.{overview.exchange}"
            
            # 只检查监控列表中的交易对
            if vt_symbol not in self.symbol_list:
                continue
            
            # 1. 检查数据是否落后当前时间
            delay_minutes = int((now - overview.end).total_seconds() / 60)
            if self.delay_threshold <= delay_minutes < self.max_delay_threshold:
                self.anomalies[vt_symbol]["delay_minutes"] = delay_minutes
                self.write_log(f"检测到数据滞后: {vt_symbol}, 滞后时间: {delay_minutes} 分钟")
                
                # 检查是否需要发送滞后告警（使用指数退避策略）
                # 在每小时05分时，忽略冷却时间限制
                if self.should_send_delay_alert(vt_symbol, now) or (current_minute >= 5 and not self._normal_alert_sent):
                    delay_symbols.append(vt_symbol)
                    self.anomalies[vt_symbol]["last_delay_alert"] = now
                    self.anomalies[vt_symbol]["consecutive_delay_alerts"] += 1
                    self.write_log(f"将发送滞后告警: {vt_symbol}, 连续告警次数: {self.anomalies[vt_symbol]['consecutive_delay_alerts']}")
            elif delay_minutes < self.delay_threshold:
                # 数据恢复正常
                if self.anomalies[vt_symbol].get("delay_minutes", 0) >= self.delay_threshold:
                    self.write_log(f"数据滞后已恢复正常: {vt_symbol}")
                    # 重置连续告警次数
                    self.anomalies[vt_symbol]["consecutive_delay_alerts"] = 0
                self.anomalies[vt_symbol]["delay_minutes"] = 0
                
            # 2. 检查数据是否有缺失
            cached_data = self.cache.get(overview.symbol, overview.exchange)
            if cached_data and overview.end > cached_data["end"]:
                # 计算应该增加的分钟数和实际增加的数据条数
                minutes_diff = int((overview.end - cached_data["end"]).total_seconds() / 60)
                count_diff = overview.count - cached_data["count"]
                
                # 如果分钟数和数据条数不匹配，可能存在数据缺失
                if minutes_diff > count_diff:
                    missing_count = minutes_diff - count_diff
                    self.anomalies[vt_symbol]["missing_count"] += missing_count
                    
                    # 记录缺失数据详情
                    self.anomalies[vt_symbol]["missing_details"] = {
                        "previous_end": cached_data["end"],
                        "current_end": overview.end,
                        "minutes_diff": minutes_diff,
                        "count_diff": count_diff,
                        "missing_count": missing_count
                    }
                    
                    self.write_log(f"检测到数据缺失: {vt_symbol}, "
                                  f"时间增加: {minutes_diff}分钟, 数据增加: {count_diff}条, "
                                  f"缺失: {missing_count}条, "
                                  f"时间段: {cached_data['end']} 至 {overview.end}")
                    
                    # 数据缺失每次检测到都告警
                    missing_symbols.append(vt_symbol)
            
            # 更新缓存
            self.cache.update(overview)
        
        # 发送合并告警消息
        self.send_combined_alert(missing_symbols, delay_symbols, current_minute)
        
        # 重置已告警合约的缺失计数
        for symbol in missing_symbols:
            self.anomalies[symbol]["missing_count"] = 0
                
    def should_send_delay_alert(self, vt_symbol: str, now: datetime) -> bool:
        """判断是否应该发送数据滞后告警（使用指数退避策略）"""
        anomaly = self.anomalies[vt_symbol]
        
        # 计算滞后告警冷却时间（指数退避策略）
        # 确保冷却时间至少为基础冷却时间
        cooldown_seconds = min(
            max(self.base_delay_cooldown, self.base_delay_cooldown * (2 ** anomaly["consecutive_delay_alerts"])),
            self.max_delay_cooldown
        )
        
        # 检查是否超过冷却时间
        seconds_since_last_alert = (now - anomaly["last_delay_alert"]).total_seconds()
        should_alert = seconds_since_last_alert >= cooldown_seconds
        
        if not should_alert:
            remaining = int(cooldown_seconds - seconds_since_last_alert)
            self.write_log(f"{vt_symbol} 滞后告警冷却中，还需等待 {remaining} 秒 (冷却时间: {int(cooldown_seconds)}秒)")
            
        return should_alert
                
    def send_combined_alert(self, missing_symbols: List[str], delay_symbols: List[str], current_minute: int) -> None:
        """发送合并告警消息"""            
        # 构建消息主题
        if missing_symbols and delay_symbols:
            subject = f"数据监控报警: 检测到数据缺失和滞后 ({len(missing_symbols) + len(delay_symbols)}个合约)"
        elif missing_symbols:
            subject = f"数据监控报警: 检测到数据缺失 ({len(missing_symbols)}个合约)"
        elif delay_symbols:
            subject = f"数据监控报警: 检测到数据滞后 ({len(delay_symbols)}个合约)"
        elif current_minute >= 5 and not self._normal_alert_sent:
            # 在每小时的05分及以后，如果还未发送过正常状态通知，则发送
            subject = f"数据监控通知: 所有合约数据正常"
            self._normal_alert_sent = True
        else:
            return
        
        # 构建消息内容
        content = f"{subject}\n\n检测时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
        
        # 添加数据缺失部分
        if missing_symbols:
            content += "===== 数据缺失合约 =====\n"
            for i, symbol in enumerate(missing_symbols, 1):
                anomaly = self.anomalies[symbol]
                details = anomaly.get("missing_details", {})
                
                previous_end = details.get("previous_end", "未知")
                current_end = details.get("current_end", "未知")
                minutes_diff = details.get("minutes_diff", 0)
                count_diff = details.get("count_diff", 0)
                missing_count = details.get("missing_count", 0)
                
                # 格式化时间显示
                if isinstance(previous_end, datetime):
                    previous_end_str = previous_end.strftime('%Y-%m-%d %H:%M:%S')
                else:
                    previous_end_str = str(previous_end)
                    
                if isinstance(current_end, datetime):
                    current_end_str = current_end.strftime('%Y-%m-%d %H:%M:%S')
                else:
                    current_end_str = str(current_end)
                
                content += f"{i}. {symbol}:\n"
                content += f"   - 缺失时间段: {previous_end_str} 至 {current_end_str}\n"
                content += f"   - 时间跨度: {minutes_diff} 分钟，应有数据: {minutes_diff} 条\n"
                content += f"   - 实际数据: {count_diff} 条，缺失: {missing_count} 条\n"
            content += "\n"
            
        # 添加数据滞后部分
        if delay_symbols:
            content += "===== 数据滞后合约 =====\n"
            for i, symbol in enumerate(delay_symbols, 1):
                anomaly = self.anomalies[symbol]
                # 获取数据结束时间
                cached_data = self.cache.get(*symbol.split('.'))
                end_time = cached_data["end"] if cached_data else "未知"
                
                # 格式化时间显示
                if isinstance(end_time, datetime):
                    end_time_str = end_time.strftime('%Y-%m-%d %H:%M:%S')
                else:
                    end_time_str = str(end_time)
                
                content += f"{i}. {symbol}:\n"
                content += f"   - 最新数据时间: {end_time_str}\n"
                content += f"   - 滞后时间: {anomaly['delay_minutes']} 分钟\n"
                content += f"   - 连续告警次数: {anomaly['consecutive_delay_alerts']}\n"
            content += "\n"
        
        # 如果是正常状态通知，添加所有合约的状态信息
        if not missing_symbols and not delay_symbols:
            content += "===== 所有合约状态正常 =====\n"
            content += "所有合约的分钟线数据均正常采集，无延迟和缺失。\n\n"
            
        
        # 使用企业微信机器人发送告警
        success, msg = report_we_alert(content, key=self.setting["wecom_key"])
        if success:
            if missing_symbols or delay_symbols:
                self.write_log(f"告警发送成功，包含 {len(missing_symbols)} 个缺失合约和 {len(delay_symbols)} 个滞后合约")
            else:
                self.write_log("正常状态通知发送成功")
        else:
            self.write_log(f"告警发送失败: {msg}")
            
    def write_log(self, msg: str) -> None:
        """写入日志"""
        self.main_engine.write_log(msg)
        
    def run(self) -> None:
        """运行监控器"""
        self.write_log("数据监控器已启动")
        while self._active:
            time.sleep(1)
        self.write_log("数据监控器正在关闭...")
        self.main_engine.close()
            
def main():
    """主函数"""
    monitor = DataMonitor()
    monitor.run()
    
if __name__ == "__main__":
    main()
