import time
import random
from datetime import datetime, timedelta
from typing import List
import traceback

from vnpy.trader.constant import Exchange, Interval
from vnpy.trader.object import BarData
from vnpy.trader.utility import extract_vt_symbol
from vnpy_mysql.mysql_database import MysqlDatabase
from vnpy.trader.database import DB_TZ


class DataGeneratorTest:
    """数据生成测试类"""
    
    def __init__(self):
        """初始化"""
        self.database = MysqlDatabase()
        self.vt_symbols = ["BTCUSDT.BINANCE", "ETHUSDT.BINANCE", "BNBUSDT.BINANCE", "ADAUSDT.BINANCE", "DOGEUSDT.BINANCE"]
        self.interval = Interval.MINUTE
        
        # 测试设置
        self.drop_data_minute = 3  # 在第3分钟丢失数据
        self.delay_data_duration = 5  # 生成5分钟延迟的数据
        self.missing_symbol = None  # 将在运行时随机选择
        
    def generate_bar_data(self, vt_symbol: str, dt: datetime, skip_data: bool = False) -> BarData:
        """生成随机K线数据"""
        if skip_data:
            print(f"跳过生成数据: {vt_symbol} {dt}")
            return None
            
        # 解析vt_symbol获取symbol和exchange
        symbol, exchange = extract_vt_symbol(vt_symbol)
        
        # 生成随机价格数据
        close_price = random.uniform(100, 1000)
        open_price = close_price * random.uniform(0.99, 1.01)
        high_price = max(close_price, open_price) * random.uniform(1.0, 1.02)
        low_price = min(close_price, open_price) * random.uniform(0.98, 1.0)
        
        # 生成随机成交量和成交额
        volume = random.uniform(10, 100)
        turnover = volume * close_price
        
        # 创建K线数据对象
        bar = BarData(
            symbol=symbol,
            exchange=exchange,
            datetime=dt,
            interval=self.interval,
            volume=volume,
            turnover=turnover,
            open_price=open_price,
            high_price=high_price,
            low_price=low_price,
            close_price=close_price,
            gateway_name="DB"
        )
        
        return bar
        
    def run_test(self, total_minutes: int = 10):
        """运行测试"""
        print(f"开始生成测试数据，共 {total_minutes} 分钟")
        
        # 随机选择一个合约作为丢失数据的对象
        self.missing_vt_symbol = random.choice(self.vt_symbols)
        print(f"随机选择的丢失数据合约: {self.missing_vt_symbol}")
        
        # 获取当前时间，并向前推算，生成历史数据
        now = datetime.now().replace(second=0, microsecond=0)
        start_time = now - timedelta(minutes=total_minutes)
        
        # 为每个合约分别生成数据
        for vt_symbol in self.vt_symbols:
            print(f"\n生成 {vt_symbol} 的历史数据")
            
            # 根据不同合约设置不同的延迟
            # 对于丢失数据的合约，设置较大延迟；其他合约设置较小延迟
            if vt_symbol == self.missing_vt_symbol:
                delay_minutes = self.delay_data_duration
            else:
                delay_minutes = random.randint(1, 3)  # 其他合约随机设置1-3分钟的延迟
                
            # 计算数据结束时间（根据延迟）
            end_time = now - timedelta(minutes=delay_minutes)
            
            # 计算此合约的数据需要生成多少分钟
            minutes_to_generate = int((end_time - start_time).total_seconds() / 60)
            print(f"{vt_symbol} 将生成 {minutes_to_generate} 分钟的数据，延迟 {delay_minutes} 分钟")
            
            # 批量生成数据
            bar_list = []
            
            for minute in range(minutes_to_generate):
                current_time = start_time + timedelta(minutes=minute)
                
                # 判断是否需要丢失数据
                skip_data = (minute + 1 == self.drop_data_minute and vt_symbol == self.missing_vt_symbol)
                
                # 生成K线数据
                bar = self.generate_bar_data(vt_symbol, current_time, skip_data)
                
                # 如果生成了数据，则添加到列表
                if bar:
                    bar_list.append(bar)
            
            # 批量保存数据到数据库
            try:
                if bar_list:
                    self.database.save_bar_data(bar_list)
                    print(f"批量保存 {len(bar_list)} 条 {vt_symbol} 数据成功")
            except Exception as e:
                print(f"保存数据失败: {vt_symbol}, 错误: {str(e)}")
                print(traceback.format_exc())
                
        print("\n测试数据生成完成！")
        print(f"合约 {self.missing_vt_symbol} 在第 {self.drop_data_minute} 分钟的数据被丢失")
        print(f"当前时间: {now}, 最新数据时间: {now - timedelta(minutes=self.delay_data_duration)}")
        print("现在可以运行 data_monitor.py 来检测数据缺失和延迟")
        
def main():
    """主函数"""
    test = DataGeneratorTest()
    test.run_test(30)  # 生成30分钟的数据
    
if __name__ == "__main__":
    main() 