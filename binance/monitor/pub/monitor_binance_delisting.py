"""
监控币安下架公告和钱包维护更新公告并发送邮件通知
"""
import smtplib
from datetime import datetime
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from typing import List, Dict, Set

from vnpy.trader.setting import SETTINGS
from vnpy.trader.utility import load_json, save_json
import requests


SENT_FILE = "sent.json"
CATALOG_IDS = {
    "48": "New Cryptocurrency Listing",
    "50": "New Fiat Listings",
    "161": "Delisting",
    "157": "Wallet Maintenance Updates"
}


class BinanceDelistingMonitor:
    def __init__(self):
        self.sent_announcements = self._load_sent_announcements()

    def _load_sent_announcements(self) -> Set[str]:
        """加载已发送过的公告记录"""
        data = load_json(SENT_FILE)
        if not data:
            # 初始化为空列表并保存
            save_json(SENT_FILE, [])
            return set()
        return set(data)

    def _save_sent_announcements(self):
        """保存已发送的公告记录"""
        save_json(SENT_FILE, list(self.sent_announcements))

    def get_announcement_links(self) -> List[Dict]:
        """获取所有公告链接"""
        print("开始获取公告链接...")
        announcements = []
        
        # 设置请求头
        headers = {"lang": "zh-CN"}
        
        # 遍历每个分类
        for catalog_id, catalog_name in CATALOG_IDS.items():
            page = 1
            print(f"\n开始获取 {catalog_name} 分类的公告...")
            
            while True:
                try:
                    # API请求参数
                    url = "https://www.binance.com/bapi/apex/v1/public/apex/cms/article/list/query"
                    params = {
                        "type": "1",
                        "pageNo": str(page),
                        "pageSize": "10",
                        "catalogId": catalog_id
                    }
                    
                    # 发送请求
                    response = requests.get(url, headers=headers, params=params)
                    data = response.json()
                    
                    # 检查是否为空白页面
                    if not data["data"]["catalogs"]:
                        print(f"已到达最后一页: 第{page}页")
                        break
                    
                    # 解析响应
                    if data["code"] == "000000":
                        articles = data["data"]["catalogs"][0]["articles"]
                        for article in articles:
                            announcements.append({
                                'title': article['title'],
                                'code': article['code'],
                                'catalog_name': catalog_name
                            })
                        print(f"第{page}页: 获取到 {len(articles)} 条公告")
                        
                        page += 1
                    else:
                        print(f"API响应错误: {data}")
                        break
                    
                except Exception as e:
                    import traceback
                    print(f"获取公告链接出错: {str(e)}\n{traceback.format_exc()}")
                    break
        
        print(f"\n总共获取到 {len(announcements)} 条公告")
        return announcements

    def send_email(self, title: str, content: str):
        """发送邮件通知"""
        try:
            # 获取邮件配置
            server = SETTINGS["email.server"]
            port = SETTINGS["email.port"]
            username = SETTINGS["email.username"]
            password = SETTINGS["email.password"]
            sender = SETTINGS["email.sender"]
            receiver = SETTINGS["email.receiver"]

            # 创建邮件
            msg = MIMEMultipart()
            msg["From"] = sender
            msg["To"] = receiver
            msg["Subject"] = title

            # 添加正文
            msg.attach(MIMEText(content, "plain", "utf-8"))

            # 发送邮件
            with smtplib.SMTP_SSL(server, port) as smtp:
                smtp.login(username, password)
                smtp.send_message(msg)
            print(f"邮件发送成功: {title}")
            
        except Exception as e:
            print(f"邮件发送失败: {str(e)}")

    def run(self):
        """运行监控"""
        try:
            print("开始监控币安公告...")
            
            # 获取所有公告链接
            announcements = self.get_announcement_links()
            
            # 检查新公告
            new_announcements = []
            for announcement in announcements:
                if announcement['code'] not in self.sent_announcements:
                    new_announcements.append(announcement)
            
            if not new_announcements:
                print("没有新的公告")
                return
            
            # 发送邮件通知
            print(f"发现 {len(new_announcements)} 条新公告")
            
            title = f"币安公告通知 - {datetime.now().strftime('%Y-%m-%d')}"
            content = ""
            
            # 按分类组织公告内容
            announcements_by_category = {}
            for announcement in new_announcements:
                category = announcement['catalog_name']
                if category not in announcements_by_category:
                    announcements_by_category[category] = []
                announcements_by_category[category].append(announcement)
            
            # 生成邮件内容
            for category, category_announcements in announcements_by_category.items():
                content += f"\n\n=== {category} ===\n"
                for announcement in category_announcements:
                    url = f"https://www.binance.com/zh-CN/support/announcement/{announcement['code']}"
                    content += f"\n标题: {announcement['title']}\n链接: {url}\n"
                    self.sent_announcements.add(announcement['code'])
            
            self.send_email(title, content)
            
            # 保存已发送记录
            self._save_sent_announcements()
            
        except Exception as e:
            print(f"监控运行出错: {str(e)}")


if __name__ == "__main__":
    monitor = BinanceDelistingMonitor()
    monitor.run() 