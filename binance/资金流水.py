from time import sleep

import pandas as pd
from vnpy_binance import (BinanceLinearGateway)
from vnpy_binance.binance_linear_gateway import BinanceLinearRestApi
from vnpy_evo.event import EventEngine
from vnpy_evo.trader.engine import MainEngine
from vnpy_evo.trader.utility import load_json


def summary_incomes_df(df):
    for symbol in df["symbol"].unique():
        df_symbol = df[df["symbol"] == symbol]
        funding_fee_sum = df_symbol[df_symbol["incomeType"] == "FUNDING_FEE"]["income"].sum()
        commission_sum = df_symbol[df_symbol["incomeType"] == "COMMISSION"]["income"].sum()
        print(f"{symbol}: funding fee sum：{funding_fee_sum}，commission sum：{commission_sum}")
        if commission_sum == 0:
            continue
        print(f"{symbol}: funding fee/commission rate: {round(funding_fee_sum / commission_sum * 100, 2)}%")


def main():
    """主入口函数"""

    event_engine = EventEngine()
    main_engine = MainEngine(event_engine)

    main_engine.add_gateway(BinanceLinearGateway)

    setting = load_json('connect_binance.json')

    main_engine.connect(setting=setting, gateway_name=BinanceLinearGateway.default_name)

    gateway: BinanceLinearGateway = main_engine.get_gateway(BinanceLinearGateway.default_name)
    api: BinanceLinearRestApi = gateway.rest_api

    api.query_income()

    sleep(3)

    incomes = pd.concat([pd.DataFrame([v1]) for v in gateway.incomes.values() for v1 in v.values()])
    incomes.to_csv('incomes.csv')
    summary_incomes_df(incomes)

    main_engine.close()


if __name__ == "__main__":
    main()
