import signal
import sys

from vnpy.trader.setting import SETTINGS
from vnpy.trader.utility import load_json, save_json
from vnpy_evo.trader.event import EVENT_ACCOUNT, EVENT_POSITION, EVENT_CONTRACT
from vnpy_evo.event import Event

from vnpy.trader.database import DB_TZ, ZoneInfo
UTC_TZ = ZoneInfo("UTC")

from vnpy_evo.event import EventEngine
from vnpy_evo.trader.engine import MainEngine
from time import sleep
from datetime import datetime

from prod.barGen_redis_engine import BarGenEngine
from prod.binance_linear_gateway import BinanceLinearGateway
# from prod.binance_linear_gateway_log import BinanceLinearGateway
from prod.recorder_engine import RecorderEngine
from vnpy_ctastrategy import CtaStrategyApp
from vnpy_ctastrategy.base import EVENT_CTA_LOG
import urllib.request

def get_system_proxy() -> dict:
    """获取系统代理设置"""
    proxy_handler = urllib.request.ProxyHandler()
    proxies = {}
    
    # 从系统获取代理设置
    for protocol in ['http', 'https']:
        if proxy := proxy_handler.proxies.get(protocol):
            proxies[protocol] = proxy
            
    return proxies

class BinanceApp:
    """币安应用"""
    
    def __init__(self):
        """构造函数"""
        self.event_engine = EventEngine()
        self.main_engine = MainEngine(self.event_engine)
        
        # 添加合约初始化标志
        self.contract_inited = False

        # 添加运行状态标记
        self._active = True

        self.register_event()
        self.init_engines()
        
    def init_engines(self) -> None:
        """初始化引擎"""
        # 添加币安网关
        self.main_engine.add_gateway(BinanceLinearGateway)
        # setting = load_json("connect_binance_read.json")
        setting = load_json("connect_binance_testnet.json")
        self.main_engine.connect(setting, "BINANCE_LINEAR")
        self.main_engine.write_log("接口添加成功")
        
        # 添加K线生成引擎
        self.bar_gen_engine = self.main_engine.add_engine(BarGenEngine)
        self.bar_gen_engine.start()
        self.main_engine.write_log("添加Bar生成引擎")

        # 添加K线记录引擎
        self.recorder_engine = self.main_engine.add_engine(RecorderEngine)
        self.main_engine.write_log("添加数据记录引擎")

        # 添加CTA策略引擎
        self.cta_engine = self.main_engine.add_app(CtaStrategyApp)
        self.main_engine.write_log("CTA策略引擎创建成功")

        # 注册日志事件监听
        log_engine = self.main_engine.get_engine("log")
        self.event_engine.register(EVENT_CTA_LOG, log_engine.process_log_event)
        self.main_engine.write_log("注册日志事件监听")

    def get_origin_time(self, symbol: str) -> datetime:
        """获取原点时间"""
        # 先从本地文件获取
        first_minute_data = load_json("binance_first_minute.json")
        
        if symbol in first_minute_data:
            first_minute = datetime.fromisoformat(first_minute_data[symbol])
        else:
            # 从API获取
            first_minute = self.query_first_minute(symbol)
            if first_minute:
                first_minute_data[symbol] = first_minute.isoformat()
                save_json("binance_first_minute.json", first_minute_data)
            else:
                return None

        # 设置为当天UTC 0点
        origin = first_minute.astimezone(UTC_TZ).replace(
            hour=0, 
            minute=0, 
            second=0, 
            microsecond=0
        )
        return origin

    def query_first_minute(self, symbol: str) -> datetime:
        """查询第一分钟K线时间"""
        # 从2000年开始查询
        start_time = int(datetime(2000, 1, 1).timestamp() * 1000)
        
        params = {
            "symbol": symbol.replace(".", ""),  # 移除交易所后缀
            "interval": "1m",
            "limit": 1,
            "startTime": start_time
        }

        # 使用gateway的REST API查询
        path = "/fapi/v1/klines"
        resp = self.main_engine.get_gateway("BINANCE_LINEAR").rest_api.request(
            "GET",
            path=path,
            data={"security": "NONE"},
            params=params
        )

        if resp.status_code // 100 != 2:
            self.main_engine.write_log(f"查询K线历史失败，状态码：{resp.status_code}，信息：{resp.text}，标的：{symbol}")
            return None

        data = resp.json()
        if not data:
            self.main_engine.write_log(f"未接收到K线历史数据，开始时间：{start_time}，标的：{symbol}")
            return None

        print(f'{symbol} data: {data}')

        # 返回开盘时间（与fetch_first_minute_data.py保持一致，使用DB_TZ）
        first_kline_time = datetime.fromtimestamp(
            data[0][0] / 1000,  # 转换毫秒为秒
            tz=UTC_TZ
        ).astimezone(DB_TZ)
        
        return first_kline_time

    def init_cta_strategy(self):
        """初始化CTA策略"""
        self.cta_engine.init_engine()
        
        # 添加ETHUSDT的策略
        strategy_name = "MultiTimeframe_ETHUSDT"
        symbol = "ETHUSDT.BINANCE"
        
        if strategy_name not in self.cta_engine.strategies:
            # 获取origin时间
            origin = self.get_origin_time("ETHUSDT")
            if not origin:
                self.main_engine.write_log("无法获取ETHUSDT的第一分钟数据")
                return

            self.cta_engine.add_strategy(
                "MultiTimeframeStrategy",
                strategy_name,
                symbol,
                {
                    "fixed_size": 0.001,
                    "origin": origin.isoformat()  # 传入ISO格式的origin时间
                }
            )
        
        # 初始化所有策略
        self.main_engine.write_log("开始初始化CTA策略")
        self.cta_engine.init_all_strategies()

        # 等待所有策略初始化完成
        time1 = datetime.now()
        while not all([strategy.inited for strategy in self.cta_engine.strategies.values()]):
            sleep(1)
        time_used = datetime.now() - time1
        self.main_engine.write_log(f"CTA策略全部初始化，耗时：{time_used}，平均每个策略耗时：{time_used / len(self.cta_engine.strategies)}")

        # 启动所有策略
        self.cta_engine.start_all_strategies()
        self.main_engine.write_log("CTA策略全部启动")

    def register_event(self):
        """注册事件监听"""
        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

        # 在Windows平台上signal模块可能不完全支持
        if sys.platform != 'win32':
            # 忽略SIGTSTP信号(Ctrl+Z)，避免进程被挂起
            signal.signal(signal.SIGTSTP, signal.SIG_IGN)

        self.event_engine.register(EVENT_ACCOUNT, self.process_event)
        self.event_engine.register(EVENT_POSITION, self.process_event)
        self.event_engine.register(EVENT_CONTRACT, self.process_contract_event)

    def _signal_handler(self, signum, frame):
        """信号处理函数"""
        sig_name = signal.Signals(signum).name
        self.main_engine.write_log(f"收到信号: {sig_name}，准备关闭应用...")
        self._active = False

    def process_event(self, event: Event):
        """处理账户事件"""
        self.main_engine.write_log(f"{event.type} Update: {event.data}")

    def process_contract_event(self, event: Event):
        """处理合约事件"""
        contract = event.data
        if contract.vt_symbol == "ETHUSDT.BINANCE" and not self.contract_inited:
            self.contract_inited = True
            self.main_engine.write_log(f"收到合约信息：{contract.vt_symbol}，开始初始化CTA策略")
            self.init_cta_strategy()

    def run(self):
        """运行应用"""
        self.main_engine.write_log("应用已启动")
        while self._active:
            sleep(1)
        self.main_engine.write_log("应用正在关闭...")
        self.close()
            
    def close(self):
        """关闭应用"""
        self.main_engine.close()

def main():
    """主函数"""
    app = BinanceApp()
    app.run()

if __name__ == "__main__":
    main()