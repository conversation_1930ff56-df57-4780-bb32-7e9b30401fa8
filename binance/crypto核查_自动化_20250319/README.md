0. 部署步骤：
    1. pip install -r requirements.txt
    2. 参照文末config.json参数详解，填写config.json
    3. 运行CENTRAL_CONTROL.py (在收到main实盘数据之后运行，大概15:06之后)
    4. 将morning_monitor_CONTROL.py定时每日早上9:00运行，CENTRAL_CONTROL定时每日下午15:10运行，项目路径为本文件夹地址。

1. 所有的参数在 config.json 中修改即可。邮件需要开通smtp,imap权限, password是权限密钥不是账户密码。config.json说明请看本文末


2. 请勿随意移动项目内文件位置, 若要移动需要相应的更换文件中config.json等涉及的文件的读取路径, 否则可能会导致有文件读取失败。


3. config中的symbol_pool由morning_monitor_CONTROL.py来每日自动更新。


4. config.json中的tolerance_1和tolerance_2可以控制数据核查的精度，默认为0即两者差的绝对值小于等0，可以修改，不同核查脚本有不同需求的可以去脚本内手动修改并单独运行。


5. 自动核查程序通过 CENTRAL_CONTROL.py 控制, 目前的流程是： 

    获取邮件中的文件 --> 

    数据入库 --> 

    1min数据核查, 邮件告知结果并附上文件(如有) --> 

    如果一致, 聚合当日k线,不一致, 需手动检查原因并覆盖错误的数据 (看具体要求, 也可以自动覆盖, 但是自动覆盖了再去找原因略有不便)

    (如果部署环境有vpn, 可在此步骤处手动增加 运行kline_download.py)

    自动核对k线, 邮件告知结果并附上文件(如有) --> 

    如果全部一致, 或者只有bar内首根分钟volume为0跳过导致的不一致, 自动生成并核对技术指标, 邮件告知结果并附上文件(如有), 如果有其他原因的k线不一致, 需手动检查报错文件 --> 自动核查结束

    注意*：若任何步骤出问题了需要查明原因，修正，再重复运行每一步程序，直至分钟线、k线核查、技术指标核查都报一致
    注意**：若CENTRAL_CONTROL.py一直报错无法正常运行，请尝试将config.json中的encoding 'gb2312' 为 'utf-8'


6. 目前还在测试阶段, 尽量只修改config.json，若其他文件有任何必要的修改、问题、建议或者bug, 请随时联系花, 我们一起让自动核查更完善、更方便，感谢。


config.json 参数详解:

    "market_name": "Crypto",        # 市场名称，用于邮件通知时标题的前缀，可自定义
    "symbol_pool": [],              # 合约列表，无需更新，morning_monitor_CONTROL.PY会自动更新这个列表
    "to_be_delisted": [],           # 即将退市的合约，之前统计涨跌幅时用来提醒栋哥的，现在没这个需求，无需维护
    "delisted": [],                 # 已退市的合约，之前没有自动更新合约列表时用来跳过已退市的合约，现在自动更新后无需维护了，也可以有退市的合约就往里填，双重保险
    
    "host": "*************",           # 用来核对数据的标准数据库的host，这边已经填好，这是服务器上的用来核对数字币的标准库地址，可以用navicat查看数据库结构，https://www.navicat.com.cn/download/navicat-premium-lite
    "port": 3308,                      # 标准数据库的port
    "user": "zh",                      # 标准数据库的user
    "password": "zhP@55word",                 # 标准数据库的password
    "database1_standard": "vnpy_crypto_agg",       # 标准数据库的名称
    
    "host2": "127.0.0.1",                   # 需要核对的数据库的host，需要填写本地地址
    "port2": 3306,                          # 需要核对的数据库的port，本地数据库端口
    "user2": "root",                        # 需要核对的数据库的user，本地数据库用户名
    "password2": "",                        # 需要核对的数据库的password，本地数据库密码
    "database2_needcheck": "",                          # 需要核对的数据库的名称，需本地创建一个数据库后填写数据库名称
    "get_file_mail_subject": "main_Daily Kline Data",   # 获取每日邮件时用来搜索的邮件主题，不用改，发生变化是需要改
    "email_username": "<EMAIL>",             # 邮件通知时的发信人地址
    "email_password": "xxxxxxxxxxx",                    # 邮件通知时的发信人的密钥，注意不是密码，需要协助获取请联系花，https://open.work.weixin.qq.com/help2/pc/19886?person_id=1 企业微信邮箱 smtp imap开通 密码生成
    "to_addr": [
        "<EMAIL>"                        # 邮件通知时的收信人，我一般发信人收信人都写自己
    ],
    "imap_port": 993,                               # imap的port，993是企业微信邮箱的
    "imap_server": "imap.exmail.qq.com",            # imap的服务器地址，这个是企业微信邮箱的， imap用来搜索收件箱
    "smtp_port": 465,                               # smtp的port，465是企业微信邮箱的, smtp用来发信息
    "smtp_server": "smtp.exmail.qq.com",            # smtp的服务器地址，这个是企业微信邮箱的
    "tolerance_1": 0,                               # 高开低收的容忍度，一般为0，想查看允许错过0.01以内时的核查结果可以输入0.011，0.011而不是0.01是为了避免浮点数误差
    "tolerance_2": 0,                               # 成交量、成交额的容忍度，一般为0，想查看允许错过0.01以内时的核查结果可以输入0.011，0.011而不是0.01是为了避免浮点数误差
    "proxy_port_for_klinedownload": 29290           #   用来下载k线数据的port，核查程序不涉及
    "encoding": "gb2312"                            # encoding参数，若central_control.py跑不通，尝试将gb2312替换为utf-8