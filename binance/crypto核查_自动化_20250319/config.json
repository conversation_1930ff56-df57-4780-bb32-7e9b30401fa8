{"market_name": "Crypto", "symbol_pool": ["1000BONKUSDT", "1000FLOKIUSDT", "1000PEPEUSDT", "1000RATSUSDT", "1000SATSUSDT", "AAVEUSDT", "AGLDUSDT", "ALGOUSDT", "APEUSDT", "ARKMUSDT", "ARUSDT", "AVAXUSDT", "BIGTIMEUSDT", "BTCUSDT", "CRVUSDT", "DOGEUSDT", "ENAUSDT", "ENSUSDT", "ETHUSDT", "FETUSDT", "GALAUSDT", "GRTUSDT", "HBARUSDT", "IOTAUSDT", "JASMYUSDT", "LPTUSDT", "MEWUSDT", "MYROUSDT", "NEARUSDT", "NEIROUSDT", "ONEUSDT", "PEOPLEUSDT", "RSRUSDT", "SAGAUSDT", "SOLUSDT", "SUIUSDT", "SUPERUSDT", "SUSHIUSDT", "THETAUSDT", "TOKENUSDT", "TRBUSDT", "TURBOUSDT", "UXLINKUSDT", "WIFUSDT", "WLDUSDT", "XLMUSDT", "XRPUSDT", "XVGUSDT", "ZECUSDT", "ZENUSDT", "ZRXUSDT"], "to_be_delisted": [], "delisted": ["AMBUSDT", "STMXUSDT", "OMGUSDT", "REEFUSDT", "FTMUSDT", "UNFIUSDT", "XEMUSDT", "KLAYUSDT", "KEYUSDT", "LOOMUSDT", "BONDUSDT", "ORBSUSDT", "MAVIAUSDT", "BLZUSDT", "AKROUSDT", "WRXUSDT"], "host": "*************", "port": 3308, "user": "zh", "password": "zhP@55word", "database1_standard": "vnpy_crypto_agg", "host2": "localhost", "port2": 3306, "user2": "root", "password2": "**************your_password***************", "database2_needcheck": "**************your_databasename***************", "get_file_mail_subject": "main_Daily Kline Data", "email_username": "**************your_email_address**************", "email_password": "***************your_email_smtp_password**************", "to_addr": ["***************receiver_email_address**************"], "imap_port": 993, "imap_server": "imap.exmail.qq.com", "smtp_port": 465, "smtp_server": "smtp.exmail.qq.com", "tolerance_1": 0, "tolerance_2": 0, "proxy_port_for_klinedownload": 29290, "encoding": "gb2312"}