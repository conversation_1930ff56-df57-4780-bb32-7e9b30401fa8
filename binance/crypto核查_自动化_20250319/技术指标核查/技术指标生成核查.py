import pandas as pd
from datetime import datetime,timedelta
import numpy as np
from sqlalchemy import create_engine
import os
import warnings
warnings.filterwarnings("ignore")
import json
from urllib.parse import quote_plus as urlquote
path = os.path.dirname(__file__)
parent_path = os.path.dirname(path)

with open(os.path.join(parent_path,'config.json'), 'r') as file:
    config = json.load(file)

n_1 = 2
# start_date = datetime(2025,2,23).replace(hour=15, minute=0, second=0)
# end_date = datetime(2025,3,5).replace(hour=15, minute=0, second=0)
start_date = (datetime.now() - timedelta(days=n_1)).replace(hour=14, minute=59, second=59)
end_date = datetime.now().replace(hour=15, minute=0, second=0)

symbol_pool = config['symbol_pool']
delisted = config['delisted']

interval_list = ['3m','5m','15m','30m','60m','120m','240m','360m','480m','720m','1d']

symbol_pool = [a for a in symbol_pool if a not in delisted]

host = config['host']
port = config['port']
user = config['user']
password = config['password']

database1_standard = config['database1_standard']
engine1 = create_engine(f'mysql+pymysql://{user}:{urlquote(password)}@{host}:{port}/{database1_standard}')

host2 = config['host2']
port2 = config['port2']
user2 = config['user2']
password2 = config['password2']
database2_needcheck = config['database2_needcheck']
engine2 = create_engine(f'mysql+pymysql://{user2}:{urlquote(password2)}@{host2}:{port2}/{database2_needcheck}')
path_ = os.path.dirname(__file__)

tolerance_1 = config['tolerance_1']

def is_within_range_list(list1,list2,tolerance=0):
    for index in range(len(list1)):
        value1 = list1[index]
        value2 = list2[index]
        if type(value1) == str and type(value2) == str:
            return True
        elif (type(value1)  == str and f'{type(value2)}' != str) or (type(value1)  != str and type(value2) == str):
            return False
        
        if abs(value1 - value2) <= tolerance:
            continue
        else:
            return False
    return True

def fetch_data(engine, symbol, start_date,end_date, interval = '1d'):
    if interval == '1m':
        query = """
        SELECT datetime, symbol,`interval`, open_price, high_price, low_price, close_price, volume FROM dbbardata 
        WHERE symbol = %s AND datetime BETWEEN %s AND %s AND `interval` = %s
        ORDER BY datetime
        """
    else:
        # if engine == engine1:
        #     query = """
        #     SELECT datetime, symbol, `interval`, open_price, high_price, low_price, close_price, volume FROM apibardata 
        #     WHERE symbol = %s AND datetime BETWEEN %s AND %s AND `interval` = %s
        #     ORDER BY datetime
        #     """
        if engine == engine1:
            query = """
            SELECT datetime, symbol, `interval`, open_price, high_price, low_price, close_price, volume FROM aggregatedbardata 
            WHERE symbol = %s AND datetime BETWEEN %s AND %s AND `interval` = %s
            ORDER BY datetime
            """
        else:
            query = """
            SELECT datetime, symbol, `interval`, open_price, high_price, low_price, close_price, volume FROM aggregatedbardata 
            WHERE symbol = %s AND datetime BETWEEN %s AND %s AND `interval` = %s
            ORDER BY datetime
            """
    df = pd.read_sql(query, engine, params=(symbol, start_date,end_date,interval))

    df['datetime'] = pd.to_datetime(df['datetime'])
    df = df.iloc[:-1]
    df = df.iloc[1:]
    # df['datetime'] = df['datetime'].dt.date
    df.set_index('datetime',inplace = True)
    return df

def gen_ma(df,param):
    df['ma'] = df['close_price'].rolling(window=param).mean()
    return df

def my_stdev(x, boll_period):
    avg = x.mean()
    sum_squared_diff = ((x - avg) ** 2).sum()
    return (sum_squared_diff / boll_period) ** 0.5

def gen_boll(df,boll_period = 5,boll_band_width = 2):
    df['ma_boll'] = df['close_price'].rolling(window=boll_period).mean()
    df['std'] = df['close_price'].rolling(window=boll_period).apply(lambda x: my_stdev(x, boll_period), raw=False)
    df['boll_up'] = df['ma_boll'] + (boll_band_width * df['std'])
    df['boll_down'] = df['ma_boll'] - (boll_band_width * df['std'])
    return df

def gen_rsi(df, rsi_length=14, rsi_oblevel=70, rsi_oslevel=30):
    u = np.maximum(df['close_price'] - df['close_price'].shift(1), 0)
    d = np.maximum(df['close_price'].shift(1) - df['close_price'], 0)

    df['sumu'] = np.zeros(len(df['close_price']))
    df['sumd'] = np.zeros(len(df['close_price']))

    for i in range(1, len(df['close_price'])):
        df['sumu'].iloc[i] = (u.iloc[i] + (rsi_length-1) * df['sumu'].iloc[i-1]) / rsi_length
        df['sumd'].iloc[i] = (d.iloc[i] + (rsi_length-1) * df['sumd'].iloc[i-1]) / rsi_length

    rs = np.where(df['sumd'] != 0, df['sumu']/df['sumd'], 0)
    df['rsi'] = 100 - 100 / (1+rs)

    # 计算超买和超卖水平
    diffupob = (df['sumd'] * ((100 / (100 - rsi_oblevel)) - 1)) * rsi_length - (rsi_length - 1) * df['sumu'].shift(1).fillna(0)
    diffdnob = (df['sumu'] / ((100 / (100 - rsi_oblevel)) - 1)) * rsi_length - (rsi_length - 1) * df['sumd'].shift(1).fillna(0)
    diffupos = (df['sumd'] * ((100 / (100 - rsi_oslevel)) - 1)) * rsi_length - (rsi_length - 1) * df['sumu'].shift(1).fillna(0)
    diffdnos = (df['sumu'] / ((100 / (100 - rsi_oslevel)) - 1)) * rsi_length - (rsi_length - 1) * df['sumd'].shift(1).fillna(0)

    # 超买、超卖水平
    df['oblev'] = np.where(df['rsi'] <= rsi_oblevel, df['close_price'] + diffupob, df['close_price'] - diffdnob)
    df['oslev'] = np.where(df['rsi'] <= rsi_oslevel, df['close_price'] + diffupos, df['close_price'] - diffdnos)
    return df
def compare_df(df1,df2):
    time_check = []
    mark = []
    check_col = ['ma','boll_up', 'boll_down','oblev','oslev']

    for i in list(df1.index):
        if i not in list(df2.index):
            time_check.append(i)
            mark.append('lack')
    for j in list(df2.index):
        if j not in list(df1.index):
            time_check.append(j)
            mark.append('surplus')
    dfq = pd.DataFrame({'error':mark,'bar':time_check})
    if len(dfq)!=0:
        dfq['symbol'] = list(df1['symbol'])[0]
        dfq['interval'] = list(df1['interval'])[0]
        dfq = dfq[['symbol','interval','error','bar']]
        
    inconsistent = []
    for ind in df1.index:
        if ind not in df2.index:
            # print(ind)
            continue
        for col in check_col:
            indi1 = []
            indi2 =[]
            if np.isnan(df1.loc[ind,col]) or np.isnan(df2.loc[ind,col]):
                if np.isnan(df1.loc[ind,col]):
                    indi1.append('nan')
                    if np.isnan(df2.loc[ind,col]):
                       indi2.append('nan')
                    else:
                       indi2.append(df2.loc[ind,col])
                else:
                    indi2.append('nan')
                    if np.isnan(df1.loc[ind,col]):
                       indi1.append('nan')
                    else:
                       indi1.append(df1.loc[ind,col])
            else:
                indi1.append(df1.loc[ind,col])
                indi2.append(df2.loc[ind,col])
            if not is_within_range_list(indi1,indi2,tolerance=tolerance_1): 
                text = col+': '+str(indi1)+'!=' +str(indi2)
                inconsistent.append((ind,df1.loc[ind,'symbol'],df1.loc[ind,'interval'],text))
    df = pd.DataFrame(data= inconsistent,columns=['datetime','symbol','interval','error'])
    return dfq,df

def inspect_all():
    quantity = pd.DataFrame(data = np.nan, index=range(0), columns=['symbol','interval','error','bar'])
    quality = pd.DataFrame(data = np.nan, index=range(0), columns=['datetime','symbol','interval','error'])
    # for symbol in tqdm(symbol_pool,desc='核查进度'):
    for symbol in symbol_pool:
        for interval in interval_list:
            df1 = fetch_data(engine1, symbol, start_date,end_date, interval = interval)
            df1 = gen_ma(df1,param = 30)
            df1 = gen_boll(df1)
            df1 = gen_rsi(df1)

            df2 = fetch_data(engine2, symbol, start_date,end_date, interval = interval)
            df2 = gen_ma(df2,param = 30)
            df2 = gen_boll(df2)
            df2 = gen_rsi(df2)

            dfq,df = compare_df(df1,df2)
            quantity = pd.concat([quantity,dfq])
            quality = pd.concat([quality,df])
    if len(quantity) == 0:
        print('数量核对一致')
    else:
        path = os.path.join(path_, "quantity_check")
        if not os.path.exists(path):
            os.makedirs(path)
        quantity.index=pd.RangeIndex(start=1, stop = len(quantity)+1,step=1)
        quantity.to_csv(os.path.join(path, f"{str(start_date.date())}_{str(end_date.date())}技术指标数量核查.csv"), encoding='utf-8')
        print('数量核对不一致')
    if len(quality) == 0:
        print('质量核对一致')
    else:
        path = os.path.join(path_, "quality_check")
        if not os.path.exists(path):
            os.makedirs(path)
        quality.index=pd.RangeIndex(start=1, stop = len(quality)+1,step=1)
        quality.to_csv(os.path.join(path, f"{str(start_date.date())}_{str(end_date.date())}技术指标质量核查.csv"), encoding='utf-8')
        print('质量核对不一致')
if __name__ == "__main__":
    inspect_all()
