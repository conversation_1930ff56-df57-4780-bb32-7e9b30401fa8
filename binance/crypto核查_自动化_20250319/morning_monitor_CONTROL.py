import subprocess
import sys
import logging
import os 

import json

path = os.path.dirname(__file__)
with open(os.path.join(path,'config.json'), 'r') as file:
    config = json.load(file)

encode = config["encoding"]

path = os.path.dirname(__file__)
def run_script(script_name):
    """通用执行函数，返回执行结果"""
    try:
        result = subprocess.run(
            [sys.executable, script_name],
            check=True,
            capture_output=True,
            text=True,
            encoding=encode
        )
        logging.info(f"{script_name} 执行成功\n输出：{result.stdout}")
        return True
    except subprocess.CalledProcessError as e:
        logging.error(f"{script_name} 执行失败（代码 {e.returncode}）\n错误信息：{e.stderr}")
        return False
    except Exception as e:
        logging.critical(f"执行{script_name}时发生意外错误：{str(e)}")
        return False

if __name__ == "__main__":
    # 配置日志记录
    logging.basicConfig(
        level=logging.INFO,
        encoding='utf-8',
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('morning_monitor_CONTROL.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    
    # 执行流程
    scripts = [os.path.join(path,'合成日线核查','receive_kline_email.py') ,os.path.join(path,'合成日线核查','合成日线核查.py')]
    
    for script in scripts:
        logging.info(f"开始执行 {script}")
        success = run_script(script)
        status = "成功" if success else "完成（有错误）"
        logging.info(f"{script} 执行{status}")
    
    logging.info("所有流程执行完毕，请查看morning_monitor_CONTROL.log获取详细信息")
