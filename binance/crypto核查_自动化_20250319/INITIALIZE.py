import pandas as pd
from datetime import datetime,timedelta
import numpy as np
from sqlalchemy import create_engine
import os
from tqdm import tqdm
import warnings
import json
from urllib.parse import quote_plus as urlquote
from typing import List, Dict

path = os.path.dirname(__file__)

with open(os.path.join(path,'config.json'), 'r',encoding='utf-8') as file:
    config = json.load(file)


warnings.filterwarnings("ignore")
n_1 = 3
# start_date = datetime(2025,3,17).replace(hour=17, minute=5, second=0)
# end_date = datetime(2025,3,17).replace(hour=17, minute=19, second=0)
start_date = (datetime.now() - timedelta(days=n_1)).replace(hour=15, minute=0, second=0)
end_date = (datetime.now() - timedelta(days=1)).replace(hour=15, minute=0, second=0)

symbol_pool = config['symbol_pool']
delisted = config['delisted']

interval_list = ['1m']
# interval_list = ['1m','2m','3m','5m','6m','8m','10m','12m','15m','20m','23m','30m','45m','60m','90m','120m','180m','240m','360m','480m','720m','1d','2d','3d']
symbol_pool = [a for a in symbol_pool if a not in delisted]
# symbol_pool = ['DUSKUSDT']
host = config['host']
port = config['port']
user = config['user']
password = config['password']
database1_standard = config['database1_standard']
engine1 = create_engine(f'mysql+pymysql://{user}:{urlquote(password)}@{host}:{port}/{database1_standard}')

def fetch_data(engine, symbol, start_date,end_date, interval = '1d'):
    if interval == '1m':
        query = """
        SELECT datetime, symbol, exchange, `interval`, open_price, high_price, low_price, close_price, volume, turnover FROM dbbardata 
        WHERE symbol = %s AND datetime BETWEEN %s AND %s AND `interval` = %s
        ORDER BY datetime
        """
    else:
        if engine == engine1:
            query = """
            SELECT datetime, symbol, exchange, `interval`, open_price, high_price, low_price, close_price, volume, turnover FROM aggregatedbardata 
            WHERE symbol = %s AND datetime BETWEEN %s AND %s AND `interval` = %s
            ORDER BY datetime
            """
        else:
            query = """
            SELECT datetime, symbol, exchange, `interval`, open_price, high_price, low_price, close_price, volume, turnover FROM aggregatedbardata 
            WHERE symbol = %s AND datetime BETWEEN %s AND %s AND `interval` = %s
            ORDER BY datetime
            """
    df = pd.read_sql(query, engine, params=(symbol, start_date,end_date,interval))
    return df

import os

vt_setting_file_path = os.path.join(path,'.vntrader', 'vt_setting.json')
with open(vt_setting_file_path, 'r', encoding='utf-8') as vt_setting_file:
    vt_setting_data = json.load(vt_setting_file)
vt_setting_data['database.database'] = config['database2_needcheck']
vt_setting_data['database.host'] = config['host2']
vt_setting_data['database.port'] = config['port2']
vt_setting_data['database.user'] = config['user2']
vt_setting_data['database.password'] = config['password2']
with open(vt_setting_file_path, 'w', encoding='utf-8') as vt_setting_file:
    json.dump(vt_setting_data, vt_setting_file, indent=4)

from peewee import *
from vnpy_mysql.mysql_database import DB_TZ, db

class dbBarData(Model):
    symbol = CharField()
    exchange = CharField()
    interval = CharField()
    datetime = DateTimeField()
    volume = DoubleField()
    turnover = DoubleField()
    open_interest = DoubleField()
    open_price = DoubleField()
    high_price = DoubleField()
    low_price = DoubleField()
    close_price = DoubleField()

    class Meta:
        database = db
        indexes = (
            (('symbol', 'exchange', 'interval', 'datetime'), True),
        )

class dbBarOverview(Model):
    symbol = CharField()
    exchange = CharField()
    interval = CharField()
    count = IntegerField()
    start = DateTimeField()
    end = DateTimeField()

    class Meta:
        database = db
        indexes = (
            (('symbol', 'exchange', 'interval'), True),
        )

db.create_tables([dbBarData, dbBarOverview], safe=True)

def save_to_mysql(df:pd.DataFrame):

    for symbol in df['symbol'].unique():
        exchange ='BINANCE'
        interval = '1m'
        rows: List[Dict] = []
        df_sub = df[df['symbol'] == symbol]
        for index,row in df_sub.iterrows():
            rows.append({
                    'symbol':row['symbol'],
                    'exchange':exchange,
                    'datetime':row['datetime'],
                    'interval':interval,
                    'volume':float(row['volume']),
                    'turnover':float(row['turnover']),
                    'open_price':float(row['open_price']),
                    'high_price':float(row['high_price']),
                    'low_price':float(row['low_price']),
                    'close_price':float(row['close_price']),
                })
            if isinstance(rows[-1]["datetime"], str):
                rows[-1]["datetime"] = datetime.strptime(rows[-1]["datetime"], '%Y-%m-%d %H:%M:%S')

        with db.atomic():
            for batch in rows:
                dbBarData.insert_many(batch).on_conflict_replace().execute()

        overview: dbBarOverview
        created: bool
        overview, created = dbBarOverview.get_or_create(
            symbol=symbol,
            exchange=exchange,
            interval=interval
        )
        aggregated_info = (dbBarData
            .select(
                fn.COUNT(dbBarData.id).alias('count'),
                fn.MIN(dbBarData.datetime).alias('start'),
                fn.MAX(dbBarData.datetime).alias('end')
            )
            .where(
                (dbBarData.symbol == symbol) &
                (dbBarData.exchange == exchange) &
                (dbBarData.interval == interval)
            )
            .get())

        overview.count = aggregated_info.count
        overview.start = aggregated_info.start
        overview.end = aggregated_info.end
        overview.save() 

for symbol in tqdm(symbol_pool,desc='覆盖进度'):
    for interval in interval_list:
        df = fetch_data(engine1, symbol, start_date,end_date, interval = interval)
        save_to_mysql(df)

