#%%
import pandas as pd
from datetime import datetime,timedelta,time
import numpy as np
from sqlalchemy import create_engine
import os
# from tqdm import tqdm
import warnings
warnings.filterwarnings("ignore")
import json
from urllib.parse import quote_plus as urlquote
import re
import email
import smtplib
import email.utils
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.application import MIMEApplication
import sys


path = os.path.dirname(__file__)
parent_path = os.path.dirname(path)
with open(os.path.join(parent_path,'config.json'), 'r') as file:
    config = json.load(file)

username: str = config["email_username"]
password: str = config["email_password"]

to_addr = config["to_addr"]

smtp_server = config["smtp_server"]
smtp_port: int = config["smtp_port"]

def send_email(subject:str, body:str, to_addr:list, file_list:list=[]):
    smtp_obj = smtplib.SMTP_SSL(smtp_server, smtp_port) 
    smtp_obj.login("<EMAIL>", "3vWUP4WFNDxVksNC")
    smtp_obj.set_debuglevel(True)

    message = MIMEMultipart()
    message["From"] = email.utils.formataddr(('Data_BOT', username))
    # message["To"] = email.utils.formataddr(('Hua', username))
    message["Subject"] = subject
    if type(body) == list:
        z = "\n".join(body)
        message.attach(MIMEText(z))
    else:
        message.attach(MIMEText(body))
    if len(file_list) != 0:
        for file_path in file_list:
            with open(file_path, 'rb') as f:
                attachment = MIMEApplication(f.read(), _subtype='csv')
                file_name = file_path.split('/')[-1] if '/' in file_path else file_path.split('\\')[-1]
                attachment.add_header('Content-Disposition', 'attachment', filename=file_name)
                message.attach(attachment)
    try:
        smtp_obj.sendmail(username, to_addr, msg=message.as_string())
    except smtplib.SMTPServerDisconnected:
        print("Connection unexpectedly closed. Retrying...")
        time.sleep(5) 
        send_email(subject,body,to_addr,file_list)
    finally:
        smtp_obj.quit()

# start_date = datetime(2025,2,20).replace(hour=7, minute=0, second=0)
# end_date = datetime(2025,2,20).replace(hour=9, minute=0, second=0)
# today = datetime(2025,2,21).replace(hour=15, minute=0, second=0)
start_date = (datetime.now() - timedelta(days=1)).replace(hour=7, minute=0, second=0)
end_date = (datetime.now() - timedelta(days=1)).replace(hour=9, minute=0, second=0)
today = datetime.now().replace(hour=15, minute=0, second=0)

path = os.path.dirname(__file__)
parent_path = os.path.dirname(path)

if not os.path.exists(os.path.join(path,'实盘数据',f'main_{today.strftime("%Y%m%d")}_dailykline_check.txt')):
    send_email(subject = '未收到合成日线文件',body = '未收到合成日线文件',to_addr = to_addr,file_list = [])
    sys.exit()
else:
    with open(os.path.join(path,'实盘数据',f'main_{today.strftime("%Y%m%d")}_dailykline_check.txt'), 'r') as f:
        lines = f.readlines()

pattern = r'(\w+)\.BINANCE'
symbols = set()
for line in lines:
    match = re.search(pattern, line)
    if match:
        symbols.add(match.group(1))

symbol_list = sorted(list(symbols))

with open(os.path.join(parent_path,'config.json'), 'r', encoding='utf-8') as f:
    config = json.load(f)

config['symbol_pool'] = symbol_list

with open(os.path.join(parent_path,'config.json'), 'w', encoding='utf-8') as f:
    json.dump(config, f, indent=4, ensure_ascii=False)

with open(os.path.join(parent_path,'config.json'), 'r') as file:
    config = json.load(file)


import pandas as pd
import re

try:
    with open(os.path.join(path, '实盘数据', f'main_{today.strftime("%Y%m%d")}_dailykline_check.txt'), 
             'r', encoding='utf-8') as f:
        lines = f.readlines()
except UnicodeDecodeError:
    try:
        with open(os.path.join(path, '实盘数据', f'main_{today.strftime("%Y%m%d")}_dailykline_check.txt'),
                 'r', encoding='gb18030') as f:
            lines = f.readlines()
    except Exception as e:
        raise ValueError(f"文件解码失败: {str(e)}")
data = []
# 优化后的正则表达式
pattern = re.compile(
    r'\s+([A-Za-z0-9]+)\.BINANCE[^:]+:\s+(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}).*?'  # 匹配合约名和时间
    r'o:([\d.]+)\s+h:([\d.]+)\s+l:([\d.]+)\s+c:([\d.]+)\s+v:([\d.]+)'
)

for line in lines:
    try:
        match = pattern.search(line)
        if not match:
            print(f"模式不匹配: {line.strip()}")
            continue
            
        # 提取合约名称（第一个捕获组）
        symbol = match.group(1)
        
        # 提取价格数据（调整分组索引）
        dt = pd.to_datetime(match.group(2))
        o = float(match.group(3))
        h = float(match.group(4))
        l = float(match.group(5))
        c = float(match.group(6))
        v = float(match.group(7))
        
        data.append({
            'symbol': symbol,
            'datetime': dt.strftime('%Y-%m-%d %H:%M:%S'),
            'interval': '1d',
            'open_interest': 0.0,
            'open_price': o,
            'high_price': h,
            'low_price': l,
            'close_price': c,
            'volume': round(v,1),
            'turnover': 0.0
        })
        
    except Exception as e:
        print(f"解析失败行: {line.strip()}")
        print(f"错误详情: {str(e)}")
        continue

need_check = pd.DataFrame(data)
need_check['datetime'] = pd.to_datetime(need_check['datetime'])
need_check.set_index('datetime',inplace = True)
need_check.drop_duplicates(inplace = True  )

error_msgs = []
time_pattern = re.compile(r'(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d+\+\d{4})')
symbol_pattern = re.compile(r'\s+([A-Za-z0-9]+)\.BINANCE')

for line in lines:
    try:
        # 提取时间戳
        time_match = time_pattern.search(line)
        if not time_match:
            continue
            
        # 解析时间（修正时区格式）
        raw_time = time_match.group(1).replace('+0800', '+08:00')
        dt = datetime.fromisoformat(raw_time)
        
        # 创建时间比较对象
        threshold = time(8, 1, 0)  # 使用datetime.time类
        if dt.time() >= threshold:
            # 提取合约名称
            symbol_match = symbol_pattern.search(line)
            if symbol_match:
                symbol = symbol_match.group(1)
                error_msgs.append(f"{symbol}日线的合成时间不一致，为{raw_time}")
                
    except Exception as e:
        print(f"处理行时出错: {line.strip()}")
        print(f"错误详情: {str(e)}")
if len(error_msgs) != 0:
    error_content = '\n\n'.join(error_msgs)
else:
    error_content = '合成日线时间一致'

symbol_pool = config['symbol_pool']
delisted = config['delisted']

interval_list = ['1d']

symbol_pool = [a for a in symbol_pool if a not in delisted]

host = config['host']
port = config['port']
user = config['user']
password = config['password']

database1_standard = config['database1_standard']
engine1 = create_engine(f'mysql+pymysql://{user}:{urlquote(password)}@{host}:{port}/{database1_standard}')

tolerance_1 = config['tolerance_1']
tolerance_2 = config['tolerance_2']
path_ = os.path.dirname(__file__)
#%%
def is_within_range_list(list1,list2,tolerance=0):
    for index in range(len(list1)):
        value1 = list1[index]
        value2 = list2[index]
        if type(value1) == str and type(value2) == str:
            return True
        elif (type(value1)  == str and f'{type(value2)}' != str) or (type(value1)  != str and type(value2) == str):
            return False
        
        if abs(value1 - value2) <= tolerance:
            continue
        else:
            return False
    return True

def fetch_data(engine, symbol, start_date,end_date, interval = '1d'):
    query = """
    SELECT datetime, symbol, `interval`, open_price, high_price, low_price, close_price, volume, turnover FROM apibardata 
    WHERE symbol = %s AND datetime BETWEEN %s AND %s AND `interval` = %s
    ORDER BY datetime
            """

    df = pd.read_sql(query, engine, params=(symbol, start_date,end_date,interval))

    df['datetime'] = pd.to_datetime(df['datetime'])
    df['volume'] = round(df['volume'],1)
    df.set_index('datetime',inplace = True)
    return df

def compare_df(df1,df2):
    check_col = ['open_price','high_price', 'low_price', 'close_price']
    check_col2 = ['volume'] #如果不需要成交量成交额，直接空list就好[]
    time_check = []
    mark = []
    #保证df2.loc[ind,col]不是series
    for index_value, group in df2.groupby(df2.index):
        if len(group) > 1:  # 如果有重复索引
            time_check.append(index_value)
            mark.append('相同的日线，有不同数据')
            df2 = df2[~df2.index.duplicated(keep='first')]

    for j in list(df2.index):
        if j not in list(df1.index):
            time_check.append(j)
            mark.append('surplus')


    for j in list(df1.index):
        if j not in list(df2.index):
            time_check.append(j)
            mark.append('lack')


    dfq = pd.DataFrame({'error':mark,'bar':time_check})
    if len(dfq)!=0:
        # print(list(df2['symbol'])[0])
        dfq['symbol'] = list(df1['symbol'])[0]
        dfq['interval'] = list(df1['interval'])[0]
        dfq = dfq[['symbol','interval','error','bar']]
        
    inconsistent = []
    
    for ind in df1.index:
        if ind not in df2.index:
            continue
        indi1 = []
        indi2 =[]
        indi_v1 = []
        indi_v2 =[]
        indi_v1_str = []
        indi_v2_str =[]
        for col in check_col:
            if np.isnan(df1.loc[ind,col]) or np.isnan(df2.loc[ind,col]):
                if np.isnan(df1.loc[ind,col]):
                   indi1.append('nan')
                else:
                    indi2.append('nan')
            else:
                indi1.append(df1.loc[ind,col])
                indi2.append(df2.loc[ind,col])
        for col in check_col2:
            if np.isnan(df1.loc[ind,col]) or np.isnan(df2.loc[ind,col]):
                if np.isnan(df1.loc[ind,col]):
                   indi_v1.append('nan')
                else:
                    indi_v2.append('nan')
            else:
                indi_v1.append(df1.loc[ind,col])
                indi_v2.append(df2.loc[ind,col])
                indi_v1_str.append(col+': '+str(df1.loc[ind,col]))
                indi_v2_str.append(col+': '+str(df2.loc[ind,col]))
        if not is_within_range_list(indi1,indi2,tolerance=tolerance_1): 
            text = str(indi1)+'!=' +str(indi2)
            inconsistent.append((ind,df1.loc[ind,'symbol'],df1.loc[ind,'interval'],text))

        if not is_within_range_list(indi_v1,indi_v2,tolerance=tolerance_2):
            text = str(indi_v1_str)+'!=' +str(indi_v2_str)
            inconsistent.append((ind,df1.loc[ind,'symbol'],df1.loc[ind,'interval'],text))

    df = pd.DataFrame(data= inconsistent,columns=['datetime','symbol','interval','error'])
    return dfq,df

def inspect_all(check=need_check):
    quantity = pd.DataFrame(data = np.nan, index=range(0), columns=['symbol','interval','error','bar'])
    quality = pd.DataFrame(data = np.nan, index=range(0), columns=['datetime','symbol','interval','error'])

    # for symbol in tqdm(symbol_pool,desc='核查进度'):
    for symbol in symbol_pool:
        for interval in interval_list:
            df1 = fetch_data(engine1, symbol, start_date,end_date, interval = interval)
            df2 = check[check['symbol']==symbol]
            dfq,df = compare_df(df1,df2)
            quantity = pd.concat([quantity,dfq])
            quality = pd.concat([quality,df])

    if len(quantity) == 0:
        print('数量核对一致')
    else:
        path = os.path.join(path_, "quantity_check")
        if not os.path.exists(path):
            os.makedirs(path)
        quantity.index=pd.RangeIndex(start=1, stop = len(quantity)+1,step=1)
        quantity.to_csv(os.path.join(path, f"{str(today.date())}合成日线数量核查.csv"), encoding='utf-8')
        print('数量核对不一致')
    if len(quality) == 0:
        print('质量核对一致')
    else:
        path = os.path.join(path_, "quality_check")
        if not os.path.exists(path):
            os.makedirs(path)
        quality.index=pd.RangeIndex(start=1, stop = len(quality)+1,step=1)
        quality.to_csv(os.path.join(path, f"{str(today.date())}合成日线质量核查.csv"), encoding='utf-8')
        print('质量核对不一致')

if __name__ == "__main__":
    inspect_all()
    file_list = []
        
    if not os.path.exists(os.path.join(path, "quantity_check",f"{str(today.date())}合成日线数量核查.csv")):
        error_content+='\n\n合成日线数量核查一致'
    else:
        file_list.append(os.path.join(path, "quantity_check", f"{str(today.date())}合成日线数量核查.csv"))
        error_content+='\n\n合成日线数量核查不一致'
    
    if not os.path.exists(os.path.join(path, "quality_check", f"{str(today.date())}合成日线质量核查.csv")):
        error_content+='\n\n合成日线质量核查一致'
    else:
        file_list.append(os.path.join(path, "quality_check", f"{str(today.date())}合成日线质量核查.csv"))
        error_content+='\n\n合成日线质量核查不一致'

    send_email(subject = 'Crypto_morning_monitor',body = error_content,to_addr = to_addr,file_list = file_list)

# %%
