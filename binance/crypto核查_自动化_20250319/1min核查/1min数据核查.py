import pandas as pd
from datetime import datetime,timedelta
import numpy as np
from sqlalchemy import create_engine
import os
# from tqdm import tqdm
import warnings
warnings.filterwarnings("ignore")
import json
from urllib.parse import quote_plus as urlquote

path = os.path.dirname(__file__)
parent_path = os.path.dirname(path)

with open(os.path.join(parent_path,'config.json'), 'r') as file:
    config = json.load(file)

n_1 = 1
# start_date = datetime(2025,3,9).replace(hour=15, minute=0, second=0)
# end_date = datetime(2025,3,10).replace(hour=15, minute=0, second=0)
start_date = (datetime.now() - timedelta(days=n_1)).replace(hour=14, minute=59, second=59)
end_date = datetime.now().replace(hour=15, minute=0, second=0)

symbol_pool = config['symbol_pool']
delisted = config['delisted']

interval_list = ['1m']

symbol_pool = [a for a in symbol_pool if a not in delisted]
################## 看各自市场情况和收到文件情况需做改动################################
yesterday = (datetime.today().date() -timedelta(days=1)).strftime("%Y%m%d")

path = os.path.dirname(__file__)
folder_path = os.path.join(path, '实盘数据')
os.makedirs(folder_path,exist_ok=True)
csv_path = None
for root, dirs, files in os.walk(folder_path):
    for file in files:
        if file.startswith(f"kline_data_{yesterday}"):  
            csv_path = os.path.join(root, file)
if csv_path == None:
    print('数据文件不一致，未收到当日数据')

else:
    data = pd.read_csv(csv_path)
    data_s = list(data['symbol'].unique())
    for i in data_s:
        if i not in symbol_pool:
            symbol_pool.append(i)
######################################################################################
host = config['host']
port = config['port']
user = config['user']
password = config['password']

database1_standard = config['database1_standard']
engine1 = create_engine(f'mysql+pymysql://{user}:{urlquote(password)}@{host}:{port}/{database1_standard}')

host2 = config['host2']
port2 = config['port2']
user2 = config['user2']
password2 = config['password2']
database2_needcheck = config['database2_needcheck']
engine2 = create_engine(f'mysql+pymysql://{user2}:{urlquote(password2)}@{host2}:{port2}/{database2_needcheck}')

tolerance_1 = config['tolerance_1']
tolerance_2 = config['tolerance_2']
path_ = os.path.dirname(__file__)

def is_within_range_list(list1,list2,tolerance=0):
    for index in range(len(list1)):
        value1 = list1[index]
        value2 = list2[index]
        if type(value1) == str and type(value2) == str:
            return True
        elif (type(value1)  == str and f'{type(value2)}' != str) or (type(value1)  != str and type(value2) == str):
            return False
        
        if abs(value1 - value2) <= tolerance:
            continue
        else:
            return False
    return True

def fetch_data(engine, symbol, start_date,end_date, interval = '1d'):
    if interval == '1m':
        query = """
        SELECT datetime, symbol,`interval`, open_price, high_price, low_price, close_price, volume, turnover FROM dbbardata 
        WHERE symbol = %s AND datetime BETWEEN %s AND %s AND `interval` = %s
        ORDER BY datetime
        """
    else:
        if engine == engine1:
            query = """
            SELECT datetime, symbol, `interval`, open_price, high_price, low_price, close_price, volume, turnover FROM apibardata 
            WHERE symbol = %s AND datetime BETWEEN %s AND %s AND `interval` = %s
            ORDER BY datetime
            """
        else:
            query = """
            SELECT datetime, symbol, `interval`, open_price, high_price, low_price, close_price, volume, turnover FROM aggregatedbardata 
            WHERE symbol = %s AND datetime BETWEEN %s AND %s AND `interval` = %s
            ORDER BY datetime
            """
    df = pd.read_sql(query, engine, params=(symbol, start_date,end_date,interval))

    df['datetime'] = pd.to_datetime(df['datetime'])
    df['hour'] = df['datetime'].dt.strftime('%Y-%m-%d ') + df['datetime'].dt.hour.astype(str) +':00'
    df.set_index('datetime',inplace = True)
    return df

def compare_df(df1,df2):
    hours = []
    hour_check = []
    mark = []
    check_col = ['open_price','high_price', 'low_price', 'close_price']
    check_col2 = ['volume','turnover'] #如果不需要成交量成交额，直接空list就好[]
    total_hours = list(set(list(df1['hour'].unique())+list(df2['hour'].unique()))) #两个表中hours的并集去重
    total_hours = sorted(total_hours, key=lambda x: datetime.strptime(x, '%Y-%m-%d %H:%M'))
    for hour in total_hours:
        if hour in list(df1['hour']) and hour not in list(df2['hour']): #standard表包含但是needcheck表不包含的hour
            hourdf1 = df1[df1['hour']==hour]
            time_check = list(hourdf1.index)
            time_check = ','.join(map(str,time_check))
            if time_check !='':
                hour_check.append(time_check)
                mark.append('lack')
                hours.append(hour)   
        elif hour in list(df2['hour']) and hour not in list(df1['hour']): #needcheck表包含但是standard表不包含的hour
            hourdf2 = df2[df2['hour']==hour]
            time_check = list(hourdf2.index)
            time_check = ','.join(map(str,time_check))
            if time_check !='':
                hour_check.append(time_check)
                mark.append('surplus')
                hours.append(hour)   
        else:   #hour属于两表交集
            hourdf1 = df1[df1['hour']==hour]
            hourdf2 = df2[df2['hour']==hour]
            time_check = []
            for i in list(hourdf1.index):
                if i not in list(hourdf2.index):
                    time_check.append(i)
            time_check = ','.join(map(str,time_check))
            if time_check !='':
                hour_check.append(time_check)
                mark.append('lack')
                hours.append(hour)

            time_check = []
            for j in list(hourdf2.index):
                if j not in list(hourdf1.index):
                    time_check.append(j)
            time_check = ','.join(map(str,time_check))
            if time_check !='':
                hour_check.append(time_check)
                mark.append('surplus')
                hours.append(hour)

    dfq = pd.DataFrame({'hours':hours,'error':mark,'bar':hour_check})
    if len(dfq)!=0:
        dfq['symbol'] = list(df1['symbol'])[0]
        dfq['interval'] = list(df1['interval'])[0]
        dfq['count'] = dfq['bar'].apply(lambda x: int(len(x.split(','))))
        dfq = dfq[['symbol','interval','hours','error','count','bar']]
        
    inconsistent = []
    for ind in df1.index:
        if ind not in df2.index:
            continue
        indi1 = []
        indi2 =[]
        indi_v1 = []
        indi_v2 =[]
        indi_v1_str = []
        indi_v2_str =[]
        for col in check_col:
            if np.isnan(df1.loc[ind,col]) or np.isnan(df2.loc[ind,col]):
                if np.isnan(df1.loc[ind,col]):
                   indi1.append('nan')
                else:
                    indi2.append('nan')
            else:
                indi1.append(df1.loc[ind,col])
                indi2.append(df2.loc[ind,col])
        for col in check_col2:
            if np.isnan(df1.loc[ind,col]) or np.isnan(df2.loc[ind,col]):
                if np.isnan(df1.loc[ind,col]):
                   indi_v1.append('nan')
                else:
                    indi_v2.append('nan')
            else:
                indi_v1.append(df1.loc[ind,col])
                indi_v2.append(df2.loc[ind,col])
                indi_v1_str.append(col+': '+str(df1.loc[ind,col]))
                indi_v2_str.append(col+': '+str(df2.loc[ind,col]))
        if not is_within_range_list(indi1,indi2,tolerance=tolerance_1): 
            text = str(indi1)+'!=' +str(indi2)
            inconsistent.append((ind,df1.loc[ind,'symbol'],df1.loc[ind,'interval'],text))
        if not is_within_range_list(indi_v1,indi_v2,tolerance=tolerance_2):
            text = str(indi_v1_str)+'!=' +str(indi_v2_str)
            inconsistent.append((ind,df1.loc[ind,'symbol'],df1.loc[ind,'interval'],text))
    df = pd.DataFrame(data= inconsistent,columns=['datetime','symbol','interval','error'])
    return dfq,df

def inspect_all():
    quantity = pd.DataFrame(data = np.nan, index=range(0), columns=['symbol','interval','hours','error','count','bar'])
    quality = pd.DataFrame(data = np.nan, index=range(0), columns=['datetime','symbol','interval','error'])

    # for symbol in tqdm(symbol_pool,desc='核查进度'):
    for symbol in symbol_pool:
        for interval in interval_list:
            df1 = fetch_data(engine1, symbol, start_date,end_date, interval = interval)
            df2 = fetch_data(engine2, symbol, start_date,end_date, interval = interval)
            dfq,df = compare_df(df1,df2)
            quantity = pd.concat([quantity,dfq])
            quality = pd.concat([quality,df])
    if len(quantity) == 0:
        print('数量核对一致')
    else:
        path = os.path.join(path_, "quantity_check")
        if not os.path.exists(path):
            os.makedirs(path)
        quantity.index=pd.RangeIndex(start=1, stop = len(quantity)+1,step=1)
        quantity.to_csv(os.path.join(path, f"{str(start_date.date())}_{str(end_date.date())}数据数量核查.csv"), encoding='utf-8')
        print('数量核对不一致')
    if len(quality) == 0:
        print('质量核对一致')
    else:
        path = os.path.join(path_, "quality_check")
        if not os.path.exists(path):
            os.makedirs(path)
        quality.index=pd.RangeIndex(start=1, stop = len(quality)+1,step=1)
        quality.to_csv(os.path.join(path, f"{str(start_date.date())}_{str(end_date.date())}数据质量核查.csv"), encoding='utf-8')
        print('质量核对不一致')

if __name__ == "__main__":
    inspect_all()
#%%