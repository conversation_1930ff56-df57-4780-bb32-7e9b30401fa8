#%%

import os
import json

path = os.path.dirname(__file__)
parent_path = os.path.dirname(path)
with open(os.path.join(parent_path,'config.json'), 'r',encoding='utf-8') as file:
    config = json.load(file)
vt_setting_file_path = os.path.join(parent_path,'.vntrader', 'vt_setting.json')
with open(vt_setting_file_path, 'r', encoding='utf-8') as vt_setting_file:
    vt_setting_data = json.load(vt_setting_file)
vt_setting_data['database.database'] = config['database2_needcheck']
vt_setting_data['database.host'] = config['host2']
vt_setting_data['database.port'] = config['port2']
vt_setting_data['database.user'] = config['user2']
vt_setting_data['database.password'] = config['password2']
with open(vt_setting_file_path, 'w', encoding='utf-8') as vt_setting_file:
    json.dump(vt_setting_data, vt_setting_file, indent=4)
from datetime import datetime,timedelta
import time
from typing import List, Dict
import pandas as pd
from datetime import datetime

date = datetime.today().date().strftime("%Y%m%d")
yesterday = (datetime.today().date() -timedelta(days=1)).strftime("%Y%m%d")
# date = datetime(2025,1,20).date().strftime("%Y%m%d")
# yesterday = datetime(2025,1,19).date().strftime("%Y%m%d")

path = os.path.dirname(__file__)
folder_path = os.path.join(path, '实盘数据')
os.makedirs(folder_path,exist_ok=True)
for root, dirs, files in os.walk(folder_path):
    for file in files:
        if file.startswith(f"kline_data_{yesterday}"):  
            csv_path = os.path.join(root, file)


from peewee import *
from vnpy_mysql.mysql_database import DB_TZ, db

class dbBarData(Model):
    symbol = CharField()
    exchange = CharField()
    interval = CharField()
    datetime = DateTimeField()
    volume = DoubleField()
    turnover = DoubleField()
    open_interest = DoubleField()
    open_price = DoubleField()
    high_price = DoubleField()
    low_price = DoubleField()
    close_price = DoubleField()

    class Meta:
        database = db
        indexes = (
            (('symbol', 'exchange', 'interval', 'datetime'), True),
        )

class dbBarOverview(Model):
    symbol = CharField()
    exchange = CharField()
    interval = CharField()
    count = IntegerField()
    start = DateTimeField()
    end = DateTimeField()

    class Meta:
        database = db
        indexes = (
            (('symbol', 'exchange', 'interval'), True),
        )

db.create_tables([dbBarData, dbBarOverview], safe=True)

def save_to_mysql(df:pd.DataFrame):

    for symbol in df['symbol'].unique():
        exchange ='BINANCE'
        interval = '1m'
        rows: List[Dict] = []
        df_sub = df[df['symbol'] == symbol]
        for index,row in df_sub.iterrows():
            rows.append({
                    'symbol':row['symbol'],
                    'exchange':exchange,
                    'datetime':row['datetime'],
                    'interval':interval,
                    'volume':float(row['volume']),
                    'turnover':float(row['turnover']),
                    'open_price':float(row['open_price']),
                    'high_price':float(row['high_price']),
                    'low_price':float(row['low_price']),
                    'close_price':float(row['close_price']),
                })
            if isinstance(rows[-1]["datetime"], str):
                rows[-1]["datetime"] = datetime.strptime(rows[-1]["datetime"], '%Y-%m-%d %H:%M:%S')

        with db.atomic():
            for batch in rows:
                dbBarData.insert_many(batch).on_conflict_replace().execute()

        overview: dbBarOverview
        created: bool
        overview, created = dbBarOverview.get_or_create(
            symbol=symbol,
            exchange=exchange,
            interval=interval
        )
        aggregated_info = (dbBarData
            .select(
                fn.COUNT(dbBarData.id).alias('count'),
                fn.MIN(dbBarData.datetime).alias('start'),
                fn.MAX(dbBarData.datetime).alias('end')
            )
            .where(
                (dbBarData.symbol == symbol) &
                (dbBarData.exchange == exchange) &
                (dbBarData.interval == interval)
            )
            .get())

        overview.count = aggregated_info.count
        overview.start = aggregated_info.start
        overview.end = aggregated_info.end
        overview.save() 
def main():

    df = pd.read_csv(csv_path)
    save_to_mysql(df)

if __name__ == '__main__':
    try:
        import time

        print_date = time.strftime("%Y-%m-%d %H:%M:%S")
        main()
    except Exception as e:
        print(e)

