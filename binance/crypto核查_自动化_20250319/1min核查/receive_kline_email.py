import email
from email.message import Message
import imaplib
import os
from datetime import datetime, timedelta
from email.header import decode_header
from typing import Tuple, List, Optional, Any
import typer
import zipfile
import email.utils
import json

path = os.path.dirname(__file__)
parent_path = os.path.dirname(path)

with open(os.path.join(parent_path,'config.json'), 'r') as file:
    config = json.load(file)

username: str = config["email_username"]
password: str = config["email_password"]

server: str = config["imap_server"]
imap_port: int = config["imap_port"]

path = os.path.dirname(__file__)
date = datetime.today().date().strftime("%Y%m%d")
yesterday = (datetime.today().date() -timedelta(days=1)).strftime("%Y%m%d")
# date = (datetime.today().date() -timedelta(days=1)).strftime("%Y%m%d")
# yesterday = (datetime.today().date() -timedelta(days=2)).strftime("%Y%m%d")

mail_subject = config["get_file_mail_subject"]+' '+(datetime.today().date()).strftime("%Y-%m-%d")

def decode_str(s: str) -> str:
    """
    解码邮件主题或文件名
    邮件标题和附件名可能包含非ASCII字符，需要根据指定的编码方式解码
    decode_header返回一个list，每个元素是一个tuple(bytes/str, charset)
    """
    decoded_bytes, charset = decode_header(s)[0] # # type: Tuple[Union[bytes, str], Optional[str]]
    if isinstance(decoded_bytes, bytes):
        return decoded_bytes.decode(charset or 'utf-8')
    return decoded_bytes

def download_attachments() -> None:
    """从邮件下载K线数据附件"""
    # 获取邮件配置
    
    # 创建下载目录
    download_path = os.path.join(path,'email_downloads')
    unpack_path = os.path.join(path,'实盘数据')
    os.makedirs(download_path,exist_ok=True)
    os.makedirs(unpack_path,exist_ok=True)
    have_file = False
    try:

        imap: imaplib.IMAP4_SSL = imaplib.IMAP4_SSL(
            host=server,
            port=imap_port
        )
        
        imap.login(username, password)
        imap.select('INBOX')
        
        # 搜索最近3天的邮件
        # SENTSINCE "dd-Mon-yyyy" 表示发件日期在指定日期及之后
        # 使用3天前的日期以确保不会遗漏
        search_date: datetime = datetime.now()-timedelta(days=2)
        date_str: str = search_date.strftime("%d-%b-%Y")
        
        # imap.search返回一个tuple: (状态, [消息ID列表])
        # 消息ID列表是一个只包含一个元素的list，元素是以空格分隔的消息ID字符串
        # 例如: [b'1 2 3 4 5'] 表示有5封邮件，ID分别是1到5
        # (ON "date") 是IMAP搜索条件，表示搜索指定日期的邮件
        # https://gist.github.com/martinrusev/6121028
        # ALL：所有邮件，UNSEEN：未读邮件
        # 其他常用条件：SUBJECT "xxx", FROM "xxx", TO "xxx" 等

        # IMAP搜索条件
        # 1. SUBJECT "xxx" 搜索主题包含xxx的邮件
        # 2. SENTSINCE "dd-Mon-yyyy" 搜索指定日期之后的邮件
        # 3. 多个条件组合使用
        search_criteria: str = f'(SUBJECT "{mail_subject}") (SENTSINCE {date_str})'
        status: str
        message_numbers: List[bytes]
        status, message_numbers = imap.search(None, search_criteria)
        # print(f"搜索状态: {status}")
        # print(f"找到的邮件ID: {message_numbers[0] if message_numbers[0] else '无'}")
        if not message_numbers[0]:
            print("没有找到K线数据邮件")
            return

        # print(f"当前日期: {today}")
        
        # 遍历所有邮件
        for num in message_numbers[0].split():
            # imap.fetch获取邮件内容
            # RFC822是IMAP协议定义的获取完整邮件的格式标准
            # 返回值是一个tuple: (状态, [邮件数据])
            # 邮件数据是一个list，每个元素是一个tuple(邮件ID, 邮件内容)
            fetch_status: str
            msg_data: List[Tuple[bytes, bytes]]
            fetch_status, msg_data = imap.fetch(num, '(RFC822)')
            
            email_body: bytes = msg_data[0][1]
            # 解析邮件内容为Message对象
            message: Message = email.message_from_bytes(email_body)
            
            # 获取邮件日期
            email_date: str = message.get("Date", "")
            subject: str = decode_str(message["subject"])
            # print(f"\n处理邮件: {subject}")
            # print(f"邮件日期: {email_date}")
            
            # 只检查是否是今天的数据
            
            if subject != mail_subject:
                continue
            # 遍历邮件的附件
            # message.walk()会遍历邮件的所有部分，包括正文和附件

            for part in message.walk():
                # multipart表示这是一个复合消息，需要进一步解析
                # 在发送邮件时，MIMEMultipart用于创建复合消息，包含正文和附件
                # 在接收邮件时，multipart表示这部分需要进一步处理才能获取实际内容
                if part.get_content_maintype() == 'multipart':
                    continue

                # Content-Disposition 是邮件头部字段，用于指定内容的显示方式
                # 常见值：
                # - attachment: 表示这是一个附件
                # - inline: 表示这是要直接显示的内容（如正文）
                # 如果没有这个字段，说明这部分可能是邮件正文
                content_disposition: Optional[str] = part.get('Content-Disposition')
                if content_disposition is None:
                    continue
                    
                filename: Optional[str] = part.get_filename()
                if filename:
                    filename = decode_str(filename)
                    if not filename.endswith('.zip'):
                        continue
                        
                    # 构建保存路径
                    filepath = os.path.join(path,download_path,filename)
                    # print(f"下载附件: {filename}")
                    
                    # 获取附件内容并保存
                    # get_payload(decode=True) 会自动进行base64解码
                    payload: bytes = part.get_payload(decode=True)
                    with open(filepath, 'wb') as f:
                        f.write(payload)
                    # print(f"已保存到: {filepath}")

                    with zipfile.ZipFile(filepath, 'r') as zip_ref:
                        zip_ref.extractall(unpack_path)
                        have_file = True
                    # print(f"已解压到: {unpack_path}")
                    # to_list = message['To'].split(';')
        # print("\n所有附件下载完成")
        
    except imaplib.IMAP4.error as e:
        print(f"IMAP错误: {str(e)}")
    except Exception as e:
        print(f"发生错误: {str(e)}")
    finally:
        # 确保正确关闭连接
        try:
            imap.close()  # 关闭当前选择的邮箱
            imap.logout() # 登出IMAP服务器
        except:
            pass
    if have_file == True:
        pass
    else:
        print('未收到文件')
app: typer.Typer = typer.Typer()

@app.command()
def main() -> None:
    """从邮件下载K线数据附件"""
    download_attachments()

if __name__ == "__main__":
    app()
