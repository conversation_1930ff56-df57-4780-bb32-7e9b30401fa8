# -*- coding:utf-8 -*-
#%%
import email
import os
import smtplib
import email.utils
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.application import MIMEApplication
import time
import subprocess
from datetime import datetime, timedelta
import pandas as pd
import json

path = os.path.dirname(__file__)

with open(os.path.join(path,'config.json'), 'r') as file:
    config = json.load(file)

username: str = config["email_username"]
password: str = config["email_password"]

to_addr = config["to_addr"]

smtp_server = config["smtp_server"]
smtp_port: int = config["smtp_port"]

market_name = config["market_name"]

encode = config["encoding"]
date = datetime.today().date().strftime("%Y-%m-%d")
yesterdate = (datetime.today().date() -timedelta(days=1)).strftime("%Y-%m-%d")
daybefore_yesterday = (datetime.now() - timedelta(days=2)).strftime("%Y-%m-%d")

def send_email(subject:str, body:str, to_addr:list, file_list:list=[]):

    smtp_obj = smtplib.SMTP_SSL(smtp_server, smtp_port) 
    smtp_obj.login(username, password)
    smtp_obj.set_debuglevel(True)

    message = MIMEMultipart()
    message["From"] = email.utils.formataddr(('Data_BOT', username))
    # message["To"] = email.utils.formataddr(('Hua', username))
    message["Subject"] = subject
    if type(body) == list:
        z = "\n".join(body)
        message.attach(MIMEText(z))
    else:
        message.attach(MIMEText(body))
    if len(file_list) != 0:
        for file_path in file_list:
            with open(file_path, 'rb') as f:
                attachment = MIMEApplication(f.read(), _subtype='csv')
                file_name = file_path.split('/')[-1] if '/' in file_path else file_path.split('\\')[-1]
                attachment.add_header('Content-Disposition', 'attachment', filename=file_name)
                message.attach(attachment)
    try:
        smtp_obj.sendmail(username, to_addr, msg=message.as_string())
    except smtplib.SMTPServerDisconnected:
        print("Connection unexpectedly closed. Retrying...")
        time.sleep(5) 
        send_email(subject,body,to_addr,file_list)
    finally:
        smtp_obj.quit()
#%%

def initialize():
    # 设置文件夹路径
    check_dir = os.path.join(path, "合成日线核查", "实盘数据")

    # 检查"实盘数据"文件夹是否存在
    if not os.path.exists(check_dir):
        print("首次运行，需要初始化\n")
        # 运行morning_monitor_CONTROL.py
        morning_monitor_script = os.path.join(path, "morning_monitor_CONTROL.py")

        print("初始化(1/2): 正在运行morning_monitor_CONTROL.py\n")
        result = subprocess.run(["python", morning_monitor_script])
        if result.stderr !='':            
            print(f"初始化(1/2)出错，请手动核查: {str(result.stderr)}")
            send_email(subject=f"初始化(1/2)出错，请手动核查", \
                    body=f'{result.stderr}', \
                    to_addr = to_addr)
            return False
        
        print("初始化(2/2): 正在运行INITIALIZE.py\n")
        # 运行initialize.py
        initialize_script = os.path.join(path, "INITIALIZE.py")
        result = subprocess.run(["python", initialize_script])
        if result.stderr != '':
            print(f"初始化(2/2)出错，请手动核查: {str(result.stderr)}")
            send_email(subject=f"初始化(1/2)出错，请手动核查", \
                    body=f'{result.stderr}', \
                    to_addr = to_addr)
            return False
        else:
            print("初始化完成\n")
            return True

    
def auto_inspect():
    try:

        print("\n====== 数据核查系统启动 ======\n")
        print('获取实盘数据-开始\n')
        PROCESS = "获取实盘数据"
        result = subprocess.run(['python', os.path.join(path,'1min核查','receive_kline_email.py')], capture_output=True, text=True, encoding=encode)
        print('获取实盘数据-结束\n')
        
        if result.stderr != '' or result.stdout !='':
            if result.stderr != '' and result.stdout =='':
                send_email(subject=f"自动核查程序 - 获取实盘数据文件出错", body=f'获取实盘数据文件出错，请检查是否收到邮件。\n报错：{result.stderr}',to_addr = to_addr)
            elif result.stdout != '' and result.stderr =='':
                send_email(subject=f"自动核查程序 - 获取实盘数据文件出错", body=f'获取实盘数据文件出错，请检查是否收到邮件。\n报错：{result.stdout}',to_addr = to_addr)
            else:
                send_email(subject=f"自动核查程序 - 获取实盘数据文件出错", body=f'获取实盘数据文件出错，请检查是否收到邮件。\n报错：{result.stderr};\n输出：{result.stdout}',to_addr = to_addr)
            return
        print('数据入库-开始\n')
        PROCESS = "数据入库"
        result = subprocess.run(['python', os.path.join(path,'1min核查','数据入库.py')], capture_output=True, text=True, encoding=encode)

        print('数据入库-结束\n')

        if result.stderr != '':
            send_email(subject=f"自动核查程序 - 实盘数据入库出错", body=f'数据入库出错，请检查程序。报错：{result.stderr}',to_addr = to_addr)
            return

        print('1min数据核查-开始\n')
        PROCESS = "1min数据核查"
        result = subprocess.run(['python', os.path.join(path,'1min核查','1min数据核查.py')], capture_output=True, text=True, encoding=encode)

        print('1min数据核查-结束\n')

        body1 = f'{date} {market_name} 1min数据 核查完成\n'
        files =[]
        if '数量核对一致' in result.stdout and '质量核对一致' in result.stdout and '不一致' not in result.stdout:
            pass
        else:
            body1+="核对有异常：\n"
            for i in result.stdout.split('\n'):
                if '不一致' in i:
                    print(i)
                    body1+=f'{i}\n'
                    if '数量' in i or '质量' in i:
                        if i.startswith('数量'):
                            problem = 'quantity_check'
                            name = '数量'
                        else:
                            problem = 'quality_check'
                            name = '质量'
                        files.append(os.path.join(path,'1min核查', problem,f"{yesterdate}_{date}数据{name}核查.csv"))
                elif '核对一致' in i or i.strip() =='':
                    continue
                else:
                    body1+=f'打印结果出错，请手动核查程序 {i}\n'
        if body1 == f'{date} {market_name} 1min数据 核查完成\n':
            subject1 = f'{yesterdate}-{date} {market_name} 1min数据 核查一致'
        else:
            subject1 = f'{yesterdate}-{date} {market_name} 1min数据 核查不一致'

        send_email(subject=subject1, \
                    body=body1, \
                    to_addr = to_addr,\
                    file_list=files)

        if subject1 == f'{yesterdate}-{date} {market_name} 1min数据 核查一致':

            print('\nk线聚合-开始\n')
            PROCESS = "k线聚合"
            result = subprocess.run(['python', os.path.join(path,'k线聚合','kline_aggregator_origin.py')], encoding=encode)
            print('k线聚合-结束\n')

            

            print('k线数据核查-开始\n')
            PROCESS = "k线数据核查"
            result = subprocess.run(['python', os.path.join(path,'k线核查','k线数据核查.py')], capture_output=True, text=True, encoding=encode)

            print('\nk线数据核查-结束\n')

            body1 = f'{date} {market_name} k线数据 核查完成\n'
            files =[]
            if '数量核对一致' in result.stdout and '质量核对一致' in result.stdout and '不一致' not in result.stdout:
                pass
            else:
                body1+="核对有异常：\n"
                for i in result.stdout.split('\n'):
                    if '不一致' in i:
                        print(i)
                        body1+=f'{i}\n'
                        if '数量' in i or '质量' in i:
                            if i.startswith('数量'):
                                problem = 'quantity_check'
                                name = '数量'
                            else:
                                problem = 'quality_check'
                                name = '质量'
                            files.append(os.path.join(path,'k线核查', problem,f"{daybefore_yesterday}_{date}数据{name}核查.csv"))
                    elif '核对一致' in i or i.strip() =='':
                        continue
                    else:
                        body1+=f'打印结果出错，请手动核查程序 {i}\n'
            if body1 == f'{date} {market_name} k线数据 核查完成\n':
                subject1 = f'{daybefore_yesterday}-{date} {market_name} k线数据 核查一致'
            else:
                subject1 = f'{daybefore_yesterday}-{date} {market_name} k线数据 核查不一致'

            send_email(subject=subject1, \
                        body=body1, \
                        to_addr = to_addr,\
                        file_list=files)
            gonext = False
            if '质量核对不一致' in body1:
                kline_check = pd.read_csv(os.path.join(path,'k线核查', 'quality_check',f"{daybefore_yesterday}_{date}数据质量核查.csv"))
                if list(kline_check['error_type'].unique()) == ['首根分钟线volume为0']:
                    gonext = True
                else: 
                    print(list(kline_check['error_type'].unique()))
                

            if subject1 == f'{daybefore_yesterday}-{date} {market_name} k线数据 核查一致' or ('质量核对不一致' in body1 and '数量核对一致' in body1 and gonext):

                print('技术指标核查-开始\n')
                PROCESS = "技术指标核查"
                result = subprocess.run(['python', os.path.join(path,'技术指标核查','技术指标生成核查.py')], capture_output=True, text=True, encoding=encode)

                print('技术指标核查-结束\n')

                body1 = f'{date} {market_name} 技术指标 核查完成\n'
                files =[]
                if '数量核对一致' in result.stdout and '质量核对一致' in result.stdout and '不一致' not in result.stdout:
                    pass
                else:
                    body1+="核对有异常：\n"
                    for i in result.stdout.split('\n'):
                        if '不一致' in i:
                            print(i)
                            body1+=f'{i}\n'
                            if '数量' in i or '质量' in i:
                                if i.startswith('数量'):
                                    problem = 'quantity_check'
                                    name = '数量'
                                else:
                                    problem = 'quality_check'
                                    name = '质量'
                                files.append(os.path.join(path,'技术指标核查', problem,f"{daybefore_yesterday}_{date}数据{name}核查.csv"))
                        elif '核对一致' in i or i.strip() =='':
                            continue
                        else:
                            body1+=f'打印结果出错，请手动核查程序 {i}\n'
                if body1 == f'{date} {market_name} 技术指标 核查完成\n':
                    subject1 = f'{daybefore_yesterday}-{date} {market_name} 技术指标 核查一致'
                else:
                    subject1 = f'{daybefore_yesterday}-{date} {market_name} 技术指标 核查不一致'

                send_email(subject=subject1, \
                            body=body1, \
                            to_addr = to_addr,\
                            file_list=files)
    except Exception as e:
        print(f"自动核查出错，请手动核查: {str(e)}")
        send_email(subject=f"自动核查至'{PROCESS}'出错，请手动核查", \
                body=f'{e}', \
                to_addr = to_addr)
    finally:
        print("\n====== 数据核查系统关闭 ======\n")

if __name__ == "__main__":
    check_init = initialize()
    if check_init:
        auto_inspect()

# %%
