import os
import json

path = os.path.dirname(__file__)
parent_path = os.path.dirname(path)
with open(os.path.join(parent_path,'config.json'), 'r',encoding='utf-8') as file:
    config = json.load(file)
vt_setting_file_path = os.path.join(parent_path,'.vntrader', 'vt_setting.json')
with open(vt_setting_file_path, 'r', encoding='utf-8') as vt_setting_file:
    vt_setting_data = json.load(vt_setting_file)
vt_setting_data['database.database'] = config['database2_needcheck']
vt_setting_data['database.host'] = config['host2']
vt_setting_data['database.port'] = config['port2']
vt_setting_data['database.user'] = config['user2']
vt_setting_data['database.password'] = config['password2']
with open(vt_setting_file_path, 'w', encoding='utf-8') as vt_setting_file:
    json.dump(vt_setting_data, vt_setting_file, indent=4)

from peewee import *
from vnpy.trader.constant import Interval, Exchange
from vnpy.trader.object import BarData
from vnpy_mysql.mysql_database import DbBarData, DbBarOverview, DB_TZ, db
from datetime import datetime, timedelta
from typing import List, Dict, Iterable, Union, Optional
import pandas as pd
from tqdm import tqdm
from enum import Enum
import warnings
import json
from zoneinfo import ZoneInfo
import os
warnings.filterwarnings("ignore")

# 定义新的K线数据表
class AggregatedBarData(Model):
    symbol = CharField()
    exchange = CharField()
    interval = CharField()
    datetime = DateTimeField()
    volume = DoubleField()
    turnover = DoubleField()
    open_interest = DoubleField()
    open_price = DoubleField()
    high_price = DoubleField()
    low_price = DoubleField()
    close_price = DoubleField()

    class Meta:
        database = db
        indexes = (
            (('symbol', 'exchange', 'interval', 'datetime'), True),
        )

# 定义新的K线汇总数据表
class AggregatedBarOverview(Model):
    symbol = CharField()
    exchange = CharField()
    interval = CharField()
    count = IntegerField()
    start = DateTimeField()
    end = DateTimeField()

    class Meta:
        database = db
        indexes = (
            (('symbol', 'exchange', 'interval'), True),
        )

# 确保表存在
db.create_tables([AggregatedBarData, AggregatedBarOverview], safe=True)

# 定义要聚合的时间间隔
AGGREGATION_INTERVALS: List[Dict[str, Union[int, str]]] = [
    {"minutes": 2, "rule": "2T"},
    {"minutes": 3, "rule": "3T"},
    {"minutes": 5, "rule": "5T"},
    {"minutes": 6, "rule": "6T"},
    {"minutes": 8, "rule": "8T"},
    {"minutes": 10, "rule": "10T"},
    {"minutes": 12, "rule": "12T"},
    {"minutes": 15, "rule": "15T"},
    {"minutes": 20, "rule": "20T"},
    {"minutes": 23, "rule": "23T"},
    {"minutes": 30, "rule": "30T"},
    {"minutes": 45, "rule": "45T"},
    {"minutes": 60, "rule": "1H"},  # 1小时及以上改用H
    {"minutes": 90, "rule": "90T"},
    {"minutes": 120, "rule": "2H"},
    {"minutes": 180, "rule": "3H"},
    {"minutes": 240, "rule": "4H"},
    {"minutes": 360, "rule": "6H"},
    {"minutes": 480, "rule": "8H"},
    {"minutes": 720, "rule": "12H"},
    {"minutes": 1440, "rule": "1D"},
    {"minutes": 2880, "rule": "2D"},
    {"minutes": 4320, "rule": "3D"}
]

def get_value(obj: Union[str, Enum]) -> str:
    return obj.value if hasattr(obj, 'value') else obj

def load_first_minute_data() -> Dict[str, datetime]:
    """Load first minute data from JSON file"""
    path = os.path.dirname(__file__)
    try:
        first_minute_file = os.path.join(path, "binance_first_minute.json")
        with open(first_minute_file, 'r') as f:
            data = json.load(f)
            # Convert string timestamps to datetime objects
            return {symbol: pd.to_datetime(dt).astimezone(DB_TZ) 
                   for symbol, dt in data.items()}
    except FileNotFoundError:
        print("Warning: binance_first_minute.json not found")
        return {}
    except json.JSONDecodeError:
        print("Warning: Error decoding binance_first_minute.json")
        return {}

def get_resample_origin(symbol: str, exchange: str, start_time: datetime, 
                       first_minute_data: Dict[str, datetime]) -> datetime:
    """Get the appropriate origin time for resampling"""
    # Try to get from first_minute_data
    if symbol in first_minute_data:
        first_minute = first_minute_data[symbol]
        # 转换到UTC
        utc_time = first_minute.astimezone(ZoneInfo('UTC'))
        # 设置为当天UTC 0点
        utc_origin = utc_time.replace(hour=0, minute=0, second=0, microsecond=0)
        # 转换回DB_TZ
        return utc_origin.astimezone(DB_TZ)
    
    # Fallback to overview.start
    overview = (DbBarOverview
               .select()
               .where(
                   (DbBarOverview.symbol == symbol) &
                   (DbBarOverview.exchange == exchange) &
                   (DbBarOverview.interval == get_value(Interval.MINUTE))
               )
               .first())
    
    if overview:
        # 数据库中的时间无时区信息，需要先添加时区
        start = overview.start.replace(tzinfo=DB_TZ)
        # 转换到UTC
        utc_time = start.astimezone(ZoneInfo('UTC'))
        # 设置为当天UTC 0点
        utc_origin = utc_time.replace(hour=0, minute=0, second=0, microsecond=0)
        # 转换回DB_TZ
        return utc_origin.astimezone(DB_TZ)
    
    # 如果都没有，使用1970年作为起点，并记录error日志
    print(f"Error: No first minute data found for {symbol} {exchange}")
    return datetime(1970, 1, 1, tzinfo=DB_TZ)

def aggregate_klines() -> None:
    # Load first minute data
    first_minute_data = load_first_minute_data()
    
    contracts: ModelSelect = (DbBarOverview
                 .select(DbBarOverview.symbol, DbBarOverview.exchange)
                 .where(DbBarOverview.interval == get_value(Interval.MINUTE))
                 .distinct())

    # for contract in tqdm(contracts, desc="处理合约"):
    for contract in contracts:
        symbol: str = contract.symbol
        exchange: str = get_value(contract.exchange)

        latest_original_bar: Optional[DbBarData] = (DbBarData
                      .select()
                      .where(
                          (DbBarData.symbol == symbol) &
                          (DbBarData.exchange == exchange) &
                          (DbBarData.interval == get_value(Interval.MINUTE))
                      )
                      .order_by(DbBarData.datetime.desc())
                      .first())

        if not latest_original_bar:
            continue

        for interval_config in AGGREGATION_INTERVALS:
            minutes = interval_config["minutes"]
            resample_rule = interval_config["rule"]
            interval = f"{minutes}m"
            if minutes >= 1440:
                interval = f"{minutes // 1440}d"

            latest_agg_bar: Optional[AggregatedBarData] = (AggregatedBarData
                      .select()
                      .where(
                          (AggregatedBarData.symbol == symbol) &
                          (AggregatedBarData.exchange == exchange) &
                          (AggregatedBarData.interval == interval)
                      )
                      .order_by(AggregatedBarData.datetime.desc())
                      .first())

            start_time: datetime = latest_agg_bar.datetime.replace(tzinfo=DB_TZ) if latest_agg_bar else datetime(1970, 1, 1, tzinfo=DB_TZ)

            new_bars: ModelSelect = (DbBarData
                        .select()
                        .where(
                            (DbBarData.symbol == symbol) &
                            (DbBarData.exchange == exchange) &
                            (DbBarData.interval == get_value(Interval.MINUTE)) &
                            (DbBarData.datetime >= start_time)
                        )
                        .order_by(DbBarData.datetime))

            if not new_bars:
                continue
            
            df: pd.DataFrame = pd.DataFrame(list(new_bars.dicts()))
            df['datetime'] = pd.to_datetime(df['datetime']).dt.tz_localize(DB_TZ)
            df.set_index('datetime', inplace=True)
            if symbol =='ETHUSDT' and interval == '3d':
                first_minute = pd.to_datetime('2022-12-30 08:00:00+08:00').astimezone(DB_TZ)
                utc_time = first_minute.astimezone(ZoneInfo('UTC'))
                utc_origin = utc_time.replace(hour=0, minute=0, second=0, microsecond=0)
                origin = utc_origin.astimezone(DB_TZ)
            else:
            # Get appropriate origin time
                origin = get_resample_origin(symbol, exchange, start_time, first_minute_data)
            
            agg_df: pd.DataFrame = df.resample(
                resample_rule,
                origin=origin  # Use the determined origin time
            ).agg({
                'open_price': 'first',
                'high_price': 'max',
                'low_price': 'min',
                'close_price': 'last',
                'volume': 'sum',
                'turnover': 'sum',
                'open_interest': 'last'
            }).dropna()

            rows: List[Dict] = []
            for row in agg_df.itertuples():
                rows.append({
                    'symbol': symbol,
                    'exchange': exchange,
                    'interval': interval,
                    'datetime': row.Index.to_pydatetime(),
                    'volume': float(row.volume),
                    'turnover': float(row.turnover),
                    'open_interest': float(row.open_interest),
                    'open_price': float(row.open_price),
                    'high_price': float(row.high_price),
                    'low_price': float(row.low_price),
                    'close_price': float(row.close_price)
                })

            with db.atomic():
                for batch in chunked(rows, 100):
                    AggregatedBarData.insert_many(batch).on_conflict_replace().execute()

            overview: AggregatedBarOverview
            created: bool
            overview, created = AggregatedBarOverview.get_or_create(
                symbol=symbol,
                exchange=exchange,
                interval=interval
            )
            # 合并三次查询为一次
            aggregated_info = (AggregatedBarData
                .select(
                    fn.COUNT(AggregatedBarData.id).alias('count'),
                    fn.MIN(AggregatedBarData.datetime).alias('start'),
                    fn.MAX(AggregatedBarData.datetime).alias('end')
                )
                .where(
                    (AggregatedBarData.symbol == symbol) &
                    (AggregatedBarData.exchange == exchange) &
                    (AggregatedBarData.interval == interval)
                )
                .get())

            overview.count = aggregated_info.count
            overview.start = aggregated_info.start
            overview.end = aggregated_info.end
            overview.save()

    # tqdm.write("所有K线数据聚合完成")

if __name__ == "__main__":
    aggregate_klines()
