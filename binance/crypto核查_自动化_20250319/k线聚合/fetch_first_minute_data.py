
import os
import json

path = os.path.dirname(__file__)
parent_path = os.path.dirname(path)
with open(os.path.join(parent_path,'config.json'), 'r',encoding='utf-8') as file:
    config = json.load(file)
vt_setting_file_path = os.path.join(parent_path,'.vntrader', 'vt_setting.json')
with open(vt_setting_file_path, 'r', encoding='utf-8') as vt_setting_file:
    vt_setting_data = json.load(vt_setting_file)
vt_setting_data['database.database'] = config['database2_needcheck']
vt_setting_data['database.host'] = config['host2']
vt_setting_data['database.port'] = config['port2']
vt_setting_data['database.user'] = config['user2']
vt_setting_data['database.password'] = config['password2']
with open(vt_setting_file_path, 'w', encoding='utf-8') as vt_setting_file:
    json.dump(vt_setting_data, vt_setting_file, indent=4)

from vnpy_binance.binance_spot_gateway import INTERVAL_VT2BINANCE, UTC_TZ
from datetime import datetime
from time import sleep
from vnpy_evo.trader.constant import (Interval, Exchange)
from vnpy_evo.trader.object import HistoryRequest
from vnpy_rest import RestClient, Response
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from zoneinfo import ZoneInfo

from vnpy_mysql.mysql_database import DB_TZ

class BinanceSpotRestAPi(RestClient):
    _instance_lock = threading.Lock()
    _instance = None
    _exchange_info = None

    @classmethod
    def get_instance(cls):
        if cls._instance is None:
            with cls._instance_lock:
                if cls._instance is None:
                    cls._instance = cls()
                    cls._instance.init("https://www.binance.com",proxy_host="127.0.0.1",proxy_port=7890)
                    cls._instance.start()
                    cls._instance._fetch_exchange_info()
        return cls._instance

    def _fetch_exchange_info(self):
        """获取交易所信息"""
        path = "/fapi/v1/exchangeInfo"
        resp: Response = self.request("GET", path)
        
        if resp.status_code // 100 != 2:
            print(f"查询交易所信息失败，状态码：{resp.status_code}，信息：{resp.text}")
            return
        
        self._exchange_info = resp.json()

    def contracts(self) -> list:
        """查询所有USDT报价的永续期货合约信息"""
        if not self._exchange_info:
            return []
        contracts = [d["symbol"] for d in self._exchange_info["symbols"] 
                if d["quoteAsset"] == "USDT" and 
                d["contractType"] == "PERPETUAL" and 
                d["status"] == "TRADING"]
        
        print(f"USDT报价的永续合约数量: {len(contracts)}")
        return contracts

    def query_first_minute(self, symbol: str) -> datetime:
        """只查询第一分钟的K线数据"""
        path = "/fapi/v1/continuousKlines"
        
        # 从2000年开始查询
        start_time = int(datetime(2000, 1, 1).timestamp() * 1000)
        
        params = {
            "pair": symbol,
            "contractType": 'PERPETUAL',
            "interval": "1m",
            "limit": 1,
            "startTime": start_time
        }

        resp: Response = self.request("GET", path, params=params)

        if resp.status_code // 100 != 2:
            print(f"查询K线历史失败，状态码：{resp.status_code}，信息：{resp.text}，标的：{symbol}")
            return None

        data = resp.json()
        if not data:
            print(f"未接收到K线历史数据，开始时间：{start_time}，标的：{symbol}")
            return None

        # 返回开盘时间
        first_kline_time = datetime.fromtimestamp(
            data[0][0] / 1000,  # 转换毫秒为秒
            tz=UTC_TZ
        ).astimezone(DB_TZ)
        
        return first_kline_time

def save_to_json(data: dict, filename='binance_first_minute.json'):
    """保存数据到JSON文件"""
    try:
        with open(filename, 'w') as f:
            json.dump(data, f, indent=4, default=str)
        print(f"成功保存数据到 {filename}")
    except IOError as e:
        print(f"保存JSON文件时出错: {e}")

def process_symbol(symbol: str) -> tuple:
    """处理单个标的"""
    api = BinanceSpotRestAPi.get_instance()
    first_minute = api.query_first_minute(symbol)
    if first_minute:
        return symbol, first_minute
    return None

def main():
    api = BinanceSpotRestAPi.get_instance()
    contracts = api.contracts()
    
    first_minute_data = {}
    
    # 使用线程池并行处理
    with ThreadPoolExecutor(max_workers=10) as executor:
        future_to_symbol = {executor.submit(process_symbol, symbol): symbol for symbol in contracts}
        
        for future in as_completed(future_to_symbol):
            result = future.result()
            if result:
                symbol, first_minute = result
                first_minute_data[symbol] = first_minute
                print(f"获取到 {symbol} 的第一分钟数据: {first_minute}")
    
    # 保存结果到JSON文件
    save_to_json(first_minute_data)

if __name__ == '__main__':
    try:
        import time
        print_date = time.strftime("%Y-%m-%d %H:%M:%S")
        print(f"{print_date}: {__file__}")
        
        main()
        
        print(f"{__file__}: 完成所有工作！")
    except Exception as e:
        print(f"发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 确保在程序结束时停止API
        BinanceSpotRestAPi.get_instance().stop()
