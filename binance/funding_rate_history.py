from datetime import datetime, timedelta
import pandas as pd
from time import sleep
from vnpy.trader.database import DB_TZ  # 导入数据库时区

from vnpy_evo.event import EventEngine
from vnpy_evo.trader.engine import MainEngine
from vnpy_evo.trader.utility import load_json, ZoneInfo
from vnpy_binance import BinanceLinearGateway, BinanceInverseGateway
from vnpy_rest import Request, RestClient
from prod.binance_linear_gateway import Security  # 导入Security枚举类

# 定义UTC时区
UTC_TZ = ZoneInfo("UTC")

class FundingRateDownloader:
    """资金费率下载器"""
    
    def __init__(self):
        """构造函数"""
        self.event_engine = EventEngine()
        self.main_engine = MainEngine(self.event_engine)
        
        # 添加U本位和币本位网关
        self.linear_gateway = self.main_engine.add_gateway(BinanceLinearGateway)
        self.inverse_gateway = self.main_engine.add_gateway(BinanceInverseGateway)
        
        # 加载连接设置
        self.setting = load_json("connect_binance.json")
        
    def connect_gateway(self):
        """连接网关"""
        # 连接U本位和币本位网关
        self.main_engine.connect(self.setting, "BINANCE_LINEAR")
        self.main_engine.connect(self.setting, "BINANCE_INVERSE")
        
        # 等待连接成功
        sleep(3)
    
    def download_funding_rates(self):
        """下载资金费率数据"""
        # 设置查询参数
        end_time = int(datetime.now().timestamp() * 1000)
        start_time = int(datetime(2020, 1, 1).timestamp() * 1000)
        
        # 由于时间跨度较大，需要分批查询
        batch_days = 200  # 每次查询200天的数据
        current_start = start_time
        
        linear_rates_list = []
        inverse_rates_list = []
        
        while current_start < end_time:
            current_end = min(current_start + batch_days * 24 * 60 * 60 * 1000, end_time)
            
            # 查询U本位合约资金费率
            self.main_engine.write_log(f"查询BTCUSDT资金费率... {datetime.fromtimestamp(current_start/1000)} - {datetime.fromtimestamp(current_end/1000)}")
            
            # 直接使用REST API请求
            params = {
                "symbol": "BTCUSDT",
                "startTime": current_start,
                "endTime": current_end,
                "limit": 1000
            }
            resp = self.linear_gateway.rest_api.request(
                "GET",
                "/fapi/v1/fundingRate",
                data={"security": Security.NONE},
                params=params
            )
            if resp.status_code == 200:
                linear_batch = resp.json()
                if linear_batch:
                    linear_rates_list.extend(linear_batch)
            
            # 查询币本位合约资金费率
            self.main_engine.write_log(f"查询BTCUSD_PERP资金费率... {datetime.fromtimestamp(current_start/1000)} - {datetime.fromtimestamp(current_end/1000)}")
            
            params = {
                "symbol": "BTCUSD_PERP",
                "startTime": current_start,
                "endTime": current_end,
                "limit": 1000
            }
            resp = self.inverse_gateway.rest_api.request(
                "GET",
                "/dapi/v1/fundingRate",
                data={"security": Security.NONE},
                params=params
            )
            if resp.status_code == 200:
                inverse_batch = resp.json()
                if inverse_batch:
                    inverse_rates_list.extend(inverse_batch)
            
            current_start = current_end
            sleep(1)  # 添加延时避免触发频率限制
        
        # 转换为DataFrame并保存
        if linear_rates_list:
            linear_df = pd.DataFrame(linear_rates_list)
            # 保留原始时间戳
            linear_df['timestamp'] = linear_df['fundingTime']
            # 添加UTC时间
            linear_df['datetime_utc'] = pd.to_datetime(linear_df['fundingTime'], unit='ms', utc=True)
            # 添加DB_TZ时间
            linear_df['datetime'] = linear_df['datetime_utc'].dt.tz_convert(DB_TZ)
            
            # 为Excel导出创建不带时区的时间列
            linear_df['datetime_utc_str'] = linear_df['datetime_utc'].dt.strftime('%Y-%m-%d %H:%M:%S')
            linear_df['datetime_str'] = linear_df['datetime'].dt.strftime('%Y-%m-%d %H:%M:%S')
            
            # 重新排列列顺序
            linear_df = linear_df[['symbol', 'timestamp', 'datetime_utc_str', 'datetime_str', 
                                 'fundingRate', 'markPrice']]
            linear_df = linear_df.sort_values('datetime_str')  # 按时间排序
            
            # 重命名列以便于显示
            linear_df = linear_df.rename(columns={
                'datetime_utc_str': 'datetime_utc',
                'datetime_str': 'datetime'
            })
            
        if inverse_rates_list:
            inverse_df = pd.DataFrame(inverse_rates_list)
            # 保留原始时间戳
            inverse_df['timestamp'] = inverse_df['fundingTime']
            # 添加UTC时间
            inverse_df['datetime_utc'] = pd.to_datetime(inverse_df['fundingTime'], unit='ms', utc=True)
            # 添加DB_TZ时间
            inverse_df['datetime'] = inverse_df['datetime_utc'].dt.tz_convert(DB_TZ)
            
            # 为Excel导出创建不带时区的时间列
            inverse_df['datetime_utc_str'] = inverse_df['datetime_utc'].dt.strftime('%Y-%m-%d %H:%M:%S')
            inverse_df['datetime_str'] = inverse_df['datetime'].dt.strftime('%Y-%m-%d %H:%M:%S')
            
            # 重新排列列顺序
            inverse_df = inverse_df[['symbol', 'timestamp', 'datetime_utc_str', 'datetime_str', 
                                   'fundingRate']]
            inverse_df = inverse_df.sort_values('datetime_str')  # 按时间排序
            
            # 重命名列以便于显示
            inverse_df = inverse_df.rename(columns={
                'datetime_utc_str': 'datetime_utc',
                'datetime_str': 'datetime'
            })
        
        # 生成文件名
        filename = f"funding_rates_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        
        # 保存到Excel，并设置时间列的格式
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            if linear_rates_list:
                linear_df.to_excel(writer, sheet_name='USDT合约', index=False)
                worksheet = writer.sheets['USDT合约']
                # 设置列宽
                worksheet.column_dimensions['A'].width = 15  # symbol
                worksheet.column_dimensions['B'].width = 15  # timestamp
                worksheet.column_dimensions['C'].width = 25  # datetime_utc
                worksheet.column_dimensions['D'].width = 25  # datetime
                worksheet.column_dimensions['E'].width = 15  # fundingRate
                worksheet.column_dimensions['F'].width = 15  # markPrice
                
                # 使用原始的带时区的datetime进行日志输出
                temp_df = pd.DataFrame(linear_rates_list)
                temp_df['datetime'] = pd.to_datetime(temp_df['fundingTime'], unit='ms', utc=True).dt.tz_convert(DB_TZ)
                self.main_engine.write_log(f"USDT合约数据行数: {len(linear_df)}")
                self.main_engine.write_log(f"USDT合约时间范围: {temp_df['datetime'].min()} - {temp_df['datetime'].max()}")
            
            if inverse_rates_list:
                inverse_df.to_excel(writer, sheet_name='USD合约', index=False)
                worksheet = writer.sheets['USD合约']
                # 设置列宽
                worksheet.column_dimensions['A'].width = 15  # symbol
                worksheet.column_dimensions['B'].width = 15  # timestamp
                worksheet.column_dimensions['C'].width = 25  # datetime_utc
                worksheet.column_dimensions['D'].width = 25  # datetime
                worksheet.column_dimensions['E'].width = 15  # fundingRate
                
                # 使用原始的带时区的datetime进行日志输出
                temp_df = pd.DataFrame(inverse_rates_list)
                temp_df['datetime'] = pd.to_datetime(temp_df['fundingTime'], unit='ms', utc=True).dt.tz_convert(DB_TZ)
                self.main_engine.write_log(f"USD合约数据行数: {len(inverse_df)}")
                self.main_engine.write_log(f"USD合约时间范围: {temp_df['datetime'].min()} - {temp_df['datetime'].max()}")
        
        self.main_engine.write_log(f"资金费率数据已保存至: {filename}")

def main():
    """主入口函数"""
    downloader = FundingRateDownloader()
    downloader.connect_gateway()
    downloader.download_funding_rates()

if __name__ == "__main__":
    main() 