from typing import Dict, List, Set, Tuple, Any
from vnpy.trader.utility import load_json, get_file_path

def get_symbols_dict(data: dict) -> Dict[str, dict]:
    """将symbols列表转换为以symbol为key的字典"""
    return {item['symbol']: item for item in data['symbols']}

def compare_dict_values(old_value: Any, new_value: Any) -> bool:
    """递归比较两个值是否相等"""
    if isinstance(old_value, dict) and isinstance(new_value, dict):
        return compare_dicts(old_value, new_value)
    elif isinstance(old_value, list) and isinstance(new_value, list):
        if len(old_value) != len(new_value):
            return False
        # 如果列表为空，直接返回True
        if not old_value and not new_value:
            return True
        # 如果列表元素是字典，按filterType排序
        if isinstance(old_value[0], dict) and 'filterType' in old_value[0]:
            old_sorted = sorted(old_value, key=lambda x: x['filterType'])
            new_sorted = sorted(new_value, key=lambda x: x['filterType'])
        else:
            old_sorted = sorted(old_value) if not isinstance(old_value[0], dict) else old_value
            new_sorted = sorted(new_value) if not isinstance(new_value[0], dict) else new_value
        return all(compare_dict_values(a, b) for a, b in zip(old_sorted, new_sorted))
    return old_value == new_value

def compare_dicts(old_dict: dict, new_dict: dict) -> bool:
    """递归比较两个字典是否相等"""
    if set(old_dict.keys()) != set(new_dict.keys()):
        return False
    return all(compare_dict_values(old_dict[key], new_dict[key]) for key in old_dict)

def get_filter_changes(old_filter: dict, new_filter: dict) -> Dict[str, str]:
    """获取filter中字段的变化"""
    changes = {}
    all_keys = set(old_filter.keys()) | set(new_filter.keys())
    for key in all_keys:
        if key == 'filterType':
            continue
        old_value = old_filter.get(key)
        new_value = new_filter.get(key)
        if old_value != new_value:
            changes[key] = f"{old_value} -> {new_value}"
    return changes

def compare_symbols(old_file: str, new_file: str) -> Tuple[List[str], List[Tuple[str, Dict[str, str]]]]:
    """
    比较两个exchangeInfo文件中的symbols
    
    Returns:
        Tuple[List[str], List[Tuple[str, Dict[str, str]]]]: 
            - 新增的symbols列表
            - 变化的symbols列表，每个元素为(symbol, field_changes)，
              其中field_changes为字段名到变化描述的映射
    """
    old_data = load_json(old_file)
    new_data = load_json(new_file)
    
    old_symbols = get_symbols_dict(old_data)
    new_symbols = get_symbols_dict(new_data)
    
    # 找出新增的symbols
    added_symbols = set(new_symbols.keys()) - set(old_symbols.keys())
    
    # 找出变化的symbols
    changed_symbols = []
    for symbol in set(old_symbols.keys()) & set(new_symbols.keys()):
        old_symbol_data = old_symbols[symbol].copy()
        new_symbol_data = new_symbols[symbol].copy()
        
        # 移除不需要比较的字段
        old_symbol_data.pop('settlePlan', None)
        new_symbol_data.pop('settlePlan', None)
        
        # 比较所有字段
        field_changes = {}
        for key in set(old_symbol_data.keys()) | set(new_symbol_data.keys()):
            if key == 'filters':
                # 特殊处理filters数组
                old_filters = {f["filterType"]: f for f in old_symbol_data.get(key, [])}
                new_filters = {f["filterType"]: f for f in new_symbol_data.get(key, [])}
                
                for filter_type in set(old_filters.keys()) | set(new_filters.keys()):
                    if filter_type not in old_filters:
                        field_changes[f"filters.{filter_type}"] = f"新增: {new_filters[filter_type]}"
                    elif filter_type not in new_filters:
                        field_changes[f"filters.{filter_type}"] = f"删除: {old_filters[filter_type]}"
                    else:
                        filter_changes = get_filter_changes(old_filters[filter_type], new_filters[filter_type])
                        for fname, fchange in filter_changes.items():
                            field_changes[f"filters.{filter_type}.{fname}"] = fchange
            else:
                old_value = old_symbol_data.get(key)
                new_value = new_symbol_data.get(key)
                if old_value != new_value:
                    if key not in old_symbol_data:
                        field_changes[key] = f"新增: {new_value}"
                    elif key not in new_symbol_data:
                        field_changes[key] = f"删除: {old_value}"
                    else:
                        field_changes[key] = f"{old_value} -> {new_value}"
        
        if field_changes:
            changed_symbols.append((symbol, field_changes))
    
    return list(added_symbols), changed_symbols

def main():
    old_file = "exchangeInfo_old.json"
    new_file = "exchangeInfo_new.json"
    output_file = get_file_path("exchange_changes.txt")
    
    added_symbols, changed_symbols = compare_symbols(old_file, new_file)
    
    with open(output_file, "w", encoding="utf-8") as f:
        # 写入新增的交易对
        f.write("新增的交易对: " + ", ".join(sorted(added_symbols)) + "\n")
        
        # 写入变化的交易对
        f.write("\n发生变化的交易对:\n")
        for symbol, field_changes in sorted(changed_symbols):
            f.write(f"- {symbol}:\n")
            for field, change in sorted(field_changes.items()):
                f.write(f"  {field}: {change}\n")
            f.write("\n")  # 在交易对之间添加空行
    
    print(f"对比结果已保存到: {output_file}")

if __name__ == "__main__":
    main()