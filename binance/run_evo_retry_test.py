from vnpy.trader.setting import SETTINGS
from vnpy.trader.utility import load_json, round_to, extract_vt_symbol
from vnpy_evo.trader.event import EVENT_ACCOUNT, EVENT_POSITION, EVENT_CONTRACT, EVENT_TICK, EVENT_ORDER, EVENT_TRADE
from vnpy_evo.event import Event, EventEngine
from vnpy_evo.trader.engine import MainEngine
from vnpy_evo.trader.object import (
    OrderRequest, Direction, Offset, OrderType,
    Exchange, Status, SubscribeRequest, ContractData
)
from datetime import datetime
import time
import json
from vnpy_ctastrategy import CtaEngine
from vnpy_ctastrategy.template import CtaTemplate

class RetryOrderTestApp:
    """订单重试测试应用"""

    def __init__(self):
        """构造函数"""
        self.event_engine = EventEngine()
        self.main_engine = MainEngine(self.event_engine)

        self.test_vt_symbol = "ETHUSDT.BINANCE"  # 测试标的
        self.test_symbol, self.test_exchange = extract_vt_symbol(self.test_vt_symbol)

        self.tick = None  # 最新行情
        self.contract = None  # 合约信息

        # 记录已重试订单ID
        self.retried_orderids = set()
        # 记录4164错误重试的订单ID
        self.retried_4164_orderids = set()

        # 测试标志位
        self.test_4164_error = True  # 是否测试4164错误

        self.register_event()
        self.init_engines()

    def init_engines(self) -> None:
        """初始化引擎"""
        # 添加币安网关
        # from prod.binance_linear_gateway import BinanceLinearGateway
        from prod.binance_linear_gateway_log import BinanceLinearGateway
        self.main_engine.add_gateway(BinanceLinearGateway)
        setting = load_json("connect_binance_testnet.json")
        self.main_engine.connect(setting, "BINANCE_LINEAR")
        self.main_engine.write_log("接口添加成功")

    def register_event(self):
        """注册事件监听"""
        self.event_engine.register(EVENT_TICK, self.process_tick_event)
        self.event_engine.register(EVENT_CONTRACT, self.process_contract_event)
        self.event_engine.register(EVENT_ORDER, self.process_order_event)
        self.event_engine.register(EVENT_TRADE, self.process_trade_event)

    def process_tick_event(self, event: Event):
        """处理TICK事件"""
        tick = event.data
        if tick.vt_symbol == self.test_vt_symbol:
            self.tick = tick

    def process_contract_event(self, event: Event):
        """处理合约事件"""
        contract = event.data
        if contract.vt_symbol == self.test_vt_symbol:
            self.contract = contract
            # 订阅行情
            req = SubscribeRequest(
                symbol=contract.symbol,
                exchange=contract.exchange
            )
            self.main_engine.subscribe(req, "BINANCE_LINEAR")
            self.main_engine.write_log(f"订阅{self.test_vt_symbol}行情数据")

    def process_order_event(self, event: Event):
        """处理委托事件"""
        order = event.data
        if order.vt_symbol != self.test_vt_symbol:
            return

        msg = (f"收到委托回报 {order.vt_symbol} {order.direction} {order.offset} "
               f"价格={order.price} 数量={order.volume} "
               f"状态={order.status}")
        self.main_engine.write_log(msg)

        # 处理订单被拒绝的情况
        if order.status == Status.REJECTED:
            # if hasattr(order, 'rejected_reason'):
            #     try:
            #         self.main_engine.write_log(f"订单被拒绝: {order.rejected_reason}")
            #         error_info = json.loads(order.rejected_reason)
            #         error_code = error_info.get('code')

            #         # 处理1008错误 - 无限重试
            #         if error_code == -1008 and order.orderid not in self.retried_orderids:
            #             self.main_engine.write_log(f"检测到系统过载错误(1008),准备重试订单: {order.orderid}")
            #             self.retried_orderids.add(order.orderid)
            #             self.retry_order(order)
            #             return

            #         # 处理4164错误 - 只重试一次，放大10倍volume，因-1008无法复现，测试用
            #         if self.test_4164_error and error_code == -4164 and order.orderid not in self.retried_4164_orderids:
            #             self.main_engine.write_log(f"检测到最小名义价值错误(4164),准备重试订单: {order.orderid}")
            #             self.retried_4164_orderids.add(order.orderid)
            #             # 增加数量重试
            #             self.retry_order(order, increase_volume=True)
            #             return

            # [BinanceLinearRestApi.on_send_order_failed] send order failed, orderid: 2505132110101000008 ETHUSDT, status code:400, msg:{"code":-2022,"msg":"ReduceOnly Order is rejected."}
            # [BinanceLinearRestApi.on_send_order_failed] send order failed, orderid: 2505132110101000012 ETHUSDT, status code:400, msg:{"code":-4164,"msg":"Order's notional must be no smaller than 20 (unless you choose reduce only)."}

            #     except (json.JSONDecodeError, AttributeError):
            #         pass
            self.main_engine.write_log(f"订单被拒绝: {order.rejected_reason}")
            rejected_reason = getattr(order, 'rejected_reason', '')
            error_info = json.loads(rejected_reason) if rejected_reason else {}
            error_code = int(error_info.get('code', -1))  # 转换为int类型

            # 处理1008错误 - 无限重试
            if error_code == -1008 and order.orderid not in self.retried_orderids:
                self.main_engine.write_log(f"检测到系统过载错误(1008),准备重试订单: {order.orderid}")
                self.retried_orderids.add(order.orderid)
                self.retry_order(order)
                return
            
            # 处理4164错误 - 只重试一次，放大10倍volume
            if self.test_4164_error and error_code == -4164 and order.orderid not in self.retried_4164_orderids:
                self.main_engine.write_log(f"检测到最小名义价值错误(4164),准备重试订单: {order.orderid}")
                self.retried_4164_orderids.add(order.orderid)
                # 增加数量重试
                self.retry_order(order, increase_volume=True)
                return
            
        
    def process_trade_event(self, event: Event):
        """处理成交事件"""
        trade = event.data
        if trade.vt_symbol != self.test_vt_symbol:
            return

        msg = (f"收到成交回报 {trade.vt_symbol} {trade.direction} {trade.offset} "
               f"价格={trade.price} 数量={trade.volume}")
        self.main_engine.write_log(msg)

    def retry_order(self, order, increase_volume: bool = False):
        """重试发送订单"""
        # 创建新的委托请求
        volume = order.volume * 10 if increase_volume else order.volume  # 4164错误时增加数量

        req = OrderRequest(
            symbol=order.symbol,
            exchange=order.exchange,
            direction=order.direction,
            offset=order.offset,
            type=order.type,
            price=order.price,
            volume=volume
        )

        # 发送订单
        self.main_engine.write_log(f"重试发送订单: {order.vt_symbol}, 数量: {volume}")
        vt_orderid = self.main_engine.send_order(req, "BINANCE_LINEAR")

        return vt_orderid

    def send_test_order(self):
        """发送测试订单"""
        # 等待tick数据和合约信息
        while not self.tick or not self.contract:
            time.sleep(1)

        # 计算开仓数量
        min_notional = self.contract.extra.get("min_notional", 0)
        if not min_notional:
            self.main_engine.write_log("无法获取最小名义价值")
            return

        price = self.tick.ask_price_1
        if not price:
            self.main_engine.write_log("无效的行情价格")
            return

        # 测试4164错误时使用较小的数量
        if self.test_4164_error:
            volume = (min_notional * 0.2) / price  # 使用0.2倍最小名义价值来触发4164错误
        else:
            volume = (min_notional * 1.1) / price

        volume = round_to(volume, self.contract.min_volume)

        if volume <= 0:
            self.main_engine.write_log(f"计算得到的数量无效: {volume}")
            return

        # 创建测试订单
        req = OrderRequest(
            symbol=self.test_symbol,
            exchange=self.test_exchange,
            direction=Direction.LONG,
            offset=Offset.OPEN,
            type=OrderType.LIMIT,
            price=self.tick.ask_price_5,
            volume=volume
        )

        # 发送订单
        vt_orderid = self.main_engine.send_order(req, "BINANCE_LINEAR")

        # 记录订单信息
        self.main_engine.write_log(f"发送测试订单: {self.test_vt_symbol}")
        self.main_engine.write_log(f"方向: {Direction.LONG} {Offset.OPEN}")
        self.main_engine.write_log(f"价格: {self.tick.ask_price_5}")
        self.main_engine.write_log(f"数量: {volume}")
        self.main_engine.write_log(f"订单号: {vt_orderid}")

    def run(self):
        """运行应用"""
        try:
            self.main_engine.write_log("重试订单测试应用已启动")
            time.sleep(5)  # 等待连接
            self.send_test_order()  # 发送测试订单
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            self.main_engine.write_log("应用正在关闭...")
        finally:
            self.close()

    def close(self):
        """关闭应用"""
        self.main_engine.close()

def main():
    """主函数"""
    app = RetryOrderTestApp()
    app.run()

if __name__ == "__main__":
    main()