from vnpy.event import EventEngine
from vnpy.trader.engine import MainEngine
from vnpy.trader.utility import load_json
import time
import argparse

from vnpy_algotrading.base import EVENT_ALGO_LOG
from vnpy_algotrading.dispatch_algo_ref_symbol_lock import DispatchEngine

# 全局变量
TEST_ENABLED = True

class DispatchApp:
    def __init__(self, test_dispatch=False):
        """初始化应用"""
        self.event_engine = EventEngine(interval=0.5)
        self.main_engine = MainEngine(self.event_engine)
        self.test_dispatch = test_dispatch
        self.init_engines()

    def init_engines(self):
        """初始化引擎"""
        # 添加币安网关
        from prod.binance_linear_gateway_log import BinanceLinearGateway
        self.main_engine.add_gateway(BinanceLinearGateway)
        setting = load_json("connect_binance_testnet.json")
        self.main_engine.connect(setting, "BINANCE_LINEAR")
        self.main_engine.write_log("接口添加成功")

        # 添加K线生成引擎
        from prod.barGen_redis_engine import BarGenEngine
        bar_gen_engine = self.main_engine.add_engine(BarGenEngine)
        bar_gen_engine.start()
        self.main_engine.write_log("添加Bar生成引擎")

        # 记录算法引擎日志
        log_engine = self.main_engine.get_engine('log')
        self.event_engine.register(EVENT_ALGO_LOG, log_engine.process_log_event)
        
        # 添加订单分发引擎
        self.dispatch_engine = self.main_engine.add_engine(DispatchEngine)
        
        # 启动订单分发引擎
        self.dispatch_engine.start(test_enabled=self.test_dispatch)
        self.main_engine.write_log(f"订单分发引擎启动成功，测试模式：{'开启' if self.test_dispatch else '关闭'}")

    def run(self):
        """运行应用"""
        try:
            self.main_engine.write_log("订单分发应用已启动")
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            self.main_engine.write_log("应用正在关闭...")
        finally:
            self.close()

    def close(self):
        """关闭应用"""
        # 关闭订单分发引擎
        self.dispatch_engine.stop()
        self.main_engine.write_log("订单分发引擎已关闭")
        
        self.main_engine.close()

def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="启动订单分发应用")
    parser.add_argument("--test", action="store_true", help="启用测试模式")
    args = parser.parse_args()
    
    # 创建并运行应用
    app = DispatchApp(test_dispatch=args.test)
    app.run()

if __name__ == "__main__":
    main()