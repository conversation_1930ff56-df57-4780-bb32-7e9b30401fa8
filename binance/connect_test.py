import websocket
import json
import time
import requests
import logging
from datetime import datetime
import threading

# 配置日志
logging.basicConfig(filename='connection_test.log', level=logging.INFO,
                    format='%(asctime)s - %(levelname)s - %(message)s')


# 定义连接测试函数
def test_baidu_connection():
    start_time = time.time()
    try:
        response = requests.get('http://www.baidu.com', timeout=10)
        if response.status_code == 200:
            elapsed_time = time.time() - start_time
            logging.info(f'成功连接到 www.baidu.com，用时 {elapsed_time:.2f} 秒')
            print(f'成功连接到 www.baidu.com，用时 {elapsed_time:.2f} 秒')
        else:
            logging.warning(f'无法连接到 www.baidu.com，HTTP状态码：{response.status_code}')
            print(f'无法连接到 www.baidu.com，HTTP状态码：{response.status_code}')
    except Exception as e:
        elapsed_time = time.time() - start_time
        logging.error(f'无法连接到 www.baidu.com，尝试用时 {elapsed_time:.2f} 秒，错误：{e}')
        print(f'无法连接到 www.baidu.com，尝试用时 {elapsed_time:.2f} 秒，错误：{e}')


def on_message(ws, message):
    data = json.loads(message)
    if "id" in data and data["id"] == 1:  # 订阅确认消息
        elapsed_time = time.time() - ws.start_time
        print(f"WebSocket订阅确认，用时 {elapsed_time:.2f} 秒")
        logging.info(f'Binance WebSocket订阅确认，用时 {elapsed_time:.2f} 秒')
    elif "stream" in data:  # 行情数据
        elapsed_time = time.time() - ws.start_time
        print(f"WebSocket收到行情，用时 {elapsed_time:.2f} 秒")
        logging.info(f'Binance WebSocket收到行情，用时 {elapsed_time:.2f} 秒')
        ws.close()  # 收到行情后关闭连接


def on_error(ws, error):
    print(f'WebSocket错误: {error}')


def on_close(ws, close_status_code, close_msg):
    print('WebSocket连接关闭')


def on_open(ws):
    elapsed_time = time.time() - ws.start_time
    print(f'WebSocket连接已建立，用时 {elapsed_time:.2f} 秒')
    logging.info(f'Binance WebSocket连接已建立，用时 {elapsed_time:.2f} 秒')
    # 发送订阅消息
    subscribe_message = {
        'method': 'SUBSCRIBE',
        'params': ['btcusdt@ticker'],
        'id': 1
    }
    ws.send(json.dumps(subscribe_message))


def test_binance_ws_connection():
    ws = None
    try:
        ws = websocket.WebSocketApp('wss://fstream.binance.com/stream',
                                  on_message=on_message,
                                  on_error=on_error,
                                  on_close=on_close,
                                  on_open=on_open)
        
        # 记录开始时间
        ws.start_time = time.time()
        
        # 创建WebSocket运行线程
        ws_thread = threading.Thread(target=lambda: ws.run_forever(
            proxy_type='http', 
            http_proxy_host='localhost', 
            http_proxy_port=7890
        ))
        ws_thread.daemon = True  # 设置为守护线程，主线程结束时会自动结束
        ws_thread.start()
        
        # 等待线程结束
        ws_thread.join(timeout=10)  # 设置10秒超时，防止永久等待
        
    except Exception as e:
        elapsed_time = time.time() - ws.start_time if ws and hasattr(ws, 'start_time') else 0
        logging.error(f'Binance WebSocket连接测试失败，用时 {elapsed_time:.2f} 秒，错误：{e}')
        print(f'Binance WebSocket连接测试失败，用时 {elapsed_time:.2f} 秒，错误：{e}')
    finally:
        if ws:
            ws.close()


# 主循环，每分钟执行一次连接测试
if __name__ == '__main__':
    while True:
        print(f'开始测试连接 - {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
        logging.info(f'开始测试连接 - {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')

        test_baidu_connection()
        test_binance_ws_connection()

        print(f'完成测试连接 - {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
        logging.info(f'完成测试连接 - {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')

        # 每分钟执行一次
        time.sleep(60)