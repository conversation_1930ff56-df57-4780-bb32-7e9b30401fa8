# 获取账户损益资金流水(USER_DATA)
接口描述
获取账户损益资金流水

HTTP请求
GET /dapi/v1/income

请求权重
20

请求参数
名称	类型	是否必需	描述
symbol	STRING	NO	交易对
incomeType	STRING	NO	收益类型 "TRANSFER","WELCOME_BONUS", "FUNDING_FEE", "REALIZED_PNL", "COMMISSION", "INSURANCE_CLEAR", "DELIVERED_SETTELMENT"
startTime	LONG	NO	起始时间
endTime	LONG	NO	结束时间
page	INT	NO	分页数
limit	INT	NO	返回的结果集数量 默认值:100 最大值:1000
recvWindow	LONG	NO	
timestamp	LONG	YES	
如果incomeType没有发送,返回所有类型账户损益资金流水。
"trandId" 在相同用户的同一种收益流水类型中是唯一的。
startTime与endTime间隔不能超过1年回
响应示例
[
	{
    	"symbol": "", // 交易对,仅针对涉及交易对的资金流
    	"incomeType": "TRANSFER",	// 资金流类型
    	"income": "-0.37500000", // 资金流数量,正数代表流入,负数代表流出
    	"asset": "BTC", // 资产内容
    	"info":"WITHDRAW", // 备注信息,取决于流水类型
    	"time": 1570608000000, // 时间
    	"tranId":"**********",		// 划转ID
    	"tradeId":""					// 引起流水产生的原始交易ID
	},
	{
   		"symbol": "BTCUSD_200925",
    	"incomeType": "COMMISSION", 
    	"income": "-0.********",
    	"asset": "BTC",
    	"info":"",
    	"time": *************,
    	"tranId":"**********",		
    	"tradeId":"2059192"					
	}
]

# 用户持仓风险V3 (USER_DATA)
接口描述
查询持仓风险，仅返回有持仓或挂单的交易对

HTTP请求
GET /fapi/v3/positionRisk

请求权重
5

请求参数
名称	类型	是否必需	描述
symbol	STRING	NO	
recvWindow	LONG	NO	
timestamp	LONG	YES	
注意

请与账户推送信息ACCOUNT_UPDATE配合使用，以满足您的及时性和准确性需求。
响应示例
单向持仓模式下：

[
  {
        "symbol": "ADAUSDT",
        "positionSide": "BOTH",               // 持仓方向
        "positionAmt": "30",
        "entryPrice": "0.385",
        "breakEvenPrice": "0.385077",
        "markPrice": "0.********",
        "unRealizedProfit": "0.********",     // 持仓未实现盈亏 
        "liquidationPrice": "0",
        "isolatedMargin": "0",
        "notional": "12.********",
        "marginAsset": "USDT",
        "isolatedWallet": "0",
        "initialMargin": "0.********",        // 初始保证金
        "maintMargin": "0.********",          // 维持保证金
        "positionInitialMargin": "0.********",// 仓位初始保证金
        "openOrderInitialMargin": "0",        // 订单初始保证金
        "adl": 2,
        "bidNotional": "0",                   
        "askNotional": "0",                   
        "updateTime": *************           // 更新时间
  }
]

双向持仓模式下：

[
  {
        "symbol": "ADAUSDT",
        "positionSide": "LONG",               
        "positionAmt": "30",
        "entryPrice": "0.385",
        "breakEvenPrice": "0.385077",
        "markPrice": "0.********",
        "unRealizedProfit": "0.********",    
        "liquidationPrice": "0",
        "isolatedMargin": "0",
        "notional": "12.********",
        "marginAsset": "USDT",
        "isolatedWallet": "0",
        "initialMargin": "0.********",        
        "maintMargin": "0.********",          
        "positionInitialMargin": "0.********",
        "openOrderInitialMargin": "0",       
        "adl": 2,
        "bidNotional": "0",                  
        "askNotional": "0",                
        "updateTime": *************
  },
  {
        "symbol": "COMPUSDT",
        "positionSide": "SHORT",
        "positionAmt": "-1.000",
        "entryPrice": "70.92841",
        "breakEvenPrice": "70.900038636",
        "markPrice": "49.72023376",
        "unRealizedProfit": "21.20817624",
        "liquidationPrice": "2260.56757210",
        "isolatedMargin": "0",
        "notional": "-49.72023376",
        "marginAsset": "USDT",
        "isolatedWallet": "0",
        "initialMargin": "2.48601168",
        "maintMargin": "0.49720233",
        "positionInitialMargin": "2.48601168",
        "openOrderInitialMargin": "0",
        "adl": 2,
        "bidNotional": "0",
        "askNotional": "0",
        "updateTime": 1708943511656
  }
]

# 账户成交历史 (USER_DATA)
接口描述
获取某交易对的成交历史

HTTP请求
GET /fapi/v1/userTrades

请求权重
5

请求参数
名称	类型	是否必需	描述
symbol	STRING	YES	交易对
orderId	LONG	NO	必须要和参数symbol一起使用
startTime	LONG	NO	起始时间
endTime	LONG	NO	结束时间
fromId	LONG	NO	返回该fromId及之后的成交，缺省返回最近的成交
limit	INT	NO	返回的结果集数量 默认值:500 最大值:1000.
recvWindow	LONG	NO	
timestamp	LONG	YES	
如果startTime 和 endTime 均未发送, 只会返回最近7天的数据。
startTime 和 endTime 的最大间隔为7天
本接口仅支持最近6个月历史交易的查询
响应示例
[
  {
  	"buyer": false,	// 是否是买方
  	"commission": "-0.07819010", // 手续费
  	"commissionAsset": "USDT", // 手续费计价单位
  	"id": 698759,	// 交易ID
  	"maker": false,	// 是否是挂单方
  	"orderId": 25851813, // 订单编号
  	"price": "7819.01",	// 成交价
  	"qty": "0.002",	// 成交量
  	"quoteQty": "15.63802",	// 成交额
  	"realizedPnl": "-0.91539999",	// 实现盈亏
  	"side": "SELL",	// 买卖方向
  	"positionSide": "SHORT",  // 持仓方向
  	"symbol": "BTCUSDT", // 交易对
  	"time": 1569514978020 // 时间
  }
]

# 获取合约交易历史下载Id(USER_DATA)
接口描述
获取合约交易历史下载Id

HTTP请求
GET /fapi/v1/trade/asyn

请求权重
5

请求参数
名称	类型	是否必需	描述
startTime	LONG	YES	起始时间，ms格式时间戳
endTime	LONG	YES	结束时间，ms格式时间戳
recvWindow	LONG	NO	
timestamp	LONG	YES	
存在每月5次的请求限制，网页端和Rest接口下载次数共用。
startTime与endTime间隔不能超过1年
响应示例
{
	"avgCostTimestampOfLast30d":7241837, //过去30天平均数据下载时间
  	"downloadId":"546975389218332672",   //下载Id
}

# 通过下载Id获取合约交易历史下载链接 (USER_DATA)
接口描述
通过下载Id获取合约交易历史下载链接

HTTP请求
GET /fapi/v1/trade/asyn/id

请求权重
5

请求参数
名称	类型	是否必需	描述
downloadId	STRING	YES	通过下载id 接口获取
recvWindow	LONG	NO	
timestamp	LONG	YES	
下载链接有效期：24小时。
响应示例
响应:

{
	"downloadId":"545923594199212032", // 下载Id
  	"status":"completed",     // 状态，枚举类型：completed 已完成，processing 处理中
  	"url":"www.binance.com",  // 适配该笔ID请求的下载链接       
  	"notified":true,          // 忽略
  	"expirationTimestamp":1645009771000,  // 晚于该时间戳之后链接将自动失效
  	"isExpired":null,
}

或 (服务器仍在处理中会返回)

{
	"downloadId":"545923594199212032",
  	"status":"processing",
  	"url":"", 
  	"notified":false,
  	"expirationTimestamp":-1
  	"isExpired":null, 	
}


# 查询资金费率历史 U本位合约
接口描述
查询资金费率历史

HTTP请求
GET /fapi/v1/fundingRate

请求权重
和GET /fapi/v1/fundingInfo共享500/5min/IP

请求参数
名称	类型	是否必需	描述
symbol	STRING	NO	交易对
startTime	LONG	NO	起始时间
endTime	LONG	NO	结束时间
limit	INT	NO	默认值:100 最大值:1000
如果 startTime 和 endTime 都未发送, 返回最近 limit 条数据.
如果 startTime 和 endTime 之间的数据量大于 limit, 返回 startTime + limit情况下的数据。
响应示例
[
	{
    	"symbol": "BTCUSDT",			// 交易对
    	"fundingRate": "-0.03750000",	// 资金费率
    	"fundingTime": 1570608000000,	// 资金费时间
        "markPrice": "34287.54619963"   // 资金费对应标记价格
	},
	{
   		"symbol": "BTCUSDT",
    	"fundingRate": "0.00010000",
    	"fundingTime": *************,
        "markPrice": "34287.54619963"   // 资金费对应标记价格
	}
]


# 查询永续合约资金费率历史 币本位合约
接口描述
查询永续合约资金费率历史

HTTP请求
GET /dapi/v1/fundingRate

请求权重
1

请求参数
名称	类型	是否必需	描述
symbol	STRING	YES	交易对
startTime	LONG	NO	起始时间
endTime	LONG	NO	结束时间
limit	INT	NO	默认值:100 最大值:1000
对非永续合约，将返回空列表
响应示例
[
	{
		"symbol": "BTCUSD_PERP",	// 交易对
  		"fundingTime": 1596038400000,	// 资金费时间
  		"fundingRate": "-0.00300000"	// 资金费率
  	},
 	{
 		"symbol": "BTCUSD_PERP",
  		"fundingTime": 1596067200000,
  		"fundingRate": "-0.00300000"
  	}
]