最近很多朋友关心来问，
我统一发个朋友圈回复一下。
我们海外科技公司团队 Quantweb3 打造的
NexusTrader 上线两天 GitHub 已经收获了
60 多星
这是一个专为机构级量化交易打造的数字
货币高性能开源平台，支持大规模资本管
理和复杂策略开发。与现在流行的开源框
架 hummingbot\ccxt\Freqtrade 相比，我们
的速度、稳定性和并行支持都全面领先。
欢迎同行朋友们测试或是给我们贡献代码。
功能亮点：
🔥 极致性能事件循环：采用 uvloop，比
默认 asyncio 快 2-4 倍。
⚡ 顶级 WebSocket 框架：基于 picows，
媲美 C++ 的 Boost.Beast，远超
websockets 和 aiohttp。
📊 高效数据处理：使用 msgspec 序列化，
性能全面超越 orjson、ujson 和 json。
✔️ 高效订单管理：基于 asyncio.Queue，
轻松处理高并发订单。
🛠️ Rust 核心加持：MessageBus 和 Clock
模块采用 Rust 实现，结合速度与可靠性。
✅ 完全开源，MIT 许可，免费使用
GitHub 项目链接：https://github.com/Quantweb3-com/NexusTrader?tab=readme-ov-file
文档链接：https://nexustrader.readthedocs.io/en/latest/