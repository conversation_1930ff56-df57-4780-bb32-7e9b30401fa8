# RPC行情主备切换方案

## 一、现状
- RPC服务端提供行情数据
- 客户端通过RPC Gateway订阅接收

## 二、架构
- 服务端client: RpcServiceApp + RpcEngine + BinanceGateway
- 客户端gateway: RpcGateway (REQ-REP + PUB-SUB)

## 三、主备方案

### 3.1 基本原则
- 客户端内部实现切换

### 3.2 订阅策略
1. 主client:
   - 订阅BTC/ETH监控状态
   - 被动触发合约订阅

2. 备client:
   - 仅订阅BTC/ETH监控状态

### 3.3 切换机制
1. 健康检测:
   - 两者BTC/ETH最新行情距今都超60秒触发切换

2. 状态监控:
   - 正常: 30分钟企业微信通知
   - 异常: 即时企业微信告警

### 3.4 切换处理
- 主异常备正常时切换
- 备接管订阅，主恢复监控.
- 当前非主且主恢复5min后再切换回主

## 四、实现要点

### 4.1 代码改造
-  RpcGateway:
   - 主备client实例
   - 健康检测逻辑
   - 切换控制逻辑

### 4.2 配置项
- 超时阈值: 60秒
- 通知间隔: 30分钟
- 监控币种: BTC/ETH
- 主备接口参数

## 五、升级功能对比

### 5.1 服务端升级 (run_rpc_engine2.py vs run_rpc_engine.py)

#### 新增功能:
1. **客户端管理优化**
   - 使用UUID生成客户端ID，替代简单计数
   - 客户端集合管理，支持多客户端订阅同一标的
   - 客户端断开连接时自动清理订阅
   - 订阅时不限制client_id是否已存在，动态添加新客户端
   - 生成分配client_id时自动去重

2. **订阅管理增强**
   - 从计数模式改为客户端集合模式
   - 支持多客户端订阅同一标的，只发送一次订阅请求
   - 退订缓存机制，10秒后才真正退订
   - 对monitor_vt_symbols的维护移动到服务端
   - 忽略对monitor_vt_symbols的退订请求
   - 客户端断联时自动退订其所有订阅的标的

3. **监控告警系统**
   - 集成企业微信告警管理器
   - tick超时自动告警和数据库记录
   - 30分钟正常状态通知
   - 超时标的自动重新订阅
   - 服务端主动维护监控标的的订阅和测活

4. **健康检测**
   - 60秒定时检查所有订阅标的tick状态
   - 超时标的自动重新订阅
   - 详细的活跃订阅状态日志
   - 服务端负责监控标的的健康检测和通知

5. **数据时序保证**
   - tick和bar数据时间不能倒序
   - bar数据自动补充缺失时间段
   - 时间顺序检查和数据完整性保证

### 5.2 客户端网关升级 (rpc_gateway_quote2.py vs rpc_gateway_quote.py)

#### 新增功能:
1. **主备架构**
   - 支持多服务器配置，同时连接所有服务器
   - 主备自动切换机制
   - 主服务器恢复后自动切回
   - 所有服务器始终保持连接状态

2. **健康检测机制**
   - 监控指定标的(BTC/ETH)数据超时检测
   - 30秒间隔健康检查
   - 所有监控标的都超时才触发切换
   - 客户端只负责测活，不维护监控标的订阅

3. **智能切换逻辑**
   - 主服务器异常时切换到健康的备服务器
   - 主服务器恢复且稳定5分钟后自动切回
   - 切换时自动对当前服务器退订再对新服务器重新订阅所有标的
   - 使用线程锁保证切换过程的线程安全

4. **企业微信告警**
   - 服务器切换告警通知
   - 主服务器恢复通知
   - 连接失败告警

5. **订阅管理**
   - 区分监控标的和业务标的
   - 监控标的由服务端维护，客户端不主动订阅
   - 业务标的只订阅到当前活跃服务器
   - 退订时忽略monitor_vt_symbols

6. **数据处理优化**
   - tick和bar数据时间倒序检查
   - bar数据缺失时间段自动补充
   - 只处理当前活跃服务器的数据
   - 线程安全的数据处理机制

### 5.3 应用层升级 (run_evo_rpc_algo2.py vs run_evo_rpc_algo.py)

#### 新增功能:
1. **配置管理**
   - 支持多服务器配置
   - 健康检查间隔配置
   - 数据超时阈值配置
   - 监控标的配置
   - 默认配置自动生成和保存

2. **网关集成**
   - 使用升级版RpcGateway (rpc_gateway_quote2)
   - 支持主备切换的行情订阅
   - 配置文件结构从单服务器升级为多服务器数组

3. **兼容性保证**
   - 保持与原版本相同的启动参数和接口
   - 无缝升级，不影响现有业务逻辑
   - 配置文件自动迁移和兼容

## 六、技术特性

### 6.1 高可用性
- 多服务器同时连接，无单点故障
- 自动故障检测和切换
- 主服务器恢复后自动切回

### 6.2 数据一致性
- 监控标的持续订阅确保健康检测
- 切换时重新订阅所有业务标的
- 客户端ID管理避免重复订阅

### 6.3 监控告警
- 企业微信实时告警
- 数据库状态记录
- 详细的日志记录

### 6.4 性能优化
- 只处理行情数据提高回调性能
- 客户端集合管理减少重复订阅
- 定时清理机制避免内存泄漏
- 线程安全锁保护关键数据操作
- 时间顺序检查避免无效数据处理

## 七、系统流程图

### 7.1 客户端测活和主备切换流程

```mermaid
graph TD
    A[客户端启动] --> B[连接所有服务器]
    B --> C[设置主服务器为活跃]
    C --> D[注册定时器事件]
    D --> E[定时健康检查]
    
    E --> F{检查当前服务器健康状态}
    F -->|不健康| H[尝试切换到备服务器]
    F -->|健康| G[检查主服务器恢复]
    
    H --> I{找到健康的备服务器?}
    I -->|是| J[退订当前服务器所有标的]
    I -->|否| K[记录所有服务器不可用]
    
    J --> L[切换活跃客户端]
    L --> M[重新订阅所有标的]
    M --> N[记录切换时间]
    N --> G
    
    G --> O{当前是主服务器?}
    O -->|是| E
    O -->|否| P{主服务器健康且切换超过5分钟?}
    P -->|是| Q[切回主服务器]
    P -->|否| E
    Q --> J
    
    K --> G
    
    style A fill:#e1f5fe
    style L fill:#fff3e0
    style Q fill:#f3e5f5
```

### 7.2 服务端测活和通知流程

```mermaid
graph TD
    A[服务端启动] --> B[初始化监控标的订阅]
    B --> C[启动定时健康检查]
    C --> D[60秒间隔检查所有标的]
    
    D --> E{检查标的tick超时?}
    E -->|正常| F[30分钟正常状态通知]
    E -->|超时| G[企业微信告警]
    
    G --> H[数据库记录异常]
    H --> I[自动重新订阅超时标的]
    I --> J[记录重订阅日志]
    J --> D
    
    F --> K{是否到达通知间隔?}
    K -->|是| L[发送正常状态通知]
    K -->|否| D
    L --> D
    
    M[客户端连接] --> N[生成UUID客户端ID]
    N --> O[添加到客户端集合]
    O --> P[自动订阅监控标的]
    
    Q[客户端断开] --> R[清理客户端订阅]
    R --> S[从客户端集合移除]
    S --> T{标的还有其他客户端?}
    T -->|否| U[10秒后真正退订]
    T -->|是| V[保持订阅状态]
    
    style A fill:#e8f5e8
    style G fill:#ffebee
    style L fill:#e3f2fd
    style P fill:#fff8e1
```

### 7.3 数据处理和时序保证流程

```mermaid
graph TD
    A[接收数据] --> B{数据类型?}
    B -->|Tick| C[检查Tick时间顺序]
    B -->|Bar| D[检查Bar时间顺序]
    
    C --> E{时间倒序?}
    E -->|是| F[忽略倒序数据]
    E -->|否| G[更新最后时间]
    G --> H[推送Tick事件]
    
    D --> I{时间倒序或重复?}
    I -->|是| J[忽略无效数据]
    I -->|否| K[检查时间间隔]
    
    K --> L{缺失Bar?}
    L -->|是| M[补充缺失Bar]
    L -->|否| N[更新最后Bar]
    
    M --> O[生成补充Bar事件]
    O --> N
    N --> P[推送Bar事件]
    
    Q[多服务器数据] --> R{当前活跃服务器?}
    R -->|是| A
    R -->|否| S[忽略非活跃服务器数据]
    
    style F fill:#ffcdd2
    style J fill:#ffcdd2
    style M fill:#fff3e0
    style H fill:#e8f5e8
    style P fill:#e8f5e8
```