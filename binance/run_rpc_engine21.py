import signal
import sys
from time import sleep

from vnpy.event import EventEngine
from vnpy.trader.engine import MainEngine
from vnpy.trader.utility import load_json

from prod.binance_linear_gateway2 import BinanceLinearGateway
from vnpy_rpcservice.rpc_service.engine_quote2 import RpcEngine, EVENT_RPC_LOG


class RpcServiceApp:
    """RPC服务应用 - 简化为应用层调度"""
    
    def __init__(self):
        """构造函数"""
        self.event_engine = EventEngine()
        self.main_engine = MainEngine(self.event_engine)
        self.rpc_engine: RpcEngine = None
        self.gateway = None
        
        self._active = True
        
        self.register_event()
        self.init_engines()

    def write_log(self, msg: str) -> None:
        """输出日志"""
        frame = sys._getframe(1)
        func_name = frame.f_code.co_name
        line_no = frame.f_lineno
        class_name = self.__class__.__name__
        formatted_msg = f"[{class_name}.{func_name}:{line_no}] {msg}"
        self.main_engine.write_log(formatted_msg)

    def register_event(self) -> None:
        """注册事件监听"""
        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

        # 在Windows平台上signal模块可能不完全支持
        if sys.platform != 'win32':
            # 忽略SIGTSTP信号(Ctrl+Z)，避免进程被挂起
            signal.signal(signal.SIGTSTP, signal.SIG_IGN)

        # 注册日志事件监听
        log_engine = self.main_engine.get_engine("log")
        self.event_engine.register(EVENT_RPC_LOG, log_engine.process_log_event)

    def _signal_handler(self, signum, frame):
        """信号处理函数"""
        sig_name = signal.Signals(signum).name
        self.write_log(f"收到信号: {sig_name}，准备关闭应用...")
        self._active = False

    def init_engines(self) -> None:
        """初始化引擎"""
        # 添加RPC服务引擎
        self.rpc_engine = self.main_engine.add_engine(RpcEngine)

        # 添加网关
        self.gateway = self.main_engine.add_gateway(BinanceLinearGateway, "RPC")
        
        # 设置网关到RPC引擎
        self.rpc_engine.set_gateway(self.gateway)

    def run(self) -> None:
        """运行应用"""
        self.write_log("RPC服务应用启动")

        # 启动RPC服务
        success = self.rpc_engine.start(
            "tcp://*:2015",
            "tcp://*:5102"
        )

        if not success:
            self.write_log("RPC服务启动失败，程序退出")
            self.close()
            return

        self.write_log(f"RPC服务已启动 - 请求地址：{self.rpc_engine.rep_address}，推送地址：{self.rpc_engine.pub_address}")

        # 连接网关
        self.gateway.connect(load_json("connect_binance_read.json"))
        self.write_log("网关连接成功")

        # 等待on_contract订阅监控标的
        sleep(1)
        self.rpc_engine.subscribe_monitor_symbols()

        while self._active:
            sleep(1)
        self.close()

    def close(self) -> None:
        """关闭应用"""
        self.write_log("应用正在关闭...")
        self.rpc_engine.close()
        self.main_engine.close()


def main() -> None:
    """主函数"""
    app = RpcServiceApp()
    app.run()


if __name__ == "__main__":
    main()
