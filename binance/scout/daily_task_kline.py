import schedule
import time
import os
from datetime import datetime, timedelta
from peewee import *
# from vnpy.usertools.db_status_manager import Status
# from vnpy.usertools.notify_tool import get_flag,report_we_alert
from vnpy.trader.setting import SETTINGS
from email.message import EmailMessage
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON>Multipart
from email.mime.application import MIMEApplication
from loguru import logger
import sys
import traceback
import glob
import threading
from vnpy.trader.constant import Exchange, Interval

# 实时监控K线
# 每整点+5分核实上一小时数据
# 每日8点核查上月数据量
symbol_list = ["1000BONKUSDT.BINANCE","1000FLOKIUSDT.BINANCE","1000PEPEUSDT.BINANCE","1000RATSUSDT.BINANCE","1000SATSUSDT.BINANCE","AAVEUSDT.BINANCE","AGLDUSDT.BINANCE","ALGOUSDT.BINANCE","APEUSDT.BINANCE","ARKMUSDT.BINANCE"]

from vnpy_mysql.mysql_database import DbBarData, db

def setup_logging():
    """设置日志配置"""
    # 确保日志目录存在
    log_dir = "kline_check"
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 移除默认的 sink
    logger.remove()
    
    # 添加控制台输出
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>",
        level="INFO"
    )
    
    # 添加文件输出
    log_path = os.path.join(log_dir, "daily_kline_{time:YYYY-MM-DD}.log")
    logger.add(
        log_path,
        rotation="00:00",  # 每天午夜切换新文件
        retention="7 days",  # 保留7天的日志
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {message}",
        encoding="utf-8",
        level="INFO"
    )
    
    logger.info("日志系统初始化完成")

def check_kline_count(symbol: str, start_time: datetime, end_time: datetime, expected_count: int) -> tuple:
    """检查指定时间范围内的K线数量"""
    try:
        symbol_name, exchange_name = symbol.split('.')
        count = DbBarData.select().where(
            (DbBarData.symbol == symbol_name) &
            (DbBarData.exchange == exchange_name) &
            (DbBarData.interval == Interval.MINUTE.value) &
            (DbBarData.datetime >= start_time) &
            (DbBarData.datetime < end_time)
        ).count()
        
        return count == expected_count, count
    except Exception as e:
        logger.error(f"检查K线数量时发生错误: {symbol}, {e}")
        return False, 0

# 每小时第10分钟执行
# 核查symbol_list中的上一个完整小时的分钟线数量是否是60
# 将不满足60的 symbol及数量汇总为一个string，打印出来
def hourly_check():
    """检查上一小时的分钟线数据完整性"""
    now = datetime.now()
    end_time = now.replace(minute=0, second=0, microsecond=0)
    start_time = end_time - timedelta(hours=1)
    
    incomplete_symbols = []
    
    for symbol in symbol_list:
        is_complete, count = check_kline_count(symbol, start_time, end_time, 60)
        if not is_complete:
            incomplete_symbols.append(f"{symbol}(实际:{count}/期望:60)")
    
    if incomplete_symbols:
        error_msg = f"上一小时({start_time.strftime('%Y-%m-%d %H:00')}~{end_time.strftime('%H:00')})以下币种分钟K线数据不完整:\n" + "\n".join(incomplete_symbols)
        logger.error(error_msg)
    else:
        logger.info(f"上一小时({start_time.strftime('%Y-%m-%d %H:00')}~{end_time.strftime('%H:00')})所有币种分钟K线数据完整")

# 每天8:20点执行1次
# 核查symbol_list中的上一周完整小时的分钟线数量是否是60*24*7
# 数量不对的symbol及数量汇总为一个string，打印出来
def daily_check():
    """检查上一周的分钟线数据完整性"""
    now = datetime.now()
    end_time = now.replace(hour=0, minute=0, second=0, microsecond=0)
    start_time = end_time - timedelta(days=7)
    expected_count = 60 * 24 * 7  # 一周的分钟数
    
    incomplete_symbols = []
    
    for symbol in symbol_list:
        is_complete, count = check_kline_count(symbol, start_time, end_time, expected_count)
        if not is_complete:
            incomplete_symbols.append(f"{symbol}(实际:{count}/期望:{expected_count})")
    
    if incomplete_symbols:
        error_msg = f"上周({start_time.strftime('%Y-%m-%d')}~{end_time.strftime('%Y-%m-%d')})以下币种分钟K线数据不完整:\n" + "\n".join(incomplete_symbols)
        logger.error(error_msg)
    else:
        logger.info(f"上周({start_time.strftime('%Y-%m-%d')}~{end_time.strftime('%Y-%m-%d')})所有币种分钟K线数据完整")
    
def run_with_timeout(job_func, timeout):
    # flag = get_flag()
    def job_wrapper():
        try:
            job_func()
        except Exception as e:
            logger.error(f"Task {job_func.__name__} failed: {e}")

    thread = threading.Thread(target=job_wrapper)
    thread.start()
    thread.join(timeout=timeout)
    if thread.is_alive():
        err_msg = f"Task {job_func.__name__} timed out and was terminated"
        logger.error(err_msg)
        # report_we_alert(f"{flag}_{err_msg},please check")

def run_test():
    """运行一次检查测试"""
    setup_logging()  # 确保日志已设置
    logger.info("开始运行测试检查...")
    
    try:
        logger.info("执行小时检查...")
        hourly_check()
        
        logger.info("执行每日检查...")
        daily_check()
        
        logger.info("测试检查完成")
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}\n{traceback.format_exc()}")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "-t":
        # 运行测试模式
        run_test()
    else:
        # 运行正常调度模式
        setup_logging()  # 初始化日志配置
        # 每小时的第10分钟执行任务
        for hour in range(24):  # 0到23小时
            schedule.every().day.at(f"{hour:02d}:10").do(run_with_timeout, hourly_check, timeout=900)
        
        schedule.every().day.at("08:20").do(run_with_timeout, daily_check, timeout=900)
        while True:
            schedule.run_pending()
            time.sleep(1)