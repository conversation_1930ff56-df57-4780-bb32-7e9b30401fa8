# 数据库配置
database:
  host: db
  port: 3306
  user: root
  password: 123456
  database: zh

# 用户配置
users:
  zhmain:
    work_dir: /opt/test/db_handler_ld
  <PERSON>:
    work_dir: /opt/test/db_handler_<PERSON>:
    work_dir: /opt/test/db_handler_ly
  Liub:
    work_dir: /opt/test/db_handler_liubin
  <PERSON>:
    work_dir: /opt/test/db_handler_gao<PERSON>:
    work_dir: /opt/test/db_handler_z<PERSON><PERSON>:
    work_dir: /opt/test/db_handler_xie
  Wangxy:
    work_dir: /opt/test/db_handler_wxy
  Zhangqf:
    work_dir: /opt/test/db_handler_zqf
  Zouyw:
    work_dir: /opt/test/db_handler_Zouyw
  zhuser1:
    work_dir: /opt/test/db_handler
  zhfund:
    work_dir: /opt/test/db_handler_zhfund
