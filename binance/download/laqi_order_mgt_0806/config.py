import os
from urllib.parse import quote_plus as urlquote

basedir = os.path.abspath(os.path.dirname(__file__))

# user = 'root'
# password = 'your_password'
# host = 'localhost'
# port = '3306'
# database_name = 'ping_test'

user = 'flask'
password = '^zhP@55word$'
host = 'localhost'
port = '3308'
database_name = 'scout'
class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key'
    SQLALCHEMY_DATABASE_URI = f'mysql+pymysql://{user}:{urlquote(password)}@{host}:{port}/{database_name}?charset=utf8mb4'
    SQLALCHEMY_TRACK_MODIFICATIONS = False