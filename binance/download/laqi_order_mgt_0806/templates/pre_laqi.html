{% extends "base.html" %}

{% block title %}Pre Laqi Orders Management{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>Pre Laqi Orders</h2>
    <a href="{{ url_for('add_pre_laqi_order') }}" class="btn btn-primary">Add Order</a>
</div>

<!-- 搜索框 -->
<div class="mb-3">
    <form method="GET" action="{{ url_for('pre_laqi') }}">
        <div class="input-group">
            <input type="text" class="form-control" name="search" placeholder="Search by User or Date (YYYYMMDD)" value="{{ search }}">
            <div class="input-group-append">
                <button class="btn btn-outline-secondary" type="submit">Search</button>
                {% if search %}
                <a href="{{ url_for('pre_laqi') }}" class="btn btn-outline-secondary">Clear</a>
                {% endif %}
            </div>
        </div>
    </form>
</div>

<!-- 订单表格 -->
<div class="table-responsive">
    <table class="table table-striped table-hover">
        <thead class="thead-dark">
            <tr>
                <th>User</th>
                <th>Ratio</th>
                <th>Type</th>
                <th>Trigger Value</th>
                <th>Status</th>
                <th>Create Date</th>
                <th>Update Date</th>
                <th>Expiry Date</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for order in orders %}
            <tr>
                <td>{{ order.user }}</td>
                <td>{{ order.ratio }}</td>
                <td>
                    {% if order.type == 0 %}
                        Higher than
                    {% else %}
                        Lower than
                    {% endif %}
                </td>
                <td>{{ order.trigger_value }}</td>
                <td>
                    {% if order.status == 0 %}
                        <span class="badge badge-warning">Monitoring</span>
                    {% elif order.status == 1 %}
                        <span class="badge badge-success">Triggered</span>
                    {% elif order.status == 2 %}
                        <span class="badge badge-secondary">Expired</span>
                    {% endif %}
                </td>
                <td>{{ order.create_date.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                <td>{{ order.update_date.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                <td>{{ order.expiry_date.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                <td>
                    {% if order.status == 0 %}
                        <a href="{{ url_for('edit_pre_laqi_order', order_id=order.id) }}" class="btn btn-sm btn-warning">Edit</a>
                    {% endif %}
                    <form method="POST" action="{{ url_for('delete_pre_laqi_order', order_id=order.id) }}" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this order?')">
                        <button type="submit" class="btn btn-sm btn-danger">Delete</button>
                    </form>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<!-- 分页 -->
<nav aria-label="Page navigation">
    <ul class="pagination justify-content-center">
        {% if pagination.has_prev %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('pre_laqi', page=pagination.prev_num, search=search) }}">&laquo; Previous</a>
        </li>
        {% endif %}
        
        {% for page_num in pagination.iter_pages() %}
            {% if page_num %}
                {% if page_num != pagination.page %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('pre_laqi', page=page_num, search=search) }}">{{ page_num }}</a>
                </li>
                {% else %}
                <li class="page-item active">
                    <span class="page-link">{{ page_num }}</span>
                </li>
                {% endif %}
            {% else %}
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
            {% endif %}
        {% endfor %}
        
        {% if pagination.has_next %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('pre_laqi', page=pagination.next_num, search=search) }}">Next &raquo;</a>
        </li>
        {% endif %}
    </ul>
</nav>

{% if not orders %}
<div class="alert alert-info">
    <h4>No Pre Laqi Orders Found</h4>
    <p>There are no pre laqi orders to display{% if search %} for your search "{{ search }}"{% endif %}.</p>
    <a href="{{ url_for('add_pre_laqi_order') }}" class="btn btn-primary">Create First Order</a>
</div>
{% endif %}

{% endblock %}