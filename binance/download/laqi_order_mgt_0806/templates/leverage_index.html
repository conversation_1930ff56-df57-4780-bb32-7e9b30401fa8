{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>Leverage Reset Management</h2>
    <a href="{{ url_for('reset_leverage') }}" class="btn btn-primary">Leverage</a>
</div>

<!-- 搜索框 -->
<div class="mb-3">
    <form method="GET" action="{{ url_for('leverage_index') }}">
        <div class="input-group">
            <input type="text" class="form-control" name="search" placeholder="Search for Username, Symbol or Create Date (YYYYMMDD)" value="{{ request.args.get('search', '') }}">
            <div class="input-group-append">
                <button class="btn btn-outline-secondary" type="submit">Search</button>
                {% if request.args.get('search', '') %}
                <a href="{{ url_for('leverage_index') }}" class="btn btn-outline-secondary">Clear</a>
                {% endif %}
            </div>
        </div>
    </form>
</div>

<!-- 任务列表 -->
<div class="table-responsive">
    <table class="table table-striped table-hover">
        <thead class="thead-dark">
            <tr>
                <th>Username</th>
                <th>Symbol</th>
                <th>Leverage</th>
                <th>Create Date</th>
                <th>Update Date</th>
                <th>Status</th>
                <th>Remarks</th>
            </tr>
        </thead>
        <tbody>
            {% for order in orders %}
            <tr>
                <td>{{ order.username }}</td>
                <td>{{ order.symbol }}</td>
                <td>{{ order.leverage }}</td>
                <td>{{ order.create_date.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                <td>{{ order.update_date.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                <td>
                    {% if order.status == 0 %}<span class="badge badge-warning">Order Sent</span>
                    {% elif order.status == 2 %}<span class="badge badge-success">Order Completed</span>
                    {% elif order.status == 5 %}<span class="badge badge-danger">Order Error</span>{% endif %}
                </td>
                <td>{{ order.remarks }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<!-- 分页 -->
<nav aria-label="Page navigation">
    <ul class="pagination justify-content-center">
        {% if pagination.has_prev %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('leverage_index', page=pagination.prev_num, search=request.args.get('search', '')) }}">&laquo; Previous</a>
        </li>
        {% endif %}
        
        {% for page_num in pagination.iter_pages() %}
            {% if page_num %}
                {% if page_num != pagination.page %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('leverage_index', page=page_num, search=request.args.get('search', '')) }}">{{ page_num }}</a>
                </li>
                {% else %}
                <li class="page-item active">
                    <span class="page-link">{{ page_num }}</span>
                </li>
                {% endif %}
            {% else %}
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
            {% endif %}
        {% endfor %}
        
        {% if pagination.has_next %}
        <li class="page-item">
            <a class="page-link" href="{{ url_for('leverage_index', page=pagination.next_num, search=request.args.get('search', '')) }}">Next &raquo;</a>
        </li>
        {% endif %}
    </ul>
</nav>

{% if not orders %}
<div class="alert alert-info">
    <h4>No Leverage Reset Orders Found</h4>
    <p>There are no leverage reset orders to display{% if request.args.get('search', '') %} for your search "{{ request.args.get('search', '') }}"{% endif %}.</p>
    <a href="{{ url_for('reset_leverage') }}" class="btn btn-primary">Create First Order</a>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    $.fn.editable.defaults.mode = 'inline';
    $('.editable').editable({
        success: function(response, newValue) {
            if(response.status == 'error') return response.msg;
        }
    });
});
</script>
{% endblock %} 