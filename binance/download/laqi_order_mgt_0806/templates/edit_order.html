{% extends "base.html" %}

{% block content %}
<h2>Edit Order</h2>
<form method="POST" id="editOrderForm">
    {{ form.hidden_tag() }}
    <div class="form-group">
        {{ form.symbol.label }}
        {{ form.symbol(class="form-control", placeholder="Please Input Symbol (eg. BTCUSDT.BINANCE)", value=order.symbol) }}
    </div>
    <div class="form-group">
        {{ form.ping_volume.label }}
        {{ form.ping_volume(class="form-control", placeholder="Please Input Ping Volume", value=order.ping_volume) }}
    </div>
    <div class="form-group">
        {{ form.user.label }}
        {{ form.user(class="form-control") }}
    </div>
    <div class="form-group">
        <a href="{{ url_for('index') }}" class="btn btn-secondary">Back</a>
        <button type="submit" class="btn btn-primary float-right" id="submitBtn">Complete Edit</button>
    </div>
</form>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('editOrderForm');
    const submitBtn = document.getElementById('submitBtn');
    let isSubmitting = false;
    
    form.addEventListener('submit', function(e) {
        if (isSubmitting) {
            e.preventDefault();
            return false;
        }
        
        // 禁用提交按钮，防止重复点击
        isSubmitting = true;
        submitBtn.disabled = true;
        submitBtn.innerHTML = '提交中...';
        submitBtn.classList.add('disabled');
        
        // 设置超时，如果30秒后还没有响应，重新启用按钮
        setTimeout(function() {
            if (isSubmitting) {
                // 不自动恢复，而是给用户提示
                submitBtn.innerHTML = '提交超时，请刷新页面重试';
                submitBtn.style.backgroundColor = '#ffc107'; // 黄色警告
                // 保持 isSubmitting = true，防止重复提交
            }
        }, 30000);
    });
    
    // 页面卸载时重置状态
    window.addEventListener('beforeunload', function() {
        isSubmitting = false;
        submitBtn.disabled = false;
        submitBtn.innerHTML = 'Complete Edit';
        submitBtn.classList.remove('disabled');
    });
});
</script>

<div class="mt-4">
    <h4>Order Information</h4>
    <ul>
        <li><strong>Created:</strong> {{ order.create_date.strftime('%Y-%m-%d %H:%M:%S') }}</li>
        <li><strong>Updated:</strong> {{ order.update_date.strftime('%Y-%m-%d %H:%M:%S') }}</li>
        <li><strong>Status:</strong> 
            {% if order.status == 0 %}<span class="badge badge-warning">Order Sent</span>
            {% elif order.status == 1 %}<span class="badge badge-secondary">Order Received</span>
            {% elif order.status == 2 %}<span class="badge badge-success">Order Completed</span>
            {% elif order.status == 3 %}<span class="badge badge-primary">Order Processing</span>
            {% elif order.status == 5 %}<span class="badge badge-danger">Order Error</span>{% endif %}
        </li>
    </ul>
</div>
{% endblock %}