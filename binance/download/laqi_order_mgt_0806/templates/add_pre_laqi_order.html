{% extends "base.html" %}

{% block title %}New Pre Laqi Order{% endblock %}

{% block content %}
<h2>Add Pre Laqi Order</h2>

<form method="POST">
    {{ form.hidden_tag() }}
    
    <div class="form-group">
        {{ form.user.label(class="form-label") }}
        {{ form.user(class="form-control") }}
        {% for error in form.user.errors %}
            <div class="text-danger">{{ error }}</div>
        {% endfor %}
    </div>
    
    <div class="form-group">
        {{ form.ratio.label(class="form-label") }}
        {{ form.ratio(class="form-control") }}
        {% for error in form.ratio.errors %}
            <div class="text-danger">{{ error }}</div>
        {% endfor %}
    </div>
    
    <div class="form-group">
        {{ form.type.label(class="form-label") }}
        {{ form.type(class="form-control") }}
        {% for error in form.type.errors %}
            <div class="text-danger">{{ error }}</div>
        {% endfor %}
    </div>
    
    <div class="form-group">
        {{ form.trigger_value.label(class="form-label") }}
        {{ form.trigger_value(class="form-control", step="0.01") }}
        {% for error in form.trigger_value.errors %}
            <div class="text-danger">{{ error }}</div>
        {% endfor %}
    </div>
    
    {% if show_confirmation %}
    <div class="alert alert-warning" id="confirmationAlert">
        <h5>⚠️ Warning</h5>
        {% if order_type == 0 %}
        <p><strong>Current Balance Is Already Higher Than The Trigger Value, Are You Sure You Want To Proceed?</strong></p>
        {% else %}
        <p><strong>Current Balance Is Already Lower Than The Trigger Value, Are You Sure You Want To Proceed?</strong></p>
        {% endif %}
        <p>Current Balance: <strong>{{ "%.2f"|format(current_balance) }}</strong></p>
        <p>Trigger Value: <strong>{{ form.trigger_value.data }}</strong></p>
        <div class="mt-3">
            <button type="button" class="btn btn-warning" onclick="proceedWithSubmission()">Yes, Proceed</button>
            <button type="button" class="btn btn-secondary" onclick="resetTriggerValue()">No, Reset Trigger Value</button>
        </div>
    </div>
    {% endif %}
    
    <div class="form-group">
        {{ form.confirmation_needed }}
        {{ form.user_confirmed }}
        {{ form.submit(class="btn btn-primary", id="submitBtn", style=("display: none" if show_confirmation else "display: inline-block")) }}
        <a href="{{ url_for('pre_laqi') }}" class="btn btn-secondary">Cancel</a>
    </div>
</form>

<div class="mt-4">
    <h4>Instructions</h4>
    <ul>
        <li><strong>User:</strong> Select the user for this Pre Laqi order</li>
        <li><strong>Ratio:</strong> Select the laqi ratio to be applied when triggered</li>
        <li><strong>Type:</strong> 
            <ul>
                <li><strong>Higher than:</strong> Order will get triggered when user's balance value is higher than trigger value</li>
                <li><strong>Lower than:</strong> Order will get triggered when user's balance value is lower than trigger value</li>
            </ul>
        </li>
        <li><strong>Trigger Value:</strong> The balance value that will trigger this order</li>
        <li><strong>Expiry:</strong> Order will automatically expire 12 hours after creation</li>
    </ul>
</div>

<script>
    // 确认对话框处理函数
    function proceedWithSubmission() {
        // 设置用户确认标志
        const userConfirmedField = document.querySelector('input[name="user_confirmed"]');
        if (userConfirmedField) {
            userConfirmedField.value = 'yes';
        }
        // 隐藏确认对话框
        const alertDiv = document.getElementById('confirmationAlert');
        if (alertDiv) {
            alertDiv.style.display = 'none';
        }
        // 显示Submit按钮
        const submitBtn = document.getElementById('submitBtn');
        if (submitBtn) {
            submitBtn.style.display = 'inline-block';
        }
        // 自动提交表单
        document.querySelector('form').submit();
    }

    function resetTriggerValue() {
        // 隐藏确认对话框
        const alertDiv = document.getElementById('confirmationAlert');
        if (alertDiv) {
            alertDiv.style.display = 'none';
        }
        // 清空trigger value并聚焦
        const triggerField = document.querySelector('input[name="trigger_value"]');
        if (triggerField) {
            triggerField.value = '';
            triggerField.focus();
        }
        // 重新显示提交按钮
        const submitBtn = document.getElementById('submitBtn');
        if (submitBtn) {
            submitBtn.style.display = 'inline-block';
        }
    }

    document.addEventListener('DOMContentLoaded', function() {
        const form = document.querySelector('form');
        const submitBtn = document.getElementById('submitBtn');
        let isSubmitting = false;
        
        if (form && submitBtn) {
            form.addEventListener('submit', function(e) {
                if (isSubmitting) {
                    e.preventDefault();
                    return false;
                }
                
                // 禁用提交按钮，防止重复点击
                isSubmitting = true;
                submitBtn.disabled = true;
                submitBtn.value = '提交中...';
                submitBtn.classList.add('disabled');
                
                // 设置超时，如果30秒后还没有响应，重新启用按钮
                setTimeout(function() {
                    if (isSubmitting) {
                        // 不自动恢复，而是给用户提示
                        submitBtn.value = '提交超时，请刷新页面重试';
                        submitBtn.style.backgroundColor = '#ffc107'; // 黄色警告
                        // 保持 isSubmitting = true，防止重复提交
                    }
                }, 30000);
            });
            
            // 页面卸载时重置状态
            window.addEventListener('beforeunload', function() {
                isSubmitting = false;
                submitBtn.disabled = false;
                submitBtn.value = 'Submit';
                submitBtn.classList.remove('disabled');
            });
        }
    });
    </script>
{% endblock %}