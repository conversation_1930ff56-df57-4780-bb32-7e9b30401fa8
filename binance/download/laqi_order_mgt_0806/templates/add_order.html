{% extends "base.html" %}

{% block content %}
<h2>New Ping Order</h2>
<form method="POST" id="orderForm">
    {{ form.hidden_tag() }}
    {{ form.submission_token() }}
    <div class="form-group">
        {{ form.symbol.label }}
        {{ form.symbol(class="form-control", placeholder="Please Input Symbol (eg. BTCUSDT.BINANCE)") }}
    </div>
    <div class="form-group">
        {{ form.ping_volume.label }}
        {{ form.ping_volume(class="form-control", placeholder="Please Input Ping Volume") }}
    </div>
    <div class="form-group">
        {{ form.user.label }}
        {{ form.user(class="form-control", placeholder="Please Select User") }}
    </div>
    <div class="form-group">
        {{ form.submit(class="btn btn-primary", id="submitBtn") }}
    </div>
</form>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('orderForm');
    const submitBtn = document.getElementById('submitBtn');
    let isSubmitting = false;
    
    form.addEventListener('submit', function(e) {
        if (isSubmitting) {
            e.preventDefault();
            return false;
        }
        
        // 禁用提交按钮，防止重复点击
        isSubmitting = true;
        submitBtn.disabled = true;
        submitBtn.innerHTML = '提交中...';
        submitBtn.classList.add('disabled');
        
        // 设置超时，如果30秒后还没有响应，重新启用按钮
        setTimeout(function() {
            if (isSubmitting) {
                // 不自动恢复，而是给用户提示
                submitBtn.innerHTML = '提交超时，请刷新页面重试';
                submitBtn.style.backgroundColor = '#ffc107'; // 黄色警告
                // 保持 isSubmitting = true，防止重复提交
            }
        }, 30000);
    });
    
    // 页面卸载时重置状态
    window.addEventListener('beforeunload', function() {
        isSubmitting = false;
        submitBtn.disabled = false;
        submitBtn.innerHTML = 'Submit';
        submitBtn.classList.remove('disabled');
    });
});
</script>
{% endblock %} 