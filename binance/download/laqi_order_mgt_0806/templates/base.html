<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Ping Orders{% endblock %}</title>
    <!-- 使用国内可访问的 Bootstrap CSS CDN -->
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/4.5.2/css/bootstrap.min.css">
    <!-- 添加 X-editable CSS -->
    <link href="https://cdn.bootcdn.net/ajax/libs/x-editable/1.5.1/bootstrap3-editable/css/bootstrap-editable.css" rel="stylesheet">
    {% block extra_css %}{% endblock %}
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-light bg-light">
        <div class="navbar-brand">
            <a class="mr-3" href="{{ url_for('index') }}">Ping</a>
            <span class="mx-1">|</span>
            <a class="ml-2 mr-2" href="{{ url_for('laqi_index') }}">Laqi</a>
            <span class="mx-1">|</span>
            <a class="ml-2 mr-2" href="{{ url_for('pre_laqi') }}">Pre Laqi</a>
            <span class="mx-1">|</span>
            <a class="ml-2 mr-2" href="{{ url_for('qr_index') }}">QR</a>
            <span class="mx-1">|</span>
            <a class="ml-2 mr-2" href="{{ url_for('position_monitor') }}">Position</a>
            <span class="mx-1">|</span>
            <a class="ml-2 mr-2" href="{{ url_for('leverage_index') }}">Leverage</a>
            <span class="mx-1">|</span>
            <a class="ml-2" href="{{ url_for('capital_monitor') }}">Capital</a>
        </div>
        <div class="navbar-nav ml-auto">
            {% if current_user.is_authenticated %}
                {% if current_user.is_admin %}
                    <a class="nav-item nav-link" href="{{ url_for('users') }}">User Management</a>
                {% endif %}
                <a class="nav-item nav-link" href="{{ url_for('logout') }}">Logout</a>
            {% else %}
                <a class="nav-item nav-link" href="{{ url_for('login') }}">Login</a>
            {% endif %}
        </div>
    </nav>
    <div class="container mt-4 px-1">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }}">{{ message }}</div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        {% block content %}{% endblock %}
    </div>
    <!-- 使用国内可访问的 jQuery 和 Popper.js CDN -->
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/popper.js/2.5.3/umd/popper.min.js"></script>
    <!-- 使用国内可访问的 Bootstrap JS CDN -->
    <script src="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <!-- 添加 X-editable JS -->
    <script src="https://cdn.bootcdn.net/ajax/libs/x-editable/1.5.1/bootstrap3-editable/js/bootstrap-editable.min.js"></script>
    <script>
    // 移除旧的输入监听逻辑
    </script>
    {% block extra_js %}{% endblock %}
</body>
</html>
