{% extends "base.html" %}

{% block title %}Capital Monitor{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">    
    <h2>Capital Monitor</h2>
</div>

<!-- 数据表格 -->
<div class="table-responsive">
    <table class="table table-striped table-hover" id="capitalTable">
        <thead class="thead-dark">
            <tr>
                <th class="sortable" data-column="user" style="cursor: pointer;">
                    User 
                    <span class="sort-indicator" data-column="user">
                        <i class="fas fa-sort text-muted"></i>
                    </span>
                </th>
                <th class="sortable" data-column="capital" style="cursor: pointer;">
                    Capital 
                    <span class="sort-indicator" data-column="capital">
                        <i class="fas fa-sort text-muted"></i>
                    </span>
                </th>
                <th>Update Time</th>
            </tr>
        </thead>
        <tbody>
            {% for record in equity_records %}
            <tr>
                <td>{{ record.username }}</td>
                <td data-value="{{ record.equity_value }}">{{ '{:.2f}'.format(record.equity_value) }}</td>
                <td>{{ record.equity_time.strftime('%Y-%m-%d %H:%M:%S') }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

{% if not equity_records %}
<div class="alert alert-info">
    <h4>No Capital Data Found</h4>
    <p>暂无权益数据，请检查数据库和网络连接</p>
</div>
{% endif %}

<style>
.sortable:hover {
    background-color: rgba(255, 255, 255, 0.1);
}
.sort-indicator {
    margin-left: 5px;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    let sortState = {
        column: null,
        direction: 'asc'  // 'asc' or 'desc'
    };

    // 为所有可排序的表头添加点击事件
    document.querySelectorAll('.sortable').forEach(function(header) {
        header.addEventListener('click', function() {
            const column = this.getAttribute('data-column');
            
            // 如果点击的是当前排序列，则切换排序方向
            if (sortState.column === column) {
                sortState.direction = sortState.direction === 'asc' ? 'desc' : 'asc';
            } else {
                // 如果点击的是新列，设置为升序
                sortState.column = column;
                sortState.direction = 'asc';
            }
            
            sortTable(column, sortState.direction);
            updateSortIndicators(column, sortState.direction);
        });
    });

    function sortTable(column, direction) {
        const table = document.getElementById('capitalTable');
        const tbody = table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));

        rows.sort(function(a, b) {
            let aValue, bValue;
            
            if (column === 'user') {
                aValue = a.cells[0].textContent.trim();
                bValue = b.cells[0].textContent.trim();
                return direction === 'asc' 
                    ? aValue.localeCompare(bValue)
                    : bValue.localeCompare(aValue);
            } else if (column === 'capital') {
                aValue = parseFloat(a.cells[1].getAttribute('data-value'));
                bValue = parseFloat(b.cells[1].getAttribute('data-value'));
                return direction === 'asc' 
                    ? aValue - bValue
                    : bValue - aValue;
            }
        });

        // 重新排列表格行
        rows.forEach(function(row) {
            tbody.appendChild(row);
        });
    }

    function updateSortIndicators(activeColumn, direction) {
        // 重置所有排序指示器
        document.querySelectorAll('.sort-indicator i').forEach(function(icon) {
            icon.className = 'fas fa-sort text-muted';
        });

        // 更新当前活跃列的排序指示器
        const activeIndicator = document.querySelector(`[data-column="${activeColumn}"] .sort-indicator i`);
        if (activeIndicator) {
            activeIndicator.className = direction === 'asc' 
                ? 'fas fa-sort-up text-primary'
                : 'fas fa-sort-down text-primary';
        }
    }
});
</script>

{% endblock %} 