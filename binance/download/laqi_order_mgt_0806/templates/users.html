{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>User Management</h2>
    <a href="{{ url_for('add_user') }}" class="btn btn-primary">Add User</a>
</div>

<div class="table-responsive">
    <table class="table table-striped table-hover">
        <thead class="thead-dark">
            <tr>
                <th>Username</th>
                <th>Admin</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for user in users %}
            <tr>
                <td>{{ user.username }}</td>
                <td>{{ 'Yes' if user.is_admin else 'No' }}</td>
                <td>
                    {% if user.username != 'root' %}
                    <form method="POST" action="{{ url_for('delete_user', user_id=user.id) }}" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this user?')">
                        <button type="submit" class="btn btn-sm btn-danger">Delete</button>
                    </form>
                    {% endif %}
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

{% if not users %}
<div class="alert alert-info">
    <h4>No Users Found</h4>
    <p>There are no users in the system.</p>
    <a href="{{ url_for('add_user') }}" class="btn btn-primary">Add First User</a>
</div>
{% endif %}

{% endblock %}
