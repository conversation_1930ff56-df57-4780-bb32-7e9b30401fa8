from flask_sqlalchemy import SQLAlchemy
from datetime import datetime, timedelta
from werkzeug.security import generate_password_hash, check_password_hash
from flask_login import UserMixin
import uuid

db = SQLAlchemy()

class FkOrder(db.Model):
    __tablename__ = 'fk_order'
    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True)
    symbol = db.Column(db.String(63), nullable=False)
    ping_volume = db.Column(db.Double, nullable=False)
    user = db.Column(db.String(63), nullable=False)
    create_date = db.Column(db.DateTime, nullable=False, default=datetime.now)
    update_date = db.Column(db.DateTime, nullable=False, 
                          default=datetime.now, onupdate=datetime.now)
    status = db.Column(db.SmallInteger, nullable=False, default=0)
    remarks = db.Column(db.Text, nullable=True)
    del_flag = db.Column(db.<PERSON>Integer, nullable=False, default=0)
    __table_args__ = (
        db.Index('idx_symbol_status', 'symbol', 'status'),
    )

class LaqiOrder(db.Model):
    __tablename__ = 'laqi_order'
    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True)
    user = db.Column(db.String(63), nullable=False)
    ratio = db.Column(db.Double, nullable=False)
    create_date = db.Column(db.DateTime, nullable=False, default=datetime.now)
    update_date = db.Column(db.DateTime, nullable=False, 
                          default=datetime.now, onupdate=datetime.now)
    status = db.Column(db.SmallInteger, nullable=False, default=0)
    remarks = db.Column(db.Text, nullable=True)

    __table_args__ = (
        db.Index('idx_user_status', 'user', 'status'),
    )

class SubmissionToken(db.Model):
    __tablename__ = 'submission_tokens'
    id = db.Column(db.Integer, primary_key=True)
    token = db.Column(db.String(36), unique=True, nullable=False)
    user_id = db.Column(db.Integer, nullable=False)
    form_type = db.Column(db.String(50), nullable=False)  # 'ping_order' 或 'laqi_order'
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.now)
    used_at = db.Column(db.DateTime, nullable=True)
    is_used = db.Column(db.Boolean, default=False, nullable=False)
    
    __table_args__ = (
        db.Index('idx_token', 'token'),
        db.Index('idx_user_form_type', 'user_id', 'form_type'),
    )

    @staticmethod
    def generate_token():
        return str(uuid.uuid4())
    
    @staticmethod
    def create_token(user_id, form_type):
        token = SubmissionToken.generate_token()
        submission_token = SubmissionToken(
            token=token,
            user_id=user_id,
            form_type=form_type
        )
        db.session.add(submission_token)
        db.session.commit()
        return token
    
    @staticmethod
    def validate_and_use_token(token, user_id, form_type):
        """验证并使用令牌，返回True表示验证成功"""
        submission_token = SubmissionToken.query.filter_by(
            token=token,
            user_id=user_id,
            form_type=form_type,
            is_used=False
        ).first()
        
        if submission_token:
            # 检查令牌是否过期（1分钟）
            if (datetime.now() - submission_token.created_at).total_seconds() > 60:
                return False
            
            # 标记为已使用
            submission_token.is_used = True
            submission_token.used_at = datetime.now()
            db.session.commit()
            return True
        
        return False

class FkUser(db.Model):
    __tablename__ = 'fk_user'
    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True)
    user = db.Column(db.String(63), unique=True, nullable=False)
    
class User(UserMixin, db.Model):
    __tablename__ = 'fk_mgt'
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(64), unique=True, nullable=False)
    password_hash = db.Column(db.String(255))
    is_admin = db.Column(db.Boolean, default=False)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

class PositionMonitor(db.Model):
    __tablename__ = 'position_monitor'
    username = db.Column(db.String(63), nullable=False,primary_key=True)
    symbol = db.Column(db.String(63), nullable=False,primary_key=True)
    exchange = db.Column(db.String(63), nullable=False,primary_key=True)
    market_type = db.Column(db.String(63), nullable=False)  
    leverage = db.Column(db.Integer, nullable=False)
    position = db.Column(db.Double, nullable=False)
    current_price = db.Column(db.Double, nullable=False)
    position_value = db.Column(db.Double, nullable=False)
    max_notional_value = db.Column(db.Integer, nullable=False)
    update_time = db.Column(db.DateTime, nullable=False)
    
    __table_args__ = (
        db.Index('username', 'symbol', 'exchange'),
    )

class UserEquityHistory(db.Model):
    __tablename__ = 'user_equity_history'
    username = db.Column(db.String(255), nullable=False, primary_key=True)
    equity_time = db.Column(db.DateTime, nullable=False, primary_key=True, comment='权益时间点')
    equity_value = db.Column(db.Double, nullable=False, comment='权益值')
    
    __table_args__ = (
        # BTREE索引：equity_time DESC, username ASC，优化查询性能
        db.Index('idx_equity_time_username', 
                 db.text('equity_time ASC'), 
                 db.text('username ASC'),
                 mysql_using='BTREE'),
    )

class PreLaqi(db.Model):
    __tablename__ = 'pre_laqi'
    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True)
    user = db.Column(db.String(63), nullable=False)
    ratio = db.Column(db.Double, nullable=False)
    type = db.Column(db.Integer, nullable=False)  # 0: Higher than, 1: Lower than
    trigger_value = db.Column(db.Double, nullable=False)
    status = db.Column(db.SmallInteger, nullable=False, default=0)  # 0: Monitoring, 1: Triggered, 2: Expired
    create_date = db.Column(db.DateTime, nullable=False, default=datetime.now)
    update_date = db.Column(db.DateTime, nullable=False, 
                          default=datetime.now, onupdate=datetime.now)
    expiry_date = db.Column(db.DateTime, nullable=False, 
                          default=lambda: datetime.now() + timedelta(hours=12))
    del_flag = db.Column(db.SmallInteger, nullable=False, default=0)
    
    __table_args__ = (
        db.Index('idx_user_status', 'user', 'status'),
        db.Index('idx_status_expiry', 'status', 'expiry_date'),
    )

class QR(db.Model):
    __tablename__ = 'qr_order'
    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True)
    symbol = db.Column(db.String(63), nullable=False)
    create_date = db.Column(db.DateTime, nullable=False, default=datetime.now)
    update_date = db.Column(db.DateTime, nullable=False, 
                          default=datetime.now, onupdate=datetime.now)
    status = db.Column(db.SmallInteger, nullable=False, default=0)
    remarks = db.Column(db.Text, nullable=True)

    __table_args__ = (
        db.Index('idx_symbol_status', 'symbol', 'status'),
    )

class LeverageReset(db.Model):
    __tablename__ = 'leverage_reset'
    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True)
    username = db.Column(db.String(63), nullable=False)
    symbol = db.Column(db.String(63), nullable=False)
    leverage = db.Column(db.Integer, nullable=False)
    create_date = db.Column(db.DateTime, nullable=False, default=datetime.now)
    update_date = db.Column(db.DateTime, nullable=False, 
                          default=datetime.now, onupdate=datetime.now)
    status = db.Column(db.SmallInteger, nullable=False, default=0)
    remarks = db.Column(db.Text, nullable=True)

    __table_args__ = (
        db.Index('idx_username_symbol', 'username', 'symbol'),
    )