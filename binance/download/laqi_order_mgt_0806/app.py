from flask import Flask, request, jsonify, render_template, redirect, url_for, flash, abort, session
from flask_login import LoginMana<PERSON>, login_user, login_required, logout_user, current_user
from flask_wtf import FlaskForm
from wtforms import StringField, PasswordField, SubmitField, BooleanField, FloatField, SelectField, HiddenField
from wtforms.validators import DataRequired, Length, ValidationError, NumberRange, InputRequired
from datetime import datetime, timedelta
import re
import pandas as pd
import requests
import json
import logging
import os
from config import Config
from models import db, User, FkUser, FkOrder, LaqiOrder, PositionMonitor, SubmissionToken, UserEquityHistory, PreLaqi, QR, LeverageReset
from sqlalchemy import func

app = Flask(__name__)
app.config.from_object(Config)
db.init_app(app)
login_manager = LoginManager(app)
login_manager.login_view = 'login'

# 配置统一日志
if not os.path.exists('logs'):
    os.makedirs('logs')

app_logger = logging.getLogger('app_operations')
app_logger.setLevel(logging.INFO)
handler = logging.FileHandler('logs/app_operations.log', encoding='utf-8')
formatter = logging.Formatter(
    '%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
handler.setFormatter(formatter)
if not app_logger.handlers:
    app_logger.addHandler(handler)

@login_manager.user_loader
def load_user(user_id):
    return db.session.get(User, int(user_id))

class LoginForm(FlaskForm):
    username = StringField('Username', validators=[DataRequired()])
    password = PasswordField('Password', validators=[DataRequired()])
    submit = SubmitField('Log In')

class OrderForm(FlaskForm):
    symbol = StringField('Symbol', validators=[
        DataRequired(message="Symbol Is Required"),
        Length(max=63, message="Symbol Length Cannot Exceed 63 Characters")
    ])
    ping_volume = FloatField('Ping Volume', validators=[
        InputRequired(message="Ping Volume Is required"),
        NumberRange(min=0, message="Ping Volume Must Be Greater than or Equal to 0")
    ])
    user = SelectField('User', validators=[
        DataRequired(message="Must Select a Valid User")
    ], choices=[])
    submission_token = HiddenField('Submission Token')
    submit = SubmitField('Submit')

    def validate_symbol(self, field):
        if not re.match(r'^[A-Z0-9]*USDT\.(BINANCE)$', field.data.upper()):
            raise ValidationError('Incorrect Symbol Format, Should Be "SYMBOLUSDT.EXCHANGE" (eg. BTCUSDT.BINANCE)')

    def validate_user(self, field):
        # 严格校验用户（区分大小写）
        user_exists = FkUser.query.filter(func.binary(FkUser.user) == field.data.strip()).first()
        if not user_exists:
            raise ValidationError("Please Select a Valid User")

class UserForm(FlaskForm):
    username = StringField('Username', validators=[DataRequired(), Length(max=64)])
    password = PasswordField('Password', validators=[DataRequired()])
    is_admin = BooleanField('Admin')
    submit = SubmitField('Add User')

class LaqiOrderForm(FlaskForm):
    user = SelectField('User', validators=[
        DataRequired(message="Must Select a Valid User")
    ], choices=[])
    ratio = SelectField('Ratio', validators=[
        DataRequired(message="Must Select a Valid Ratio")
    ], choices=[
        ('0.9', '0.9'), ('0.85', '0.85'), ('0.8', '0.8'), 
        ('0.75', '0.75'), ('0.7', '0.7'), ('0.65', '0.65'), 
        ('0.6', '0.6'), ('0.5', '0.5')
    ])
    submission_token = HiddenField('Submission Token')
    submit = SubmitField('Submit')

    def validate_user(self, field):
        # 严格校验用户（区分大小写）
        user_exists = FkUser.query.filter(func.binary(FkUser.user) == field.data.strip()).first()
        if not user_exists:
            raise ValidationError("Please Select a Valid User")

    def validate_ratio(self, field):
        if not re.match(r'^[0-9.]+$', field.data):
            raise ValidationError("Invalid Ratio Format, Should Be a Number")

class PreLaqiOrderForm(FlaskForm):
    user = SelectField('User', validators=[
        DataRequired(message="Must Select a Valid User")
    ], choices=[])
    ratio = SelectField('Ratio', validators=[
        DataRequired(message="Must Select a Valid Ratio")
    ], choices=[
        ('0.9', '0.9'), ('0.85', '0.85'), ('0.8', '0.8'), 
        ('0.75', '0.75'), ('0.7', '0.7'), ('0.65', '0.65'), 
        ('0.6', '0.6'), ('0.5', '0.5')
    ])
    type = SelectField('Type', validators=[
        DataRequired(message="Must Select a Valid Type")
    ], choices=[
        ('0', 'Higher than'), ('1', 'Lower than')
    ])
    trigger_value = FloatField('Trigger Value', validators=[
        InputRequired(message="Trigger Value Is required"),
        NumberRange(min=0, message="Trigger Value Must Be Positive")
    ])
    confirmation_needed = HiddenField('Confirmation Needed')
    user_confirmed = HiddenField('User Confirmed')
    submission_token = HiddenField('Submission Token')
    submit = SubmitField('Submit')

    def validate_user(self, field):
        # 严格校验用户（区分大小写）
        user_exists = FkUser.query.filter(func.binary(FkUser.user) == field.data.strip()).first()
        if not user_exists:
            raise ValidationError("Please Select a Valid User")

    def validate_ratio(self, field):
        if not re.match(r'^[0-9.]+$', field.data):
            raise ValidationError("Invalid Ratio Format, Should Be a Number")

    def validate_type(self, field):
        if field.data not in ['0', '1']:
            raise ValidationError("Invalid Type Selection")

class QROrderForm(FlaskForm):
    symbol = StringField('Symbol', validators=[
        DataRequired(message="Symbol Is Required"),
        Length(max=63, message="Symbol Length Cannot Exceed 63 Characters")
    ])
    submission_token = HiddenField('Submission Token')
    submit = SubmitField('Submit')
    def validate_symbol(self, field):
        if not re.match(r'^[A-Z0-9]*USDT$', field.data.upper()):
            raise ValidationError('Incorrect Symbol Format, Should Be "SYMBOLUSDT" (eg. BTCUSDT)')

class LeverageResetForm(FlaskForm):
    username = SelectField('Username', validators=[
        DataRequired(message="Must Select a Valid Username")
    ], choices=[])
    symbol = StringField('Symbol', validators=[
        DataRequired(message="Symbol Is Required"),
        Length(max=63, message="Symbol Length Cannot Exceed 63 Characters")
    ])
    leverage = FloatField('Leverage', validators=[
        InputRequired(message="Leverage Is required"),
        NumberRange(min=1, max=30, message="Leverage Must Be Between 1 and 30")
    ])
    submission_token = HiddenField('Submission Token')
    submit = SubmitField('Submit')

    def validate_username(self, field):
        # 严格校验用户（区分大小写）
        user_exists = FkUser.query.filter(func.binary(FkUser.user) == field.data.strip()).first()
        if not user_exists:
            raise ValidationError("Please Select a Valid User")

    def validate_leverage(self, field):
        if not isinstance(field.data, (int, float)) or field.data != int(field.data):
            raise ValidationError("Leverage Must Be an Integer")
        if not (1 <= int(field.data) <= 30):
            raise ValidationError("Leverage Must Be Between 1 and 30")
    
    def validate_symbol(self, field):
        if self.username.data and field.data:
            # 验证symbol是否属于所选username（统一转换为大写进行比较）
            symbol_exists = PositionMonitor.query.filter(
                PositionMonitor.username == self.username.data.strip(),
                PositionMonitor.symbol == field.data.strip().upper()
            ).first()
            if not symbol_exists:
                raise ValidationError("Selected Symbol Does Not Exist for The Chosen Username")
def check_duplicate_order(user, symbol, ping_volume, seconds=10):
    """检查最近几秒内是否有相同的订单"""
    time_threshold = datetime.now() - timedelta(seconds=seconds)
    duplicate = FkOrder.query.filter(
        FkOrder.user == user,
        FkOrder.symbol == symbol.upper(),
        FkOrder.ping_volume == ping_volume,
        FkOrder.create_date >= time_threshold,
        FkOrder.del_flag == 0
    ).first()
    return duplicate is not None

def check_duplicate_laqi_order(user, ratio, seconds=10):
    """检查最近几秒内是否有相同的Laqi订单"""
    time_threshold = datetime.now() - timedelta(seconds=seconds)
    duplicate = LaqiOrder.query.filter(
        LaqiOrder.user == user,
        LaqiOrder.ratio == ratio,
        LaqiOrder.create_date >= time_threshold
    ).first()
    return duplicate is not None

def check_duplicate_qr_order(symbol, seconds=10):
    """检查最近几秒内是否有相同的QR订单"""
    time_threshold = datetime.now() - timedelta(seconds=seconds)
    duplicate = QR.query.filter(
        QR.symbol == symbol,
        QR.create_date >= time_threshold
    ).first()
    return duplicate is not None
def check_duplicate_pre_laqi_order(user, ratio, type, trigger_value, seconds=10):
    """检查最近几秒内是否有相同的PreLaqi订单"""
    time_threshold = datetime.now() - timedelta(seconds=seconds)
    duplicate = PreLaqi.query.filter(
        PreLaqi.user == user,
        PreLaqi.ratio == ratio,
        PreLaqi.type == type,
        PreLaqi.trigger_value == trigger_value,
        PreLaqi.create_date >= time_threshold,
        PreLaqi.del_flag == 0
    ).first()
    return duplicate is not None

def get_user_current_balance(username):
    """获取用户当前权益值"""
    # 优化查询：只获取equity_value字段，减少数据传输
    # 添加limit(1)确保只取一条记录，提高性能
    latest_equity = db.session.query(UserEquityHistory.equity_value).filter(
        UserEquityHistory.username == username
    ).order_by(UserEquityHistory.equity_time.desc()).first()
    
    if latest_equity:
        return latest_equity[0]  # latest_equity是tuple，取第一个元素
    return None

@app.route('/')
@login_required
def index():
    page = request.args.get('page', 1, type=int)
    per_page = 8
    search = request.args.get('search', '')
    
    query = FkOrder.query.filter(FkOrder.del_flag == 0)
    if search:
        # 尝试解析日期格式
        if re.match(r'^\d{8}$', search):  # 匹配YYYYMMDD格式
            search_date = datetime.strptime(search, "%Y%m%d").date()
            next_day = search_date + timedelta(days=1)
            query = query.filter(FkOrder.create_date >= search_date).filter(FkOrder.create_date < next_day)
        else:
            query = query.filter(
                (FkOrder.symbol.like(f'%{search}%')) |
                # (FkOrder.ping_volume.like(f'%{search}%')) |
                (FkOrder.user.like(f'%{search}%'))
            )
    pagination = query.order_by(FkOrder.create_date.desc()).paginate(page=page, per_page=per_page)
    return render_template(
        'index.html', 
        orders=pagination.items, 
        pagination=pagination,
        search=search
    )

@app.route('/order/add', methods=['GET', 'POST'])
@login_required
def add_order():
    # if not current_user.is_admin:
    #     abort(403)
        
    form = OrderForm()
    form.user.choices = [(None, 'Please Select User')] + [(u.user, u.user) for u in FkUser.query.order_by(FkUser.user).all()]
    
    # GET请求：生成新的提交令牌
    if request.method == 'GET':
        token = SubmissionToken.create_token(current_user.id, 'ping_order')
        form.submission_token.data = token
        return render_template('add_order.html', form=form)
    
    # POST请求：验证表单和令牌
    if form.validate_on_submit():
        # 验证提交令牌
        token = form.submission_token.data
        if not token or not SubmissionToken.validate_and_use_token(token, current_user.id, 'ping_order'):
            flash('Token Is Invalid or Outdated, Please Review The Input and Submit Again', 'warning')
            # 生成新令牌
            new_token = SubmissionToken.create_token(current_user.id, 'ping_order')
            form.submission_token.data = new_token
            return render_template('add_order.html', form=form)
        check_ping_seconds = 5
        if check_duplicate_order(form.user.data.strip(), form.symbol.data.strip().upper(), float(form.ping_volume.data), seconds=check_ping_seconds):
            flash(f'Order With The Same Input Is Submitted {check_ping_seconds} Seconds Ago, Please Try Again Later', 'warning')
            # 生成新令牌
            new_token = SubmissionToken.create_token(current_user.id, 'ping_order')
            form.submission_token.data = new_token
            return render_template('add_order.html', form=form)
        try:
            new_order = FkOrder(
                symbol=form.symbol.data.strip().upper(),
                ping_volume=float(form.ping_volume.data),
                user=form.user.data.strip(),
            )
            db.session.add(new_order)
            db.session.flush()  # 获取订单ID
            
            # 记录创建日志
            app_logger.info(f"CREATE - PingOrder#{new_order.id} - Operator:{current_user.username} - Symbol:{new_order.symbol}, Volume:{new_order.ping_volume}, User:{new_order.user}")
            
            db.session.commit()
            flash('Ping Order Created Successfully', 'success')
            return redirect(url_for('index'))
        except Exception as e:
            db.session.rollback()
            flash(f'Failed to Create Ping Order: {str(e)}', 'error')
            app.logger.error(f"Failed to add order: {str(e)}")
            # 生成新令牌用于重试
            new_token = SubmissionToken.create_token(current_user.id, 'ping_order')
            form.submission_token.data = new_token
    else:
        # 表单验证失败，生成新令牌
        new_token = SubmissionToken.create_token(current_user.id, 'ping_order')
        form.submission_token.data = new_token
        for field, errors in form.errors.items():
            for error in errors:
                flash(f'{field}: {error}', 'error')
    
    return render_template('add_order.html', form=form)

@app.route('/order/edit/<int:order_id>', methods=['GET', 'POST'])
@login_required
def edit_order(order_id):
    # if not current_user.is_admin:
    #     abort(403)
        
    order = FkOrder.query.get_or_404(order_id)

    # 检查订单状态
    if order.status != 0:
        flash("This Order Is Processed, Thus It Can't Be Edited. You Can Only Edit Orders With a Status of 'Order Sent'.", 'warning')
        return redirect(url_for('index'))

    form = OrderForm(obj=order)
    form.user.choices = [(None, 'Please Select User')] + [(u.user, u.user) for u in FkUser.query.order_by(FkUser.user).all()]

    if form.validate_on_submit():
        if (form.symbol.data.strip() == order.symbol and
            form.ping_volume.data == order.ping_volume and
            form.user.data == order.user):
            flash('No Edits Were Made to The Order.', 'info')
            return redirect(url_for('index'))
        current_order = FkOrder.query.get(order_id)
        if current_order.status != 0:
            flash("This Order Got Processed while You Were Editing. You Can Only Edit Orders With a Status of 'Order Sent'.", 'warning')
            return redirect(url_for('index'))

        try:
            # 记录修改前的值
            old_symbol = order.symbol
            old_ping_volume = order.ping_volume
            old_user = order.user
            
            # 更新字段
            order.symbol = form.symbol.data.strip().upper()
            order.ping_volume = float(form.ping_volume.data)
            order.user = form.user.data.strip()
            
            # 记录修改日志
            changes = []
            if old_symbol != order.symbol:
                changes.append(f"Symbol:{old_symbol}->{order.symbol}")
            if old_ping_volume != order.ping_volume:
                changes.append(f"Volume:{old_ping_volume}->{order.ping_volume}")
            if old_user != order.user:
                changes.append(f"User:{old_user}->{order.user}")
            
            if changes:
                app_logger.info(f"UPDATE - FkOrder#{order.id} - Operator:{current_user.username} - Changes:[{', '.join(changes)}]")
            
            db.session.commit()
            flash('Order Updated Successfully.', 'success')
            return redirect(url_for('index'))
        except Exception as e:
            db.session.rollback()
            flash(f'Failed to edit order: {str(e)}', 'info')
            app.logger.error(f"Failed to edit order: {str(e)}")
    else:
        for field, errors in form.errors.items():
            for error in errors:
                flash(f'{field}: {error}', 'error')
    return render_template('edit_order.html', form=form, order=order)

@app.route('/order/delete/<int:order_id>', methods=['POST'])
@login_required
def delete_order(order_id):
    # if not current_user.is_admin:
    #     abort(403)

    order = FkOrder.query.get_or_404(order_id)
    # if order.status != 0:
    #     flash("This Order Is Processed, Thus It Can't Be Deleted. You Can Only Delete Orders With a Status of 'Order Sent'.", 'warning')
    #     return redirect(url_for('index'))

    try:
        order.del_flag = 1
        
        # 记录删除日志
        app_logger.info(f"DELETE - FkOrder#{order.id} - Operator:{current_user.username} - Symbol:{order.symbol}, Volume:{order.ping_volume}, User:{order.user}")
        
        db.session.commit()
        flash('Order Deleted Successfully.', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'Failed to Delete Order: {str(e)}', 'error')
        app.logger.error(f"Failed to Delete Order: {str(e)}.")
    
    return redirect(url_for('index'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    form = LoginForm()
    if form.validate_on_submit():
        user = User.query.filter_by(username=form.username.data).first()
        if user and user.check_password(form.password.data):
            login_user(user)
            return redirect(url_for('index'))
        flash('Invalid Username or Password.')
    return render_template('login.html', form=form)

@app.route('/logout')
@login_required
def logout():
    logout_user()
    return redirect(url_for('login'))


@app.route('/users')
@login_required
def users():
    if not current_user.is_admin:
        flash('You do not have permission to access this page. Please contact admin.', 'error')
        return redirect(url_for('index'))
    users = User.query.all()
    return render_template('users.html', users=users)

@app.route('/add_user', methods=['GET', 'POST'])
@login_required
def add_user():
    if not current_user.is_admin:
        flash('You do not have permission to access this page. Please contact admin.', 'error')
        return redirect(url_for('index'))
    form = UserForm()
    if form.validate_on_submit():
        user = User(username=form.username.data, is_admin=form.is_admin.data)
        user.set_password(form.password.data)
        db.session.add(user)
        try:
            db.session.commit()
            flash('User Added Successfully', 'success')
            return redirect(url_for('users'))
        except Exception as e:
            db.session.rollback()
            flash(f'Database error: {str(e)}', 'error')
    return render_template('add_user.html', form=form)

@app.route('/delete_user/<int:user_id>', methods=['POST'])
@login_required
def delete_user(user_id):
    if not current_user.is_admin:
        flash('You do not have permission to perform this action. Please contact admin.', 'error')
        return redirect(url_for('index'))
    user = User.query.get_or_404(user_id)
    if user.username == 'root':
        flash('Cannot Delete Root User.', 'error')
        return redirect(url_for('users'))
    db.session.delete(user)
    db.session.commit()
    flash('User Deleted Successfully', 'success')
    return redirect(url_for('users'))

@app.route('/laqi')
@login_required
def laqi_index():
    page = request.args.get('page', 1, type=int)
    per_page = 8
    search = request.args.get('search', '')
    
    query = LaqiOrder.query
    if search:
        # 尝试解析日期格式
        if re.match(r'^\d{8}$', search):  # 匹配YYYYMMDD格式
            search_date = datetime.strptime(search, "%Y%m%d").date()
            next_day = search_date + timedelta(days=1)
            query = query.filter(LaqiOrder.create_date >= search_date).filter(LaqiOrder.create_date < next_day)
        else:
            query = query.filter(
                (LaqiOrder.user.like(f'%{search}%'))
            )
    pagination = query.order_by(LaqiOrder.create_date.desc()).paginate(page=page, per_page=per_page)
    return render_template(
        'laqi_index.html', 
        orders=pagination.items, 
        pagination=pagination,
        search=search
    )

@app.route('/laqi/add', methods=['GET', 'POST'])
@login_required
def add_laqi_order():
    form = LaqiOrderForm()
    form.user.choices = [(None, 'Please Select User')] + [(u.user, u.user) for u in FkUser.query.order_by(FkUser.user).all() if u.user != 'all']
    form.ratio.choices = [(None, 'Please Select Ratio')] + [('0.9', '0.9'), ('0.85', '0.85'), ('0.8', '0.8'), ('0.75', '0.75'), ('0.7', '0.7'), ('0.65', '0.65'), ('0.6', '0.6'), ('0.5', '0.5')]
    
    # GET请求：生成新的提交令牌
    if request.method == 'GET':
        token = SubmissionToken.create_token(current_user.id, 'laqi_order')
        form.submission_token.data = token
        return render_template('add_laqi_order.html', form=form)
    
    # POST请求：验证表单和令牌
    if form.validate_on_submit():
        # 验证提交令牌
        token = form.submission_token.data
        if not token or not SubmissionToken.validate_and_use_token(token, current_user.id, 'laqi_order'):
            flash('Token Is Invalid or Outdated, Please Review The Input and Submit Again', 'warning')
            # 生成新令牌
            new_token = SubmissionToken.create_token(current_user.id, 'laqi_order')
            form.submission_token.data = new_token
            return render_template('add_laqi_order.html', form=form)
        
        check_laqi_seconds = 10
        # 检查重复订单
        if check_duplicate_laqi_order(form.user.data.strip(), float(form.ratio.data), seconds=check_laqi_seconds):
            flash(f'Order With The Same Input Is Submitted {check_laqi_seconds} Seconds Ago, Please Try Again Later', 'warning')
            # 生成新令牌
            new_token = SubmissionToken.create_token(current_user.id, 'laqi_order')
            form.submission_token.data = new_token
            return render_template('add_laqi_order.html', form=form)
        
        try:
            new_order = LaqiOrder(
                user=form.user.data.strip(),
                ratio=float(form.ratio.data),
            )
            db.session.add(new_order)
            db.session.flush()  # 获取订单ID
            
            # 记录创建日志
            app_logger.info(f"CREATE - LaqiOrder#{new_order.id} - Operator:{current_user.username} - User:{new_order.user}, Ratio:{new_order.ratio}")
            
            db.session.commit()
            flash('Laqi Order Created Successfully', 'success')
            return redirect(url_for('laqi_index'))
        except Exception as e:
            db.session.rollback()
            flash(f'Failed to Create Laqi Order: {str(e)}', 'error')
            app.logger.error(f"Failed to add laqi order: {str(e)}")
            # 生成新令牌用于重试
            new_token = SubmissionToken.create_token(current_user.id, 'laqi_order')
            form.submission_token.data = new_token
    else:
        # 表单验证失败，生成新令牌
        new_token = SubmissionToken.create_token(current_user.id, 'laqi_order')
        form.submission_token.data = new_token
        for field, errors in form.errors.items():
            for error in errors:
                flash(f'{field}: {error}', 'error')
    
    return render_template('add_laqi_order.html', form=form)

@app.route('/position_monitor')
@login_required
def position_monitor():
    username = request.args.get('username', '')
    symbol = request.args.get('symbol', '')
    
    # 获取所有可用的username和symbol
    usernames = db.session.query(PositionMonitor.username).distinct().all()
    usernames = [u[0] for u in usernames]
    
    symbols = db.session.query(PositionMonitor.symbol).distinct().all()
    symbols = [s[0] for s in symbols]
    
    query = PositionMonitor.query
    if username:
        query = query.filter(PositionMonitor.username.ilike(f'{username}%'))
    if symbol:
        query = query.filter(PositionMonitor.symbol.ilike(f'{symbol}%'))
    
    positions = query.order_by(PositionMonitor.update_time.desc()).all()

    for pos in positions:
        if pos.position_value is not None:
            # 保留原始值但添加格式化后的值以供显示
            pos.formatted_position_value = '{:.6f}'.format(float(pos.position_value))
        else:
            pos.formatted_position_value = ''
    
    return render_template(
        'position_monitor.html',
        positions=positions,
        usernames=usernames,
        symbols=symbols,
        selected_username=username,
        selected_symbol=symbol
    )

@app.route('/capital_monitor')
@login_required
def capital_monitor():
    subquery = db.session.query(
        UserEquityHistory.username,
        func.max(UserEquityHistory.equity_time).label('max_time')
    ).filter(
        UserEquityHistory.username != 'all'
    ).group_by(UserEquityHistory.username).subquery()
    
    latest_equity = db.session.query(UserEquityHistory).join(
        subquery,
        (UserEquityHistory.username == subquery.c.username) &
        (UserEquityHistory.equity_time == subquery.c.max_time)
    ).order_by(UserEquityHistory.equity_value.desc()).all()
    latest_equity.sort(key=lambda x: x.equity_time, reverse=True)
    return render_template('capital_monitor.html', equity_records=latest_equity)

@app.route('/pre_laqi')
@login_required
def pre_laqi():
    page = request.args.get('page', 1, type=int)
    per_page = 8
    search = request.args.get('search', '')
    
    query = PreLaqi.query.filter(PreLaqi.del_flag == 0)
    if search:
        # 尝试解析日期格式
        if re.match(r'^\d{8}$', search):  # 匹配YYYYMMDD格式
            search_date = datetime.strptime(search, "%Y%m%d").date()
            next_day = search_date + timedelta(days=1)
            query = query.filter(PreLaqi.create_date >= search_date).filter(PreLaqi.create_date < next_day)
        else:
            query = query.filter(
                (PreLaqi.user.like(f'%{search}%'))
            )
    pagination = query.order_by(PreLaqi.create_date.desc()).paginate(page=page, per_page=per_page)
    return render_template(
        'pre_laqi.html', 
        orders=pagination.items, 
        pagination=pagination,
        search=search
    )

@app.route('/pre_laqi/add', methods=['GET', 'POST'])
@login_required
def add_pre_laqi_order():
    form = PreLaqiOrderForm()
    form.user.choices = [(None, 'Please Select User')] + [(u.user, u.user) for u in FkUser.query.order_by(FkUser.user).all() if u.user != 'all']
    form.ratio.choices = [(None, 'Please Select Ratio')] + [('0.9', '0.9'), ('0.85', '0.85'), ('0.8', '0.8'), ('0.75', '0.75'), ('0.7', '0.7'), ('0.65', '0.65'), ('0.6', '0.6'), ('0.5', '0.5')]
    form.type.choices = [(None, 'Please Select Type')] + [('0', 'Higher than'), ('1', 'Lower than')]
    
    # GET请求：生成新的提交令牌
    if request.method == 'GET':
        token = SubmissionToken.create_token(current_user.id, 'pre_laqi_order')
        form.submission_token.data = token
        return render_template('add_pre_laqi_order.html', form=form)
    
    # POST请求：验证表单和令牌
    if form.validate_on_submit():
        # 验证提交令牌
        token = form.submission_token.data
        if not token or not SubmissionToken.validate_and_use_token(token, current_user.id, 'pre_laqi_order'):
            flash('Token Is Invalid or Outdated, Please Review The Input and Submit Again', 'warning')
            # 生成新令牌
            new_token = SubmissionToken.create_token(current_user.id, 'pre_laqi_order')
            form.submission_token.data = new_token
            return render_template('add_pre_laqi_order.html', form=form)
        
        # 检查余额逻辑，判断是否需要确认
        # 只有在用户未确认时才查询余额，避免重复数据库查询
        if form.user_confirmed.data != 'yes':
            current_balance = get_user_current_balance(form.user.data.strip())
            if current_balance is not None:
                show_confirmation_needed = False
                if int(form.type.data) == 0:  # Higher than
                    if float(form.trigger_value.data) < current_balance:
                        show_confirmation_needed = True
                elif int(form.type.data) == 1:  # Lower than
                    if float(form.trigger_value.data) > current_balance:
                        show_confirmation_needed = True
                
                if show_confirmation_needed:
                    form.confirmation_needed.data = 'yes'
                    # 生成新令牌用于重新提交
                    new_token = SubmissionToken.create_token(current_user.id, 'pre_laqi_order')
                    form.submission_token.data = new_token
                    return render_template('add_pre_laqi_order.html', form=form, 
                                        current_balance=current_balance,
                                        show_confirmation=True,
                                        order_type=int(form.type.data))
        
        check_pre_laqi_seconds = 10
        # 检查重复订单
        if check_duplicate_pre_laqi_order(form.user.data.strip(), float(form.ratio.data), int(form.type.data), float(form.trigger_value.data), seconds=check_pre_laqi_seconds):
            flash(f'Order With The Same Input Is Submitted {check_pre_laqi_seconds} Seconds Ago, Please Try Again Later', 'warning')
            # 生成新令牌
            new_token = SubmissionToken.create_token(current_user.id, 'pre_laqi_order')
            form.submission_token.data = new_token
            return render_template('add_pre_laqi_order.html', form=form)
        
        try:
            new_order = PreLaqi(
                user=form.user.data.strip(),
                ratio=float(form.ratio.data),
                type=int(form.type.data),
                trigger_value=float(form.trigger_value.data),
            )
            db.session.add(new_order)
            db.session.flush()  # 获取订单ID
            
            # 记录创建日志
            type_text = "Higher than" if new_order.type == 0 else "Lower than"
            app_logger.info(f"CREATE - PreLaqi#{new_order.id} - Operator:{current_user.username} - User:{new_order.user}, Ratio:{new_order.ratio}, Type:{type_text}, TriggerValue:{new_order.trigger_value}")
            
            db.session.commit()
            flash('Pre Laqi Order Created Successfully', 'success')
            return redirect(url_for('pre_laqi'))
        except Exception as e:
            db.session.rollback()
            flash(f'Failed to Create Pre Laqi Order: {str(e)}', 'error')
            app.logger.error(f"Failed to add pre laqi order: {str(e)}")
            # 生成新令牌用于重试
            new_token = SubmissionToken.create_token(current_user.id, 'pre_laqi_order')
            form.submission_token.data = new_token
    else:
        # 表单验证失败，生成新令牌
        new_token = SubmissionToken.create_token(current_user.id, 'pre_laqi_order')
        form.submission_token.data = new_token
        for field, errors in form.errors.items():
            for error in errors:
                flash(f'{field}: {error}', 'error')
    
    return render_template('add_pre_laqi_order.html', form=form)


@app.route('/pre_laqi/edit/<int:order_id>', methods=['GET', 'POST'])
@login_required
def edit_pre_laqi_order(order_id):
    order = PreLaqi.query.get_or_404(order_id)

    # 检查订单状态
    if order.status != 0:
        flash('Only Orders With Status "Monitoring" Can Be Edited', 'error')
        return redirect(url_for('pre_laqi'))

    form = PreLaqiOrderForm(obj=order)
    form.user.choices = [(u.user, u.user) for u in FkUser.query.order_by(FkUser.user).all() if u.user != 'all']
    form.ratio.choices = [('0.9', '0.9'), ('0.85', '0.85'), ('0.8', '0.8'), ('0.75', '0.75'), ('0.7', '0.7'), ('0.65', '0.65'), ('0.6', '0.6'), ('0.5', '0.5')]
    form.type.choices = [('0', 'Higher than'), ('1', 'Lower than')]

    if form.validate_on_submit():
        # 检查是否有实际修改
        if (form.user.data.strip() == order.user and
            float(form.ratio.data) == order.ratio and
            int(form.type.data) == order.type and
            float(form.trigger_value.data) == order.trigger_value):
            flash('No Edits Were Made to The Order.', 'info')
            return redirect(url_for('pre_laqi'))
        
        # 检查余额逻辑，判断是否需要确认
        # 只有在用户未确认时才查询余额，避免重复数据库查询
        if form.user_confirmed.data != 'yes':
            current_balance = get_user_current_balance(form.user.data.strip())
            if current_balance is not None:
                show_confirmation_needed = False
                if int(form.type.data) == 0:  # Higher than
                    if float(form.trigger_value.data) < current_balance:
                        show_confirmation_needed = True
                elif int(form.type.data) == 1:  # Lower than
                    if float(form.trigger_value.data) > current_balance:
                        show_confirmation_needed = True
                
                if show_confirmation_needed:
                    form.confirmation_needed.data = 'yes'
                    return render_template('edit_pre_laqi_order.html', form=form, order=order,
                                        current_balance=current_balance,
                                        show_confirmation=True,
                                        order_type=int(form.type.data))
        
        # 再次检查订单状态（防止编辑过程中状态改变）
        current_order = PreLaqi.query.get(order_id)
        if current_order.status != 0:
            flash('This Order Status Changed while You Were Editing. You Can Only Edit Orders With Status "Monitoring".', 'warning')
            return redirect(url_for('pre_laqi'))

        try:
            # 记录修改前的值
            old_user = order.user
            old_ratio = order.ratio
            old_type = order.type
            old_trigger_value = order.trigger_value
            
            # 更新字段
            order.user = form.user.data.strip()
            order.ratio = float(form.ratio.data)
            order.type = int(form.type.data)
            order.trigger_value = float(form.trigger_value.data)
            
            # 记录修改日志
            changes = []
            if old_user != order.user:
                changes.append(f"User:{old_user}->{order.user}")
            if old_ratio != order.ratio:
                changes.append(f"Ratio:{old_ratio}->{order.ratio}")
            if old_type != order.type:
                old_type_text = "Higher than" if old_type == 0 else "Lower than"
                new_type_text = "Higher than" if order.type == 0 else "Lower than"
                changes.append(f"Type:{old_type_text}->{new_type_text}")
            if old_trigger_value != order.trigger_value:
                changes.append(f"TriggerValue:{old_trigger_value}->{order.trigger_value}")
            
            if changes:
                app_logger.info(f"UPDATE - PreLaqi#{order.id} - Operator:{current_user.username} - Changes:[{', '.join(changes)}]")
            
            db.session.commit()
            flash('Pre Laqi Order Updated Successfully', 'success')
            return redirect(url_for('pre_laqi'))
        except Exception as e:
            db.session.rollback()
            flash(f'Failed to Update Pre Laqi Order: {str(e)}', 'error')
            app.logger.error(f"Failed to update pre laqi order: {str(e)}")
    else:
        for field, errors in form.errors.items():
            for error in errors:
                flash(f'{field}: {error}', 'error')

    return render_template('edit_pre_laqi_order.html', form=form, order=order)

@app.route('/pre_laqi/delete/<int:order_id>', methods=['POST'])
@login_required
def delete_pre_laqi_order(order_id):
    order = PreLaqi.query.get_or_404(order_id)
    try:
        order.del_flag = 1
        
        # 记录删除日志
        type_text = "Higher than" if order.type == 0 else "Lower than"
        app_logger.info(f"DELETE - PreLaqi#{order.id} - Operator:{current_user.username} - User:{order.user}, Ratio:{order.ratio}, Type:{type_text}, TriggerValue:{order.trigger_value}")
        
        db.session.commit()
        flash('Pre Laqi Order Deleted Successfully', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'Failed to Delete Pre Laqi Order: {str(e)}', 'error')
    return redirect(url_for('pre_laqi'))


@app.route('/qr')
@login_required
def qr_index():
    page = request.args.get('page', 1, type=int)
    per_page = 8
    search = request.args.get('search', '')
    
    query = QR.query
    if search:
        # 尝试解析日期格式
        if re.match(r'^\d{8}$', search):  # 匹配YYYYMMDD格式
            search_date = datetime.strptime(search, "%Y%m%d").date()
            next_day = search_date + timedelta(days=1)
            query = query.filter(QR.create_date >= search_date).filter(QR.create_date < next_day)
        else:
            query = query.filter(
                (QR.symbol.like(f'%{search}%'))
            )
    pagination = query.order_by(QR.create_date.desc()).paginate(page=page, per_page=per_page)
    return render_template(
        'qr_index.html', 
        orders=pagination.items, 
        pagination=pagination,
        search=search
    )

@app.route('/qr/add', methods=['GET', 'POST'])
@login_required
def add_qr_order():
    form = QROrderForm()
    
    # GET请求：生成新的提交令牌
    if request.method == 'GET':
        token = SubmissionToken.create_token(current_user.id, 'qr_order')
        form.submission_token.data = token
        return render_template('add_qr_order.html', form=form)
    
    # POST请求：验证表单和令牌
    if form.validate_on_submit():
        # 验证提交令牌
        token = form.submission_token.data
        if not token or not SubmissionToken.validate_and_use_token(token, current_user.id, 'qr_order'):
            flash('Token Is Invalid or Outdated, Please Review The Input and Submit Again', 'warning')
            # 生成新令牌
            new_token = SubmissionToken.create_token(current_user.id, 'qr_order')
            form.submission_token.data = new_token
            return render_template('add_qr_order.html', form=form)
        
        check_qr_seconds = 10
        # 检查重复订单
        if check_duplicate_qr_order(form.symbol.data.strip().upper(), seconds=check_qr_seconds):
            flash(f'Order With The Same Input Is Submitted {check_qr_seconds} Seconds Ago, Please Try Again Later', 'warning')
            # 生成新令牌
            new_token = SubmissionToken.create_token(current_user.id, 'qr_order')
            form.submission_token.data = new_token
            return render_template('add_qr_order.html', form=form)
        
        try:
            new_order = QR(
                symbol=form.symbol.data.strip().upper(),
            )
            db.session.add(new_order)
            db.session.flush()  # 获取订单ID
            
            # 记录创建日志
            app_logger.info(f"CREATE - QROrder#{new_order.id} - Operator:{current_user.username} - Symbol:{new_order.symbol}")
            
            db.session.commit()
            flash('QR Order Created Successfully', 'success')
            return redirect(url_for('qr_index'))
        except Exception as e:
            db.session.rollback()
            flash(f'Failed to Create QR Order: {str(e)}', 'error')
            app.logger.error(f"Failed to add qr order: {str(e)}")
            # 生成新令牌用于重试
            new_token = SubmissionToken.create_token(current_user.id, 'qr_order')
            form.submission_token.data = new_token
    else:
        # 表单验证失败，生成新令牌
        new_token = SubmissionToken.create_token(current_user.id, 'qr_order')
        form.submission_token.data = new_token
        for field, errors in form.errors.items():
            for error in errors:
                flash(f'{field}: {error}', 'error')
    
    return render_template('add_qr_order.html', form=form)

@app.route('/leverage')
@login_required
def leverage_index():
    page = request.args.get('page', 1, type=int)
    per_page = 8
    search = request.args.get('search', '')
    
    query = LeverageReset.query
    if search:
        # 尝试解析日期格式
        if re.match(r'^\d{8}$', search):  # 匹配YYYYMMDD格式
            search_date = datetime.strptime(search, "%Y%m%d").date()
            next_day = search_date + timedelta(days=1)
            query = query.filter(LeverageReset.create_date >= search_date).filter(LeverageReset.create_date < next_day)
        else:
            query = query.filter(
                (LeverageReset.username.like(f'%{search}%')) |
                (LeverageReset.symbol.like(f'%{search}%'))
            )
    pagination = query.order_by(LeverageReset.create_date.desc()).paginate(page=page, per_page=per_page)
    return render_template(
        'leverage_index.html', 
        orders=pagination.items, 
        pagination=pagination,
        search=search
    )

@app.route('/position_monitor/reset_leverage', methods=['GET', 'POST'])
@login_required
def reset_leverage():
    form = LeverageResetForm()
    
    # 获取URL参数中的默认值
    default_username = request.args.get('username', '')
    default_symbol = request.args.get('symbol', '')
    
    # 获取所有用户选择，过滤掉'all'
    form.username.choices = [(None, 'Please Select Username')] + [(u.user, u.user) for u in FkUser.query.order_by(FkUser.user).all() if u.user != 'all']
    
    # GET请求：生成新的提交令牌并设置默认值
    if request.method == 'GET':
        token = SubmissionToken.create_token(current_user.id, 'leverage_reset')
        form.submission_token.data = token
        
        # 设置默认值
        if default_username:
            form.username.data = default_username
        if default_symbol:
            form.symbol.data = default_symbol
            
        return render_template('reset_leverage.html', form=form)
    
    # POST请求：验证表单和令牌
    if form.validate_on_submit():
        # 验证提交令牌
        token = form.submission_token.data
        if not token or not SubmissionToken.validate_and_use_token(token, current_user.id, 'leverage_reset'):
            flash('Token Is Invalid or Outdated, Please Review The Input and Submit Again', 'warning')
            # 生成新令牌
            new_token = SubmissionToken.create_token(current_user.id, 'leverage_reset')
            form.submission_token.data = new_token
            return render_template('reset_leverage.html', form=form)
        
        # 检查是否已有相同的leverage reset订单正在处理
        existing_reset = LeverageReset.query.filter(
            LeverageReset.username == form.username.data.strip(),
            LeverageReset.symbol == form.symbol.data.strip().upper(),
            LeverageReset.status == 0
        ).first()
        
        if existing_reset:
            flash('Reset Leverage Order with The Same Username And Symbol Is Being Processed', 'warning')
            # 生成新令牌
            new_token = SubmissionToken.create_token(current_user.id, 'leverage_reset')
            form.submission_token.data = new_token
            return render_template('reset_leverage.html', form=form)
        
        # 检查leverage是否与当前的相同
        current_position = PositionMonitor.query.filter(
            PositionMonitor.username == form.username.data.strip(),
            PositionMonitor.symbol == form.symbol.data.strip().upper()
        ).first()
        
        if current_position and current_position.leverage == int(form.leverage.data):
            flash(f'The Current Leverage for {form.username.data.strip()} - {form.symbol.data.strip().upper()} Is Already {int(form.leverage.data)}, Please Input a Different Value', 'warning')
            # 生成新令牌
            new_token = SubmissionToken.create_token(current_user.id, 'leverage_reset')
            form.submission_token.data = new_token
            return render_template('reset_leverage.html', form=form)
        
        try:
            new_reset = LeverageReset(
                username=form.username.data.strip(),
                symbol=form.symbol.data.strip().upper(),
                leverage=int(form.leverage.data),
            )
            db.session.add(new_reset)
            db.session.flush()  # 获取订单ID
            
            # 记录创建日志
            app_logger.info(f"CREATE - LeverageReset#{new_reset.id} - Operator:{current_user.username} - Username:{new_reset.username}, Symbol:{new_reset.symbol}, Leverage:{new_reset.leverage}")
            
            db.session.commit()
            flash('Leverage Reset Order Created Successfully', 'success')
            return redirect(url_for('leverage_index'))
        except Exception as e:
            db.session.rollback()
            flash(f'Failed to Create Leverage Reset Order: {str(e)}', 'error')
            app.logger.error(f"Failed to add leverage reset order: {str(e)}")
            # 生成新令牌用于重试
            new_token = SubmissionToken.create_token(current_user.id, 'leverage_reset')
            form.submission_token.data = new_token
    else:
        # 表单验证失败，生成新令牌
        new_token = SubmissionToken.create_token(current_user.id, 'leverage_reset')
        form.submission_token.data = new_token
        for field, errors in form.errors.items():
            for error in errors:
                flash(f'{field}: {error}', 'error')
    
    return render_template('reset_leverage.html', form=form)



def init_db():
    with app.app_context():
        db.create_all()

        if not User.query.filter_by(username='xxjs@zh').first():
            root_user = User(username='xxjs@zh', is_admin=True)
            root_user.set_password('^zhP@55word$')
            db.session.add(root_user)

        if not User.query.filter_by(username='scout').first():
            zh_user = User(username='scout', is_admin=False)
            zh_user.set_password('zhP@55word')
            db.session.add(zh_user)

        db.session.commit()


if __name__ == '__main__':
    init_db()
    app.run(host='0.0.0.0',port = 5001)
