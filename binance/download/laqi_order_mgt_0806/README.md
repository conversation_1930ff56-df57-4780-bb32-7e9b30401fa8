# 订单管理系统

## 项目简介

这是一个基于Flask开发的Web订单管理系统，专门用于管理多种类型的订单和监控系统。系统提供了完整的用户认证、订单增删改查、实时监控以及自动化订单处理功能，界面简洁直观，操作便捷。

## 功能概览

- 用户认证与权限管理
- Ping Orders、Laqi Orders、PreLaqi Orders三模块管理
- 订单添加、编辑、删除和查询
- 高级搜索与过滤功能
- 实时Position Monitor和Capital Monitor
- 杠杆重置订单管理系统
- PreLaqi自动监控和触发系统
- 企业微信通知集成
- 完整的操作日志记录
- 分页显示

## 技术栈

- **后端框架**: Flask
- **数据库**: SQLAlchemy ORM
- **前端框架**: Bootstrap 4
- **用户认证**: Flask-Login
- **表单处理**: WTForms
- **日志系统**: Python logging
- **通知系统**: 企业微信机器人
- **定时任务**: Python threading

## 安装说明

### 环境要求
- Python 3.6+
- pip包管理工具

### 安装步骤

1. 安装依赖
   ```bash
   pip install -r requirements.txt
   ```

2. 配置数据库
   ```bash
   # 编辑config.py文件，设置数据库连接参数
   ```

3. 配置企业微信通知（可选）
   ```python
   # 在app.py中替换webhook_url为实际的企业微信机器人地址
   webhook_url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=YOUR_KEY"
   ```

4. 运行应用
   ```bash
   python app.py
   ```

5. 访问系统
   ```
   http://localhost:5001
   ```

## 系统模块

### 1. 用户管理
- **登录/登出**: 安全的用户认证系统
- **用户管理**: 管理员可添加、删除用户账户
- **权限控制**: 区分普通用户和管理员权限

### 2. Ping Orders管理
- **订单列表**: 查看所有Ping Orders，按创建日期降序排列
- **添加订单**: 创建新的Ping Order，包含Symbol、Ping Volume和User信息
- **编辑订单**: 修改现有Ping Order的信息（仅状态为0的订单可编辑）
- **删除订单**: 软删除不需要的Ping Order
- **搜索功能**: 支持按Symbol、User或创建日期(YYYYMMDD格式)搜索
- **防重复提交功能**: 通过令牌验证机制和短期（5秒）禁止提交相同订单机制防范

### 3. Laqi Orders管理
- **订单列表**: 查看所有Laqi Orders，按创建日期降序排列
- **添加订单**: 创建新的Laqi Order，选择User和Ratio
- **搜索功能**: 支持按User或创建日期搜索
- **防重复提交功能**: 通过令牌验证机制和短期（10秒）禁止提交相同订单机制防范

### 4. PreLaqi Orders管理（预设Laqi订单）
- **订单列表**: 查看所有PreLaqi Orders，支持状态筛选和搜索
- **添加订单**: 创建预设Laqi订单，设置触发条件
  - User: 选择用户
  - Ratio: 设置比例
  - Type: 选择触发类型（Higher than/Lower than）
  - Trigger Value: 设置触发阈值
- **编辑订单**: 修改现有PreLaqi订单（仅状态为Monitoring的订单可编辑）
- **删除订单**: 软删除PreLaqi订单
- **自动监控**: 系统每5秒自动检查触发条件
- **自动触发**: 满足条件时自动创建Laqi订单并更新状态
- **过期处理**: 订单创建12小时后自动过期
- **企业微信通知**: 有排队情况时企业微信通知

### 5. Position Monitor（仓位监控）
- **实时监控**: 显示所有用户的实时仓位信息
- **多维度筛选**: 支持按Username和Symbol筛选
- **详细信息**: 显示Exchange、Leverage、Position、Current Price等信息
- **数据格式化**: 仓位值精确到6位小数显示
- **杠杆重置**: 每行数据提供Reset Leverage按钮，可快速重置对应用户和Symbol的杠杆

### 6. Leverage Reset Orders（杠杆重置订单）
- **订单列表**: 查看所有Leverage Reset订单，支持状态筛选和搜索
- **订单创建**: 创建杠杆重置订单，设置目标杠杆值
  - Username: 选择用户（过滤掉'all'选项）
  - Symbol: 输入交易对（支持大小写，自动转换为大写存储）
  - Leverage: 设置目标杠杆（1-20之间的整数）
- **快速访问**: 从Position Monitor页面直接跳转，自动填充Username和Symbol
- **防重复提交**: 通过令牌验证机制和重复检测防范
- **智能验证**: 
  - 检查是否已有相同Username和Symbol的处理中订单
  - 验证输入的杠杆值是否与当前值不同
  - 确保Symbol存在于指定Username下
- **操作日志**: 完整记录杠杆重置订单的创建过程

### 7. Capital Monitor（资金监控）
- **权益监控**: 显示指定用户的最新权益信息
- **用户排序**: 按预设顺序显示（zhmain, Liub, Gaoy等）
- **实时数据**: 显示最新的权益值和更新时间
- **响应式布局**: 表格居中显示，适配不同屏幕

### 8. 日志系统
- **统一日志**: 所有操作记录到`logs/app_operations.log`
- **操作类型**: CREATE、UPDATE、DELETE、STATUS_CHANGE、AUTO_CREATE_LAQI_ORDER、LEVERAGE_RESET
- **详细记录**: 包含操作人、时间戳、详细变更内容
- **自动记录**: 系统自动记录PreLaqi状态变更和触发事件

## PreLaqi订单状态说明

### 状态类型
- **Monitoring (0)**: 监控中（黄色显示）
- **Triggered (1)**: 已触发（绿色显示）
- **Expired (2)**: 已过期（灰色显示）

### 触发条件
- **Higher than (0)**: 当用户权益值高于设定值时触发
- **Lower than (1)**: 当用户权益值低于设定值时触发

### 生命周期
1. **创建**: 用户创建PreLaqi订单，状态为Monitoring
2. **监控**: 系统每5秒检查一次触发条件
3. **触发**: 满足条件时自动创建Laqi订单，状态变为Triggered
4. **过期**: 创建12小时后未触发则状态变为Expired
5. **通知**: 有排队情况时企业微信通知

## 使用指南

### 登录系统
- 默认管理员账号: `admin` 密码: `123`
- 默认普通用户: `zh` 密码: `zhP@55word`

### 导航与切换
- 通过顶部导航栏在各模块间切换
- Ping / Laqi / PreLaqi / Position / Capital

### Ping Orders操作流程
1. 查看订单列表: 系统默认显示Ping Orders列表
2. 添加订单: 点击"Add Order"按钮，填写表单并提交
3. 编辑订单: 点击对应订单行的"Edit"按钮（仅状态为0的订单可编辑）
4. 删除订单: 点击对应订单行的"Delete"按钮
5. 搜索订单: 在搜索框中输入关键词，支持Symbol、User或日期格式(YYYYMMDD)

### Laqi Orders操作流程
1. 切换到Laqi Orders: 点击顶部导航栏的"Laqi"
2. 添加订单: 点击"Add Order"按钮，选择User和Ratio并提交
3. 搜索订单: 在搜索框中输入User或日期

### PreLaqi Orders操作流程
1. 切换到PreLaqi: 点击顶部导航栏的"PreLaqi"
2. 添加预设订单: 点击"Add Order"按钮，设置触发条件
   - 选择User
   - 选择Ratio
   - 选择Type（Higher than/Lower than）
   - 设置Trigger Value
3. 编辑订单: 点击"Edit"按钮（仅Monitoring状态可编辑）
4. 删除订单: 点击"Delete"按钮
5. 监控状态: 系统自动监控，无需手动操作

### QR Orders操作流程
1. 切换到QR Orders: 点击顶部导航栏的"QR"
2. 添加订单: 点击"Add Order"按钮，输入Symbol（格式如：BTCUSDT）并提交
3. 搜索订单: 在搜索框中输入Symbol或日期

### Position Monitor使用
1. 切换到Position Monitor: 点击导航栏的"Position Monitor"
2. 筛选数据: 使用Username和Symbol下拉框筛选
3. 查看详情: 表格显示完整的仓位信息
4. 杠杆重置: 点击数据行的"Leverage"按钮进行杠杆重置

### Leverage Reset Orders操作流程
1. 快速访问方式:
   - 从Position Monitor页面点击任意数据行的"Leverage"按钮
   - 系统自动填充对应的Username和Symbol
2. 手动访问方式:
   - 从Position Monitor页面点击右上角的"Leverage"按钮
   - 手动选择Username和输入Symbol
3. 设置杠杆值:
   - 在Leverage字段输入目标杠杆值（1-20之间的整数）
   - 系统会验证输入值与当前杠杆是否不同
4. 提交订单: 点击"Submit"按钮创建杠杆重置订单
5. 系统验证:
   - 检查是否有相同Username和Symbol的处理中订单
   - 验证Symbol是否存在于指定Username下
   - 确保杠杆值为有效整数且与当前值不同

### Capital Monitor使用
1. 切换到Capital Monitor: 点击导航栏的"Capital Monitor"
2. 查看权益: 显示各用户最新权益信息
3. 实时更新: 页面数据来自最新的权益历史记录

### 用户管理(仅管理员)
1. 访问用户管理: 点击顶部导航栏的"User Management"
2. 添加用户: 点击"Add User"按钮，填写用户信息
3. 删除用户: 点击用户对应的"Delete"按钮

## 数据规范

### Symbol格式
- 格式: 看各页面提示，Ping 需要以USDT结尾+.BINANCE(`BTCUSDT.BINANCE`), QR需要以USDT结尾(`BTCUSDT`)
- 支持的交易所: BINANCE

### Ratio选项
支持的比率值: 0.9, 0.85, 0.8, 0.75, 0.7, 0.65, 0.6, 0.5

### Leverage规范
- **取值范围**: 1-20之间的整数
- **输入格式**: 支持大小写Symbol输入，系统自动转换为大写存储
- **重复检测**: 相同Username和Symbol组合在同一时间只能有一个处理中的订单

### 订单状态说明
- **Order Sent (0)**: 订单已发送
- **Order Received (1)**: 订单已接收
- **Order Completed (2)**: 订单已完成
- **Order Processing (3)**: 订单处理中
- **Order Error (5)**: 订单出错

## 系统特性

### 安全特性
- CSRF令牌防护
- 重复提交检测
- 用户权限验证
- 订单状态锁定

### 自动化特性
- PreLaqi订单自动监控
- 条件满足时自动创建Laqi订单
- 订单自动过期处理
- 企业微信自动通知

### 监控特性
- 完整操作日志记录
- 实时仓位监控
- 权益变化跟踪
- 系统性能监控

## 日志文件
- **位置**: `logs/app_operations.log`
- **格式**: 时间戳 - 级别 - 操作详情
- **内容**: 所有订单操作、状态变更、自动触发事件

## 注意事项
1. PreLaqi订单创建后12小时自动过期
2. 只有状态为"Monitoring"的PreLaqi订单可以编辑
3. 系统每5秒自动检查PreLaqi触发条件
4. 企业微信通知需要配置正确的webhook地址
5. 删除操作为软删除，数据不会真正删除
6. 每次新增或删除用户，请修改数据库fkuser表
7. 杠杆重置订单要求输入的杠杆值必须与当前值不同
8. 相同Username和Symbol组合在同一时间只能有一个处理中的杠杆重置订单
9. Symbol输入支持大小写，系统会自动转换为大写格式存储