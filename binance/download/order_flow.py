import json
import os
import random
import signal
import sys
import time
import urllib.request
from datetime import datetime
import requests
from PySide6 import QtGui
from sqlalchemy import text
from vnpy.trader.setting import SETTINGS
import hmac
import base64
import hashlib

SETTINGS["log.active"] = True
SETTINGS["log.level"] = "INFO"
SETTINGS["log.console"] = True
SETTINGS["log.file"] = True
from PySide6.QtCore import Qt
from PySide6.QtWidgets import QMainWindow, QTableWidget, QTableWidgetItem, QVBoxLayout, QWidget, QHeaderView, QLineEdit, \
    QPushButton, QHBoxLayout, QMessageBox
from vnpy.event import Event, EventEngine, EVENT_TIMER
from vnpy.trader.engine import MainEngine
from vnpy.trader.event import EVENT_LOG
from vnpy.trader.object import LogData
from vnpy.trader.ui import create_qapp
from vnpy.trader.ui.widget import BaseCell
import pandas as pd
from peewee import fn
from functools import wraps
import peewee
from models import db, SettingLocal, TodoNotify, create_tables
APP_NAME = 'OrderFlow'

class CheckableTableWidgetItem(QTableWidgetItem):
    def __lt__(self, other):
        # 确保两个比较的项都是 QTableWidgetItem 的实例
        if isinstance(other, QTableWidgetItem):
            # 比较两个项的复选框状态（Checked > Unchecked）
            # return self.checkState() < other.checkState()
            return int(self.checkState()) < int(other.checkState())
        return super().__lt__(other)

class MainWindow(QMainWindow):
    def __init__(self, main_engine: MainEngine, event_engine: EventEngine):
        super().__init__()

        self.main_engine: MainEngine = main_engine
        self.event_engine: EventEngine = event_engine

        self.setWindowTitle("触发通知-过滤器")
        self.last_reload_date = None

        # 初始化数据库
        create_tables()

        # 初始化通知系统
        self.msger = PrintMsg()
        # self.msger = QMsg(key="4d25554874012507f59d45b03ffd7669", group=False)
        # self.msger = WeComMsg(key="4d7737f2-67f9-4e3d-afda-7a54b44817a5")
        # self.msger = WeComAppMsg(touser='liaoyuan')
        # self.msger = LarkMsg(key="f02f094046fd7a5e3f45fd2282859dfc", sign_key="cosmostar")

        # 创建主窗口
        main_widget = QWidget()
        main_layout = QVBoxLayout(main_widget)

        # 创建查询部分
        self.filter_line = QLineEdit()
        self.filter_line.setPlaceholderText("输入vt_symbol，留空则查询所有")
        # 按回车键直接查询
        self.filter_line.returnPressed.connect(self.query_table)

        self.query_button = QPushButton("查询")
        self.query_button.clicked.connect(self.query_table)
        # 添加全（不）选按钮
        self.select_all_button = QPushButton("全选")
        self.select_all_button.setCheckable(True)
        self.select_all_button.toggled.connect(self.select_all_rows)

        query_layout = QHBoxLayout()
        query_layout.addWidget(self.filter_line)
        query_layout.addWidget(self.query_button)
        query_layout.addWidget(self.select_all_button)  # 添加全选按钮到查询布局
        main_layout.addLayout(query_layout)

        # 创建QTableWidget
        self.table = QTableWidget()
        self.table.setColumnCount(4)
        self.table.setHorizontalHeaderLabels(["通知", "vt_symbol", "stage", "create_date"])

        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)  # 自适应列宽
        # 为 QTableWidget 的 itemChanged 信号连接槽函数
        self.table.itemChanged.connect(self.handle_item_changed)
        self.table.setSortingEnabled(True)  # 启用表格排序功能
        # 状态栏
        self.statusBar().showMessage("程序启动")

        main_layout.addWidget(self.table)

        self.setCentralWidget(main_widget)

        # 设置窗口尺寸
        self.resize(1920 // 2, 1080 // 2)

        # 记录上次通知任务的id
        self.last_max_id = 0

        # 任务列表
        self.tasks = []

        # 改为使用vnpy的EVENT_TIMER
        self.register_event()

        # 标志变量，指示是否在重连中
        self.reconnect_attempt = False

        # 填充表格数据
        self.inited = False
        self.notify_filtered_tasks(Event(type=EVENT_TIMER))
        self.load_setting()

    def register_event(self):
        self.event_engine.register(EVENT_TIMER, self.generate_tasks)
        self.event_engine.register(EVENT_TIMER, self.notify_filtered_tasks)
        self.event_engine.register(EVENT_TIMER, self.check_reload_time)  # Add timer event for checking reload time

    def handle_database_error(self, msg: str) -> None:
        """处理数据库连接错误"""
        self.write_log(msg)
        self.statusBar().showMessage("！！！数据库连接错误，正在自动重连！！！")
        self.reconnect_attempt = True

    def db_error_handler(func):
        """装饰器用于处理数据库错误并更新状态栏"""
        @wraps(func)
        def wrapper(self, /, *args, **kwargs):
            try:
                result = func(self, *args, **kwargs)
                if self.reconnect_attempt:
                    self.statusBar().showMessage("数据库重连成功", 5000)
                    self.reconnect_attempt = False
                return result
            except peewee.OperationalError as e:
                self.handle_database_error(f"数据库连接错误: {str(e)}")
        return wrapper

    @db_error_handler
    def load_setting(self):
        """使用ORM加载设置"""
        query = (SettingLocal
                .select()
                .where(fn.LENGTH(fn.REGEXP_SUBSTR(fn.SUBSTRING_INDEX(SettingLocal.vt_symbol, '.', 1), '[0-9]+$')) >= 3))
        
        row = 0
        for record in query:
            self.table.insertRow(row)
            checkbox_item = CheckableTableWidgetItem()
            checkbox_item.setFlags(Qt.ItemIsUserCheckable | Qt.ItemIsEnabled)
            checkbox_item.setCheckState(Qt.Unchecked)
            self.table.setItem(row, 0, checkbox_item)

            self.table.setItem(row, 1, BaseCell(record.vt_symbol, ''))
            self.table.setItem(row, 2, BaseCell(record.stage, ''))
            self.table.setItem(row, 3, BaseCell(str(record.create_date), ''))

            row += 1
        self.inited = True

    def query_table(self):
        filter_text = self.filter_line.text().strip().lower()
        for row in range(self.table.rowCount()):
            vt_symbol_item = self.table.item(row, 1)
            self.table.setRowHidden(row, filter_text not in vt_symbol_item.text().lower())

    def select_all_rows(self, checked):
        """全选/取消全选功能，考虑排序和过滤后的实际显示顺序"""
        # 暂时禁用排序，避免在设置复选框状态时触发重新排序
        self.table.setSortingEnabled(False)

        # 遍历所有行，按照当前显示的顺序
        for row in range(self.table.rowCount()):
            if not self.table.isRowHidden(row):
                checkbox_item = self.table.item(row, 0)
                if checkbox_item:  # 确保item存在
                    checkbox_item.setCheckState(Qt.Checked if checked else Qt.Unchecked)

        # 重新启用排序
        self.table.setSortingEnabled(True)

        # 更新按钮文本
        self.select_all_button.setText("取消全选" if checked else "全选")

    def handle_item_changed(self, item):
        if item.column() == 0 and self.inited:  # 检查是否为 CheckBox 列
            if isinstance(item, QTableWidgetItem):
                check_state = item.checkState()
                row = item.row()
                vt_symbol_item = self.table.item(row, 1)  # 获取 vt_symbol 列内容
                stage_item = self.table.item(row, 2)  # 获取 stage 列内容
                create_date_item = self.table.item(row, 3)  # 获取 create_date 列内容

                # 检查获取的 item 是否为 None
                vt_symbol = vt_symbol_item.text() if vt_symbol_item else "N/A"
                stage = stage_item.text() if stage_item else "N/A"
                create_date = create_date_item.text() if create_date_item else "N/A"
                check_state_str = "Checked" if check_state == Qt.Checked else "Unchecked"

                msg = f"{check_state_str}: {vt_symbol}, {stage}, {create_date}"
                self.write_log(msg)

    def write_log(self, msg: str) -> None:
        """
        Create cta engine log event.
        """
        log: LogData = LogData(msg=msg, gateway_name=APP_NAME)
        event: Event = Event(type=EVENT_LOG, data=log)
        self.event_engine.put(event)

    @db_error_handler
    def generate_tasks(self, event: Event):
        """使用ORM生成任务"""
        # 从 setting_local 表中查询所有数据
        records = (SettingLocal
                  .select()
                  .order_by(fn.Rand())
                  .limit(random.randint(1, 5)))

        # 获取当前最大id
        max_id_query = TodoNotify.select(fn.MAX(TodoNotify.id)).scalar()
        current_max_id = max_id_query if max_id_query is not None else 0

        tasks = []
        for i, record in enumerate(records, 1):
            content = f"{record.vt_symbol}_{record.stage}"
            direction = random.choice(["buy", "sell"])
            offset = random.choice(["open", "close"])
            price = random.uniform(100, 200)
            create_date = datetime.now()

            TodoNotify.create(
                id=current_max_id + i,  # 手动递增id
                content=content,
                direction=direction,
                offset=offset,
                price=price,
                create_date=create_date
            )

            tasks.append(content)

        self.write_log(f"Generated Tasks: {tasks}")

    @db_error_handler
    def notify_filtered_tasks(self, event: Event):
        """使用ORM通知过滤后的任务"""
        # 查询新任务
        query = (TodoNotify
                .select()
                .where(TodoNotify.id > self.last_max_id)
                 # .order_by(TodoNotify.id)
                 )

        # 转换为DataFrame以便打印
        records = list(query.dicts())
        if records:
            df = pd.DataFrame(records)
            self.write_log(f"notify_filtered_tasks: {df} {self.last_max_id}")

        filtered_tasks = []
        current_max_id = self.last_max_id

        for result in query:
            contract, stage = result.content.rsplit("_", 1)
            
            for row in range(self.table.rowCount()):
                contract_item = self.table.item(row, 1)
                stage_item = self.table.item(row, 2)
                checkbox_item = self.table.item(row, 0)
                
                if (contract_item.text() == contract and
                    stage_item.text() == stage and
                    checkbox_item.checkState() == Qt.Checked):
                    
                    task_details = (f"{result.id}\t"
                                  f"{result.content}\t"
                                  f"{result.direction}\t"
                                  f"{result.offset}\t"  # 添加offset字段
                                  f"{result.price}\t"
                                  f"{result.create_date}")
                    filtered_tasks.append(task_details)

            if result.id > current_max_id:
                current_max_id = result.id

        # 更新last_max_id
        self.last_max_id = current_max_id

        if self.inited:
            # 获取之前通知失败的内容
            failed_msgs = self.msger.get_failed_msgs()
            if failed_msgs:
                filtered_tasks = failed_msgs + filtered_tasks

            if filtered_tasks:
                success, reason = self.msger.notify(filtered_tasks)
                if not success:
                    self.write_log(f"Failed to notify tasks. Error: {reason}")

    def closeEvent(self, event: QtGui.QCloseEvent) -> None:
        """关闭事件"""
        reply = QMessageBox.question(  # ?
            self, "退出", "确认退出程序？", QMessageBox.Yes | QMessageBox.No, QMessageBox.No)

        if reply == QMessageBox.Yes:
            # 关闭核心引擎
            self.main_engine.close()
            event.accept()
            sys.exit(0)
        else:
            event.ignore()

    @db_error_handler
    def reload_table(self):
        """重新加载表格设置"""
        # 清空现有表格
        self.table.setRowCount(0)
        self.inited = False
        # 重新加载设置
        self.load_setting()
        self.write_log("Table settings reloaded at 7 PM")

    def check_reload_time(self, event: Event):
        """检查是否需要重载表格（每天下午7点）"""        
        current_time = datetime.now()
        # 检查是否是下午7点且不是今天已经重载过
        if (current_time.hour == 14 and
            current_time.minute == 43 and
            current_time.date() != self.last_reload_date):
            self.reload_table()
            self.last_reload_date = current_time.date()

class BaseMsg:
    def __init__(self):
        self.failed_msgs = []
        self.header = "Id\tContent\tDirection\tOffset\tPrice\tCreate Date\n"

    def notify(self, msgs: list) -> (bool, str):
        raise NotImplementedError("Notify method should be implemented by subclasses")

    def save_failed_msg(self, msgs: list) -> None:
        self.failed_msgs.extend(msgs)

    def get_failed_msgs(self) -> list:
        msgs = self.failed_msgs
        self.failed_msgs = []  # 清空保存的失败消息
        return msgs

    def format_msgs(self, msgs: list) -> str:
        return self.header + "\n".join(msgs)


class PrintMsg(BaseMsg):
    def notify(self, msgs: list) -> (bool, str):
        formatted_msg = self.format_msgs(msgs)
        try:
            print(formatted_msg)
            return True, "操作成功"
        except Exception as e:
            self.save_failed_msg(msgs)
            return False, str(e)


class QMsg(BaseMsg):
    API_URL = "https://qmsg.zendee.cn/{via}/{key}"

    def __init__(self, key: str, qq: str = None, bot: str = None, group: bool = False):
        '''
        key: qmsg酱的key。获取地址：https://qmsg.zendee.cn/user
        '''
        super().__init__()
        self.key = key
        self.qq = qq  # 需要推送到的QQ，多个以英文逗号分割，例如：12345,12346。如果不填，则推送到所有QQ
        self.bot = bot  # （仅私有云有效）推送使用的机器人。如果不填，则随机选择一个机器人。
        self.group = group  # 是否推送到群

    def notify(self, msgs: list) -> (bool, str):
        formatted_msg = self.format_msgs(msgs)
        url = self.API_URL.format(via="jgroup" if self.group else "jsend", key=self.key)
        payload = {"msg": formatted_msg}
        if self.qq:
            payload["qq"] = self.qq
        if self.bot:
            payload["bot"] = self.bot
        try:
            # response = requests.post(url, data=payload) # send/group接口需要使用form格式
            response = requests.post(url, json=payload)  # jsend/jgroup接口需要使用json格式
            if response.status_code == 200:
                response_json = response.json()
                if response_json.get("success"):
                    return True, response_json.get("reason", "操作成功")
                else:
                    self.save_failed_msg(msgs)
                    return False, response_json.get("reason", "未知错误")
            else:
                self.save_failed_msg(msgs)
                return False, f"HTTP错误: {response.status_code}，{response.text}"
        except requests.RequestException as e:
            self.save_failed_msg(msgs)
            return False, f"请求异常: {str(e)}"
        except Exception as e:
            self.save_failed_msg(msgs)
            return False, f"未知错误: {str(e)}"


class WeComMsg(BaseMsg):
    API_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key={key}"

    def __init__(self, key: str):
        super().__init__()
        self.key = key

    def notify(self, msgs: list) -> (bool, str):
        formatted_msg = self.format_msgs(msgs)
        url = self.API_URL.format(key=self.key)
        payload = {"msgtype": "text", "text": {"content": formatted_msg}}
        try:
            response = requests.post(url, json=payload)
            if response.status_code == 200:
                response_json = response.json()
                if response_json.get("errcode") == 0:
                    return True, response_json.get("errmsg", "操作成功")
                else:
                    self.save_failed_msg(msgs)
                    return False, response_json.get("errmsg", "未知错误")
            else:
                self.save_failed_msg(msgs)
                return False, f"HTTP错误: {response.status_code}，{response.text}"
        except requests.RequestException as e:
            self.save_failed_msg(msgs)
            return False, f"请求异常: {str(e)}"
        except Exception as e:
            self.save_failed_msg(msgs)
            return False, f"未知错误: {str(e)}"


class LarkMsg(BaseMsg):
    """飞书群机器人消息推送
    """
    API_URL = "https://www.feishu.cn/flow/api/trigger-webhook/{key}"

    def __init__(self, key: str, sign_key: str = None):
        """
        Args:
            key: 飞书群机器人的webhook key
            sign_key: 安全设置中的签名校验密钥，如果机器人开启了签名校验，需要提供此参数
        """
        super().__init__()
        self.key = key
        self.sign_key = sign_key

    def _sign(self, timestamp: int) -> str:
        """计算签名
        签名方式：timestamp + key 做sha256, 再进行base64编码
        """
        if not self.sign_key:
            return None
            
        string_to_sign = f"{timestamp}\n{self.sign_key}"  # 有个换行符 \n
        hmac_code = hmac.new(
            string_to_sign.encode("utf-8"),
            digestmod=hashlib.sha256
        ).digest()
        
        return base64.b64encode(hmac_code).decode('utf-8')

    def notify(self, msgs: list) -> (bool, str):
        formatted_msg = self.format_msgs(msgs)
        url = self.API_URL.format(key=self.key)
        
        # 构造消息内容
        payload = {
            "msg_type": "text",  # 消息类型，支持 text、post、image、interactive、share_chat、share_user等
            "content": {
                "text": formatted_msg
            }
        }
        
        # 如果配置了签名校验
        if self.sign_key:
            timestamp = int(time.time())
            sign = self._sign(timestamp)
            payload.update({
                "timestamp": timestamp,
                "sign": sign
            })

        try:
            response = requests.post(url, json=payload)
            if response.status_code == 200:
                response_json = response.json()
                # 飞书API返回格式: {"code": 0, "msg": "success", "data": {}}
                if response_json.get("code") == 0:
                    return True, response_json.get("msg", "操作成功")
                else:
                    self.save_failed_msg(msgs)
                    return False, f"{response_json.get('msg', '未知错误')} (code: {response_json.get('code')})"
            else:
                self.save_failed_msg(msgs)
                return False, f"HTTP错误: {response.status_code}，{response.text}"
        except requests.RequestException as e:
            self.save_failed_msg(msgs)
            return False, f"请求异常: {str(e)}"
        except Exception as e:
            self.save_failed_msg(msgs)
            return False, f"未知错误: {str(e)}"


class WeComAppMsg(BaseMsg):
    '''适配器模式'''

    def __init__(self, touser: str = '', toparty: str = ''):
        super().__init__()
        self.wx = WeComApp()
        self.touser = touser
        self.toparty = toparty

    def notify(self, msgs: list) -> (bool, str):
        formatted_msg = self.format_msgs(msgs)
        try:
            res = self.wx.send_data(formatted_msg, touser=self.touser, toparty=self.toparty)
            if res == "ok":
                return True, "操作成功"
            else:
                self.save_failed_msg(msgs)
                return False, res
        except Exception as e:
            self.save_failed_msg(msgs)
            return False, str(e)


class WeComApp:
    def __init__(self):
        self.CORPID = 'wwd37079c917e06dbb'  # 企业ID，在管理后台获取
        self.AGENTID = '1000007'  # 应用ID，在后台应用中获取
        self.CORPSECRET = 'bUWm53U6lGRH_vpwCQK0-dHp8HbagFoTKIgKbCAciTo'  # 自建应用的Secret，每个自建应用里都有单独的secret

    def _get_access_token(self):
        url = 'https://qyapi.weixin.qq.com/cgi-bin/gettoken'
        values = {'corpid': self.CORPID, 'corpsecret': self.CORPSECRET, }
        req = requests.post(url, params=values)
        data = json.loads(req.text)
        return data["access_token"]

    def get_access_token(self):
        try:
            filepath = os.path.abspath(os.path.dirname(__file__))
            os.mkdir(filepath + '/tmp')
        except:
            pass

        try:
            with open(filepath + '/tmp/access_token.conf', 'r') as f:
                t, access_token = f.read().split()
        except:
            with open(filepath + '/tmp/access_token.conf', 'w') as f:
                access_token = self._get_access_token()
                cur_time = time.time()
                f.write('\t'.join([str(cur_time), access_token]))
                return access_token
        else:
            cur_time = time.time()
            if 0 < cur_time - float(t) < 7260:
                return access_token
            else:
                with open(filepath + '/tmp/access_token.conf', 'w') as f:
                    access_token = self._get_access_token()
                    f.write('\t'.join([str(cur_time), access_token]))
                    return access_token

    def send_data(self, message, touser='', toparty=''):
        send_url = 'https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token=' + self.get_access_token()
        send_values = {"msgtype": "text", "agentid": self.AGENTID, "text": {"content": message}, "safe": "0"}
        if len(touser) > 0:
            send_values["touser"] = touser
        if len(toparty) > 0:
            send_values["toparty"] = int(toparty)
        send_msges = (bytes(json.dumps(send_values), 'utf-8'))
        respone = requests.post(send_url, send_msges)
        respone = respone.json()  # 当返回的数据是json串的时候直接用.json即可将respone转换成字典
        # print(respone["errmsg"])
        return respone["errmsg"]

    def get_media_ID(self, path):  ##上传到临时素材  图片ID
        Gtoken = self.get_access_token()
        img_url = "https://qyapi.weixin.qq.com/cgi-bin/media/upload?access_token={}&type=image".format(Gtoken)
        xx = {'type': 'image', 'offset': 0, 'count': 20}
        files = {'image': open(path, 'rb')}
        r = requests.post(img_url, json=json.dumps(xx), files=files)
        # print(r.text)
        re = json.loads(r.text)
        return re['media_id']

    def send_pic(self, path, touser='', toparty=''):  ##发送图片
        img_id = self.get_media_ID(path)
        post_data1 = {}
        msg_content1 = {}
        msg_content1['media_id'] = img_id
        if len(touser) > 0:
            post_data1['touser'] = touser
        if len(toparty) > 0:
            post_data1['toparty'] = int(toparty)
        post_data1['msgtype'] = 'image'
        post_data1['agentid'] = self.AGENTID
        post_data1['image'] = msg_content1
        post_data1['safe'] = '0'
        Gtoken = self.get_access_token()
        purl2 = "https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token={}".format(Gtoken)
        json_post_data1 = json.dumps(post_data1)
        request_post = urllib.request.urlopen(purl2, json_post_data1.encode(encoding='UTF8'))
        return request_post

    def test(self):
        self.send_data("这是程序发送的测试信息", touser='liaoyuan')  # self.send_pic("c:/Users/<USER>/Pictures/xy.jpg"


def main():
    """"""
    qapp = create_qapp()

    event_engine = EventEngine()

    main_engine = MainEngine(event_engine)

    main_window = MainWindow(main_engine, event_engine)
    # main_window.showMaximized()
    main_window.show()

    def exit(signum, frame):
        main_window.close()
    signal.signal(signal.SIGINT, exit)

    qapp.exec()


if __name__ == "__main__":
    main()
