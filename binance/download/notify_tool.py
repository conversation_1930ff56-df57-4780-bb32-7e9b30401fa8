import requests
import traceback
from datetime import datetime
from vnpy.usertools.task_db_manager import task_db_manager
import os

def get_flag():
    flag_dir = '/opt/flag/'
    main_flag = os.path.isfile(os.path.join(flag_dir, 'main'))
    backup_flag = os.path.isfile(os.path.join(flag_dir, 'backup'))
    flag =""
    if main_flag and backup_flag:
        flag = 'error? have 2 flag main and backup'
    elif main_flag:
        flag = 'main'
    elif backup_flag:
        flag = 'backup'
    else:
        flag = 'None? no flag show main or backup'
    return flag

# 拿到初始参数,取order_dict中的第一个key对应的value
def report_we_alert(msg):
    flag = get_flag()
    if flag == "main":
        flag = '主'
    elif flag == "backup":
        flag = '备'
    else:
        flag = '未知主机'
    url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=0f7c710a-154d-457cfb2b18e1a4"
    payload = {"msgtype": "text", "text": {"content": f"{flag}_{msg}"}}
    try:
        response = requests.post(url, json=payload)
        if response.status_code == 200:
            response_json = response.json()
            if response_json.get("errcode") == 0:
                return True, response_json.get("errmsg", "操作成功")
            else:
                error_msg = f"通知失败{msg}"
                task_db_manager.report_alert(content=error_msg, assigned_to="vigar")
                return False, response_json.get("errmsg", "未知错误")
        else:
            error_msg = f"response_status code {response.status_code}  {msg}"
            task_db_manager.report_alert(content=error_msg, assigned_to="vigar")
            return False, f"HTTP错误: {response.status_code}，{response.text}"
    except Exception as e:
        error_msg = f"{traceback.format_exc()}  {msg}"
        task_db_manager.report_alert(content=error_msg, assigned_to="vigar")
        return False, f"未知错误: {str(e)}"

# 这里加上main，对上面的函数逐一做测试    
if __name__ == "__main__":
    flag = get_flag()
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    report_we_alert(f"hello zhengdao {current_time} {flag}")
    