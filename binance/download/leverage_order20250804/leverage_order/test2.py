import mysql.connector
from datetime import datetime
conn = mysql.connector.connect(
    host="localhost",
    port=3306,
    user="root",
    password="@11235813",
database="crypto"
)
cursor = conn.cursor()
cursor.execute("CREATE DATABASE IF NOT EXISTS crypto")  # [4,6](@ref)


# 批量数据插入（高效方式）
def batch_insert_records():
    insert_query = """
    INSERT INTO leverage_reset (
        username, symbol, leverage, create_date, status
    ) VALUES (%s, %s, %s, %s, %s)
    """
    batch_data = [
        ("test", "BTCUSDT.BINANCE", 30, datetime.now(), 0),
        ("test", "XRPUSDT.BINANCE", 15, datetime.now(), 0),
    ]
    cursor.executemany(insert_query, batch_data)
    conn.commit()
    print(f"批量插入 {cursor.rowcount} 条记录")

# 执行插入操作
try:

    batch_insert_records()
except mysql.connector.Error as err:
    print(f"数据库错误: {err}")
    conn.rollback()  # 事务回滚[10](@ref)
finally:
    cursor.close()
    conn.close()