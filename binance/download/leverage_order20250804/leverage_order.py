import sys
from datetime import datetime, timedelta
import time
from typing import Optional, Dict

import yaml
import json
from pathlib import Path
import traceback
from queue import Queue
from threading import Thread, Lock

from peewee import (
    Model, CharField, IntegerField, DateTimeField,
    BigIntegerField, TextField, SQL
)
from playhouse.shortcuts import ReconnectMixin
from playhouse.db_url import connect


from vnpy_evo.event import EventEngine
from vnpy_evo.trader.engine import MainEngine
from vnpy_evo.trader.object import ContractData
import sys

sys.path.append('/opt/external_lib/prod_gateway')
from binance_linear_gateway import BinanceLinearGateway,symbol_gateway_extra
from playhouse.pool import PooledMySQLDatabase
from peewee import MySQLDatabase
class ReconnectMySQLDatabase(ReconnectMixin, MySQLDatabase):
    """带有重连机制的MySQL数据库类"""
    pass


class DbUpdateTask:
    """数据库更新任务"""

    def __init__(self, task_id: int, status: int, remarks: str):
        self.task_id = task_id
        self.status = status
        self.remarks = remarks


class DbUpdateQueue:
    """数据库更新队列管理器"""

    def __init__(self, app=None):
        self._queue: Dict[int, DbUpdateTask] = {}
        self._lock = Lock()
        self._thread: Optional[Thread] = None
        self._active = False
        self._updating = False
        self._retry_interval = 2
        self._app = app

    def write_log(self, msg: str, include_traceback: bool = False):
        log_msg = msg
        if include_traceback:
            log_msg = f"{msg}\n{traceback.format_exc()}"
        self._app.write_log(log_msg)

    def start(self):
        self._active = True
        self._thread = Thread(target=self._run, daemon=True)
        self._thread.start()

    def stop(self):
        self._active = False
        if self._thread:
            self._thread.join()

    def put(self, task_id: int, status: int, remarks: str):
        with self._lock:
            self._queue[task_id] = DbUpdateTask(task_id, status, remarks)

    def is_empty(self) -> bool:
        with self._lock:
            return len(self._queue) == 0 and not self._updating

    def _reconnect_database(self) -> bool:
        try:
            try:
                LeverageReset._meta.database.close()
            except Exception:
                pass

            LeverageReset._meta.database.connect()
            self.write_log("数据库重连成功")
            return True
        except Exception as e:
            self.write_log(f"数据库重连失败: {str(e)}", include_traceback=True)
            return False

    def _execute_with_retry(self, tasks_to_update: list) -> None:
        retry_count = 0
        while True:
            try:
                with LeverageReset._meta.database.atomic():
                    for task in tasks_to_update:
                        LeverageReset.update(
                            status=task.status,
                            remarks=task.remarks
                        ).where(LeverageReset.id == task.task_id).execute()
                return
            except Exception as e:
                retry_count += 1
                self.write_log(f"数据库更新错误 (尝试 {retry_count}): {str(e)}", include_traceback=True)

                if self._reconnect_database():
                    self.write_log(f"重连成功，将在 {self._retry_interval} 秒后重试...")
                else:
                    self.write_log(f"重连失败，将在 {self._retry_interval} 秒后重试...")
                time.sleep(self._retry_interval)

    def _run(self):
        while self._active:
            tasks_to_update = []

            with self._lock:
                if self._queue:
                    tasks_to_update = list(self._queue.values())
                    self._queue.clear()
                    self._updating = True

            if tasks_to_update:
                self._execute_with_retry(tasks_to_update)
                with self._lock:
                    self._updating = False

            time.sleep(1)


class LeverageReset(Model):
    """杠杆重置任务表"""
    id = BigIntegerField(primary_key=True)
    username = CharField(max_length=63)
    symbol = CharField(max_length=63)
    leverage = IntegerField()
    create_date = DateTimeField()
    update_date = DateTimeField(constraints=[SQL('DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')])
    status = IntegerField(constraints=[SQL('DEFAULT 0')])  # 0: 初始, 2: 完成, 5: 错误
    remarks = TextField(null=True)

    class Meta:
        database = None
        table_name = 'leverage_reset'
        indexes = (
            (('username', 'symbol'), False),
        )


class UserGateway(BinanceLinearGateway):
    """每个用户的网关"""

    def write_log(self, msg: str) -> None:
        super().write_log(f"[{self.gateway_name}] {msg}")


class LeverageResetApp:
    """杠杆重置任务执行应用"""

    def __init__(self):
        self.event_engine = EventEngine()
        self.main_engine = MainEngine(self.event_engine)

        self.user_gateways: Dict[str, UserGateway] = {}
        self.db = None
        self.db_config = None
        self.users_config = None

        # 数据库更新队列
        self.db_update_queue = DbUpdateQueue(self)

        self.timer_count = 0

        self.load_config()
        self.setup_database()
        self.init_gateway()

    def write_log(self, msg: str) -> None:
        func_name = sys._getframe(1).f_code.co_name
        class_name = self.__class__.__name__
        formatted_msg = f"[{class_name}.{func_name}] {msg}"
        self.main_engine.write_log(formatted_msg)

    def load_config(self, config_path: str = r"E:\meigu\IntervalTools-master\IB相关\信号对比\杠杆\opt\test\ping_service\fk_order_config.yaml"):
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)

            if "database" not in config:
                raise ValueError("配置文件中缺少数据库配置")
            self.db_config = config["database"]
            self.users_config = config.get("users", {})

        except Exception as e:
            self.write_log(f"加载配置文件失败: {str(e)}\n{traceback.format_exc()}")
            raise

    def setup_database(self):
        try:
            # 创建数据库连接
            self.db = ReconnectMySQLDatabase(
                database=self.db_config["database"],
                user=self.db_config["user"],
                password=self.db_config["password"],
                host=self.db_config["host"],
                port=self.db_config["port"]
            )

            # 设置数据库连接
            LeverageReset._meta.database = self.db
            self.db.connect()
            self.write_log("数据库连接成功")

            # 启动数据库更新队列
            self.db_update_queue.start()

        except Exception as e:
            self.write_log(f"数据库连接失败: {str(e)}\n{traceback.format_exc()}")
            raise

    def init_gateway(self):
        try:
            # 连接所有配置的用户
            for username, settings in self.users_config.items():
                work_dir = settings["work_dir"]
                connect_file = Path(work_dir) / ".vntrader" / "connect_binance_usdt.json"
                print(connect_file)
                connect_file.parent.mkdir(parents=True, exist_ok=True)
                if not connect_file.exists():
                    self.write_log(f"用户 {username} 的连接配置文件不存在")
                    with open(connect_file ,'w') as f:
                        print(f"文件已创建：{connect_file}")
                    continue

                with open(connect_file, 'r', encoding='utf-8') as f:
                    connect_config = json.load(f)

                # 创建用户网关
                gateway = UserGateway(self.event_engine, f"{username}.BINANCE_LINEAR")
                gateway.connect(connect_config)
                self.user_gateways[username] = gateway
                self.write_log(f"用户 {username} 的交易接口初始化成功")

                time.sleep(1)

        except Exception as e:
            self.write_log(f"初始化交易接口失败: {str(e)}\n{traceback.format_exc()}")
            raise

    def process_leverage_reset(self):
        try:
            # 如果有待更新的数据库任务，跳过查询新任务
            if not self.db_update_queue.is_empty():
                return

            # 计算一小时前的时间
            one_hour_ago = datetime.now() - timedelta(hours=1)

            # 查询待执行的任务
            tasks = (LeverageReset
                     .select()
                     .where(
                (LeverageReset.status == 0) &
                (LeverageReset.create_date >= one_hour_ago)
            )
                     .order_by(LeverageReset.create_date))

            for task in tasks:
                self.write_log(f"开始处理杠杆重置任务：{task.id}")
                self.process_single_leverage_reset(task)

        except Exception as e:
            self.write_log(f"杠杆重置任务扫描过程中发生错误: {str(e)}\n{traceback.format_exc()}")

    def process_single_leverage_reset(self, task: LeverageReset):
        try:
            username = task.username
            symbol = task.symbol
            leverage = task.leverage

            # 检查用户网关是否存在
            if username not in self.user_gateways:
                self.db_update_queue.put(
                    task.id,
                    5,  # ERROR
                    f"未找到用户 {username} 的交易接口"
                )
                return

            gateway = self.user_gateways[username]

            # 获取合约信息
            contract = self.main_engine.get_contract(symbol)
            if not contract:
                self.db_update_queue.put(
                    task.id,
                    5,  # ERROR
                    f"未找到合约: {symbol}"
                )
                return

            # 尝试设置杠杆
            try:
                max_retries = 3
                success = False

                for attempt in range(max_retries):
                    try:
                        # 发送杠杆设置请求
                        gateway.rest_api.set_leverage(symbol.split('.')[0], leverage)

                        # 关键：等待足够时间让回调执行
                        time.sleep(2.5)  # 比通常响应时间稍长

                        # 现在检查缓存值
                        new_leverage = symbol_gateway_extra[symbol.split('.')[0]]["leverage"][
                            gateway.rest_api.gateway_name]

                        if new_leverage == leverage:
                            success = True
                            break  # 成功则跳出循环

                    except Exception as e:
                        # 网络错误等异常处理
                        error_detail = f"尝试 {attempt + 1}/{max_retries} 失败: {str(e)}"
                        gateway.write_log(error_detail)

                # 最终结果处理
                if success:
                    self.db_update_queue.put(
                        task.id,
                        2,  # FINISHED
                        f"成功设置 {symbol} 杠杆为 {leverage} (尝试 {attempt + 1} 次)"
                    )
                    gateway.write_log(f"成功设置 {symbol} 杠杆为 {leverage}")
                else:
                    final_error = f"设置杠杆失败: {symbol} {leverage} (重试 {max_retries} 次后仍失败)"
                    self.db_update_queue.put(
                        task.id,
                        5,  # ERROR
                        final_error
                    )
                    gateway.write_log(final_error)

            except Exception as e:
                # 顶层异常处理保持不变
                error_msg = f"设置杠杆时发生全局错误: {str(e)}"
                self.db_update_queue.put(task.id, 5, error_msg)
                gateway.write_log(error_msg)


        except Exception as e:
            error_msg = f"处理杠杆重置任务失败: {str(e)}\n{traceback.format_exc()}"
            self.write_log(error_msg)
            self.db_update_queue.put(
                task.id,
                5,  # ERROR
                error_msg
            )

    def run(self):
        try:
            self.write_log("杠杆重置任务执行应用已启动")
            while True:
                # 每10秒处理一次杠杆重置任务
                time.sleep(10)
                self.process_leverage_reset()

        except KeyboardInterrupt:
            self.write_log("应用正在关闭...")
        finally:
            self.close()

    def close(self):
        self.db_update_queue.stop()
        if self.db:
            self.db.close()
        self.main_engine.close()


def main():
    app = LeverageResetApp()
    app.run()


if __name__ == "__main__":
    main()