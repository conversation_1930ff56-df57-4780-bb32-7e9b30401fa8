2025-06-09T00:00:21.215306+0800  Level 20: 开始检查数据完整性
2025-06-09T00:01:21.273930+0800  Level 20: 开始检查数据完整性
2025-06-09T00:02:21.336825+0800  Level 20: 开始检查数据完整性
2025-06-09T00:03:21.383951+0800  Level 20: 开始检查数据完整性
2025-06-09T00:04:21.435015+0800  Level 20: 开始检查数据完整性
2025-06-09T00:05:21.480862+0800  Level 20: 开始检查数据完整性
2025-06-09T00:06:21.532128+0800  Level 20: 开始检查数据完整性
2025-06-09T00:07:21.580438+0800  Level 20: 开始检查数据完整性
2025-06-09T00:08:21.640645+0800  Level 20: 开始检查数据完整性
2025-06-09T00:09:21.692945+0800  Level 20: 开始检查数据完整性
2025-06-09T00:10:21.771864+0800  Level 20: 开始检查数据完整性
2025-06-09T00:11:21.830860+0800  Level 20: 开始检查数据完整性
2025-06-09T00:12:21.891508+0800  Level 20: 开始检查数据完整性
2025-06-09T00:13:21.942224+0800  Level 20: 开始检查数据完整性
2025-06-09T00:14:21.996357+0800  Level 20: 开始检查数据完整性
2025-06-09T00:15:22.053007+0800  Level 20: 开始检查数据完整性
2025-06-09T00:16:22.100870+0800  Level 20: 开始检查数据完整性
2025-06-09T00:17:22.164854+0800  Level 20: 开始检查数据完整性
2025-06-09T00:18:22.241075+0800  Level 20: 开始检查数据完整性
2025-06-09T00:19:22.302948+0800  Level 20: 开始检查数据完整性
2025-06-09T00:20:22.347704+0800  Level 20: 开始检查数据完整性
2025-06-09T00:21:22.398945+0800  Level 20: 开始检查数据完整性
2025-06-09T00:22:22.449453+0800  Level 20: 开始检查数据完整性
2025-06-09T00:23:22.506888+0800  Level 20: 开始检查数据完整性
2025-06-09T00:24:22.563860+0800  Level 20: 开始检查数据完整性
2025-06-09T00:25:22.627125+0800  Level 20: 开始检查数据完整性
2025-06-09T00:26:22.677652+0800  Level 20: 开始检查数据完整性
2025-06-09T00:27:22.741615+0800  Level 20: 开始检查数据完整性
2025-06-09T00:28:22.822812+0800  Level 20: 开始检查数据完整性
2025-06-09T00:29:22.865758+0800  Level 20: 开始检查数据完整性
2025-06-09T00:30:22.945995+0800  Level 20: 开始检查数据完整性
2025-06-09T00:31:22.986952+0800  Level 20: 开始检查数据完整性
2025-06-09T00:32:23.043251+0800  Level 20: 开始检查数据完整性
2025-06-09T00:33:23.093703+0800  Level 20: 开始检查数据完整性
2025-06-09T00:34:23.160719+0800  Level 20: 开始检查数据完整性
2025-06-09T00:35:23.211204+0800  Level 20: 开始检查数据完整性
2025-06-09T00:36:23.281724+0800  Level 20: 开始检查数据完整性
2025-06-09T00:37:23.339686+0800  Level 20: 开始检查数据完整性
2025-06-09T00:38:23.372776+0800  Level 20: 开始检查数据完整性
2025-06-09T00:39:23.423010+0800  Level 20: 开始检查数据完整性
2025-06-09T00:40:23.486456+0800  Level 20: 开始检查数据完整性
2025-06-09T00:41:23.561583+0800  Level 20: 开始检查数据完整性
2025-06-09T00:42:23.617023+0800  Level 20: 开始检查数据完整性
2025-06-09T00:43:23.671625+0800  Level 20: 开始检查数据完整性
2025-06-09T00:44:23.717079+0800  Level 20: 开始检查数据完整性
2025-06-09T00:45:23.768723+0800  Level 20: 开始检查数据完整性
2025-06-09T00:46:23.821883+0800  Level 20: 开始检查数据完整性
2025-06-09T00:47:23.884160+0800  Level 20: 开始检查数据完整性
2025-06-09T00:48:23.935503+0800  Level 20: 开始检查数据完整性
2025-06-09T00:49:23.986230+0800  Level 20: 开始检查数据完整性
2025-06-09T00:50:24.046181+0800  Level 20: 开始检查数据完整性
2025-06-09T00:51:24.114630+0800  Level 20: 开始检查数据完整性
2025-06-09T00:52:24.161363+0800  Level 20: 开始检查数据完整性
2025-06-09T00:53:24.223499+0800  Level 20: 开始检查数据完整性
2025-06-09T00:54:24.280774+0800  Level 20: 开始检查数据完整性
2025-06-09T00:55:24.358713+0800  Level 20: 开始检查数据完整性
2025-06-09T00:56:24.419588+0800  Level 20: 开始检查数据完整性
2025-06-09T00:57:24.500848+0800  Level 20: 开始检查数据完整性
2025-06-09T00:58:24.564062+0800  Level 20: 开始检查数据完整性
2025-06-09T00:59:24.614523+0800  Level 20: 开始检查数据完整性
2025-06-09T01:00:24.680380+0800  Level 20: 开始检查数据完整性
2025-06-09T01:01:24.738590+0800  Level 20: 开始检查数据完整性
2025-06-09T01:02:24.800981+0800  Level 20: 开始检查数据完整性
2025-06-09T01:03:24.860956+0800  Level 20: 开始检查数据完整性
2025-06-09T01:04:24.926458+0800  Level 20: 开始检查数据完整性
2025-06-09T01:05:24.970402+0800  Level 20: 开始检查数据完整性
2025-06-09T01:06:25.023642+0800  Level 20: 开始检查数据完整性
2025-06-09T01:07:25.084992+0800  Level 20: 开始检查数据完整性
2025-06-09T01:08:25.123721+0800  Level 20: 开始检查数据完整性
2025-06-09T01:09:25.176936+0800  Level 20: 开始检查数据完整性
2025-06-09T01:10:25.228525+0800  Level 20: 开始检查数据完整性
2025-06-09T01:11:25.283294+0800  Level 20: 开始检查数据完整性
2025-06-09T01:12:25.364954+0800  Level 20: 开始检查数据完整性
2025-06-09T01:13:25.414211+0800  Level 20: 开始检查数据完整性
2025-06-09T01:14:25.474099+0800  Level 20: 开始检查数据完整性
2025-06-09T01:15:25.526116+0800  Level 20: 开始检查数据完整性
2025-06-09T01:16:25.588801+0800  Level 20: 开始检查数据完整性
2025-06-09T01:17:25.666450+0800  Level 20: 开始检查数据完整性
2025-06-09T01:18:25.727282+0800  Level 20: 开始检查数据完整性
2025-06-09T01:19:25.782181+0800  Level 20: 开始检查数据完整性
2025-06-09T01:20:25.846828+0800  Level 20: 开始检查数据完整性
2025-06-09T01:21:25.913211+0800  Level 20: 开始检查数据完整性
2025-06-09T01:22:25.961592+0800  Level 20: 开始检查数据完整性
2025-06-09T01:23:26.033403+0800  Level 20: 开始检查数据完整性
2025-06-09T01:24:26.084351+0800  Level 20: 开始检查数据完整性
2025-06-09T01:25:26.127633+0800  Level 20: 开始检查数据完整性
2025-06-09T01:26:26.189558+0800  Level 20: 开始检查数据完整性
2025-06-09T01:27:26.230371+0800  Level 20: 开始检查数据完整性
2025-06-09T01:28:26.293535+0800  Level 20: 开始检查数据完整性
2025-06-09T01:29:26.338384+0800  Level 20: 开始检查数据完整性
2025-06-09T01:30:26.416755+0800  Level 20: 开始检查数据完整性
2025-06-09T01:31:26.467374+0800  Level 20: 开始检查数据完整性
2025-06-09T01:32:26.523520+0800  Level 20: 开始检查数据完整性
2025-06-09T01:33:26.582687+0800  Level 20: 开始检查数据完整性
2025-06-09T01:34:26.648774+0800  Level 20: 开始检查数据完整性
2025-06-09T01:35:26.709801+0800  Level 20: 开始检查数据完整性
2025-06-09T01:36:26.767962+0800  Level 20: 开始检查数据完整性
2025-06-09T01:37:26.815568+0800  Level 20: 开始检查数据完整性
2025-06-09T01:38:26.871153+0800  Level 20: 开始检查数据完整性
2025-06-09T01:39:26.926025+0800  Level 20: 开始检查数据完整性
2025-06-09T01:40:27.001840+0800  Level 20: 开始检查数据完整性
2025-06-09T01:41:27.054382+0800  Level 20: 开始检查数据完整性
2025-06-09T01:42:27.115698+0800  Level 20: 开始检查数据完整性
2025-06-09T01:43:27.168944+0800  Level 20: 开始检查数据完整性
2025-06-09T01:44:27.218404+0800  Level 20: 开始检查数据完整性
2025-06-09T01:45:27.271644+0800  Level 20: 开始检查数据完整性
2025-06-09T01:46:27.329502+0800  Level 20: 开始检查数据完整性
2025-06-09T01:47:27.391033+0800  Level 20: 开始检查数据完整性
2025-06-09T01:48:27.450590+0800  Level 20: 开始检查数据完整性
2025-06-09T01:49:27.501274+0800  Level 20: 开始检查数据完整性
2025-06-09T01:50:27.549042+0800  Level 20: 开始检查数据完整性
2025-06-09T01:51:27.615051+0800  Level 20: 开始检查数据完整性
2025-06-09T01:52:27.660715+0800  Level 20: 开始检查数据完整性
2025-06-09T01:53:27.719002+0800  Level 20: 开始检查数据完整性
2025-06-09T01:54:27.798996+0800  Level 20: 开始检查数据完整性
2025-06-09T01:55:27.821759+0800  Level 20: 开始检查数据完整性
2025-06-09T01:56:27.873279+0800  Level 20: 开始检查数据完整性
2025-06-09T01:57:27.938431+0800  Level 20: 开始检查数据完整性
2025-06-09T01:58:27.992534+0800  Level 20: 开始检查数据完整性
2025-06-09T01:59:28.048576+0800  Level 20: 开始检查数据完整性
2025-06-09T02:00:28.099994+0800  Level 20: 开始检查数据完整性
2025-06-09T02:01:28.174311+0800  Level 20: 开始检查数据完整性
2025-06-09T02:02:28.218739+0800  Level 20: 开始检查数据完整性
2025-06-09T02:03:28.275994+0800  Level 20: 开始检查数据完整性
2025-06-09T02:04:28.361010+0800  Level 20: 开始检查数据完整性
2025-06-09T02:05:28.403963+0800  Level 20: 开始检查数据完整性
2025-06-09T02:06:28.455901+0800  Level 20: 开始检查数据完整性
2025-06-09T02:07:28.514003+0800  Level 20: 开始检查数据完整性
2025-06-09T02:08:28.567189+0800  Level 20: 开始检查数据完整性
2025-06-09T02:09:28.636620+0800  Level 20: 开始检查数据完整性
2025-06-09T02:10:28.676468+0800  Level 20: 开始检查数据完整性
2025-06-09T02:11:28.729255+0800  Level 20: 开始检查数据完整性
2025-06-09T02:12:28.794549+0800  Level 20: 开始检查数据完整性
2025-06-09T02:13:28.873657+0800  Level 20: 开始检查数据完整性
2025-06-09T02:14:28.930681+0800  Level 20: 开始检查数据完整性
2025-06-09T02:15:28.981756+0800  Level 20: 开始检查数据完整性
2025-06-09T02:16:29.039354+0800  Level 20: 开始检查数据完整性
2025-06-09T02:17:29.107852+0800  Level 20: 开始检查数据完整性
2025-06-09T02:18:29.165388+0800  Level 20: 开始检查数据完整性
2025-06-09T02:19:29.211870+0800  Level 20: 开始检查数据完整性
2025-06-09T02:20:29.261987+0800  Level 20: 开始检查数据完整性
2025-06-09T02:21:29.308418+0800  Level 20: 开始检查数据完整性
2025-06-09T02:22:29.357436+0800  Level 20: 开始检查数据完整性
2025-06-09T02:23:29.419738+0800  Level 20: 开始检查数据完整性
2025-06-09T02:24:29.480325+0800  Level 20: 开始检查数据完整性
2025-06-09T02:25:29.543920+0800  Level 20: 开始检查数据完整性
2025-06-09T02:26:29.622740+0800  Level 20: 开始检查数据完整性
2025-06-09T02:27:29.687704+0800  Level 20: 开始检查数据完整性
2025-06-09T02:28:29.738407+0800  Level 20: 开始检查数据完整性
2025-06-09T02:29:29.818449+0800  Level 20: 开始检查数据完整性
2025-06-09T02:30:29.872917+0800  Level 20: 开始检查数据完整性
2025-06-09T02:31:29.948095+0800  Level 20: 开始检查数据完整性
2025-06-09T02:32:29.996037+0800  Level 20: 开始检查数据完整性
2025-06-09T02:33:30.071354+0800  Level 20: 开始检查数据完整性
2025-06-09T02:34:30.123105+0800  Level 20: 开始检查数据完整性
2025-06-09T02:35:30.176401+0800  Level 20: 开始检查数据完整性
2025-06-09T02:36:30.228795+0800  Level 20: 开始检查数据完整性
2025-06-09T02:37:30.283568+0800  Level 20: 开始检查数据完整性
2025-06-09T02:38:30.332513+0800  Level 20: 开始检查数据完整性
2025-06-09T02:39:30.374129+0800  Level 20: 开始检查数据完整性
2025-06-09T02:40:30.444069+0800  Level 20: 开始检查数据完整性
2025-06-09T02:41:30.496662+0800  Level 20: 开始检查数据完整性
2025-06-09T02:42:30.553302+0800  Level 20: 开始检查数据完整性
2025-06-09T02:43:30.611126+0800  Level 20: 开始检查数据完整性
2025-06-09T02:44:30.662748+0800  Level 20: 开始检查数据完整性
2025-06-09T02:45:30.725472+0800  Level 20: 开始检查数据完整性
2025-06-09T02:46:30.779051+0800  Level 20: 开始检查数据完整性
2025-06-09T02:47:30.839520+0800  Level 20: 开始检查数据完整性
2025-06-09T02:48:30.885480+0800  Level 20: 开始检查数据完整性
2025-06-09T02:49:30.939121+0800  Level 20: 开始检查数据完整性
2025-06-09T02:50:31.020911+0800  Level 20: 开始检查数据完整性
2025-06-09T02:51:31.065303+0800  Level 20: 开始检查数据完整性
2025-06-09T02:52:31.129795+0800  Level 20: 开始检查数据完整性
2025-06-09T02:53:31.186441+0800  Level 20: 开始检查数据完整性
2025-06-09T02:54:31.232678+0800  Level 20: 开始检查数据完整性
2025-06-09T02:55:31.283117+0800  Level 20: 开始检查数据完整性
2025-06-09T02:56:31.342000+0800  Level 20: 开始检查数据完整性
2025-06-09T02:57:31.418329+0800  Level 20: 开始检查数据完整性
2025-06-09T02:58:31.471182+0800  Level 20: 开始检查数据完整性
2025-06-09T02:59:31.524271+0800  Level 20: 开始检查数据完整性
2025-06-09T03:00:31.575482+0800  Level 20: 开始检查数据完整性
2025-06-09T03:01:31.641963+0800  Level 20: 开始检查数据完整性
2025-06-09T03:02:31.728112+0800  Level 20: 开始检查数据完整性
2025-06-09T03:03:31.770289+0800  Level 20: 开始检查数据完整性
2025-06-09T03:04:31.838007+0800  Level 20: 开始检查数据完整性
2025-06-09T03:05:31.896323+0800  Level 20: 开始检查数据完整性
2025-06-09T03:06:31.945480+0800  Level 20: 开始检查数据完整性
2025-06-09T03:07:32.004055+0800  Level 20: 开始检查数据完整性
2025-06-09T03:08:32.060524+0800  Level 20: 开始检查数据完整性
2025-06-09T03:09:32.113876+0800  Level 20: 开始检查数据完整性
2025-06-09T03:10:32.169060+0800  Level 20: 开始检查数据完整性
2025-06-09T03:11:32.220392+0800  Level 20: 开始检查数据完整性
2025-06-09T03:12:32.280960+0800  Level 20: 开始检查数据完整性
2025-06-09T03:13:32.359269+0800  Level 20: 开始检查数据完整性
2025-06-09T03:14:32.414497+0800  Level 20: 开始检查数据完整性
2025-06-09T03:15:32.463608+0800  Level 20: 开始检查数据完整性
2025-06-09T03:16:32.530061+0800  Level 20: 开始检查数据完整性
2025-06-09T03:17:32.606197+0800  Level 20: 开始检查数据完整性
2025-06-09T03:18:32.657993+0800  Level 20: 开始检查数据完整性
2025-06-09T03:19:32.702761+0800  Level 20: 开始检查数据完整性
2025-06-09T03:20:32.760516+0800  Level 20: 开始检查数据完整性
2025-06-09T03:21:32.803020+0800  Level 20: 开始检查数据完整性
2025-06-09T03:22:32.854955+0800  Level 20: 开始检查数据完整性
2025-06-09T03:23:32.923346+0800  Level 20: 开始检查数据完整性
2025-06-09T03:24:32.968646+0800  Level 20: 开始检查数据完整性
2025-06-09T03:25:33.016673+0800  Level 20: 开始检查数据完整性
2025-06-09T03:26:33.070927+0800  Level 20: 开始检查数据完整性
2025-06-09T03:27:33.120656+0800  Level 20: 开始检查数据完整性
2025-06-09T03:28:33.177275+0800  Level 20: 开始检查数据完整性
2025-06-09T03:29:33.224680+0800  Level 20: 开始检查数据完整性
2025-06-09T03:30:33.283346+0800  Level 20: 开始检查数据完整性
2025-06-09T03:31:33.334915+0800  Level 20: 开始检查数据完整性
2025-06-09T03:32:33.384520+0800  Level 20: 开始检查数据完整性
2025-06-09T03:33:33.454833+0800  Level 20: 开始检查数据完整性
2025-06-09T03:34:33.509680+0800  Level 20: 开始检查数据完整性
2025-06-09T03:35:33.570080+0800  Level 20: 开始检查数据完整性
2025-06-09T03:36:33.618403+0800  Level 20: 开始检查数据完整性
2025-06-09T03:37:33.688087+0800  Level 20: 开始检查数据完整性
2025-06-09T03:38:33.740400+0800  Level 20: 开始检查数据完整性
2025-06-09T03:39:33.825548+0800  Level 20: 开始检查数据完整性
2025-06-09T03:40:33.875240+0800  Level 20: 开始检查数据完整性
2025-06-09T03:41:33.924768+0800  Level 20: 开始检查数据完整性
2025-06-09T03:42:33.970841+0800  Level 20: 开始检查数据完整性
2025-06-09T03:43:34.020075+0800  Level 20: 开始检查数据完整性
2025-06-09T03:44:34.070204+0800  Level 20: 开始检查数据完整性
2025-06-09T03:45:34.116665+0800  Level 20: 开始检查数据完整性
2025-06-09T03:46:34.168869+0800  Level 20: 开始检查数据完整性
2025-06-09T03:47:34.228831+0800  Level 20: 开始检查数据完整性
2025-06-09T03:48:34.318843+0800  Level 20: 开始检查数据完整性
2025-06-09T03:49:34.375482+0800  Level 20: 开始检查数据完整性
2025-06-09T03:50:34.432744+0800  Level 20: 开始检查数据完整性
2025-06-09T03:51:34.492054+0800  Level 20: 开始检查数据完整性
2025-06-09T03:52:34.552497+0800  Level 20: 开始检查数据完整性
2025-06-09T03:53:34.603034+0800  Level 20: 开始检查数据完整性
2025-06-09T03:54:34.686951+0800  Level 20: 开始检查数据完整性
2025-06-09T03:55:34.754760+0800  Level 20: 开始检查数据完整性
2025-06-09T03:56:34.813264+0800  Level 20: 开始检查数据完整性
2025-06-09T03:57:34.880825+0800  Level 20: 开始检查数据完整性
2025-06-09T03:58:34.918834+0800  Level 20: 开始检查数据完整性
2025-06-09T03:59:34.984251+0800  Level 20: 开始检查数据完整性
2025-06-09T04:00:35.047999+0800  Level 20: 开始检查数据完整性
2025-06-09T04:01:35.109025+0800  Level 20: 开始检查数据完整性
2025-06-09T04:02:35.156830+0800  Level 20: 开始检查数据完整性
2025-06-09T04:03:35.209840+0800  Level 20: 开始检查数据完整性
2025-06-09T04:04:35.266723+0800  Level 20: 开始检查数据完整性
2025-06-09T04:05:35.308541+0800  Level 20: 开始检查数据完整性
2025-06-09T04:06:35.369151+0800  Level 20: 开始检查数据完整性
2025-06-09T04:07:35.432601+0800  Level 20: 开始检查数据完整性
2025-06-09T04:08:35.488725+0800  Level 20: 开始检查数据完整性
2025-06-09T04:09:35.544548+0800  Level 20: 开始检查数据完整性
2025-06-09T04:10:35.595187+0800  Level 20: 开始检查数据完整性
2025-06-09T04:11:35.671680+0800  Level 20: 开始检查数据完整性
2025-06-09T04:12:35.724373+0800  Level 20: 开始检查数据完整性
2025-06-09T04:13:35.767740+0800  Level 20: 开始检查数据完整性
2025-06-09T04:14:35.821263+0800  Level 20: 开始检查数据完整性
2025-06-09T04:15:35.874349+0800  Level 20: 开始检查数据完整性
2025-06-09T04:16:35.922400+0800  Level 20: 开始检查数据完整性
2025-06-09T04:17:35.976905+0800  Level 20: 开始检查数据完整性
2025-06-09T04:18:36.036048+0800  Level 20: 开始检查数据完整性
2025-06-09T04:19:36.097045+0800  Level 20: 开始检查数据完整性
2025-06-09T04:20:36.144731+0800  Level 20: 开始检查数据完整性
2025-06-09T04:21:36.190079+0800  Level 20: 开始检查数据完整性
2025-06-09T04:22:36.242401+0800  Level 20: 开始检查数据完整性
2025-06-09T04:23:36.302964+0800  Level 20: 开始检查数据完整性
2025-06-09T04:24:36.382292+0800  Level 20: 开始检查数据完整性
2025-06-09T04:25:36.430000+0800  Level 20: 开始检查数据完整性
2025-06-09T04:26:36.490953+0800  Level 20: 开始检查数据完整性
2025-06-09T04:27:36.537531+0800  Level 20: 开始检查数据完整性
2025-06-09T04:28:36.579672+0800  Level 20: 开始检查数据完整性
2025-06-09T04:29:36.633003+0800  Level 20: 开始检查数据完整性
2025-06-09T04:30:36.700097+0800  Level 20: 开始检查数据完整性
2025-06-09T04:31:36.766142+0800  Level 20: 开始检查数据完整性
2025-06-09T04:32:36.818255+0800  Level 20: 开始检查数据完整性
2025-06-09T04:33:36.878631+0800  Level 20: 开始检查数据完整性
2025-06-09T04:34:36.931231+0800  Level 20: 开始检查数据完整性
2025-06-09T04:35:36.983377+0800  Level 20: 开始检查数据完整性
2025-06-09T04:36:37.038485+0800  Level 20: 开始检查数据完整性
2025-06-09T04:37:37.082519+0800  Level 20: 开始检查数据完整性
2025-06-09T04:38:37.147235+0800  Level 20: 开始检查数据完整性
2025-06-09T04:39:37.201377+0800  Level 20: 开始检查数据完整性
2025-06-09T04:40:37.274876+0800  Level 20: 开始检查数据完整性
2025-06-09T04:41:37.319770+0800  Level 20: 开始检查数据完整性
2025-06-09T04:42:37.387809+0800  Level 20: 开始检查数据完整性
2025-06-09T04:43:37.435814+0800  Level 20: 开始检查数据完整性
2025-06-09T04:44:37.490689+0800  Level 20: 开始检查数据完整性
2025-06-09T04:45:37.544657+0800  Level 20: 开始检查数据完整性
2025-06-09T04:46:37.600963+0800  Level 20: 开始检查数据完整性
2025-06-09T04:47:37.644517+0800  Level 20: 开始检查数据完整性
2025-06-09T04:48:37.702004+0800  Level 20: 开始检查数据完整性
2025-06-09T04:49:37.763480+0800  Level 20: 开始检查数据完整性
2025-06-09T04:50:37.837623+0800  Level 20: 开始检查数据完整性
2025-06-09T04:51:37.885838+0800  Level 20: 开始检查数据完整性
2025-06-09T04:52:37.952635+0800  Level 20: 开始检查数据完整性
2025-06-09T04:53:38.012915+0800  Level 20: 开始检查数据完整性
2025-06-09T04:54:38.080786+0800  Level 20: 开始检查数据完整性
2025-06-09T04:55:38.128387+0800  Level 20: 开始检查数据完整性
2025-06-09T04:56:38.182726+0800  Level 20: 开始检查数据完整性
2025-06-09T04:57:38.256984+0800  Level 20: 开始检查数据完整性
2025-06-09T04:58:38.314022+0800  Level 20: 开始检查数据完整性
2025-06-09T04:59:38.373463+0800  Level 20: 开始检查数据完整性
2025-06-09T05:00:38.426974+0800  Level 20: 开始检查数据完整性
2025-06-09T05:01:38.474030+0800  Level 20: 开始检查数据完整性
2025-06-09T05:02:38.523873+0800  Level 20: 开始检查数据完整性
2025-06-09T05:03:38.584223+0800  Level 20: 开始检查数据完整性
2025-06-09T05:04:38.634770+0800  Level 20: 开始检查数据完整性
2025-06-09T05:05:38.693167+0800  Level 20: 开始检查数据完整性
2025-06-09T05:06:38.747562+0800  Level 20: 开始检查数据完整性
2025-06-09T05:07:38.793894+0800  Level 20: 开始检查数据完整性
2025-06-09T05:08:38.876414+0800  Level 20: 开始检查数据完整性
2025-06-09T05:09:38.935118+0800  Level 20: 开始检查数据完整性
2025-06-09T05:10:38.991897+0800  Level 20: 开始检查数据完整性
2025-06-09T05:11:39.040770+0800  Level 20: 开始检查数据完整性
2025-06-09T05:12:39.093882+0800  Level 20: 开始检查数据完整性
2025-06-09T05:13:39.139683+0800  Level 20: 开始检查数据完整性
2025-06-09T05:14:39.189371+0800  Level 20: 开始检查数据完整性
2025-06-09T05:15:39.241795+0800  Level 20: 开始检查数据完整性
2025-06-09T05:16:39.347669+0800  Level 20: 开始检查数据完整性
2025-06-09T05:17:39.428637+0800  Level 20: 开始检查数据完整性
2025-06-09T05:18:39.479056+0800  Level 20: 开始检查数据完整性
2025-06-09T05:19:39.538022+0800  Level 20: 开始检查数据完整性
2025-06-09T05:20:39.589051+0800  Level 20: 开始检查数据完整性
2025-06-09T05:21:39.668309+0800  Level 20: 开始检查数据完整性
2025-06-09T05:22:39.717464+0800  Level 20: 开始检查数据完整性
2025-06-09T05:23:39.770772+0800  Level 20: 开始检查数据完整性
2025-06-09T05:24:39.843517+0800  Level 20: 开始检查数据完整性
2025-06-09T05:25:39.898257+0800  Level 20: 开始检查数据完整性
2025-06-09T05:26:39.945512+0800  Level 20: 开始检查数据完整性
2025-06-09T05:27:40.045612+0800  Level 20: 开始检查数据完整性
2025-06-09T05:28:40.099534+0800  Level 20: 开始检查数据完整性
2025-06-09T05:29:40.157450+0800  Level 20: 开始检查数据完整性
2025-06-09T05:30:40.217796+0800  Level 20: 开始检查数据完整性
2025-06-09T05:31:40.270708+0800  Level 20: 开始检查数据完整性
2025-06-09T05:32:40.329327+0800  Level 20: 开始检查数据完整性
2025-06-09T05:33:40.377157+0800  Level 20: 开始检查数据完整性
2025-06-09T05:34:40.433300+0800  Level 20: 开始检查数据完整性
2025-06-09T05:35:40.492599+0800  Level 20: 开始检查数据完整性
2025-06-09T05:36:40.556592+0800  Level 20: 开始检查数据完整性
2025-06-09T05:37:40.619349+0800  Level 20: 开始检查数据完整性
2025-06-09T05:38:40.678390+0800  Level 20: 开始检查数据完整性
2025-06-09T05:39:40.740243+0800  Level 20: 开始检查数据完整性
2025-06-09T05:40:40.797846+0800  Level 20: 开始检查数据完整性
2025-06-09T05:41:40.846795+0800  Level 20: 开始检查数据完整性
2025-06-09T05:42:40.908086+0800  Level 20: 开始检查数据完整性
2025-06-09T05:43:40.963912+0800  Level 20: 开始检查数据完整性
2025-06-09T05:44:41.026626+0800  Level 20: 开始检查数据完整性
2025-06-09T05:45:41.072795+0800  Level 20: 开始检查数据完整性
2025-06-09T05:46:41.136251+0800  Level 20: 开始检查数据完整性
2025-06-09T05:47:41.199955+0800  Level 20: 开始检查数据完整性
2025-06-09T05:48:41.256881+0800  Level 20: 开始检查数据完整性
2025-06-09T05:49:41.307128+0800  Level 20: 开始检查数据完整性
2025-06-09T05:50:41.369232+0800  Level 20: 开始检查数据完整性
2025-06-09T05:51:41.433438+0800  Level 20: 开始检查数据完整性
2025-06-09T05:52:41.500262+0800  Level 20: 开始检查数据完整性
2025-06-09T05:53:41.553708+0800  Level 20: 开始检查数据完整性
2025-06-09T05:54:41.608627+0800  Level 20: 开始检查数据完整性
2025-06-09T05:55:41.670444+0800  Level 20: 开始检查数据完整性
2025-06-09T05:56:41.718237+0800  Level 20: 开始检查数据完整性
2025-06-09T05:57:41.768258+0800  Level 20: 开始检查数据完整性
2025-06-09T05:58:41.824755+0800  Level 20: 开始检查数据完整性
2025-06-09T05:59:41.890846+0800  Level 20: 开始检查数据完整性
2025-06-09T06:00:41.960540+0800  Level 20: 开始检查数据完整性
2025-06-09T06:01:42.015014+0800  Level 20: 开始检查数据完整性
2025-06-09T06:02:42.070023+0800  Level 20: 开始检查数据完整性
2025-06-09T06:03:42.114882+0800  Level 20: 开始检查数据完整性
2025-06-09T06:04:42.171709+0800  Level 20: 开始检查数据完整性
2025-06-09T06:05:42.216687+0800  Level 20: 开始检查数据完整性
2025-06-09T06:06:42.279428+0800  Level 20: 开始检查数据完整性
2025-06-09T06:07:42.321126+0800  Level 20: 开始检查数据完整性
2025-06-09T06:08:42.402902+0800  Level 20: 开始检查数据完整性
2025-06-09T06:09:42.453161+0800  Level 20: 开始检查数据完整性
2025-06-09T06:10:42.514500+0800  Level 20: 开始检查数据完整性
2025-06-09T06:11:42.562491+0800  Level 20: 开始检查数据完整性
2025-06-09T06:12:42.612725+0800  Level 20: 开始检查数据完整性
2025-06-09T06:13:42.679901+0800  Level 20: 开始检查数据完整性
2025-06-09T06:14:42.730965+0800  Level 20: 开始检查数据完整性
2025-06-09T06:15:42.781340+0800  Level 20: 开始检查数据完整性
2025-06-09T06:16:42.829075+0800  Level 20: 开始检查数据完整性
2025-06-09T06:17:42.900314+0800  Level 20: 开始检查数据完整性
2025-06-09T06:18:42.966001+0800  Level 20: 开始检查数据完整性
2025-06-09T06:19:43.037744+0800  Level 20: 开始检查数据完整性
2025-06-09T06:20:43.093117+0800  Level 20: 开始检查数据完整性
2025-06-09T06:21:43.142668+0800  Level 20: 开始检查数据完整性
2025-06-09T06:22:43.201665+0800  Level 20: 开始检查数据完整性
2025-06-09T06:23:43.259384+0800  Level 20: 开始检查数据完整性
2025-06-09T06:24:43.321316+0800  Level 20: 开始检查数据完整性
2025-06-09T06:25:43.363647+0800  Level 20: 开始检查数据完整性
2025-06-09T06:26:43.417677+0800  Level 20: 开始检查数据完整性
2025-06-09T06:27:43.510589+0800  Level 20: 开始检查数据完整性
2025-06-09T06:28:43.519572+0800  Level 20: 开始检查数据完整性
2025-06-09T06:29:43.576180+0800  Level 20: 开始检查数据完整性
2025-06-09T06:30:43.636294+0800  Level 20: 开始检查数据完整性
2025-06-09T06:31:43.683566+0800  Level 20: 开始检查数据完整性
2025-06-09T06:32:43.740092+0800  Level 20: 开始检查数据完整性
2025-06-09T06:33:43.786673+0800  Level 20: 开始检查数据完整性
2025-06-09T06:34:43.840161+0800  Level 20: 开始检查数据完整性
2025-06-09T06:35:43.889213+0800  Level 20: 开始检查数据完整性
2025-06-09T06:36:43.950839+0800  Level 20: 开始检查数据完整性
2025-06-09T06:37:44.021503+0800  Level 20: 开始检查数据完整性
2025-06-09T06:38:44.068838+0800  Level 20: 开始检查数据完整性
2025-06-09T06:39:44.135480+0800  Level 20: 开始检查数据完整性
2025-06-09T06:40:44.192830+0800  Level 20: 开始检查数据完整性
2025-06-09T06:41:44.234449+0800  Level 20: 开始检查数据完整性
2025-06-09T06:42:44.277443+0800  Level 20: 开始检查数据完整性
2025-06-09T06:43:44.335217+0800  Level 20: 开始检查数据完整性
2025-06-09T06:44:44.379567+0800  Level 20: 开始检查数据完整性
2025-06-09T06:45:44.459436+0800  Level 20: 开始检查数据完整性
2025-06-09T06:46:44.525680+0800  Level 20: 开始检查数据完整性
2025-06-09T06:47:44.593568+0800  Level 20: 开始检查数据完整性
2025-06-09T06:48:44.651175+0800  Level 20: 开始检查数据完整性
2025-06-09T06:49:44.720816+0800  Level 20: 开始检查数据完整性
2025-06-09T06:50:44.794967+0800  Level 20: 开始检查数据完整性
2025-06-09T06:51:44.854464+0800  Level 20: 开始检查数据完整性
2025-06-09T06:52:44.932631+0800  Level 20: 开始检查数据完整性
2025-06-09T06:53:44.998869+0800  Level 20: 开始检查数据完整性
2025-06-09T06:54:45.065567+0800  Level 20: 开始检查数据完整性
2025-06-09T06:55:45.127664+0800  Level 20: 开始检查数据完整性
2025-06-09T06:56:45.205750+0800  Level 20: 开始检查数据完整性
2025-06-09T06:57:45.252124+0800  Level 20: 开始检查数据完整性
2025-06-09T06:58:45.345749+0800  Level 20: 开始检查数据完整性
2025-06-09T06:59:45.396677+0800  Level 20: 开始检查数据完整性
2025-06-09T07:00:45.457911+0800  Level 20: 开始检查数据完整性
2025-06-09T07:01:45.512620+0800  Level 20: 开始检查数据完整性
2025-06-09T07:02:45.571780+0800  Level 20: 开始检查数据完整性
2025-06-09T07:03:45.634227+0800  Level 20: 开始检查数据完整性
2025-06-09T07:04:45.677043+0800  Level 20: 开始检查数据完整性
2025-06-09T07:05:45.733079+0800  Level 20: 开始检查数据完整性
2025-06-09T07:06:45.785715+0800  Level 20: 开始检查数据完整性
2025-06-09T07:07:45.836121+0800  Level 20: 开始检查数据完整性
2025-06-09T07:08:45.904266+0800  Level 20: 开始检查数据完整性
2025-06-09T07:09:45.947921+0800  Level 20: 开始检查数据完整性
2025-06-09T07:10:46.012222+0800  Level 20: 开始检查数据完整性
2025-06-09T07:11:46.065527+0800  Level 20: 开始检查数据完整性
2025-06-09T07:12:46.125074+0800  Level 20: 开始检查数据完整性
2025-06-09T07:13:46.188740+0800  Level 20: 开始检查数据完整性
2025-06-09T07:14:46.237682+0800  Level 20: 开始检查数据完整性
2025-06-09T07:15:46.303849+0800  Level 20: 开始检查数据完整性
2025-06-09T07:16:46.346468+0800  Level 20: 开始检查数据完整性
2025-06-09T07:17:46.410267+0800  Level 20: 开始检查数据完整性
2025-06-09T07:18:46.461967+0800  Level 20: 开始检查数据完整性
2025-06-09T07:19:46.510447+0800  Level 20: 开始检查数据完整性
2025-06-09T07:20:46.569352+0800  Level 20: 开始检查数据完整性
2025-06-09T07:21:46.631561+0800  Level 20: 开始检查数据完整性
2025-06-09T07:22:46.686294+0800  Level 20: 开始检查数据完整性
2025-06-09T07:23:46.742415+0800  Level 20: 开始检查数据完整性
2025-06-09T07:24:46.808913+0800  Level 20: 开始检查数据完整性
2025-06-09T07:25:46.878429+0800  Level 20: 开始检查数据完整性
2025-06-09T07:26:46.941010+0800  Level 20: 开始检查数据完整性
2025-06-09T07:27:46.987363+0800  Level 20: 开始检查数据完整性
2025-06-09T07:28:47.038611+0800  Level 20: 开始检查数据完整性
2025-06-09T07:29:47.093553+0800  Level 20: 开始检查数据完整性
2025-06-09T07:30:47.144805+0800  Level 20: 开始检查数据完整性
2025-06-09T07:31:47.193689+0800  Level 20: 开始检查数据完整性
2025-06-09T07:32:47.255358+0800  Level 20: 开始检查数据完整性
2025-06-09T07:33:47.301239+0800  Level 20: 开始检查数据完整性
2025-06-09T07:34:47.368293+0800  Level 20: 开始检查数据完整性
2025-06-09T07:35:47.420095+0800  Level 20: 开始检查数据完整性
2025-06-09T07:36:47.471489+0800  Level 20: 开始检查数据完整性
2025-06-09T07:37:47.529100+0800  Level 20: 开始检查数据完整性
2025-06-09T07:38:47.575342+0800  Level 20: 开始检查数据完整性
2025-06-09T07:39:47.638524+0800  Level 20: 开始检查数据完整性
2025-06-09T07:40:47.723819+0800  Level 20: 开始检查数据完整性
2025-06-09T07:41:47.778622+0800  Level 20: 开始检查数据完整性
2025-06-09T07:42:47.838571+0800  Level 20: 开始检查数据完整性
2025-06-09T07:43:47.905591+0800  Level 20: 开始检查数据完整性
2025-06-09T07:44:47.968623+0800  Level 20: 开始检查数据完整性
2025-06-09T07:45:48.011886+0800  Level 20: 开始检查数据完整性
2025-06-09T07:46:48.077084+0800  Level 20: 开始检查数据完整性
2025-06-09T07:47:48.140252+0800  Level 20: 开始检查数据完整性
2025-06-09T07:48:48.211362+0800  Level 20: 开始检查数据完整性
2025-06-09T07:49:48.267298+0800  Level 20: 开始检查数据完整性
2025-06-09T07:50:48.326864+0800  Level 20: 开始检查数据完整性
2025-06-09T07:51:48.389867+0800  Level 20: 开始检查数据完整性
2025-06-09T07:52:48.447281+0800  Level 20: 开始检查数据完整性
2025-06-09T07:53:48.497590+0800  Level 20: 开始检查数据完整性
2025-06-09T07:54:48.556039+0800  Level 20: 开始检查数据完整性
2025-06-09T07:55:48.616809+0800  Level 20: 开始检查数据完整性
2025-06-09T07:56:48.679458+0800  Level 20: 开始检查数据完整性
2025-06-09T07:57:48.741829+0800  Level 20: 开始检查数据完整性
2025-06-09T07:58:48.781947+0800  Level 20: 开始检查数据完整性
2025-06-09T07:59:48.827275+0800  Level 20: 开始检查数据完整性
2025-06-09T08:00:48.886252+0800  Level 20: 开始检查数据完整性
2025-06-09T08:01:48.937831+0800  Level 20: 开始检查数据完整性
2025-06-09T08:02:48.992592+0800  Level 20: 开始检查数据完整性
2025-06-09T08:03:49.044540+0800  Level 20: 开始检查数据完整性
2025-06-09T08:04:49.094631+0800  Level 20: 开始检查数据完整性
2025-06-09T08:05:49.158701+0800  Level 20: 开始检查数据完整性
2025-06-09T08:06:49.227397+0800  Level 20: 开始检查数据完整性
2025-06-09T08:07:49.281673+0800  Level 20: 开始检查数据完整性
2025-06-09T08:08:49.334529+0800  Level 20: 开始检查数据完整性
2025-06-09T08:09:49.394244+0800  Level 20: 开始检查数据完整性
2025-06-09T08:10:49.465435+0800  Level 20: 开始检查数据完整性
2025-06-09T08:11:49.521846+0800  Level 20: 开始检查数据完整性
2025-06-09T08:12:49.569180+0800  Level 20: 开始检查数据完整性
2025-06-09T08:13:49.637406+0800  Level 20: 开始检查数据完整性
2025-06-09T08:14:49.680158+0800  Level 20: 开始检查数据完整性
2025-06-09T08:15:49.726902+0800  Level 20: 开始检查数据完整性
2025-06-09T08:16:49.776358+0800  Level 20: 开始检查数据完整性
2025-06-09T08:17:49.834060+0800  Level 20: 开始检查数据完整性
2025-06-09T08:18:49.886178+0800  Level 20: 开始检查数据完整性
2025-06-09T08:19:49.945513+0800  Level 20: 开始检查数据完整性
2025-06-09T08:20:50.013621+0800  Level 20: 开始检查数据完整性
2025-06-09T08:21:50.082012+0800  Level 20: 开始检查数据完整性
2025-06-09T08:22:50.129019+0800  Level 20: 开始检查数据完整性
2025-06-09T08:23:50.177246+0800  Level 20: 开始检查数据完整性
2025-06-09T08:24:50.235287+0800  Level 20: 开始检查数据完整性
2025-06-09T08:25:50.301131+0800  Level 20: 开始检查数据完整性
2025-06-09T08:26:50.349444+0800  Level 20: 开始检查数据完整性
2025-06-09T08:27:50.428115+0800  Level 20: 开始检查数据完整性
2025-06-09T08:28:50.478864+0800  Level 20: 开始检查数据完整性
2025-06-09T08:29:50.532583+0800  Level 20: 开始检查数据完整性
2025-06-09T08:30:50.588433+0800  Level 20: 开始检查数据完整性
2025-06-09T08:31:50.636662+0800  Level 20: 开始检查数据完整性
2025-06-09T08:32:50.682404+0800  Level 20: 开始检查数据完整性
2025-06-09T08:33:50.724652+0800  Level 20: 开始检查数据完整性
2025-06-09T08:34:50.781562+0800  Level 20: 开始检查数据完整性
2025-06-09T08:35:50.864536+0800  Level 20: 开始检查数据完整性
2025-06-09T08:36:50.921232+0800  Level 20: 开始检查数据完整性
2025-06-09T08:37:50.984141+0800  Level 20: 开始检查数据完整性
2025-06-09T08:38:51.038965+0800  Level 20: 开始检查数据完整性
2025-06-09T08:39:51.093208+0800  Level 20: 开始检查数据完整性
2025-06-09T08:40:51.140542+0800  Level 20: 开始检查数据完整性
2025-06-09T08:41:51.194060+0800  Level 20: 开始检查数据完整性
2025-06-09T08:42:51.250420+0800  Level 20: 开始检查数据完整性
2025-06-09T08:43:51.301420+0800  Level 20: 开始检查数据完整性
2025-06-09T08:44:51.361151+0800  Level 20: 开始检查数据完整性
2025-06-09T08:45:51.411681+0800  Level 20: 开始检查数据完整性
2025-06-09T08:46:51.476278+0800  Level 20: 开始检查数据完整性
2025-06-09T08:47:51.535996+0800  Level 20: 开始检查数据完整性
2025-06-09T08:48:51.603969+0800  Level 20: 开始检查数据完整性
2025-06-09T08:49:51.662435+0800  Level 20: 开始检查数据完整性
2025-06-09T08:50:51.719320+0800  Level 20: 开始检查数据完整性
2025-06-09T08:51:51.792525+0800  Level 20: 开始检查数据完整性
2025-06-09T08:52:51.857309+0800  Level 20: 开始检查数据完整性
2025-06-09T08:53:51.926643+0800  Level 20: 开始检查数据完整性
2025-06-09T08:54:51.981937+0800  Level 20: 开始检查数据完整性
2025-06-09T08:55:52.047953+0800  Level 20: 开始检查数据完整性
2025-06-09T08:56:52.098980+0800  Level 20: 开始检查数据完整性
2025-06-09T08:57:52.139066+0800  Level 20: 开始检查数据完整性
2025-06-09T08:58:52.181609+0800  Level 20: 开始检查数据完整性
2025-06-09T08:59:52.253832+0800  Level 20: 开始检查数据完整性
2025-06-09T09:00:52.311532+0800  Level 20: 开始检查数据完整性
2025-06-09T09:01:52.365106+0800  Level 20: 开始检查数据完整性
2025-06-09T09:02:52.411832+0800  Level 20: 开始检查数据完整性
2025-06-09T09:03:52.460589+0800  Level 20: 开始检查数据完整性
2025-06-09T09:04:52.527783+0800  Level 20: 开始检查数据完整性
2025-06-09T09:05:52.592380+0800  Level 20: 开始检查数据完整性
2025-06-09T09:06:52.642948+0800  Level 20: 开始检查数据完整性
2025-06-09T09:07:52.690583+0800  Level 20: 开始检查数据完整性
2025-06-09T09:08:52.771566+0800  Level 20: 开始检查数据完整性
2025-06-09T09:09:52.819613+0800  Level 20: 开始检查数据完整性
2025-06-09T09:10:52.882760+0800  Level 20: 开始检查数据完整性
2025-06-09T09:11:52.938185+0800  Level 20: 开始检查数据完整性
2025-06-09T09:12:52.991882+0800  Level 20: 开始检查数据完整性
2025-06-09T09:13:53.038011+0800  Level 20: 开始检查数据完整性
2025-06-09T09:14:53.093157+0800  Level 20: 开始检查数据完整性
2025-06-09T09:15:53.148614+0800  Level 20: 开始检查数据完整性
2025-06-09T09:16:53.219368+0800  Level 20: 开始检查数据完整性
2025-06-09T09:17:53.287241+0800  Level 20: 开始检查数据完整性
2025-06-09T09:18:53.342588+0800  Level 20: 开始检查数据完整性
2025-06-09T09:19:53.409196+0800  Level 20: 开始检查数据完整性
2025-06-09T09:20:53.479000+0800  Level 20: 开始检查数据完整性
2025-06-09T09:21:53.545375+0800  Level 20: 开始检查数据完整性
2025-06-09T09:22:53.602342+0800  Level 20: 开始检查数据完整性
2025-06-09T09:23:53.650443+0800  Level 20: 开始检查数据完整性
2025-06-09T09:24:53.704381+0800  Level 20: 开始检查数据完整性
2025-06-09T09:25:53.772393+0800  Level 20: 开始检查数据完整性
2025-06-09T09:26:53.822489+0800  Level 20: 开始检查数据完整性
2025-06-09T09:27:53.876186+0800  Level 20: 开始检查数据完整性
2025-06-09T09:28:53.922371+0800  Level 20: 开始检查数据完整性
2025-06-09T09:29:53.982326+0800  Level 20: 开始检查数据完整性
2025-06-09T09:30:54.032400+0800  Level 20: 开始检查数据完整性
2025-06-09T09:31:54.084030+0800  Level 20: 开始检查数据完整性
2025-06-09T09:32:54.146216+0800  Level 20: 开始检查数据完整性
2025-06-09T09:33:54.204023+0800  Level 20: 开始检查数据完整性
2025-06-09T09:34:54.254317+0800  Level 20: 开始检查数据完整性
2025-06-09T09:35:54.303722+0800  Level 20: 开始检查数据完整性
2025-06-09T09:36:54.364993+0800  Level 20: 开始检查数据完整性
2025-06-09T09:37:54.427700+0800  Level 20: 开始检查数据完整性
2025-06-09T09:38:54.478575+0800  Level 20: 开始检查数据完整性
2025-06-09T09:39:54.538968+0800  Level 20: 开始检查数据完整性
2025-06-09T09:40:54.623433+0800  Level 20: 开始检查数据完整性
2025-06-09T09:41:54.670486+0800  Level 20: 开始检查数据完整性
2025-06-09T09:42:54.726059+0800  Level 20: 开始检查数据完整性
2025-06-09T09:43:54.771904+0800  Level 20: 开始检查数据完整性
2025-06-09T09:44:54.825429+0800  Level 20: 开始检查数据完整性
2025-06-09T09:45:54.896629+0800  Level 20: 开始检查数据完整性
2025-06-09T09:46:54.941258+0800  Level 20: 开始检查数据完整性
2025-06-09T09:47:54.984346+0800  Level 20: 开始检查数据完整性
2025-06-09T09:48:55.042246+0800  Level 20: 开始检查数据完整性
2025-06-09T09:49:55.096969+0800  Level 20: 开始检查数据完整性
2025-06-09T09:50:55.157586+0800  Level 20: 开始检查数据完整性
2025-06-09T09:51:55.214916+0800  Level 20: 开始检查数据完整性
2025-06-09T09:52:55.270329+0800  Level 20: 开始检查数据完整性
2025-06-09T09:53:55.328397+0800  Level 20: 开始检查数据完整性
2025-06-09T09:54:55.390689+0800  Level 20: 开始检查数据完整性
2025-06-09T09:55:55.442406+0800  Level 20: 开始检查数据完整性
2025-06-09T09:56:55.487566+0800  Level 20: 开始检查数据完整性
2025-06-09T09:57:55.562489+0800  Level 20: 开始检查数据完整性
2025-06-09T09:58:55.635557+0800  Level 20: 开始检查数据完整性
2025-06-09T09:59:55.688609+0800  Level 20: 开始检查数据完整性
2025-06-09T10:00:55.757790+0800  Level 20: 开始检查数据完整性
2025-06-09T10:01:55.824777+0800  Level 20: 开始检查数据完整性
2025-06-09T10:02:55.886129+0800  Level 20: 开始检查数据完整性
2025-06-09T10:03:55.928711+0800  Level 20: 开始检查数据完整性
2025-06-09T10:04:55.985313+0800  Level 20: 开始检查数据完整性
2025-06-09T10:05:56.038588+0800  Level 20: 开始检查数据完整性
2025-06-09T10:06:56.088138+0800  Level 20: 开始检查数据完整性
2025-06-09T10:07:56.151569+0800  Level 20: 开始检查数据完整性
2025-06-09T10:08:56.200007+0800  Level 20: 开始检查数据完整性
2025-06-09T10:09:56.253087+0800  Level 20: 开始检查数据完整性
2025-06-09T10:10:56.329074+0800  Level 20: 开始检查数据完整性
2025-06-09T10:11:56.389528+0800  Level 20: 开始检查数据完整性
2025-06-09T10:12:56.443959+0800  Level 20: 开始检查数据完整性
2025-06-09T10:13:56.491883+0800  Level 20: 开始检查数据完整性
2025-06-09T10:14:56.547498+0800  Level 20: 开始检查数据完整性
2025-06-09T10:15:56.595335+0800  Level 20: 开始检查数据完整性
2025-06-09T10:16:56.651788+0800  Level 20: 开始检查数据完整性
2025-06-09T10:17:56.711908+0800  Level 20: 开始检查数据完整性
2025-06-09T10:18:56.756632+0800  Level 20: 开始检查数据完整性
2025-06-09T10:19:56.821448+0800  Level 20: 开始检查数据完整性
2025-06-09T10:20:56.876193+0800  Level 20: 开始检查数据完整性
2025-06-09T10:21:56.946932+0800  Level 20: 开始检查数据完整性
2025-06-09T10:22:57.002433+0800  Level 20: 开始检查数据完整性
2025-06-09T10:23:57.070414+0800  Level 20: 开始检查数据完整性
2025-06-09T10:24:57.118729+0800  Level 20: 开始检查数据完整性
2025-06-09T10:25:57.166163+0800  Level 20: 开始检查数据完整性
2025-06-09T10:26:57.238120+0800  Level 20: 开始检查数据完整性
2025-06-09T10:27:57.284247+0800  Level 20: 开始检查数据完整性
2025-06-09T10:28:57.330759+0800  Level 20: 开始检查数据完整性
2025-06-09T10:29:57.381128+0800  Level 20: 开始检查数据完整性
2025-06-09T10:30:57.444623+0800  Level 20: 开始检查数据完整性
2025-06-09T10:31:57.497897+0800  Level 20: 开始检查数据完整性
2025-06-09T10:32:57.556441+0800  Level 20: 开始检查数据完整性
2025-06-09T10:33:57.612556+0800  Level 20: 开始检查数据完整性
2025-06-09T10:34:57.669400+0800  Level 20: 开始检查数据完整性
2025-06-09T10:35:57.732763+0800  Level 20: 开始检查数据完整性
2025-06-09T10:36:57.809879+0800  Level 20: 开始检查数据完整性
2025-06-09T10:37:57.863443+0800  Level 20: 开始检查数据完整性
2025-06-09T10:38:57.911924+0800  Level 20: 开始检查数据完整性
2025-06-09T10:39:57.972687+0800  Level 20: 开始检查数据完整性
2025-06-09T10:40:58.008765+0800  Level 20: 开始检查数据完整性
2025-06-09T10:41:58.063554+0800  Level 20: 开始检查数据完整性
2025-06-09T10:42:58.118283+0800  Level 20: 开始检查数据完整性
2025-06-09T10:43:58.173262+0800  Level 20: 开始检查数据完整性
2025-06-09T10:44:58.221596+0800  Level 20: 开始检查数据完整性
2025-06-09T10:45:58.269686+0800  Level 20: 开始检查数据完整性
2025-06-09T10:46:58.343659+0800  Level 20: 开始检查数据完整性
2025-06-09T10:47:58.400372+0800  Level 20: 开始检查数据完整性
2025-06-09T10:48:58.476431+0800  Level 20: 开始检查数据完整性
2025-06-09T10:49:58.540169+0800  Level 20: 开始检查数据完整性
2025-06-09T10:50:58.604558+0800  Level 20: 开始检查数据完整性
2025-06-09T10:51:58.658751+0800  Level 20: 开始检查数据完整性
2025-06-09T10:52:58.720578+0800  Level 20: 开始检查数据完整性
2025-06-09T10:53:58.775139+0800  Level 20: 开始检查数据完整性
2025-06-09T10:54:58.827082+0800  Level 20: 开始检查数据完整性
2025-06-09T10:55:58.886742+0800  Level 20: 开始检查数据完整性
2025-06-09T10:56:58.934420+0800  Level 20: 开始检查数据完整性
2025-06-09T10:57:59.002006+0800  Level 20: 开始检查数据完整性
2025-06-09T10:58:59.080204+0800  Level 20: 开始检查数据完整性
2025-06-09T10:59:59.140475+0800  Level 20: 开始检查数据完整性
2025-06-09T11:00:59.203916+0800  Level 20: 开始检查数据完整性
2025-06-09T11:01:59.257250+0800  Level 20: 开始检查数据完整性
2025-06-09T11:02:59.315116+0800  Level 20: 开始检查数据完整性
2025-06-09T11:03:59.374891+0800  Level 20: 开始检查数据完整性
2025-06-09T11:04:59.451073+0800  Level 20: 开始检查数据完整性
2025-06-09T11:05:59.502215+0800  Level 20: 开始检查数据完整性
2025-06-09T11:06:59.569154+0800  Level 20: 开始检查数据完整性
2025-06-09T11:07:59.622363+0800  Level 20: 开始检查数据完整性
2025-06-09T11:08:59.668824+0800  Level 20: 开始检查数据完整性
2025-06-09T11:09:59.725881+0800  Level 20: 开始检查数据完整性
2025-06-09T11:10:59.800295+0800  Level 20: 开始检查数据完整性
2025-06-09T11:11:59.852274+0800  Level 20: 开始检查数据完整性
2025-06-09T11:12:59.915492+0800  Level 20: 开始检查数据完整性
2025-06-09T11:13:59.976240+0800  Level 20: 开始检查数据完整性
2025-06-09T11:15:00.025211+0800  Level 20: 开始检查数据完整性
2025-06-09T11:16:00.114659+0800  Level 20: 开始检查数据完整性
2025-06-09T11:17:00.167938+0800  Level 20: 开始检查数据完整性
2025-06-09T11:18:00.227471+0800  Level 20: 开始检查数据完整性
2025-06-09T11:19:00.283582+0800  Level 20: 开始检查数据完整性
2025-06-09T11:20:00.341726+0800  Level 20: 开始检查数据完整性
2025-06-09T11:21:00.422983+0800  Level 20: 开始检查数据完整性
2025-06-09T11:22:00.497173+0800  Level 20: 开始检查数据完整性
2025-06-09T11:23:00.572793+0800  Level 20: 开始检查数据完整性
2025-06-09T11:24:00.630275+0800  Level 20: 开始检查数据完整性
2025-06-09T11:25:00.683659+0800  Level 20: 开始检查数据完整性
2025-06-09T11:26:00.744756+0800  Level 20: 开始检查数据完整性
2025-06-09T11:27:00.806628+0800  Level 20: 开始检查数据完整性
2025-06-09T11:28:00.867390+0800  Level 20: 开始检查数据完整性
2025-06-09T11:29:00.925918+0800  Level 20: 开始检查数据完整性
2025-06-09T11:30:00.968311+0800  Level 20: 开始检查数据完整性
2025-06-09T11:31:01.029722+0800  Level 20: 开始检查数据完整性
2025-06-09T11:32:01.091363+0800  Level 20: 开始检查数据完整性
2025-06-09T11:33:01.154795+0800  Level 20: 开始检查数据完整性
2025-06-09T11:34:01.205419+0800  Level 20: 开始检查数据完整性
2025-06-09T11:35:01.254210+0800  Level 20: 开始检查数据完整性
2025-06-09T11:36:01.317850+0800  Level 20: 开始检查数据完整性
2025-06-09T11:37:01.365466+0800  Level 20: 开始检查数据完整性
2025-06-09T11:38:01.425573+0800  Level 20: 开始检查数据完整性
2025-06-09T11:39:01.475149+0800  Level 20: 开始检查数据完整性
2025-06-09T11:40:01.532993+0800  Level 20: 开始检查数据完整性
2025-06-09T11:41:01.612336+0800  Level 20: 开始检查数据完整性
2025-06-09T11:42:01.670342+0800  Level 20: 开始检查数据完整性
2025-06-09T11:43:01.738612+0800  Level 20: 开始检查数据完整性
2025-06-09T11:44:01.785800+0800  Level 20: 开始检查数据完整性
2025-06-09T11:45:01.855767+0800  Level 20: 开始检查数据完整性
2025-06-09T11:46:01.916953+0800  Level 20: 开始检查数据完整性
2025-06-09T11:47:01.967073+0800  Level 20: 开始检查数据完整性
2025-06-09T11:48:02.015887+0800  Level 20: 开始检查数据完整性
2025-06-09T11:49:02.063403+0800  Level 20: 开始检查数据完整性
2025-06-09T11:50:02.120190+0800  Level 20: 开始检查数据完整性
2025-06-09T11:51:02.186245+0800  Level 20: 开始检查数据完整性
2025-06-09T11:52:02.246361+0800  Level 20: 开始检查数据完整性
2025-06-09T11:53:02.309311+0800  Level 20: 开始检查数据完整性
2025-06-09T11:54:02.375023+0800  Level 20: 开始检查数据完整性
2025-06-09T11:55:02.449518+0800  Level 20: 开始检查数据完整性
2025-06-09T11:56:02.514600+0800  Level 20: 开始检查数据完整性
2025-06-09T11:57:02.557041+0800  Level 20: 开始检查数据完整性
2025-06-09T11:58:02.623861+0800  Level 20: 开始检查数据完整性
2025-06-09T11:59:02.686251+0800  Level 20: 开始检查数据完整性
2025-06-09T12:00:02.745674+0800  Level 20: 开始检查数据完整性
2025-06-09T12:01:02.829869+0800  Level 20: 开始检查数据完整性
2025-06-09T12:02:02.876809+0800  Level 20: 开始检查数据完整性
2025-06-09T12:03:02.926039+0800  Level 20: 开始检查数据完整性
2025-06-09T12:04:02.979604+0800  Level 20: 开始检查数据完整性
2025-06-09T12:05:03.027386+0800  Level 20: 开始检查数据完整性
2025-06-09T12:06:03.097185+0800  Level 20: 开始检查数据完整性
2025-06-09T12:07:03.157681+0800  Level 20: 开始检查数据完整性
2025-06-09T12:08:03.242387+0800  Level 20: 开始检查数据完整性
2025-06-09T12:09:03.310354+0800  Level 20: 开始检查数据完整性
2025-06-09T12:10:03.383319+0800  Level 20: 开始检查数据完整性
2025-06-09T12:11:03.420936+0800  Level 20: 开始检查数据完整性
2025-06-09T12:12:03.493106+0800  Level 20: 开始检查数据完整性
2025-06-09T12:13:03.535540+0800  Level 20: 开始检查数据完整性
2025-06-09T12:14:03.607165+0800  Level 20: 开始检查数据完整性
2025-06-09T12:15:03.683899+0800  Level 20: 开始检查数据完整性
2025-06-09T12:16:03.731363+0800  Level 20: 开始检查数据完整性
2025-06-09T12:17:03.781247+0800  Level 20: 开始检查数据完整性
2025-06-09T12:18:03.855478+0800  Level 20: 开始检查数据完整性
2025-06-09T12:19:03.903355+0800  Level 20: 开始检查数据完整性
2025-06-09T12:20:03.961861+0800  Level 20: 开始检查数据完整性
2025-06-09T12:21:04.024643+0800  Level 20: 开始检查数据完整性
2025-06-09T12:22:04.079971+0800  Level 20: 开始检查数据完整性
2025-06-09T12:23:04.137338+0800  Level 20: 开始检查数据完整性
2025-06-09T12:24:04.206682+0800  Level 20: 开始检查数据完整性
2025-06-09T12:25:04.273666+0800  Level 20: 开始检查数据完整性
2025-06-09T12:26:04.343114+0800  Level 20: 开始检查数据完整性
2025-06-09T12:27:04.422344+0800  Level 20: 开始检查数据完整性
2025-06-09T12:28:04.501922+0800  Level 20: 开始检查数据完整性
2025-06-09T12:29:04.570367+0800  Level 20: 开始检查数据完整性
2025-06-09T12:30:04.632600+0800  Level 20: 开始检查数据完整性
2025-06-09T12:31:04.698194+0800  Level 20: 开始检查数据完整性
2025-06-09T12:32:04.771926+0800  Level 20: 开始检查数据完整性
2025-06-09T12:33:04.842117+0800  Level 20: 开始检查数据完整性
2025-06-09T12:34:04.892341+0800  Level 20: 开始检查数据完整性
2025-06-09T12:35:04.956813+0800  Level 20: 开始检查数据完整性
2025-06-09T12:36:05.028631+0800  Level 20: 开始检查数据完整性
2025-06-09T12:37:05.086319+0800  Level 20: 开始检查数据完整性
2025-06-09T12:38:05.155788+0800  Level 20: 开始检查数据完整性
2025-06-09T12:39:05.211610+0800  Level 20: 开始检查数据完整性
2025-06-09T12:40:05.274400+0800  Level 20: 开始检查数据完整性
2025-06-09T12:41:05.332854+0800  Level 20: 开始检查数据完整性
2025-06-09T12:42:05.390878+0800  Level 20: 开始检查数据完整性
2025-06-09T12:43:05.453785+0800  Level 20: 开始检查数据完整性
2025-06-09T12:44:05.516937+0800  Level 20: 开始检查数据完整性
2025-06-09T12:45:05.563704+0800  Level 20: 开始检查数据完整性
2025-06-09T12:46:05.618270+0800  Level 20: 开始检查数据完整性
2025-06-09T12:47:05.675820+0800  Level 20: 开始检查数据完整性
2025-06-09T12:48:05.755207+0800  Level 20: 开始检查数据完整性
2025-06-09T12:49:05.813520+0800  Level 20: 开始检查数据完整性
2025-06-09T12:50:05.875351+0800  Level 20: 开始检查数据完整性
2025-06-09T12:51:05.959134+0800  Level 20: 开始检查数据完整性
2025-06-09T12:52:06.025196+0800  Level 20: 开始检查数据完整性
2025-06-09T12:53:06.092341+0800  Level 20: 开始检查数据完整性
2025-06-09T12:54:06.160197+0800  Level 20: 开始检查数据完整性
2025-06-09T12:55:06.241125+0800  Level 20: 开始检查数据完整性
2025-06-09T12:56:06.292114+0800  Level 20: 开始检查数据完整性
2025-06-09T12:57:06.365380+0800  Level 20: 开始检查数据完整性
2025-06-09T12:58:06.426853+0800  Level 20: 开始检查数据完整性
2025-06-09T12:59:06.476392+0800  Level 20: 开始检查数据完整性
2025-06-09T13:00:06.534459+0800  Level 20: 开始检查数据完整性
2025-06-09T13:01:06.596754+0800  Level 20: 开始检查数据完整性
2025-06-09T13:02:06.659186+0800  Level 20: 开始检查数据完整性
2025-06-09T13:03:06.738997+0800  Level 20: 开始检查数据完整性
2025-06-09T13:04:06.792358+0800  Level 20: 开始检查数据完整性
2025-06-09T13:05:06.859096+0800  Level 20: 开始检查数据完整性
2025-06-09T13:06:06.921233+0800  Level 20: 开始检查数据完整性
2025-06-09T13:07:06.992243+0800  Level 20: 开始检查数据完整性
2025-06-09T13:08:07.068593+0800  Level 20: 开始检查数据完整性
2025-06-09T13:09:07.132817+0800  Level 20: 开始检查数据完整性
2025-06-09T13:10:07.193736+0800  Level 20: 开始检查数据完整性
2025-06-09T13:11:07.248821+0800  Level 20: 开始检查数据完整性
2025-06-09T13:12:07.293179+0800  Level 20: 开始检查数据完整性
2025-06-09T13:13:07.348705+0800  Level 20: 开始检查数据完整性
2025-06-09T13:14:07.401588+0800  Level 20: 开始检查数据完整性
2025-06-09T13:15:07.464675+0800  Level 20: 开始检查数据完整性
2025-06-09T13:16:07.516915+0800  Level 20: 开始检查数据完整性
2025-06-09T13:17:07.581457+0800  Level 20: 开始检查数据完整性
2025-06-09T13:18:07.637348+0800  Level 20: 开始检查数据完整性
2025-06-09T13:19:07.698320+0800  Level 20: 开始检查数据完整性
2025-06-09T13:20:07.756512+0800  Level 20: 开始检查数据完整性
2025-06-09T13:21:07.823338+0800  Level 20: 开始检查数据完整性
2025-06-09T13:22:07.879015+0800  Level 20: 开始检查数据完整性
2025-06-09T13:23:07.942674+0800  Level 20: 开始检查数据完整性
2025-06-09T13:24:07.996909+0800  Level 20: 开始检查数据完整性
2025-06-09T13:25:08.053402+0800  Level 20: 开始检查数据完整性
2025-06-09T13:25:08.053760+0800  Level 20: 检测到数据滞后: ARKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:25:08.053969+0800  Level 20: 将发送滞后告警: ARKUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:25:08.054153+0800  Level 20: 检测到数据滞后: API3USDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:25:08.054319+0800  Level 20: 将发送滞后告警: API3USDT.BINANCE, 连续告警次数: 1
2025-06-09T13:25:08.054950+0800  Level 20: 检测到数据滞后: TRUUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:25:08.055129+0800  Level 20: 将发送滞后告警: TRUUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:25:08.055304+0800  Level 20: 检测到数据滞后: 1000RATSUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:25:08.055483+0800  Level 20: 将发送滞后告警: 1000RATSUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:25:08.055663+0800  Level 20: 检测到数据滞后: CRVUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:25:08.055872+0800  Level 20: 将发送滞后告警: CRVUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:25:08.056069+0800  Level 20: 检测到数据滞后: TOKENUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:25:08.056242+0800  Level 20: 将发送滞后告警: TOKENUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:25:08.056413+0800  Level 20: 检测到数据滞后: ZECUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:25:08.057039+0800  Level 20: 将发送滞后告警: ZECUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:25:08.057255+0800  Level 20: 检测到数据滞后: UXLINKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:25:08.057465+0800  Level 20: 将发送滞后告警: UXLINKUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:25:08.057637+0800  Level 20: 检测到数据滞后: DYMUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:25:08.057805+0800  Level 20: 将发送滞后告警: DYMUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:25:08.057986+0800  Level 20: 检测到数据滞后: BSWUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:25:08.058154+0800  Level 20: 将发送滞后告警: BSWUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:25:08.058314+0800  Level 20: 检测到数据滞后: COOKIEUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:25:08.058473+0800  Level 20: 将发送滞后告警: COOKIEUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:25:08.058613+0800  Level 20: 检测到数据滞后: LAYERUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:25:08.059824+0800  Level 20: 将发送滞后告警: LAYERUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:25:08.060008+0800  Level 20: 检测到数据滞后: IPUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:25:08.060175+0800  Level 20: 将发送滞后告警: IPUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:25:08.060333+0800  Level 20: 已发送合并告警邮件，包含 0 个缺失合约和 13 个滞后合约
2025-06-09T13:26:08.154878+0800  Level 20: 开始检查数据完整性
2025-06-09T13:26:08.156963+0800  Level 20: 检测到数据滞后: ARKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:26:08.157160+0800  Level 20: ARKUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:26:08.157322+0800  Level 20: 检测到数据滞后: JASMYUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:26:08.157900+0800  Level 20: 将发送滞后告警: JASMYUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:26:08.158103+0800  Level 20: 检测到数据滞后: PEOPLEUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:26:08.158619+0800  Level 20: 将发送滞后告警: PEOPLEUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:26:08.158806+0800  Level 20: 检测到数据滞后: API3USDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:26:08.158911+0800  Level 20: API3USDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:26:08.159011+0800  Level 20: 检测到数据滞后: BELUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:26:08.159101+0800  Level 20: 将发送滞后告警: BELUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:26:08.159195+0800  Level 20: 检测到数据滞后: PENDLEUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:26:08.159338+0800  Level 20: 将发送滞后告警: PENDLEUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:26:08.159517+0800  Level 20: 数据滞后已恢复正常: TRUUSDT.BINANCE
2025-06-09T13:26:08.159696+0800  Level 20: 数据滞后已恢复正常: 1000RATSUSDT.BINANCE
2025-06-09T13:26:08.159857+0800  Level 20: 检测到数据滞后: 1000BONKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:26:08.160024+0800  Level 20: 将发送滞后告警: 1000BONKUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:26:08.160651+0800  Level 20: 检测到数据滞后: ALGOUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:26:08.160835+0800  Level 20: 将发送滞后告警: ALGOUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:26:08.161016+0800  Level 20: 数据滞后已恢复正常: CRVUSDT.BINANCE
2025-06-09T13:26:08.161188+0800  Level 20: 数据滞后已恢复正常: TOKENUSDT.BINANCE
2025-06-09T13:26:08.161347+0800  Level 20: 检测到数据滞后: ZECUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:26:08.161507+0800  Level 20: ZECUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:26:08.161680+0800  Level 20: 检测到数据滞后: UXLINKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:26:08.161803+0800  Level 20: UXLINKUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:26:08.161955+0800  Level 20: 检测到数据滞后: SPELLUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:26:08.162157+0800  Level 20: 将发送滞后告警: SPELLUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:26:08.162331+0800  Level 20: 数据滞后已恢复正常: DYMUSDT.BINANCE
2025-06-09T13:26:08.162495+0800  Level 20: 数据滞后已恢复正常: BSWUSDT.BINANCE
2025-06-09T13:26:08.162649+0800  Level 20: 检测到数据滞后: COOKIEUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:26:08.162813+0800  Level 20: COOKIEUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:26:08.163452+0800  Level 20: 检测到数据滞后: LAYERUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:26:08.163623+0800  Level 20: LAYERUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:26:08.163783+0800  Level 20: 数据滞后已恢复正常: IPUSDT.BINANCE
2025-06-09T13:26:08.163949+0800  Level 20: 已发送合并告警邮件，包含 0 个缺失合约和 7 个滞后合约
2025-06-09T13:27:08.188297+0800  Level 20: 开始检查数据完整性
2025-06-09T13:27:08.190122+0800  Level 20: 检测到数据滞后: ARKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:27:08.190343+0800  Level 20: ARKUSDT.BINANCE 滞后告警冷却中，还需等待 239 秒 (冷却时间: 360秒)
2025-06-09T13:27:08.190516+0800  Level 20: 数据滞后已恢复正常: JASMYUSDT.BINANCE
2025-06-09T13:27:08.190641+0800  Level 20: 检测到数据滞后: PEOPLEUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:27:08.190729+0800  Level 20: PEOPLEUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:27:08.190812+0800  Level 20: 检测到数据滞后: API3USDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:27:08.190905+0800  Level 20: API3USDT.BINANCE 滞后告警冷却中，还需等待 239 秒 (冷却时间: 360秒)
2025-06-09T13:27:08.191026+0800  Level 20: 数据滞后已恢复正常: BELUSDT.BINANCE
2025-06-09T13:27:08.191193+0800  Level 20: 检测到数据滞后: ENSUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:27:08.191367+0800  Level 20: 将发送滞后告警: ENSUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:27:08.191543+0800  Level 20: 数据滞后已恢复正常: PENDLEUSDT.BINANCE
2025-06-09T13:27:08.191715+0800  Level 20: 检测到数据滞后: TRUUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:27:08.191820+0800  Level 20: TRUUSDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 180秒)
2025-06-09T13:27:08.191905+0800  Level 20: 数据滞后已恢复正常: 1000BONKUSDT.BINANCE
2025-06-09T13:27:08.191986+0800  Level 20: 检测到数据滞后: AAVEUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:27:08.192082+0800  Level 20: 将发送滞后告警: AAVEUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:27:08.192254+0800  Level 20: 数据滞后已恢复正常: ALGOUSDT.BINANCE
2025-06-09T13:27:08.192423+0800  Level 20: 检测到数据滞后: CRVUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:27:08.192547+0800  Level 20: CRVUSDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 180秒)
2025-06-09T13:27:08.192646+0800  Level 20: 检测到数据滞后: XVGUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:27:08.192738+0800  Level 20: 将发送滞后告警: XVGUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:27:08.192828+0800  Level 20: 数据滞后已恢复正常: ZECUSDT.BINANCE
2025-06-09T13:27:08.192918+0800  Level 20: 数据滞后已恢复正常: UXLINKUSDT.BINANCE
2025-06-09T13:27:08.193007+0800  Level 20: 检测到数据滞后: ACHUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:27:08.193095+0800  Level 20: 将发送滞后告警: ACHUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:27:08.193351+0800  Level 20: 检测到数据滞后: SPELLUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:27:08.193530+0800  Level 20: SPELLUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:27:08.193718+0800  Level 20: 检测到数据滞后: DYMUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:27:08.193884+0800  Level 20: DYMUSDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 180秒)
2025-06-09T13:27:08.194070+0800  Level 20: 检测到数据滞后: BEAMXUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:27:08.194360+0800  Level 20: 将发送滞后告警: BEAMXUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:27:08.194936+0800  Level 20: 检测到数据滞后: LDOUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:27:08.195122+0800  Level 20: 将发送滞后告警: LDOUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:27:08.195302+0800  Level 20: 检测到数据滞后: ZEREBROUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:27:08.195466+0800  Level 20: 将发送滞后告警: ZEREBROUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:27:08.195643+0800  Level 20: 数据滞后已恢复正常: COOKIEUSDT.BINANCE
2025-06-09T13:27:08.195818+0800  Level 20: 检测到数据滞后: LAYERUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:27:08.195982+0800  Level 20: LAYERUSDT.BINANCE 滞后告警冷却中，还需等待 239 秒 (冷却时间: 360秒)
2025-06-09T13:27:08.196159+0800  Level 20: 检测到数据滞后: IPUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:27:08.196313+0800  Level 20: IPUSDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 180秒)
2025-06-09T13:27:08.196480+0800  Level 20: 已发送合并告警邮件，包含 0 个缺失合约和 7 个滞后合约
2025-06-09T13:28:08.259578+0800  Level 20: 开始检查数据完整性
2025-06-09T13:28:08.259919+0800  Level 20: 数据滞后已恢复正常: ARKUSDT.BINANCE
2025-06-09T13:28:08.260540+0800  Level 20: 检测到数据滞后: ARUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:28:08.260757+0800  Level 20: 将发送滞后告警: ARUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:28:08.260927+0800  Level 20: 检测到数据滞后: ONEUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:28:08.261094+0800  Level 20: 将发送滞后告警: ONEUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:28:08.261418+0800  Level 20: 检测到数据滞后: PEOPLEUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:28:08.261571+0800  Level 20: PEOPLEUSDT.BINANCE 滞后告警冷却中，还需等待 239 秒 (冷却时间: 360秒)
2025-06-09T13:28:08.261736+0800  Level 20: 数据滞后已恢复正常: API3USDT.BINANCE
2025-06-09T13:28:08.261896+0800  Level 20: 检测到数据滞后: BELUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:28:08.262073+0800  Level 20: BELUSDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 180秒)
2025-06-09T13:28:08.262241+0800  Level 20: 数据滞后已恢复正常: ENSUSDT.BINANCE
2025-06-09T13:28:08.262413+0800  Level 20: 检测到数据滞后: TRUUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:28:08.262573+0800  Level 20: 将发送滞后告警: TRUUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:28:08.262753+0800  Level 20: 检测到数据滞后: MEWUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:28:08.262916+0800  Level 20: 将发送滞后告警: MEWUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:28:08.263074+0800  Level 20: 检测到数据滞后: ZENUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:28:08.263242+0800  Level 20: 将发送滞后告警: ZENUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:28:08.263804+0800  Level 20: 数据滞后已恢复正常: AAVEUSDT.BINANCE
2025-06-09T13:28:08.264087+0800  Level 20: 数据滞后已恢复正常: CRVUSDT.BINANCE
2025-06-09T13:28:08.264264+0800  Level 20: 检测到数据滞后: XVGUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:28:08.264422+0800  Level 20: XVGUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:28:08.264584+0800  Level 20: 检测到数据滞后: SUPERUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:28:08.264736+0800  Level 20: 将发送滞后告警: SUPERUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:28:08.264875+0800  Level 20: 检测到数据滞后: ZECUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:28:08.265017+0800  Level 20: 将发送滞后告警: ZECUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:28:08.265171+0800  Level 20: 检测到数据滞后: UXLINKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:28:08.265340+0800  Level 20: 将发送滞后告警: UXLINKUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:28:08.265484+0800  Level 20: 检测到数据滞后: ACHUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:28:08.265614+0800  Level 20: ACHUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:28:08.265784+0800  Level 20: 检测到数据滞后: SPELLUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:28:08.265936+0800  Level 20: SPELLUSDT.BINANCE 滞后告警冷却中，还需等待 239 秒 (冷却时间: 360秒)
2025-06-09T13:28:08.266107+0800  Level 20: 数据滞后已恢复正常: DYMUSDT.BINANCE
2025-06-09T13:28:08.266277+0800  Level 20: 数据滞后已恢复正常: BEAMXUSDT.BINANCE
2025-06-09T13:28:08.266441+0800  Level 20: 数据滞后已恢复正常: LDOUSDT.BINANCE
2025-06-09T13:28:08.266593+0800  Level 20: 检测到数据滞后: BSWUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:28:08.267215+0800  Level 20: 将发送滞后告警: BSWUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:28:08.267412+0800  Level 20: 数据滞后已恢复正常: ZEREBROUSDT.BINANCE
2025-06-09T13:28:08.267552+0800  Level 20: 检测到数据滞后: PENGUUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:28:08.267724+0800  Level 20: 将发送滞后告警: PENGUUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:28:08.267858+0800  Level 20: 数据滞后已恢复正常: LAYERUSDT.BINANCE
2025-06-09T13:28:08.267984+0800  Level 20: 数据滞后已恢复正常: IPUSDT.BINANCE
2025-06-09T13:28:08.268112+0800  Level 20: 检测到数据滞后: PORTALUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:28:08.268256+0800  Level 20: 将发送滞后告警: PORTALUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:28:08.268388+0800  Level 20: 已发送合并告警邮件，包含 0 个缺失合约和 11 个滞后合约
2025-06-09T13:29:08.310008+0800  Level 20: 开始检查数据完整性
2025-06-09T13:29:08.310438+0800  Level 20: 检测到数据滞后: ARKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:29:08.310625+0800  Level 20: 将发送滞后告警: ARKUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:29:08.310785+0800  Level 20: 数据滞后已恢复正常: ARUSDT.BINANCE
2025-06-09T13:29:08.310946+0800  Level 20: 检测到数据滞后: GALAUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:29:08.311131+0800  Level 20: 将发送滞后告警: GALAUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:29:08.311285+0800  Level 20: 检测到数据滞后: ONEUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:29:08.311441+0800  Level 20: ONEUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:29:08.311750+0800  Level 20: 检测到数据滞后: PEOPLEUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:29:08.311921+0800  Level 20: PEOPLEUSDT.BINANCE 滞后告警冷却中，还需等待 179 秒 (冷却时间: 360秒)
2025-06-09T13:29:08.312075+0800  Level 20: 检测到数据滞后: SUSHIUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:29:08.312242+0800  Level 20: 将发送滞后告警: SUSHIUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:29:08.312412+0800  Level 20: 检测到数据滞后: API3USDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:29:08.312563+0800  Level 20: 将发送滞后告警: API3USDT.BINANCE, 连续告警次数: 1
2025-06-09T13:29:08.312713+0800  Level 20: 检测到数据滞后: BELUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:29:08.312859+0800  Level 20: 将发送滞后告警: BELUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:29:08.313001+0800  Level 20: 数据滞后已恢复正常: TRUUSDT.BINANCE
2025-06-09T13:29:08.313149+0800  Level 20: 检测到数据滞后: MEWUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:29:08.313351+0800  Level 20: MEWUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:29:08.313490+0800  Level 20: 数据滞后已恢复正常: ZENUSDT.BINANCE
2025-06-09T13:29:08.313628+0800  Level 20: 检测到数据滞后: CRVUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:29:08.313775+0800  Level 20: 将发送滞后告警: CRVUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:29:08.313916+0800  Level 20: 检测到数据滞后: TOKENUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:29:08.314087+0800  Level 20: 将发送滞后告警: TOKENUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:29:08.314236+0800  Level 20: 数据滞后已恢复正常: XVGUSDT.BINANCE
2025-06-09T13:29:08.314411+0800  Level 20: 检测到数据滞后: SUPERUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:29:08.314828+0800  Level 20: SUPERUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:29:08.315024+0800  Level 20: 检测到数据滞后: ZECUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:29:08.315181+0800  Level 20: ZECUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:29:08.315330+0800  Level 20: 检测到数据滞后: UXLINKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:29:08.315497+0800  Level 20: UXLINKUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:29:08.315648+0800  Level 20: 数据滞后已恢复正常: ACHUSDT.BINANCE
2025-06-09T13:29:08.315794+0800  Level 20: 检测到数据滞后: SPELLUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:29:08.315941+0800  Level 20: SPELLUSDT.BINANCE 滞后告警冷却中，还需等待 179 秒 (冷却时间: 360秒)
2025-06-09T13:29:08.316087+0800  Level 20: 检测到数据滞后: DYMUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:29:08.316231+0800  Level 20: 将发送滞后告警: DYMUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:29:08.316388+0800  Level 20: 数据滞后已恢复正常: BSWUSDT.BINANCE
2025-06-09T13:29:08.316535+0800  Level 20: 数据滞后已恢复正常: PENGUUSDT.BINANCE
2025-06-09T13:29:08.316681+0800  Level 20: 检测到数据滞后: DEGENUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:29:08.316826+0800  Level 20: 将发送滞后告警: DEGENUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:29:08.316970+0800  Level 20: 检测到数据滞后: PORTALUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:29:08.317113+0800  Level 20: PORTALUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:29:08.317257+0800  Level 20: 已发送合并告警邮件，包含 0 个缺失合约和 9 个滞后合约
2025-06-09T13:30:08.364020+0800  Level 20: 开始检查数据完整性
2025-06-09T13:30:08.364383+0800  Level 20: 检测到数据滞后: ARKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:30:08.364602+0800  Level 20: ARKUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:30:08.364788+0800  Level 20: 数据滞后已恢复正常: GALAUSDT.BINANCE
2025-06-09T13:30:08.364962+0800  Level 20: 检测到数据滞后: ONEUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:30:08.365131+0800  Level 20: ONEUSDT.BINANCE 滞后告警冷却中，还需等待 239 秒 (冷却时间: 360秒)
2025-06-09T13:30:08.365290+0800  Level 20: 检测到数据滞后: PHBUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:30:08.365446+0800  Level 20: 将发送滞后告警: PHBUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:30:08.365802+0800  Level 20: 数据滞后已恢复正常: PEOPLEUSDT.BINANCE
2025-06-09T13:30:08.366006+0800  Level 20: 数据滞后已恢复正常: SUSHIUSDT.BINANCE
2025-06-09T13:30:08.366148+0800  Level 20: 数据滞后已恢复正常: API3USDT.BINANCE
2025-06-09T13:30:08.366290+0800  Level 20: 数据滞后已恢复正常: BELUSDT.BINANCE
2025-06-09T13:30:08.366452+0800  Level 20: 检测到数据滞后: SSVUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:30:08.366614+0800  Level 20: 将发送滞后告警: SSVUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:30:08.366770+0800  Level 20: 数据滞后已恢复正常: MEWUSDT.BINANCE
2025-06-09T13:30:08.366951+0800  Level 20: 检测到数据滞后: AGLDUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:30:08.367107+0800  Level 20: 将发送滞后告警: AGLDUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:30:08.367255+0800  Level 20: 检测到数据滞后: BIGTIMEUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:30:08.367394+0800  Level 20: 将发送滞后告警: BIGTIMEUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:30:08.367558+0800  Level 20: 数据滞后已恢复正常: CRVUSDT.BINANCE
2025-06-09T13:30:08.367742+0800  Level 20: 数据滞后已恢复正常: TOKENUSDT.BINANCE
2025-06-09T13:30:08.367892+0800  Level 20: 数据滞后已恢复正常: SUPERUSDT.BINANCE
2025-06-09T13:30:08.368026+0800  Level 20: 检测到数据滞后: ZECUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:30:08.368161+0800  Level 20: ZECUSDT.BINANCE 滞后告警冷却中，还需等待 239 秒 (冷却时间: 360秒)
2025-06-09T13:30:08.368525+0800  Level 20: 数据滞后已恢复正常: UXLINKUSDT.BINANCE
2025-06-09T13:30:08.368724+0800  Level 20: 检测到数据滞后: MEMEUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:30:08.368867+0800  Level 20: 将发送滞后告警: MEMEUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:30:08.369016+0800  Level 20: 检测到数据滞后: SPELLUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:30:08.369172+0800  Level 20: SPELLUSDT.BINANCE 滞后告警冷却中，还需等待 119 秒 (冷却时间: 360秒)
2025-06-09T13:30:08.369327+0800  Level 20: 检测到数据滞后: DYMUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:30:08.369494+0800  Level 20: DYMUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:30:08.369657+0800  Level 20: 检测到数据滞后: LDOUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:30:08.369799+0800  Level 20: 将发送滞后告警: LDOUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:30:08.369929+0800  Level 20: 检测到数据滞后: DEGENUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:30:08.370095+0800  Level 20: DEGENUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:30:08.370249+0800  Level 20: 检测到数据滞后: COOKIEUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:30:08.370389+0800  Level 20: 将发送滞后告警: COOKIEUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:30:08.370553+0800  Level 20: 数据滞后已恢复正常: PORTALUSDT.BINANCE
2025-06-09T13:30:08.370687+0800  Level 20: 已发送合并告警邮件，包含 0 个缺失合约和 7 个滞后合约
2025-06-09T13:31:08.426790+0800  Level 20: 开始检查数据完整性
2025-06-09T13:31:08.427096+0800  Level 20: 数据滞后已恢复正常: ARKUSDT.BINANCE
2025-06-09T13:31:08.427372+0800  Level 20: 检测到数据滞后: ONEUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:31:08.427563+0800  Level 20: ONEUSDT.BINANCE 滞后告警冷却中，还需等待 179 秒 (冷却时间: 360秒)
2025-06-09T13:31:08.427732+0800  Level 20: 数据滞后已恢复正常: PHBUSDT.BINANCE
2025-06-09T13:31:08.427832+0800  Level 20: 检测到数据滞后: BELUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:31:08.427924+0800  Level 20: BELUSDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 180秒)
2025-06-09T13:31:08.428011+0800  Level 20: 数据滞后已恢复正常: SSVUSDT.BINANCE
2025-06-09T13:31:08.428117+0800  Level 20: 检测到数据滞后: TRUUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:31:08.428278+0800  Level 20: 将发送滞后告警: TRUUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:31:08.428428+0800  Level 20: 检测到数据滞后: AGLDUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:31:08.428528+0800  Level 20: AGLDUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:31:08.428625+0800  Level 20: 检测到数据滞后: 1000RATSUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:31:08.428708+0800  Level 20: 将发送滞后告警: 1000RATSUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:31:08.428824+0800  Level 20: 数据滞后已恢复正常: BIGTIMEUSDT.BINANCE
2025-06-09T13:31:08.429266+0800  Level 20: 数据滞后已恢复正常: ZECUSDT.BINANCE
2025-06-09T13:31:08.429440+0800  Level 20: 检测到数据滞后: IOTAUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:31:08.429615+0800  Level 20: 将发送滞后告警: IOTAUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:31:08.429773+0800  Level 20: 检测到数据滞后: UXLINKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:31:08.429921+0800  Level 20: 将发送滞后告警: UXLINKUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:31:08.430108+0800  Level 20: 检测到数据滞后: ACHUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:31:08.430268+0800  Level 20: 将发送滞后告警: ACHUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:31:08.430499+0800  Level 20: 数据滞后已恢复正常: MEMEUSDT.BINANCE
2025-06-09T13:31:08.434120+0800  Level 20: 检测到数据滞后: SPELLUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:31:08.434289+0800  Level 20: SPELLUSDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 360秒)
2025-06-09T13:31:08.434436+0800  Level 20: 检测到数据滞后: DYMUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:31:08.434584+0800  Level 20: DYMUSDT.BINANCE 滞后告警冷却中，还需等待 239 秒 (冷却时间: 360秒)
2025-06-09T13:31:08.434727+0800  Level 20: 检测到数据滞后: OMUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:31:08.434864+0800  Level 20: 将发送滞后告警: OMUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:31:08.435000+0800  Level 20: 数据滞后已恢复正常: LDOUSDT.BINANCE
2025-06-09T13:31:08.435134+0800  Level 20: 数据滞后已恢复正常: DEGENUSDT.BINANCE
2025-06-09T13:31:08.435279+0800  Level 20: 数据滞后已恢复正常: COOKIEUSDT.BINANCE
2025-06-09T13:31:08.435414+0800  Level 20: 检测到数据滞后: IPUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:31:08.435550+0800  Level 20: 将发送滞后告警: IPUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:31:08.435696+0800  Level 20: 已发送合并告警邮件，包含 0 个缺失合约和 7 个滞后合约
2025-06-09T13:32:08.491880+0800  Level 20: 开始检查数据完整性
2025-06-09T13:32:08.494003+0800  Level 20: 检测到数据滞后: ARKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:32:08.494213+0800  Level 20: 将发送滞后告警: ARKUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:32:08.494388+0800  Level 20: 检测到数据滞后: RSRUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:32:08.494628+0800  Level 20: 将发送滞后告警: RSRUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:32:08.494849+0800  Level 20: 数据滞后已恢复正常: ONEUSDT.BINANCE
2025-06-09T13:32:08.495016+0800  Level 20: 检测到数据滞后: BELUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:32:08.495178+0800  Level 20: 将发送滞后告警: BELUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:32:08.495738+0800  Level 20: 检测到数据滞后: TRUUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:32:08.495933+0800  Level 20: TRUUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:32:08.496106+0800  Level 20: 检测到数据滞后: MEWUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:32:08.496276+0800  Level 20: 将发送滞后告警: MEWUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:32:08.496442+0800  Level 20: 数据滞后已恢复正常: AGLDUSDT.BINANCE
2025-06-09T13:32:08.496604+0800  Level 20: 数据滞后已恢复正常: 1000RATSUSDT.BINANCE
2025-06-09T13:32:08.496779+0800  Level 20: 检测到数据滞后: AAVEUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:32:08.496945+0800  Level 20: 将发送滞后告警: AAVEUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:32:08.497109+0800  Level 20: 检测到数据滞后: ALGOUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:32:08.497265+0800  Level 20: 将发送滞后告警: ALGOUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:32:08.497426+0800  Level 20: 检测到数据滞后: SAGAUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:32:08.497589+0800  Level 20: 将发送滞后告警: SAGAUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:32:08.497766+0800  Level 20: 数据滞后已恢复正常: IOTAUSDT.BINANCE
2025-06-09T13:32:08.497993+0800  Level 20: 检测到数据滞后: UXLINKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:32:08.498157+0800  Level 20: UXLINKUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:32:08.498323+0800  Level 20: 检测到数据滞后: ACHUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:32:08.498484+0800  Level 20: ACHUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:32:08.498657+0800  Level 20: 检测到数据滞后: SPELLUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:32:08.498834+0800  Level 20: 将发送滞后告警: SPELLUSDT.BINANCE, 连续告警次数: 2
2025-06-09T13:32:08.498999+0800  Level 20: 数据滞后已恢复正常: DYMUSDT.BINANCE
2025-06-09T13:32:08.499156+0800  Level 20: 数据滞后已恢复正常: OMUSDT.BINANCE
2025-06-09T13:32:08.499300+0800  Level 20: 检测到数据滞后: BEAMXUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:32:08.499432+0800  Level 20: 将发送滞后告警: BEAMXUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:32:08.499557+0800  Level 20: 数据滞后已恢复正常: IPUSDT.BINANCE
2025-06-09T13:32:08.499683+0800  Level 20: 已发送合并告警邮件，包含 0 个缺失合约和 9 个滞后合约
2025-06-09T13:33:08.542875+0800  Level 20: 开始检查数据完整性
2025-06-09T13:33:08.545542+0800  Level 20: 数据滞后已恢复正常: ARKUSDT.BINANCE
2025-06-09T13:33:08.545746+0800  Level 20: 数据滞后已恢复正常: RSRUSDT.BINANCE
2025-06-09T13:33:08.546397+0800  Level 20: 检测到数据滞后: ONEUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:33:08.546565+0800  Level 20: 将发送滞后告警: ONEUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:33:08.546725+0800  Level 20: 数据滞后已恢复正常: BELUSDT.BINANCE
2025-06-09T13:33:08.547679+0800  Level 20: 检测到数据滞后: TRUUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:33:08.547854+0800  Level 20: TRUUSDT.BINANCE 滞后告警冷却中，还需等待 239 秒 (冷却时间: 360秒)
2025-06-09T13:33:08.548015+0800  Level 20: 数据滞后已恢复正常: MEWUSDT.BINANCE
2025-06-09T13:33:08.548179+0800  Level 20: 检测到数据滞后: AGLDUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:33:08.548482+0800  Level 20: 将发送滞后告警: AGLDUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:33:08.548652+0800  Level 20: 数据滞后已恢复正常: AAVEUSDT.BINANCE
2025-06-09T13:33:08.548801+0800  Level 20: 数据滞后已恢复正常: ALGOUSDT.BINANCE
2025-06-09T13:33:08.548966+0800  Level 20: 检测到数据滞后: BIGTIMEUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:33:08.549121+0800  Level 20: 将发送滞后告警: BIGTIMEUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:33:08.549414+0800  Level 20: 检测到数据滞后: CRVUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:33:08.549600+0800  Level 20: 将发送滞后告警: CRVUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:33:08.549758+0800  Level 20: 检测到数据滞后: TOKENUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:33:08.549930+0800  Level 20: 将发送滞后告警: TOKENUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:33:08.550106+0800  Level 20: 检测到数据滞后: SAGAUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:33:08.550232+0800  Level 20: SAGAUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:33:08.550554+0800  Level 20: 检测到数据滞后: ZECUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:33:08.550731+0800  Level 20: 将发送滞后告警: ZECUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:33:08.550907+0800  Level 20: 检测到数据滞后: UXLINKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:33:08.551057+0800  Level 20: UXLINKUSDT.BINANCE 滞后告警冷却中，还需等待 239 秒 (冷却时间: 360秒)
2025-06-09T13:33:08.551212+0800  Level 20: 数据滞后已恢复正常: ACHUSDT.BINANCE
2025-06-09T13:33:08.551368+0800  Level 20: 数据滞后已恢复正常: SPELLUSDT.BINANCE
2025-06-09T13:33:08.551684+0800  Level 20: 数据滞后已恢复正常: BEAMXUSDT.BINANCE
2025-06-09T13:33:08.551865+0800  Level 20: 检测到数据滞后: AUCTIONUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:33:08.552032+0800  Level 20: 将发送滞后告警: AUCTIONUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:33:08.552198+0800  Level 20: 检测到数据滞后: BSWUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:33:08.552362+0800  Level 20: 将发送滞后告警: BSWUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:33:08.554003+0800  Level 20: 检测到数据滞后: PORTALUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:33:08.554782+0800  Level 20: 将发送滞后告警: PORTALUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:33:08.554970+0800  Level 20: 已发送合并告警邮件，包含 0 个缺失合约和 9 个滞后合约
2025-06-09T13:34:08.611455+0800  Level 20: 开始检查数据完整性
2025-06-09T13:34:08.613819+0800  Level 20: 检测到数据滞后: ARKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:34:08.614048+0800  Level 20: ARKUSDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 180秒)
2025-06-09T13:34:08.615726+0800  Level 20: 检测到数据滞后: ARUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:34:08.615898+0800  Level 20: 将发送滞后告警: ARUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:34:08.616061+0800  Level 20: 检测到数据滞后: FETUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:34:08.616207+0800  Level 20: 将发送滞后告警: FETUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:34:08.616348+0800  Level 20: 检测到数据滞后: ONEUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:34:08.616486+0800  Level 20: ONEUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:34:08.616625+0800  Level 20: 检测到数据滞后: THETAUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:34:08.616764+0800  Level 20: 将发送滞后告警: THETAUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:34:08.616900+0800  Level 20: 检测到数据滞后: PENDLEUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:34:08.617053+0800  Level 20: 将发送滞后告警: PENDLEUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:34:08.617190+0800  Level 20: 数据滞后已恢复正常: TRUUSDT.BINANCE
2025-06-09T13:34:08.617325+0800  Level 20: 数据滞后已恢复正常: AGLDUSDT.BINANCE
2025-06-09T13:34:08.617472+0800  Level 20: 数据滞后已恢复正常: BIGTIMEUSDT.BINANCE
2025-06-09T13:34:08.617608+0800  Level 20: 数据滞后已恢复正常: CRVUSDT.BINANCE
2025-06-09T13:34:08.617741+0800  Level 20: 数据滞后已恢复正常: TOKENUSDT.BINANCE
2025-06-09T13:34:08.617876+0800  Level 20: 数据滞后已恢复正常: SAGAUSDT.BINANCE
2025-06-09T13:34:08.618049+0800  Level 20: 检测到数据滞后: TURBOUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:34:08.618191+0800  Level 20: 将发送滞后告警: TURBOUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:34:08.618327+0800  Level 20: 数据滞后已恢复正常: ZECUSDT.BINANCE
2025-06-09T13:34:08.618459+0800  Level 20: 检测到数据滞后: IOTAUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:34:08.618593+0800  Level 20: 将发送滞后告警: IOTAUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:34:08.618726+0800  Level 20: 检测到数据滞后: UXLINKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:34:08.618856+0800  Level 20: UXLINKUSDT.BINANCE 滞后告警冷却中，还需等待 179 秒 (冷却时间: 360秒)
2025-06-09T13:34:08.619011+0800  Level 20: 检测到数据滞后: BEAMXUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:34:08.619149+0800  Level 20: BEAMXUSDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 180秒)
2025-06-09T13:34:08.619283+0800  Level 20: 数据滞后已恢复正常: AUCTIONUSDT.BINANCE
2025-06-09T13:34:08.619415+0800  Level 20: 检测到数据滞后: BSWUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:34:08.619548+0800  Level 20: BSWUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:34:08.619681+0800  Level 20: 检测到数据滞后: COOKIEUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:34:08.619814+0800  Level 20: 将发送滞后告警: COOKIEUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:34:08.619958+0800  Level 20: 检测到数据滞后: PORTALUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:34:08.620096+0800  Level 20: PORTALUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:34:08.620231+0800  Level 20: 已发送合并告警邮件，包含 0 个缺失合约和 7 个滞后合约
2025-06-09T13:35:08.674897+0800  Level 20: 开始检查数据完整性
2025-06-09T13:35:08.675282+0800  Level 20: 检测到数据滞后: ARKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:35:08.675472+0800  Level 20: 将发送滞后告警: ARKUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:35:08.675640+0800  Level 20: 检测到数据滞后: CKBUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:35:08.675842+0800  Level 20: 将发送滞后告警: CKBUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:35:08.676728+0800  Level 20: 数据滞后已恢复正常: ARUSDT.BINANCE
2025-06-09T13:35:08.676900+0800  Level 20: 数据滞后已恢复正常: FETUSDT.BINANCE
2025-06-09T13:35:08.677035+0800  Level 20: 检测到数据滞后: RSRUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:35:08.677188+0800  Level 20: 将发送滞后告警: RSRUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:35:08.677316+0800  Level 20: 数据滞后已恢复正常: ONEUSDT.BINANCE
2025-06-09T13:35:08.677439+0800  Level 20: 数据滞后已恢复正常: THETAUSDT.BINANCE
2025-06-09T13:35:08.677561+0800  Level 20: 检测到数据滞后: BELUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:35:08.677682+0800  Level 20: 将发送滞后告警: BELUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:35:08.677802+0800  Level 20: 数据滞后已恢复正常: PENDLEUSDT.BINANCE
2025-06-09T13:35:08.677921+0800  Level 20: 检测到数据滞后: TRUUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:35:08.678126+0800  Level 20: 将发送滞后告警: TRUUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:35:08.678342+0800  Level 20: 检测到数据滞后: ZENUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:35:08.678453+0800  Level 20: 将发送滞后告警: ZENUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:35:08.678538+0800  Level 20: 检测到数据滞后: CRVUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:35:08.678616+0800  Level 20: CRVUSDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 180秒)
2025-06-09T13:35:08.678697+0800  Level 20: 检测到数据滞后: TOKENUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:35:08.678781+0800  Level 20: TOKENUSDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 180秒)
2025-06-09T13:35:08.678866+0800  Level 20: 数据滞后已恢复正常: TURBOUSDT.BINANCE
2025-06-09T13:35:08.678945+0800  Level 20: 数据滞后已恢复正常: IOTAUSDT.BINANCE
2025-06-09T13:35:08.679024+0800  Level 20: 数据滞后已恢复正常: UXLINKUSDT.BINANCE
2025-06-09T13:35:08.679974+0800  Level 20: 检测到数据滞后: ACHUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:35:08.680146+0800  Level 20: 将发送滞后告警: ACHUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:35:08.680280+0800  Level 20: 检测到数据滞后: SPELLUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:35:08.680406+0800  Level 20: 将发送滞后告警: SPELLUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:35:08.680528+0800  Level 20: 检测到数据滞后: DYMUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:35:08.680651+0800  Level 20: 将发送滞后告警: DYMUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:35:08.680772+0800  Level 20: 数据滞后已恢复正常: BEAMXUSDT.BINANCE
2025-06-09T13:35:08.680892+0800  Level 20: 数据滞后已恢复正常: BSWUSDT.BINANCE
2025-06-09T13:35:08.681010+0800  Level 20: 检测到数据滞后: ZEREBROUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:35:08.681579+0800  Level 20: 将发送滞后告警: ZEREBROUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:35:08.681765+0800  Level 20: 数据滞后已恢复正常: COOKIEUSDT.BINANCE
2025-06-09T13:35:08.681930+0800  Level 20: 检测到数据滞后: IPUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:35:08.682136+0800  Level 20: 将发送滞后告警: IPUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:35:08.682292+0800  Level 20: 数据滞后已恢复正常: PORTALUSDT.BINANCE
2025-06-09T13:35:08.682399+0800  Level 20: 已发送合并告警邮件，包含 0 个缺失合约和 11 个滞后合约
2025-06-09T13:36:08.742062+0800  Level 20: 开始检查数据完整性
2025-06-09T13:36:08.743998+0800  Level 20: 检测到数据滞后: ARKMUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:36:08.744242+0800  Level 20: 将发送滞后告警: ARKMUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:36:08.744431+0800  Level 20: 检测到数据滞后: ARKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:36:08.744602+0800  Level 20: ARKUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:36:08.744772+0800  Level 20: 数据滞后已恢复正常: CKBUSDT.BINANCE
2025-06-09T13:36:08.744937+0800  Level 20: 数据滞后已恢复正常: RSRUSDT.BINANCE
2025-06-09T13:36:08.745180+0800  Level 20: 检测到数据滞后: PHBUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:36:08.745363+0800  Level 20: 将发送滞后告警: PHBUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:36:08.745530+0800  Level 20: 数据滞后已恢复正常: BELUSDT.BINANCE
2025-06-09T13:36:08.745695+0800  Level 20: 检测到数据滞后: TRUUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:36:08.745860+0800  Level 20: TRUUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:36:08.746041+0800  Level 20: 检测到数据滞后: MEWUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:36:08.746458+0800  Level 20: 将发送滞后告警: MEWUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:36:08.746637+0800  Level 20: 数据滞后已恢复正常: ZENUSDT.BINANCE
2025-06-09T13:36:08.746808+0800  Level 20: 检测到数据滞后: AGLDUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:36:08.746972+0800  Level 20: 将发送滞后告警: AGLDUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:36:08.747138+0800  Level 20: 检测到数据滞后: 1000SATSUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:36:08.747316+0800  Level 20: 将发送滞后告警: 1000SATSUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:36:08.747480+0800  Level 20: 数据滞后已恢复正常: CRVUSDT.BINANCE
2025-06-09T13:36:08.747643+0800  Level 20: 检测到数据滞后: TOKENUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:36:08.747805+0800  Level 20: 将发送滞后告警: TOKENUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:36:08.747981+0800  Level 20: 检测到数据滞后: IOTAUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:36:08.748146+0800  Level 20: IOTAUSDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 180秒)
2025-06-09T13:36:08.748324+0800  Level 20: 检测到数据滞后: UXLINKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:36:08.748488+0800  Level 20: 将发送滞后告警: UXLINKUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:36:08.748660+0800  Level 20: 检测到数据滞后: ACHUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:36:08.748823+0800  Level 20: ACHUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:36:08.748986+0800  Level 20: 检测到数据滞后: SPELLUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:36:08.749149+0800  Level 20: SPELLUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:36:08.749327+0800  Level 20: 数据滞后已恢复正常: DYMUSDT.BINANCE
2025-06-09T13:36:08.749488+0800  Level 20: 数据滞后已恢复正常: ZEREBROUSDT.BINANCE
2025-06-09T13:36:08.749650+0800  Level 20: 检测到数据滞后: DEGENUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:36:08.749811+0800  Level 20: 将发送滞后告警: DEGENUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:36:08.749992+0800  Level 20: 检测到数据滞后: COOKIEUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:36:08.750169+0800  Level 20: COOKIEUSDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 180秒)
2025-06-09T13:36:08.750335+0800  Level 20: 数据滞后已恢复正常: IPUSDT.BINANCE
2025-06-09T13:36:08.750499+0800  Level 20: 已发送合并告警邮件，包含 0 个缺失合约和 8 个滞后合约
2025-06-09T13:37:08.800999+0800  Level 20: 开始检查数据完整性
2025-06-09T13:37:08.801351+0800  Level 20: 数据滞后已恢复正常: ARKMUSDT.BINANCE
2025-06-09T13:37:08.801542+0800  Level 20: 数据滞后已恢复正常: ARKUSDT.BINANCE
2025-06-09T13:37:08.801696+0800  Level 20: 数据滞后已恢复正常: PHBUSDT.BINANCE
2025-06-09T13:37:08.801852+0800  Level 20: 检测到数据滞后: API3USDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:37:08.802519+0800  Level 20: 将发送滞后告警: API3USDT.BINANCE, 连续告警次数: 1
2025-06-09T13:37:08.803347+0800  Level 20: 检测到数据滞后: TRUUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:37:08.803541+0800  Level 20: TRUUSDT.BINANCE 滞后告警冷却中，还需等待 239 秒 (冷却时间: 360秒)
2025-06-09T13:37:08.804088+0800  Level 20: 数据滞后已恢复正常: MEWUSDT.BINANCE
2025-06-09T13:37:08.804658+0800  Level 20: 数据滞后已恢复正常: AGLDUSDT.BINANCE
2025-06-09T13:37:08.804838+0800  Level 20: 数据滞后已恢复正常: 1000SATSUSDT.BINANCE
2025-06-09T13:37:08.805002+0800  Level 20: 检测到数据滞后: CRVUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:37:08.805588+0800  Level 20: 将发送滞后告警: CRVUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:37:08.805795+0800  Level 20: 检测到数据滞后: TOKENUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:37:08.805985+0800  Level 20: TOKENUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:37:08.806156+0800  Level 20: 检测到数据滞后: XVGUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:37:08.806317+0800  Level 20: 将发送滞后告警: XVGUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:37:08.806485+0800  Level 20: 数据滞后已恢复正常: IOTAUSDT.BINANCE
2025-06-09T13:37:08.806643+0800  Level 20: 检测到数据滞后: UXLINKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:37:08.806827+0800  Level 20: UXLINKUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:37:08.806983+0800  Level 20: 检测到数据滞后: ACHUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:37:08.807136+0800  Level 20: ACHUSDT.BINANCE 滞后告警冷却中，还需等待 239 秒 (冷却时间: 360秒)
2025-06-09T13:37:08.807303+0800  Level 20: 数据滞后已恢复正常: SPELLUSDT.BINANCE
2025-06-09T13:37:08.807461+0800  Level 20: 检测到数据滞后: DYMUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:37:08.807608+0800  Level 20: DYMUSDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 180秒)
2025-06-09T13:37:08.807760+0800  Level 20: 检测到数据滞后: BSWUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:37:08.807947+0800  Level 20: 将发送滞后告警: BSWUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:37:08.808101+0800  Level 20: 检测到数据滞后: ZEREBROUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:37:08.808249+0800  Level 20: ZEREBROUSDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 180秒)
2025-06-09T13:37:08.808407+0800  Level 20: 检测到数据滞后: VIRTUALUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:37:08.808548+0800  Level 20: 将发送滞后告警: VIRTUALUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:37:08.808694+0800  Level 20: 数据滞后已恢复正常: DEGENUSDT.BINANCE
2025-06-09T13:37:08.808844+0800  Level 20: 检测到数据滞后: COOKIEUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:37:08.809076+0800  Level 20: 将发送滞后告警: COOKIEUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:37:08.809228+0800  Level 20: 已发送合并告警邮件，包含 0 个缺失合约和 6 个滞后合约
2025-06-09T13:38:08.847332+0800  Level 20: 开始检查数据完整性
2025-06-09T13:38:08.847765+0800  Level 20: 检测到数据滞后: CKBUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:38:08.847963+0800  Level 20: 将发送滞后告警: CKBUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:38:08.848098+0800  Level 20: 检测到数据滞后: RSRUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:38:08.848203+0800  Level 20: 将发送滞后告警: RSRUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:38:08.848290+0800  Level 20: 数据滞后已恢复正常: API3USDT.BINANCE
2025-06-09T13:38:08.848370+0800  Level 20: 检测到数据滞后: TRUUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:38:08.848474+0800  Level 20: TRUUSDT.BINANCE 滞后告警冷却中，还需等待 179 秒 (冷却时间: 360秒)
2025-06-09T13:38:08.848556+0800  Level 20: 检测到数据滞后: MEWUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:38:08.848645+0800  Level 20: MEWUSDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 180秒)
2025-06-09T13:38:08.848722+0800  Level 20: 数据滞后已恢复正常: CRVUSDT.BINANCE
2025-06-09T13:38:08.848821+0800  Level 20: 检测到数据滞后: TOKENUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:38:08.849565+0800  Level 20: TOKENUSDT.BINANCE 滞后告警冷却中，还需等待 239 秒 (冷却时间: 360秒)
2025-06-09T13:38:08.849753+0800  Level 20: 检测到数据滞后: XVGUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:38:08.849938+0800  Level 20: XVGUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:38:08.850125+0800  Level 20: 检测到数据滞后: UXLINKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:38:08.850287+0800  Level 20: UXLINKUSDT.BINANCE 滞后告警冷却中，还需等待 239 秒 (冷却时间: 360秒)
2025-06-09T13:38:08.851262+0800  Level 20: 检测到数据滞后: ACHUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:38:08.851822+0800  Level 20: ACHUSDT.BINANCE 滞后告警冷却中，还需等待 179 秒 (冷却时间: 360秒)
2025-06-09T13:38:08.852002+0800  Level 20: 检测到数据滞后: SPELLUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:38:08.852150+0800  Level 20: 将发送滞后告警: SPELLUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:38:08.852301+0800  Level 20: 检测到数据滞后: DYMUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:38:08.852459+0800  Level 20: 将发送滞后告警: DYMUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:38:08.852615+0800  Level 20: 检测到数据滞后: BSWUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:38:08.852766+0800  Level 20: BSWUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:38:08.853411+0800  Level 20: 检测到数据滞后: ZEREBROUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:38:08.853584+0800  Level 20: 将发送滞后告警: ZEREBROUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:38:08.853718+0800  Level 20: 数据滞后已恢复正常: VIRTUALUSDT.BINANCE
2025-06-09T13:38:08.853857+0800  Level 20: 数据滞后已恢复正常: COOKIEUSDT.BINANCE
2025-06-09T13:38:08.853998+0800  Level 20: 检测到数据滞后: IPUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:38:08.854128+0800  Level 20: 将发送滞后告警: IPUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:38:08.854251+0800  Level 20: 已发送合并告警邮件，包含 0 个缺失合约和 6 个滞后合约
2025-06-09T13:39:08.920565+0800  Level 20: 开始检查数据完整性
2025-06-09T13:39:08.922360+0800  Level 20: 检测到数据滞后: ARKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:39:08.922552+0800  Level 20: 将发送滞后告警: ARKUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:39:08.923131+0800  Level 20: 检测到数据滞后: CKBUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:39:08.923305+0800  Level 20: CKBUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:39:08.923467+0800  Level 20: 数据滞后已恢复正常: RSRUSDT.BINANCE
2025-06-09T13:39:08.923642+0800  Level 20: 数据滞后已恢复正常: TRUUSDT.BINANCE
2025-06-09T13:39:08.923815+0800  Level 20: 数据滞后已恢复正常: MEWUSDT.BINANCE
2025-06-09T13:39:08.923956+0800  Level 20: 检测到数据滞后: ZENUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:39:08.924112+0800  Level 20: 将发送滞后告警: ZENUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:39:08.924261+0800  Level 20: 检测到数据滞后: 1000RATSUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:39:08.924468+0800  Level 20: 将发送滞后告警: 1000RATSUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:39:08.924636+0800  Level 20: 检测到数据滞后: ALGOUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:39:08.924775+0800  Level 20: 将发送滞后告警: ALGOUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:39:08.924924+0800  Level 20: 检测到数据滞后: TOKENUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:39:08.925066+0800  Level 20: TOKENUSDT.BINANCE 滞后告警冷却中，还需等待 179 秒 (冷却时间: 360秒)
2025-06-09T13:39:08.925218+0800  Level 20: 检测到数据滞后: XVGUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:39:08.925364+0800  Level 20: XVGUSDT.BINANCE 滞后告警冷却中，还需等待 239 秒 (冷却时间: 360秒)
2025-06-09T13:39:08.925535+0800  Level 20: 检测到数据滞后: ZECUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:39:08.925685+0800  Level 20: 将发送滞后告警: ZECUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:39:08.925833+0800  Level 20: 检测到数据滞后: UXLINKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:39:08.926006+0800  Level 20: UXLINKUSDT.BINANCE 滞后告警冷却中，还需等待 179 秒 (冷却时间: 360秒)
2025-06-09T13:39:08.926166+0800  Level 20: 检测到数据滞后: ACHUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:39:08.926308+0800  Level 20: ACHUSDT.BINANCE 滞后告警冷却中，还需等待 119 秒 (冷却时间: 360秒)
2025-06-09T13:39:08.926450+0800  Level 20: 检测到数据滞后: SPELLUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:39:08.926613+0800  Level 20: SPELLUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:39:08.926763+0800  Level 20: 检测到数据滞后: DYMUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:39:08.926901+0800  Level 20: DYMUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:39:08.927046+0800  Level 20: 检测到数据滞后: BEAMXUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:39:08.927198+0800  Level 20: 将发送滞后告警: BEAMXUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:39:08.927341+0800  Level 20: 检测到数据滞后: BSWUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:39:08.927494+0800  Level 20: BSWUSDT.BINANCE 滞后告警冷却中，还需等待 239 秒 (冷却时间: 360秒)
2025-06-09T13:39:08.927662+0800  Level 20: 数据滞后已恢复正常: ZEREBROUSDT.BINANCE
2025-06-09T13:39:08.927805+0800  Level 20: 数据滞后已恢复正常: IPUSDT.BINANCE
2025-06-09T13:39:08.927952+0800  Level 20: 已发送合并告警邮件，包含 0 个缺失合约和 6 个滞后合约
2025-06-09T13:40:08.968867+0800  Level 20: 开始检查数据完整性
2025-06-09T13:40:08.969205+0800  Level 20: 数据滞后已恢复正常: ARKUSDT.BINANCE
2025-06-09T13:40:08.969389+0800  Level 20: 检测到数据滞后: 1000FLOKIUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:40:08.969538+0800  Level 20: 将发送滞后告警: 1000FLOKIUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:40:08.969714+0800  Level 20: 检测到数据滞后: CKBUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:40:08.969849+0800  Level 20: CKBUSDT.BINANCE 滞后告警冷却中，还需等待 239 秒 (冷却时间: 360秒)
2025-06-09T13:40:08.970000+0800  Level 20: 检测到数据滞后: BELUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:40:08.970145+0800  Level 20: 将发送滞后告警: BELUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:40:08.970290+0800  Level 20: 检测到数据滞后: MEWUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:40:08.970637+0800  Level 20: 将发送滞后告警: MEWUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:40:08.970821+0800  Level 20: 检测到数据滞后: ZENUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:40:08.971436+0800  Level 20: ZENUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:40:08.971614+0800  Level 20: 数据滞后已恢复正常: 1000RATSUSDT.BINANCE
2025-06-09T13:40:08.971788+0800  Level 20: 数据滞后已恢复正常: ALGOUSDT.BINANCE
2025-06-09T13:40:08.971962+0800  Level 20: 检测到数据滞后: CRVUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:40:08.972118+0800  Level 20: 将发送滞后告警: CRVUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:40:08.972277+0800  Level 20: 数据滞后已恢复正常: TOKENUSDT.BINANCE
2025-06-09T13:40:08.972430+0800  Level 20: 数据滞后已恢复正常: XVGUSDT.BINANCE
2025-06-09T13:40:08.972575+0800  Level 20: 数据滞后已恢复正常: ZECUSDT.BINANCE
2025-06-09T13:40:08.972739+0800  Level 20: 检测到数据滞后: UXLINKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:40:08.972897+0800  Level 20: UXLINKUSDT.BINANCE 滞后告警冷却中，还需等待 119 秒 (冷却时间: 360秒)
2025-06-09T13:40:08.973057+0800  Level 20: 数据滞后已恢复正常: ACHUSDT.BINANCE
2025-06-09T13:40:08.973214+0800  Level 20: 检测到数据滞后: SPELLUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:40:08.973364+0800  Level 20: SPELLUSDT.BINANCE 滞后告警冷却中，还需等待 239 秒 (冷却时间: 360秒)
2025-06-09T13:40:08.973521+0800  Level 20: 检测到数据滞后: DYMUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:40:08.973691+0800  Level 20: DYMUSDT.BINANCE 滞后告警冷却中，还需等待 239 秒 (冷却时间: 360秒)
2025-06-09T13:40:08.973852+0800  Level 20: 数据滞后已恢复正常: BEAMXUSDT.BINANCE
2025-06-09T13:40:08.974028+0800  Level 20: 检测到数据滞后: AIUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:40:08.974209+0800  Level 20: 将发送滞后告警: AIUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:40:08.974338+0800  Level 20: 检测到数据滞后: BSWUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:40:08.974425+0800  Level 20: BSWUSDT.BINANCE 滞后告警冷却中，还需等待 179 秒 (冷却时间: 360秒)
2025-06-09T13:40:08.974505+0800  Level 20: 检测到数据滞后: GOATUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:40:08.974581+0800  Level 20: 将发送滞后告警: GOATUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:40:08.974685+0800  Level 20: 检测到数据滞后: IPUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:40:08.974841+0800  Level 20: IPUSDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 180秒)
2025-06-09T13:40:08.974989+0800  Level 20: 已发送合并告警邮件，包含 0 个缺失合约和 6 个滞后合约
2025-06-09T13:41:09.028570+0800  Level 20: 开始检查数据完整性
2025-06-09T13:41:09.028887+0800  Level 20: 检测到数据滞后: ARKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:41:09.029056+0800  Level 20: ARKUSDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 180秒)
2025-06-09T13:41:09.029189+0800  Level 20: 数据滞后已恢复正常: 1000FLOKIUSDT.BINANCE
2025-06-09T13:41:09.029316+0800  Level 20: 数据滞后已恢复正常: CKBUSDT.BINANCE
2025-06-09T13:41:09.029443+0800  Level 20: 检测到数据滞后: API3USDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:41:09.029568+0800  Level 20: 将发送滞后告警: API3USDT.BINANCE, 连续告警次数: 1
2025-06-09T13:41:09.029696+0800  Level 20: 数据滞后已恢复正常: BELUSDT.BINANCE
2025-06-09T13:41:09.029836+0800  Level 20: 检测到数据滞后: TRUUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:41:09.029981+0800  Level 20: 将发送滞后告警: TRUUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:41:09.030119+0800  Level 20: 数据滞后已恢复正常: MEWUSDT.BINANCE
2025-06-09T13:41:09.030220+0800  Level 20: 数据滞后已恢复正常: ZENUSDT.BINANCE
2025-06-09T13:41:09.030312+0800  Level 20: 检测到数据滞后: 1000RATSUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:41:09.030408+0800  Level 20: 1000RATSUSDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 180秒)
2025-06-09T13:41:09.031149+0800  Level 20: 检测到数据滞后: ALGOUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:41:09.031337+0800  Level 20: ALGOUSDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 180秒)
2025-06-09T13:41:09.031501+0800  Level 20: 检测到数据滞后: CRVUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:41:09.031685+0800  Level 20: CRVUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:41:09.031864+0800  Level 20: 检测到数据滞后: TOKENUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:41:09.032040+0800  Level 20: 将发送滞后告警: TOKENUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:41:09.032203+0800  Level 20: 检测到数据滞后: ZECUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:41:09.032372+0800  Level 20: ZECUSDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 180秒)
2025-06-09T13:41:09.032522+0800  Level 20: 检测到数据滞后: UXLINKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:41:09.032679+0800  Level 20: UXLINKUSDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 360秒)
2025-06-09T13:41:09.032847+0800  Level 20: 数据滞后已恢复正常: SPELLUSDT.BINANCE
2025-06-09T13:41:09.033082+0800  Level 20: 检测到数据滞后: DYMUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:41:09.033238+0800  Level 20: DYMUSDT.BINANCE 滞后告警冷却中，还需等待 179 秒 (冷却时间: 360秒)
2025-06-09T13:41:09.033387+0800  Level 20: 检测到数据滞后: BEAMXUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:41:09.033529+0800  Level 20: BEAMXUSDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 180秒)
2025-06-09T13:41:09.033678+0800  Level 20: 检测到数据滞后: AIUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:41:09.033842+0800  Level 20: AIUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:41:09.034013+0800  Level 20: 检测到数据滞后: BSWUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:41:09.034162+0800  Level 20: BSWUSDT.BINANCE 滞后告警冷却中，还需等待 119 秒 (冷却时间: 360秒)
2025-06-09T13:41:09.034302+0800  Level 20: 数据滞后已恢复正常: GOATUSDT.BINANCE
2025-06-09T13:41:09.034456+0800  Level 20: 检测到数据滞后: ICXUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:41:09.034595+0800  Level 20: 将发送滞后告警: ICXUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:41:09.034760+0800  Level 20: 数据滞后已恢复正常: IPUSDT.BINANCE
2025-06-09T13:41:09.034923+0800  Level 20: 已发送合并告警邮件，包含 0 个缺失合约和 4 个滞后合约
2025-06-09T13:42:09.088133+0800  Level 20: 开始检查数据完整性
2025-06-09T13:42:09.090199+0800  Level 20: 检测到数据滞后: ARKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:42:09.090420+0800  Level 20: 将发送滞后告警: ARKUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:42:09.090589+0800  Level 20: 检测到数据滞后: CKBUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:42:09.090754+0800  Level 20: 将发送滞后告警: CKBUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:42:09.090938+0800  Level 20: 检测到数据滞后: API3USDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:42:09.091106+0800  Level 20: API3USDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:42:09.091272+0800  Level 20: 检测到数据滞后: TRUUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:42:09.091438+0800  Level 20: TRUUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:42:09.091602+0800  Level 20: 检测到数据滞后: MEWUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:42:09.106310+0800  Level 20: MEWUSDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 180秒)
2025-06-09T13:42:09.106550+0800  Level 20: 数据滞后已恢复正常: 1000RATSUSDT.BINANCE
2025-06-09T13:42:09.106703+0800  Level 20: 检测到数据滞后: ALGOUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:42:09.106836+0800  Level 20: 将发送滞后告警: ALGOUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:42:09.106986+0800  Level 20: 检测到数据滞后: CRVUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:42:09.107127+0800  Level 20: CRVUSDT.BINANCE 滞后告警冷却中，还需等待 239 秒 (冷却时间: 360秒)
2025-06-09T13:42:09.107266+0800  Level 20: 检测到数据滞后: TOKENUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:42:09.107397+0800  Level 20: TOKENUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:42:09.107536+0800  Level 20: 数据滞后已恢复正常: ZECUSDT.BINANCE
2025-06-09T13:42:09.107664+0800  Level 20: 检测到数据滞后: UXLINKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:42:09.107787+0800  Level 20: 将发送滞后告警: UXLINKUSDT.BINANCE, 连续告警次数: 2
2025-06-09T13:42:09.107924+0800  Level 20: 检测到数据滞后: ACHUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:42:09.108048+0800  Level 20: 将发送滞后告警: ACHUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:42:09.108174+0800  Level 20: 检测到数据滞后: DYMUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:42:09.108296+0800  Level 20: DYMUSDT.BINANCE 滞后告警冷却中，还需等待 119 秒 (冷却时间: 360秒)
2025-06-09T13:42:09.109092+0800  Level 20: 检测到数据滞后: BEAMXUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:42:09.109247+0800  Level 20: 将发送滞后告警: BEAMXUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:42:09.109380+0800  Level 20: 检测到数据滞后: AIUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:42:09.109509+0800  Level 20: AIUSDT.BINANCE 滞后告警冷却中，还需等待 239 秒 (冷却时间: 360秒)
2025-06-09T13:42:09.109636+0800  Level 20: 检测到数据滞后: BSWUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:42:09.109762+0800  Level 20: BSWUSDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 360秒)
2025-06-09T13:42:09.109915+0800  Level 20: 检测到数据滞后: COOKIEUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:42:09.110064+0800  Level 20: 将发送滞后告警: COOKIEUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:42:09.110196+0800  Level 20: 数据滞后已恢复正常: ICXUSDT.BINANCE
2025-06-09T13:42:09.110318+0800  Level 20: 检测到数据滞后: LAYERUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:42:09.110446+0800  Level 20: 将发送滞后告警: LAYERUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:42:09.110579+0800  Level 20: 已发送合并告警邮件，包含 0 个缺失合约和 8 个滞后合约
2025-06-09T13:43:09.134056+0800  Level 20: 开始检查数据完整性
2025-06-09T13:43:09.134510+0800  Level 20: 检测到数据滞后: ARKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:43:09.135173+0800  Level 20: ARKUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:43:09.135336+0800  Level 20: 检测到数据滞后: CKBUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:43:09.135470+0800  Level 20: CKBUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:43:09.135603+0800  Level 20: 检测到数据滞后: RSRUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:43:09.135735+0800  Level 20: 将发送滞后告警: RSRUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:43:09.136046+0800  Level 20: 检测到数据滞后: API3USDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:43:09.136202+0800  Level 20: API3USDT.BINANCE 滞后告警冷却中，还需等待 239 秒 (冷却时间: 360秒)
2025-06-09T13:43:09.136333+0800  Level 20: 检测到数据滞后: BELUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:43:09.136461+0800  Level 20: 将发送滞后告警: BELUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:43:09.136587+0800  Level 20: 数据滞后已恢复正常: TRUUSDT.BINANCE
2025-06-09T13:43:09.136713+0800  Level 20: 检测到数据滞后: MEWUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:43:09.136866+0800  Level 20: 将发送滞后告警: MEWUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:43:09.137045+0800  Level 20: 检测到数据滞后: ZENUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:43:09.137210+0800  Level 20: 将发送滞后告警: ZENUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:43:09.137343+0800  Level 20: 检测到数据滞后: ALGOUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:43:09.137468+0800  Level 20: ALGOUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:43:09.137592+0800  Level 20: 数据滞后已恢复正常: CRVUSDT.BINANCE
2025-06-09T13:43:09.137713+0800  Level 20: 检测到数据滞后: TOKENUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:43:09.137835+0800  Level 20: TOKENUSDT.BINANCE 滞后告警冷却中，还需等待 239 秒 (冷却时间: 360秒)
2025-06-09T13:43:09.137992+0800  Level 20: 检测到数据滞后: SAGAUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:43:09.138136+0800  Level 20: 将发送滞后告警: SAGAUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:43:09.138286+0800  Level 20: 检测到数据滞后: ZECUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:43:09.139043+0800  Level 20: 将发送滞后告警: ZECUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:43:09.139226+0800  Level 20: 检测到数据滞后: UXLINKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:43:09.139385+0800  Level 20: UXLINKUSDT.BINANCE 滞后告警冷却中，还需等待 659 秒 (冷却时间: 720秒)
2025-06-09T13:43:09.139539+0800  Level 20: 数据滞后已恢复正常: ACHUSDT.BINANCE
2025-06-09T13:43:09.139686+0800  Level 20: 检测到数据滞后: MEMEUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:43:09.139835+0800  Level 20: 将发送滞后告警: MEMEUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:43:09.140004+0800  Level 20: 检测到数据滞后: SPELLUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:43:09.140159+0800  Level 20: 将发送滞后告警: SPELLUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:43:09.140302+0800  Level 20: 检测到数据滞后: DYMUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:43:09.140449+0800  Level 20: DYMUSDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 360秒)
2025-06-09T13:43:09.140581+0800  Level 20: 数据滞后已恢复正常: BEAMXUSDT.BINANCE
2025-06-09T13:43:09.140710+0800  Level 20: 检测到数据滞后: LDOUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:43:09.140839+0800  Level 20: 将发送滞后告警: LDOUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:43:09.140981+0800  Level 20: 数据滞后已恢复正常: AIUSDT.BINANCE
2025-06-09T13:43:09.141114+0800  Level 20: 数据滞后已恢复正常: BSWUSDT.BINANCE
2025-06-09T13:43:09.141240+0800  Level 20: 数据滞后已恢复正常: COOKIEUSDT.BINANCE
2025-06-09T13:43:09.141364+0800  Level 20: 数据滞后已恢复正常: LAYERUSDT.BINANCE
2025-06-09T13:43:09.141488+0800  Level 20: 已发送合并告警邮件，包含 0 个缺失合约和 9 个滞后合约
2025-06-09T13:44:09.181461+0800  Level 20: 开始检查数据完整性
2025-06-09T13:44:09.183341+0800  Level 20: 数据滞后已恢复正常: ARKUSDT.BINANCE
2025-06-09T13:44:09.183524+0800  Level 20: 数据滞后已恢复正常: CKBUSDT.BINANCE
2025-06-09T13:44:09.183661+0800  Level 20: 检测到数据滞后: ARUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:44:09.184301+0800  Level 20: 将发送滞后告警: ARUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:44:09.184488+0800  Level 20: 数据滞后已恢复正常: RSRUSDT.BINANCE
2025-06-09T13:44:09.184621+0800  Level 20: 数据滞后已恢复正常: API3USDT.BINANCE
2025-06-09T13:44:09.184746+0800  Level 20: 数据滞后已恢复正常: BELUSDT.BINANCE
2025-06-09T13:44:09.186345+0800  Level 20: 检测到数据滞后: TRUUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:44:09.186521+0800  Level 20: 将发送滞后告警: TRUUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:44:09.186673+0800  Level 20: 数据滞后已恢复正常: MEWUSDT.BINANCE
2025-06-09T13:44:09.186816+0800  Level 20: 数据滞后已恢复正常: ZENUSDT.BINANCE
2025-06-09T13:44:09.186957+0800  Level 20: 检测到数据滞后: AGLDUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:44:09.187101+0800  Level 20: 将发送滞后告警: AGLDUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:44:09.187242+0800  Level 20: 检测到数据滞后: 1000RATSUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:44:09.187823+0800  Level 20: 将发送滞后告警: 1000RATSUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:44:09.187984+0800  Level 20: 数据滞后已恢复正常: ALGOUSDT.BINANCE
2025-06-09T13:44:09.188135+0800  Level 20: 检测到数据滞后: CRVUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:44:09.188294+0800  Level 20: 将发送滞后告警: CRVUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:44:09.188435+0800  Level 20: 检测到数据滞后: TOKENUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:44:09.188573+0800  Level 20: TOKENUSDT.BINANCE 滞后告警冷却中，还需等待 179 秒 (冷却时间: 360秒)
2025-06-09T13:44:09.188708+0800  Level 20: 检测到数据滞后: XVGUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:44:09.188844+0800  Level 20: 将发送滞后告警: XVGUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:44:09.188978+0800  Level 20: 数据滞后已恢复正常: SAGAUSDT.BINANCE
2025-06-09T13:44:09.189119+0800  Level 20: 数据滞后已恢复正常: ZECUSDT.BINANCE
2025-06-09T13:44:09.189257+0800  Level 20: 检测到数据滞后: IOTAUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:44:09.189391+0800  Level 20: 将发送滞后告警: IOTAUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:44:09.189996+0800  Level 20: 数据滞后已恢复正常: UXLINKUSDT.BINANCE
2025-06-09T13:44:09.190176+0800  Level 20: 检测到数据滞后: ACHUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:44:09.190323+0800  Level 20: ACHUSDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 180秒)
2025-06-09T13:44:09.190463+0800  Level 20: 检测到数据滞后: MEMEUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:44:09.190600+0800  Level 20: MEMEUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:44:09.190735+0800  Level 20: 检测到数据滞后: SPELLUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:44:09.190869+0800  Level 20: SPELLUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:44:09.191000+0800  Level 20: 数据滞后已恢复正常: DYMUSDT.BINANCE
2025-06-09T13:44:09.191604+0800  Level 20: 检测到数据滞后: HIGHUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:44:09.191768+0800  Level 20: 将发送滞后告警: HIGHUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:44:09.191908+0800  Level 20: 数据滞后已恢复正常: LDOUSDT.BINANCE
2025-06-09T13:44:09.192043+0800  Level 20: 检测到数据滞后: IPUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:44:09.192190+0800  Level 20: 将发送滞后告警: IPUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:44:09.192325+0800  Level 20: 已发送合并告警邮件，包含 0 个缺失合约和 9 个滞后合约
2025-06-09T13:45:09.247403+0800  Level 20: 开始检查数据完整性
2025-06-09T13:45:09.249271+0800  Level 20: 检测到数据滞后: CKBUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:45:09.249674+0800  Level 20: 将发送滞后告警: CKBUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:45:09.250287+0800  Level 20: 数据滞后已恢复正常: ARUSDT.BINANCE
2025-06-09T13:45:09.250476+0800  Level 20: 检测到数据滞后: API3USDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:45:09.250644+0800  Level 20: 将发送滞后告警: API3USDT.BINANCE, 连续告警次数: 1
2025-06-09T13:45:09.250811+0800  Level 20: 检测到数据滞后: BELUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:45:09.250963+0800  Level 20: BELUSDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 180秒)
2025-06-09T13:45:09.251124+0800  Level 20: 检测到数据滞后: TRUUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:45:09.251301+0800  Level 20: TRUUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:45:09.251567+0800  Level 20: 检测到数据滞后: ZENUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:45:09.251749+0800  Level 20: ZENUSDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 180秒)
2025-06-09T13:45:09.251953+0800  Level 20: 数据滞后已恢复正常: AGLDUSDT.BINANCE
2025-06-09T13:45:09.252105+0800  Level 20: 检测到数据滞后: 1000RATSUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:45:09.252270+0800  Level 20: 1000RATSUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:45:09.252376+0800  Level 20: 检测到数据滞后: ALGOUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:45:09.252458+0800  Level 20: 将发送滞后告警: ALGOUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:45:09.252575+0800  Level 20: 数据滞后已恢复正常: CRVUSDT.BINANCE
2025-06-09T13:45:09.252719+0800  Level 20: 数据滞后已恢复正常: TOKENUSDT.BINANCE
2025-06-09T13:45:09.252874+0800  Level 20: 数据滞后已恢复正常: XVGUSDT.BINANCE
2025-06-09T13:45:09.253427+0800  Level 20: 检测到数据滞后: ZECUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:45:09.253600+0800  Level 20: ZECUSDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 180秒)
2025-06-09T13:45:09.253765+0800  Level 20: 数据滞后已恢复正常: IOTAUSDT.BINANCE
2025-06-09T13:45:09.253932+0800  Level 20: 检测到数据滞后: UXLINKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:45:09.254283+0800  Level 20: 将发送滞后告警: UXLINKUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:45:09.254456+0800  Level 20: 数据滞后已恢复正常: ACHUSDT.BINANCE
2025-06-09T13:45:09.254614+0800  Level 20: 检测到数据滞后: MEMEUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:45:09.254771+0800  Level 20: MEMEUSDT.BINANCE 滞后告警冷却中，还需等待 239 秒 (冷却时间: 360秒)
2025-06-09T13:45:09.254914+0800  Level 20: 检测到数据滞后: SPELLUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:45:09.255060+0800  Level 20: SPELLUSDT.BINANCE 滞后告警冷却中，还需等待 239 秒 (冷却时间: 360秒)
2025-06-09T13:45:09.255209+0800  Level 20: 数据滞后已恢复正常: HIGHUSDT.BINANCE
2025-06-09T13:45:09.255372+0800  Level 20: 检测到数据滞后: BSWUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:45:09.255488+0800  Level 20: 将发送滞后告警: BSWUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:45:09.255621+0800  Level 20: 检测到数据滞后: COOKIEUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:45:09.255755+0800  Level 20: 将发送滞后告警: COOKIEUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:45:09.255905+0800  Level 20: 检测到数据滞后: IPUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:45:09.256053+0800  Level 20: IPUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:45:09.256218+0800  Level 20: 已发送合并告警邮件，包含 0 个缺失合约和 6 个滞后合约
2025-06-09T13:46:09.328826+0800  Level 20: 开始检查数据完整性
2025-06-09T13:46:09.330101+0800  Level 20: 检测到数据滞后: 1000FLOKIUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:46:09.330279+0800  Level 20: 将发送滞后告警: 1000FLOKIUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:46:09.330399+0800  Level 20: 数据滞后已恢复正常: CKBUSDT.BINANCE
2025-06-09T13:46:09.330988+0800  Level 20: 检测到数据滞后: API3USDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:46:09.331193+0800  Level 20: API3USDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:46:09.331372+0800  Level 20: 数据滞后已恢复正常: BELUSDT.BINANCE
2025-06-09T13:46:09.331532+0800  Level 20: 数据滞后已恢复正常: TRUUSDT.BINANCE
2025-06-09T13:46:09.331677+0800  Level 20: 检测到数据滞后: MEWUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:46:09.331826+0800  Level 20: 将发送滞后告警: MEWUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:46:09.332595+0800  Level 20: 检测到数据滞后: ZENUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:46:09.332807+0800  Level 20: 将发送滞后告警: ZENUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:46:09.332995+0800  Level 20: 数据滞后已恢复正常: 1000RATSUSDT.BINANCE
2025-06-09T13:46:09.333184+0800  Level 20: 检测到数据滞后: ALGOUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:46:09.333445+0800  Level 20: ALGOUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:46:09.333595+0800  Level 20: 检测到数据滞后: ZECUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:46:09.333730+0800  Level 20: 将发送滞后告警: ZECUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:46:09.333825+0800  Level 20: 数据滞后已恢复正常: UXLINKUSDT.BINANCE
2025-06-09T13:46:09.333915+0800  Level 20: 检测到数据滞后: ACHUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:46:09.334018+0800  Level 20: 将发送滞后告警: ACHUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:46:09.334107+0800  Level 20: 数据滞后已恢复正常: MEMEUSDT.BINANCE
2025-06-09T13:46:09.334604+0800  Level 20: 检测到数据滞后: SPELLUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:46:09.334689+0800  Level 20: SPELLUSDT.BINANCE 滞后告警冷却中，还需等待 179 秒 (冷却时间: 360秒)
2025-06-09T13:46:09.334766+0800  Level 20: 检测到数据滞后: DYMUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:46:09.334842+0800  Level 20: 将发送滞后告警: DYMUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:46:09.334929+0800  Level 20: 检测到数据滞后: LDOUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:46:09.335006+0800  Level 20: 将发送滞后告警: LDOUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:46:09.335082+0800  Level 20: 检测到数据滞后: BSWUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:46:09.335184+0800  Level 20: BSWUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:46:09.335277+0800  Level 20: 检测到数据滞后: COOKIEUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:46:09.335367+0800  Level 20: COOKIEUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:46:09.335444+0800  Level 20: 数据滞后已恢复正常: IPUSDT.BINANCE
2025-06-09T13:46:09.335518+0800  Level 20: 已发送合并告警邮件，包含 0 个缺失合约和 7 个滞后合约
2025-06-09T13:47:09.392824+0800  Level 20: 开始检查数据完整性
2025-06-09T13:47:09.395004+0800  Level 20: 数据滞后已恢复正常: 1000FLOKIUSDT.BINANCE
2025-06-09T13:47:09.395196+0800  Level 20: 检测到数据滞后: RSRUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:47:09.395345+0800  Level 20: 将发送滞后告警: RSRUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:47:09.395797+0800  Level 20: 数据滞后已恢复正常: API3USDT.BINANCE
2025-06-09T13:47:09.395970+0800  Level 20: 检测到数据滞后: PENDLEUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:47:09.396117+0800  Level 20: 将发送滞后告警: PENDLEUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:47:09.396256+0800  Level 20: 数据滞后已恢复正常: MEWUSDT.BINANCE
2025-06-09T13:47:09.396394+0800  Level 20: 检测到数据滞后: ZENUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:47:09.396549+0800  Level 20: ZENUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:47:09.396820+0800  Level 20: 检测到数据滞后: 1000RATSUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:47:09.396975+0800  Level 20: 将发送滞后告警: 1000RATSUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:47:09.397114+0800  Level 20: 数据滞后已恢复正常: ALGOUSDT.BINANCE
2025-06-09T13:47:09.397251+0800  Level 20: 检测到数据滞后: ZECUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:47:09.397385+0800  Level 20: ZECUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:47:09.397532+0800  Level 20: 检测到数据滞后: UXLINKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:47:09.397666+0800  Level 20: UXLINKUSDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 180秒)
2025-06-09T13:47:09.397799+0800  Level 20: 数据滞后已恢复正常: ACHUSDT.BINANCE
2025-06-09T13:47:09.397929+0800  Level 20: 检测到数据滞后: SPELLUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:47:09.398083+0800  Level 20: SPELLUSDT.BINANCE 滞后告警冷却中，还需等待 119 秒 (冷却时间: 360秒)
2025-06-09T13:47:09.398216+0800  Level 20: 检测到数据滞后: DYMUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:47:09.398356+0800  Level 20: DYMUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:47:09.398499+0800  Level 20: 检测到数据滞后: BEAMXUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:47:09.398633+0800  Level 20: 将发送滞后告警: BEAMXUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:47:09.398762+0800  Level 20: 检测到数据滞后: LDOUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:47:09.398889+0800  Level 20: LDOUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:47:09.399016+0800  Level 20: 数据滞后已恢复正常: BSWUSDT.BINANCE
2025-06-09T13:47:09.399145+0800  Level 20: 数据滞后已恢复正常: COOKIEUSDT.BINANCE
2025-06-09T13:47:09.399271+0800  Level 20: 检测到数据滞后: IPUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:47:09.399399+0800  Level 20: 将发送滞后告警: IPUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:47:09.399543+0800  Level 20: 已发送合并告警邮件，包含 0 个缺失合约和 5 个滞后合约
2025-06-09T13:48:09.463419+0800  Level 20: 开始检查数据完整性
2025-06-09T13:48:09.463756+0800  Level 20: 检测到数据滞后: CKBUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:48:09.463963+0800  Level 20: 将发送滞后告警: CKBUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:48:09.464154+0800  Level 20: 数据滞后已恢复正常: RSRUSDT.BINANCE
2025-06-09T13:48:09.464343+0800  Level 20: 检测到数据滞后: BELUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:48:09.465041+0800  Level 20: 将发送滞后告警: BELUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:48:09.465229+0800  Level 20: 数据滞后已恢复正常: PENDLEUSDT.BINANCE
2025-06-09T13:48:09.465430+0800  Level 20: 检测到数据滞后: TRUUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:48:09.465604+0800  Level 20: 将发送滞后告警: TRUUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:48:09.465772+0800  Level 20: 数据滞后已恢复正常: ZENUSDT.BINANCE
2025-06-09T13:48:09.465937+0800  Level 20: 检测到数据滞后: 1000RATSUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:48:09.466113+0800  Level 20: 1000RATSUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:48:09.466275+0800  Level 20: 检测到数据滞后: ALGOUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:48:09.466425+0800  Level 20: 将发送滞后告警: ALGOUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:48:09.469725+0800  Level 20: 检测到数据滞后: ZECUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:48:09.470200+0800  Level 20: ZECUSDT.BINANCE 滞后告警冷却中，还需等待 239 秒 (冷却时间: 360秒)
2025-06-09T13:48:09.470383+0800  Level 20: 检测到数据滞后: UXLINKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:48:09.470564+0800  Level 20: 将发送滞后告警: UXLINKUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:48:09.470733+0800  Level 20: 检测到数据滞后: ACHUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:48:09.470896+0800  Level 20: ACHUSDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 180秒)
2025-06-09T13:48:09.471061+0800  Level 20: 检测到数据滞后: SPELLUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:48:09.471226+0800  Level 20: SPELLUSDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 360秒)
2025-06-09T13:48:09.471392+0800  Level 20: 数据滞后已恢复正常: DYMUSDT.BINANCE
2025-06-09T13:48:09.471534+0800  Level 20: 数据滞后已恢复正常: BEAMXUSDT.BINANCE
2025-06-09T13:48:09.471642+0800  Level 20: 数据滞后已恢复正常: LDOUSDT.BINANCE
2025-06-09T13:48:09.471721+0800  Level 20: 检测到数据滞后: BSWUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:48:09.471797+0800  Level 20: 将发送滞后告警: BSWUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:48:09.471871+0800  Level 20: 数据滞后已恢复正常: IPUSDT.BINANCE
2025-06-09T13:48:09.471944+0800  Level 20: 已发送合并告警邮件，包含 0 个缺失合约和 6 个滞后合约
2025-06-09T13:49:09.511815+0800  Level 20: 开始检查数据完整性
2025-06-09T13:49:09.512271+0800  Level 20: 数据滞后已恢复正常: CKBUSDT.BINANCE
2025-06-09T13:49:09.512460+0800  Level 20: 检测到数据滞后: API3USDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:49:09.512625+0800  Level 20: 将发送滞后告警: API3USDT.BINANCE, 连续告警次数: 1
2025-06-09T13:49:09.513365+0800  Level 20: 数据滞后已恢复正常: BELUSDT.BINANCE
2025-06-09T13:49:09.513700+0800  Level 20: 检测到数据滞后: ENSUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:49:09.514178+0800  Level 20: 将发送滞后告警: ENSUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:49:09.514317+0800  Level 20: 检测到数据滞后: TRUUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:49:09.514472+0800  Level 20: TRUUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:49:09.514627+0800  Level 20: 数据滞后已恢复正常: 1000RATSUSDT.BINANCE
2025-06-09T13:49:09.514786+0800  Level 20: 数据滞后已恢复正常: ALGOUSDT.BINANCE
2025-06-09T13:49:09.514934+0800  Level 20: 检测到数据滞后: TOKENUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:49:09.515079+0800  Level 20: 将发送滞后告警: TOKENUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:49:09.515230+0800  Level 20: 检测到数据滞后: ZECUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:49:09.515372+0800  Level 20: ZECUSDT.BINANCE 滞后告警冷却中，还需等待 179 秒 (冷却时间: 360秒)
2025-06-09T13:49:09.515521+0800  Level 20: 检测到数据滞后: UXLINKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:49:09.515692+0800  Level 20: UXLINKUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:49:09.516343+0800  Level 20: 检测到数据滞后: ACHUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:49:09.516557+0800  Level 20: 将发送滞后告警: ACHUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:49:09.516738+0800  Level 20: 检测到数据滞后: SPELLUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:49:09.516926+0800  Level 20: 将发送滞后告警: SPELLUSDT.BINANCE, 连续告警次数: 2
2025-06-09T13:49:09.517074+0800  Level 20: 检测到数据滞后: BSWUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:49:09.517214+0800  Level 20: BSWUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:49:09.517843+0800  Level 20: 检测到数据滞后: LAYERUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:49:09.518068+0800  Level 20: 将发送滞后告警: LAYERUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:49:09.518228+0800  Level 20: 检测到数据滞后: IPUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:49:09.518385+0800  Level 20: IPUSDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 180秒)
2025-06-09T13:49:09.518539+0800  Level 20: 已发送合并告警邮件，包含 0 个缺失合约和 6 个滞后合约
2025-06-09T13:50:09.569030+0800  Level 20: 开始检查数据完整性
2025-06-09T13:50:09.569404+0800  Level 20: 检测到数据滞后: CKBUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:50:09.569624+0800  Level 20: CKBUSDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 180秒)
2025-06-09T13:50:09.569797+0800  Level 20: 检测到数据滞后: ARUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:50:09.569971+0800  Level 20: 将发送滞后告警: ARUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:50:09.570129+0800  Level 20: 检测到数据滞后: PEOPLEUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:50:09.570287+0800  Level 20: 将发送滞后告警: PEOPLEUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:50:09.570434+0800  Level 20: 检测到数据滞后: API3USDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:50:09.571084+0800  Level 20: API3USDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:50:09.571698+0800  Level 20: 数据滞后已恢复正常: ENSUSDT.BINANCE
2025-06-09T13:50:09.571900+0800  Level 20: 检测到数据滞后: LEVERUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:50:09.572074+0800  Level 20: 将发送滞后告警: LEVERUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:50:09.572268+0800  Level 20: 数据滞后已恢复正常: TRUUSDT.BINANCE
2025-06-09T13:50:09.572460+0800  Level 20: 检测到数据滞后: 1000SATSUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:50:09.572558+0800  Level 20: 将发送滞后告警: 1000SATSUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:50:09.572689+0800  Level 20: 数据滞后已恢复正常: TOKENUSDT.BINANCE
2025-06-09T13:50:09.572837+0800  Level 20: 检测到数据滞后: SAGAUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:50:09.573276+0800  Level 20: 将发送滞后告警: SAGAUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:50:09.573457+0800  Level 20: 数据滞后已恢复正常: ZECUSDT.BINANCE
2025-06-09T13:50:09.573626+0800  Level 20: 检测到数据滞后: IOTAUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:50:09.573786+0800  Level 20: 将发送滞后告警: IOTAUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:50:09.573975+0800  Level 20: 检测到数据滞后: UXLINKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:50:09.574133+0800  Level 20: UXLINKUSDT.BINANCE 滞后告警冷却中，还需等待 239 秒 (冷却时间: 360秒)
2025-06-09T13:50:09.574287+0800  Level 20: 数据滞后已恢复正常: ACHUSDT.BINANCE
2025-06-09T13:50:09.574415+0800  Level 20: 数据滞后已恢复正常: SPELLUSDT.BINANCE
2025-06-09T13:50:09.574544+0800  Level 20: 检测到数据滞后: BSWUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:50:09.575181+0800  Level 20: BSWUSDT.BINANCE 滞后告警冷却中，还需等待 239 秒 (冷却时间: 360秒)
2025-06-09T13:50:09.575388+0800  Level 20: 检测到数据滞后: ZEREBROUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:50:09.575593+0800  Level 20: 将发送滞后告警: ZEREBROUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:50:09.575807+0800  Level 20: 数据滞后已恢复正常: LAYERUSDT.BINANCE
2025-06-09T13:50:09.575999+0800  Level 20: 数据滞后已恢复正常: IPUSDT.BINANCE
2025-06-09T13:50:09.576179+0800  Level 20: 已发送合并告警邮件，包含 0 个缺失合约和 7 个滞后合约
2025-06-09T13:51:09.623766+0800  Level 20: 开始检查数据完整性
2025-06-09T13:51:09.626515+0800  Level 20: 检测到数据滞后: ARKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:51:09.627802+0800  Level 20: 将发送滞后告警: ARKUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:51:09.628236+0800  Level 20: 数据滞后已恢复正常: CKBUSDT.BINANCE
2025-06-09T13:51:09.628804+0800  Level 20: 数据滞后已恢复正常: ARUSDT.BINANCE
2025-06-09T13:51:09.628989+0800  Level 20: 检测到数据滞后: RSRUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:51:09.629154+0800  Level 20: 将发送滞后告警: RSRUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:51:09.629316+0800  Level 20: 数据滞后已恢复正常: PEOPLEUSDT.BINANCE
2025-06-09T13:51:09.629478+0800  Level 20: 数据滞后已恢复正常: API3USDT.BINANCE
2025-06-09T13:51:09.629644+0800  Level 20: 数据滞后已恢复正常: LEVERUSDT.BINANCE
2025-06-09T13:51:09.629809+0800  Level 20: 数据滞后已恢复正常: 1000SATSUSDT.BINANCE
2025-06-09T13:51:09.630001+0800  Level 20: 检测到数据滞后: ALGOUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:51:09.630171+0800  Level 20: 将发送滞后告警: ALGOUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:51:09.630330+0800  Level 20: 数据滞后已恢复正常: SAGAUSDT.BINANCE
2025-06-09T13:51:09.630488+0800  Level 20: 检测到数据滞后: ZECUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:51:09.630645+0800  Level 20: 将发送滞后告警: ZECUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:51:09.630819+0800  Level 20: 数据滞后已恢复正常: IOTAUSDT.BINANCE
2025-06-09T13:51:09.631012+0800  Level 20: 检测到数据滞后: UXLINKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:51:09.631180+0800  Level 20: UXLINKUSDT.BINANCE 滞后告警冷却中，还需等待 179 秒 (冷却时间: 360秒)
2025-06-09T13:51:09.631781+0800  Level 20: 检测到数据滞后: SPELLUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:51:09.631997+0800  Level 20: SPELLUSDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 180秒)
2025-06-09T13:51:09.632208+0800  Level 20: 检测到数据滞后: DYMUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:51:09.632393+0800  Level 20: 将发送滞后告警: DYMUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:51:09.632573+0800  Level 20: 检测到数据滞后: BEAMXUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:51:09.636390+0800  Level 20: 将发送滞后告警: BEAMXUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:51:09.636623+0800  Level 20: 检测到数据滞后: LDOUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:51:09.636814+0800  Level 20: 将发送滞后告警: LDOUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:51:09.637010+0800  Level 20: 检测到数据滞后: BSWUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:51:09.637176+0800  Level 20: BSWUSDT.BINANCE 滞后告警冷却中，还需等待 179 秒 (冷却时间: 360秒)
2025-06-09T13:51:09.637334+0800  Level 20: 检测到数据滞后: ZEREBROUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:51:09.637976+0800  Level 20: ZEREBROUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:51:09.638168+0800  Level 20: 检测到数据滞后: IPUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:51:09.638403+0800  Level 20: 将发送滞后告警: IPUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:51:09.638646+0800  Level 20: 已发送合并告警邮件，包含 0 个缺失合约和 8 个滞后合约
2025-06-09T13:52:09.681590+0800  Level 20: 开始检查数据完整性
2025-06-09T13:52:09.681987+0800  Level 20: 数据滞后已恢复正常: ARKUSDT.BINANCE
2025-06-09T13:52:09.682216+0800  Level 20: 数据滞后已恢复正常: RSRUSDT.BINANCE
2025-06-09T13:52:09.682411+0800  Level 20: 检测到数据滞后: API3USDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:52:09.682586+0800  Level 20: 将发送滞后告警: API3USDT.BINANCE, 连续告警次数: 1
2025-06-09T13:52:09.682770+0800  Level 20: 检测到数据滞后: PENDLEUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:52:09.682935+0800  Level 20: 将发送滞后告警: PENDLEUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:52:09.683247+0800  Level 20: 数据滞后已恢复正常: ALGOUSDT.BINANCE
2025-06-09T13:52:09.683456+0800  Level 20: 检测到数据滞后: CRVUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:52:09.683637+0800  Level 20: 将发送滞后告警: CRVUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:52:09.683799+0800  Level 20: 检测到数据滞后: TOKENUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:52:09.683952+0800  Level 20: 将发送滞后告警: TOKENUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:52:09.684139+0800  Level 20: 检测到数据滞后: XVGUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:52:09.684289+0800  Level 20: 将发送滞后告警: XVGUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:52:09.684443+0800  Level 20: 数据滞后已恢复正常: ZECUSDT.BINANCE
2025-06-09T13:52:09.684590+0800  Level 20: 数据滞后已恢复正常: UXLINKUSDT.BINANCE
2025-06-09T13:52:09.684754+0800  Level 20: 检测到数据滞后: SPELLUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:52:09.684877+0800  Level 20: 将发送滞后告警: SPELLUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:52:09.684962+0800  Level 20: 检测到数据滞后: DYMUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:52:09.685050+0800  Level 20: DYMUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:52:09.685129+0800  Level 20: 数据滞后已恢复正常: BEAMXUSDT.BINANCE
2025-06-09T13:52:09.685204+0800  Level 20: 数据滞后已恢复正常: LDOUSDT.BINANCE
2025-06-09T13:52:09.685321+0800  Level 20: 检测到数据滞后: BSWUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:52:09.685420+0800  Level 20: BSWUSDT.BINANCE 滞后告警冷却中，还需等待 119 秒 (冷却时间: 360秒)
2025-06-09T13:52:09.685507+0800  Level 20: 检测到数据滞后: ZEREBROUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:52:09.685591+0800  Level 20: ZEREBROUSDT.BINANCE 滞后告警冷却中，还需等待 239 秒 (冷却时间: 360秒)
2025-06-09T13:52:09.686017+0800  Level 20: 检测到数据滞后: COOKIEUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:52:09.686225+0800  Level 20: 将发送滞后告警: COOKIEUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:52:09.686392+0800  Level 20: 数据滞后已恢复正常: IPUSDT.BINANCE
2025-06-09T13:52:09.686555+0800  Level 20: 已发送合并告警邮件，包含 0 个缺失合约和 7 个滞后合约
2025-06-09T13:53:09.754349+0800  Level 20: 开始检查数据完整性
2025-06-09T13:53:09.756561+0800  Level 20: 检测到数据滞后: ARKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:53:09.756764+0800  Level 20: ARKUSDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 180秒)
2025-06-09T13:53:09.756920+0800  Level 20: 检测到数据滞后: RSRUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:53:09.757026+0800  Level 20: RSRUSDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 180秒)
2025-06-09T13:53:09.757110+0800  Level 20: 检测到数据滞后: API3USDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:53:09.757219+0800  Level 20: API3USDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:53:09.757354+0800  Level 20: 数据滞后已恢复正常: PENDLEUSDT.BINANCE
2025-06-09T13:53:09.757452+0800  Level 20: 检测到数据滞后: APEUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:53:09.757564+0800  Level 20: 将发送滞后告警: APEUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:53:09.757656+0800  Level 20: 检测到数据滞后: CRVUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:53:09.757771+0800  Level 20: CRVUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:53:09.757899+0800  Level 20: 数据滞后已恢复正常: TOKENUSDT.BINANCE
2025-06-09T13:53:09.758014+0800  Level 20: 数据滞后已恢复正常: XVGUSDT.BINANCE
2025-06-09T13:53:09.758171+0800  Level 20: 检测到数据滞后: ZECUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:53:09.758327+0800  Level 20: ZECUSDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 180秒)
2025-06-09T13:53:09.758478+0800  Level 20: 检测到数据滞后: UXLINKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:53:09.758761+0800  Level 20: 将发送滞后告警: UXLINKUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:53:09.758936+0800  Level 20: 数据滞后已恢复正常: SPELLUSDT.BINANCE
2025-06-09T13:53:09.759102+0800  Level 20: 检测到数据滞后: POPCATUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:53:09.759282+0800  Level 20: 将发送滞后告警: POPCATUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:53:09.759426+0800  Level 20: 数据滞后已恢复正常: DYMUSDT.BINANCE
2025-06-09T13:53:09.759580+0800  Level 20: 检测到数据滞后: BSWUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:53:09.759736+0800  Level 20: BSWUSDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 360秒)
2025-06-09T13:53:09.759892+0800  Level 20: 检测到数据滞后: ZEREBROUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:53:09.760049+0800  Level 20: ZEREBROUSDT.BINANCE 滞后告警冷却中，还需等待 179 秒 (冷却时间: 360秒)
2025-06-09T13:53:09.760221+0800  Level 20: 数据滞后已恢复正常: COOKIEUSDT.BINANCE
2025-06-09T13:53:09.760383+0800  Level 20: 已发送合并告警邮件，包含 0 个缺失合约和 3 个滞后合约
2025-06-09T13:54:09.822788+0800  Level 20: 开始检查数据完整性
2025-06-09T13:54:09.825044+0800  Level 20: 检测到数据滞后: ARKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:54:09.825236+0800  Level 20: 将发送滞后告警: ARKUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:54:09.825389+0800  Level 20: 检测到数据滞后: RSRUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:54:09.825522+0800  Level 20: 将发送滞后告警: RSRUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:54:09.825648+0800  Level 20: 数据滞后已恢复正常: API3USDT.BINANCE
2025-06-09T13:54:09.825771+0800  Level 20: 检测到数据滞后: APEUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:54:09.825891+0800  Level 20: APEUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:54:09.826032+0800  Level 20: 数据滞后已恢复正常: CRVUSDT.BINANCE
2025-06-09T13:54:09.826262+0800  Level 20: 检测到数据滞后: XVGUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:54:09.826405+0800  Level 20: XVGUSDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 180秒)
2025-06-09T13:54:09.826938+0800  Level 20: 检测到数据滞后: ZECUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:54:09.827088+0800  Level 20: 将发送滞后告警: ZECUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:54:09.827475+0800  Level 20: 检测到数据滞后: UXLINKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:54:09.827627+0800  Level 20: UXLINKUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:54:09.827758+0800  Level 20: 数据滞后已恢复正常: POPCATUSDT.BINANCE
2025-06-09T13:54:09.827885+0800  Level 20: 检测到数据滞后: BEAMXUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:54:09.828008+0800  Level 20: 将发送滞后告警: BEAMXUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:54:09.828130+0800  Level 20: 检测到数据滞后: BSWUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:54:09.828250+0800  Level 20: 将发送滞后告警: BSWUSDT.BINANCE, 连续告警次数: 2
2025-06-09T13:54:09.828382+0800  Level 20: 数据滞后已恢复正常: ZEREBROUSDT.BINANCE
2025-06-09T13:54:09.828503+0800  Level 20: 检测到数据滞后: GOATUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:54:09.828632+0800  Level 20: 将发送滞后告警: GOATUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:54:09.828754+0800  Level 20: 已发送合并告警邮件，包含 0 个缺失合约和 6 个滞后合约
2025-06-09T13:55:09.871035+0800  Level 20: 开始检查数据完整性
2025-06-09T13:55:09.872840+0800  Level 20: 数据滞后已恢复正常: ARKUSDT.BINANCE
2025-06-09T13:55:09.873050+0800  Level 20: 检测到数据滞后: CKBUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:55:09.873224+0800  Level 20: 将发送滞后告警: CKBUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:55:09.873385+0800  Level 20: 数据滞后已恢复正常: RSRUSDT.BINANCE
2025-06-09T13:55:09.873559+0800  Level 20: 检测到数据滞后: PEOPLEUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:55:09.873717+0800  Level 20: 将发送滞后告警: PEOPLEUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:55:09.873859+0800  Level 20: 检测到数据滞后: BELUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:55:09.874117+0800  Level 20: 将发送滞后告警: BELUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:55:09.874283+0800  Level 20: 检测到数据滞后: PENDLEUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:55:09.874451+0800  Level 20: 将发送滞后告警: PENDLEUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:55:09.874610+0800  Level 20: 检测到数据滞后: TRUUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:55:09.874759+0800  Level 20: 将发送滞后告警: TRUUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:55:09.875142+0800  Level 20: 数据滞后已恢复正常: APEUSDT.BINANCE
2025-06-09T13:55:09.875322+0800  Level 20: 数据滞后已恢复正常: XVGUSDT.BINANCE
2025-06-09T13:55:09.875498+0800  Level 20: 检测到数据滞后: ZECUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:55:09.875674+0800  Level 20: ZECUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:55:09.875840+0800  Level 20: 检测到数据滞后: UXLINKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:55:09.876013+0800  Level 20: UXLINKUSDT.BINANCE 滞后告警冷却中，还需等待 239 秒 (冷却时间: 360秒)
2025-06-09T13:55:09.876173+0800  Level 20: 检测到数据滞后: MEMEUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:55:09.876323+0800  Level 20: 将发送滞后告警: MEMEUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:55:09.876494+0800  Level 20: 检测到数据滞后: SPELLUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:55:09.876651+0800  Level 20: 将发送滞后告警: SPELLUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:55:09.876799+0800  Level 20: 检测到数据滞后: DYMUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:55:09.876941+0800  Level 20: 将发送滞后告警: DYMUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:55:09.877080+0800  Level 20: 数据滞后已恢复正常: BEAMXUSDT.BINANCE
2025-06-09T13:55:09.877232+0800  Level 20: 数据滞后已恢复正常: BSWUSDT.BINANCE
2025-06-09T13:55:09.877384+0800  Level 20: 数据滞后已恢复正常: GOATUSDT.BINANCE
2025-06-09T13:55:09.877538+0800  Level 20: 已发送合并告警邮件，包含 0 个缺失合约和 8 个滞后合约
2025-06-09T13:56:09.929817+0800  Level 20: 开始检查数据完整性
2025-06-09T13:56:09.942365+0800  Level 20: 检测到数据滞后: ARKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:56:09.942566+0800  Level 20: ARKUSDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 180秒)
2025-06-09T13:56:09.942734+0800  Level 20: 检测到数据滞后: 1000FLOKIUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:56:09.942868+0800  Level 20: 将发送滞后告警: 1000FLOKIUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:56:09.942994+0800  Level 20: 数据滞后已恢复正常: CKBUSDT.BINANCE
2025-06-09T13:56:09.943635+0800  Level 20: 数据滞后已恢复正常: PEOPLEUSDT.BINANCE
2025-06-09T13:56:09.943794+0800  Level 20: 数据滞后已恢复正常: BELUSDT.BINANCE
2025-06-09T13:56:09.943923+0800  Level 20: 数据滞后已恢复正常: PENDLEUSDT.BINANCE
2025-06-09T13:56:09.944470+0800  Level 20: 数据滞后已恢复正常: TRUUSDT.BINANCE
2025-06-09T13:56:09.944638+0800  Level 20: 检测到数据滞后: MEWUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:56:09.944771+0800  Level 20: 将发送滞后告警: MEWUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:56:09.944898+0800  Level 20: 检测到数据滞后: AAVEUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:56:09.945027+0800  Level 20: 将发送滞后告警: AAVEUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:56:09.945150+0800  Level 20: 检测到数据滞后: CRVUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:56:09.945270+0800  Level 20: 将发送滞后告警: CRVUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:56:09.945399+0800  Level 20: 检测到数据滞后: ZECUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:56:09.945528+0800  Level 20: ZECUSDT.BINANCE 滞后告警冷却中，还需等待 239 秒 (冷却时间: 360秒)
2025-06-09T13:56:09.945651+0800  Level 20: 检测到数据滞后: UXLINKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:56:09.945770+0800  Level 20: UXLINKUSDT.BINANCE 滞后告警冷却中，还需等待 179 秒 (冷却时间: 360秒)
2025-06-09T13:56:09.945888+0800  Level 20: 检测到数据滞后: LQTYUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:56:09.946027+0800  Level 20: 将发送滞后告警: LQTYUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:56:09.946168+0800  Level 20: 数据滞后已恢复正常: MEMEUSDT.BINANCE
2025-06-09T13:56:09.946345+0800  Level 20: 数据滞后已恢复正常: SPELLUSDT.BINANCE
2025-06-09T13:56:09.946475+0800  Level 20: 检测到数据滞后: DYMUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:56:09.946607+0800  Level 20: DYMUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:56:09.946727+0800  Level 20: 已发送合并告警邮件，包含 0 个缺失合约和 5 个滞后合约
2025-06-09T13:57:09.965870+0800  Level 20: 开始检查数据完整性
2025-06-09T13:57:09.968119+0800  Level 20: 检测到数据滞后: ARKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:57:09.968312+0800  Level 20: 将发送滞后告警: ARKUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:57:09.968450+0800  Level 20: 数据滞后已恢复正常: 1000FLOKIUSDT.BINANCE
2025-06-09T13:57:09.968582+0800  Level 20: 检测到数据滞后: RSRUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:57:09.969064+0800  Level 20: 将发送滞后告警: RSRUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:57:09.969219+0800  Level 20: 检测到数据滞后: API3USDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:57:09.969380+0800  Level 20: 将发送滞后告警: API3USDT.BINANCE, 连续告警次数: 1
2025-06-09T13:57:09.969944+0800  Level 20: 检测到数据滞后: BELUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:57:09.970515+0800  Level 20: BELUSDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 180秒)
2025-06-09T13:57:09.970696+0800  Level 20: 检测到数据滞后: TRUUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:57:09.970835+0800  Level 20: TRUUSDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 180秒)
2025-06-09T13:57:09.970964+0800  Level 20: 数据滞后已恢复正常: MEWUSDT.BINANCE
2025-06-09T13:57:09.971091+0800  Level 20: 数据滞后已恢复正常: AAVEUSDT.BINANCE
2025-06-09T13:57:09.971214+0800  Level 20: 检测到数据滞后: ALGOUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:57:09.971337+0800  Level 20: 将发送滞后告警: ALGOUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:57:09.971465+0800  Level 20: 数据滞后已恢复正常: CRVUSDT.BINANCE
2025-06-09T13:57:09.972090+0800  Level 20: 检测到数据滞后: XVGUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:57:09.972290+0800  Level 20: 将发送滞后告警: XVGUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:57:09.972458+0800  Level 20: 检测到数据滞后: ZECUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:57:09.972649+0800  Level 20: ZECUSDT.BINANCE 滞后告警冷却中，还需等待 179 秒 (冷却时间: 360秒)
2025-06-09T13:57:09.972829+0800  Level 20: 检测到数据滞后: UXLINKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:57:09.972998+0800  Level 20: UXLINKUSDT.BINANCE 滞后告警冷却中，还需等待 119 秒 (冷却时间: 360秒)
2025-06-09T13:57:09.973700+0800  Level 20: 数据滞后已恢复正常: LQTYUSDT.BINANCE
2025-06-09T13:57:09.973876+0800  Level 20: 检测到数据滞后: DYMUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:57:09.974065+0800  Level 20: DYMUSDT.BINANCE 滞后告警冷却中，还需等待 239 秒 (冷却时间: 360秒)
2025-06-09T13:57:09.974242+0800  Level 20: 检测到数据滞后: BEAMXUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:57:09.974405+0800  Level 20: 将发送滞后告警: BEAMXUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:57:09.974557+0800  Level 20: 检测到数据滞后: COOKIEUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:57:09.974727+0800  Level 20: 将发送滞后告警: COOKIEUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:57:09.974890+0800  Level 20: 检测到数据滞后: LAYERUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:57:09.975046+0800  Level 20: 将发送滞后告警: LAYERUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:57:09.975204+0800  Level 20: 已发送合并告警邮件，包含 0 个缺失合约和 8 个滞后合约
2025-06-09T13:58:10.029429+0800  Level 20: 开始检查数据完整性
2025-06-09T13:58:10.029786+0800  Level 20: 检测到数据滞后: ARKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:58:10.030001+0800  Level 20: ARKUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:58:10.030170+0800  Level 20: 检测到数据滞后: 1000FLOKIUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:58:10.030317+0800  Level 20: 1000FLOKIUSDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 180秒)
2025-06-09T13:58:10.030473+0800  Level 20: 检测到数据滞后: CKBUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:58:10.030629+0800  Level 20: 将发送滞后告警: CKBUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:58:10.030801+0800  Level 20: 检测到数据滞后: RSRUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:58:10.031114+0800  Level 20: RSRUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:58:10.031291+0800  Level 20: 数据滞后已恢复正常: API3USDT.BINANCE
2025-06-09T13:58:10.031451+0800  Level 20: 检测到数据滞后: BELUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:58:10.031618+0800  Level 20: 将发送滞后告警: BELUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:58:10.031780+0800  Level 20: 检测到数据滞后: ENSUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:58:10.031954+0800  Level 20: 将发送滞后告警: ENSUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:58:10.032118+0800  Level 20: 检测到数据滞后: PENDLEUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:58:10.032733+0800  Level 20: 将发送滞后告警: PENDLEUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:58:10.032922+0800  Level 20: 检测到数据滞后: TRUUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:58:10.033065+0800  Level 20: 将发送滞后告警: TRUUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:58:10.033241+0800  Level 20: 检测到数据滞后: 1000RATSUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:58:10.033396+0800  Level 20: 将发送滞后告警: 1000RATSUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:58:10.033690+0800  Level 20: 检测到数据滞后: AAVEUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:58:10.033917+0800  Level 20: AAVEUSDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 180秒)
2025-06-09T13:58:10.034093+0800  Level 20: 检测到数据滞后: ALGOUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:58:10.034259+0800  Level 20: ALGOUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:58:10.034411+0800  Level 20: 数据滞后已恢复正常: XVGUSDT.BINANCE
2025-06-09T13:58:10.034569+0800  Level 20: 检测到数据滞后: ZECUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:58:10.034727+0800  Level 20: ZECUSDT.BINANCE 滞后告警冷却中，还需等待 119 秒 (冷却时间: 360秒)
2025-06-09T13:58:10.034896+0800  Level 20: 数据滞后已恢复正常: UXLINKUSDT.BINANCE
2025-06-09T13:58:10.035055+0800  Level 20: 检测到数据滞后: MEMEUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:58:10.035208+0800  Level 20: 将发送滞后告警: MEMEUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:58:10.035439+0800  Level 20: 检测到数据滞后: SPELLUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:58:10.035600+0800  Level 20: 将发送滞后告警: SPELLUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:58:10.036326+0800  Level 20: 检测到数据滞后: DYMUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:58:10.036502+0800  Level 20: DYMUSDT.BINANCE 滞后告警冷却中，还需等待 179 秒 (冷却时间: 360秒)
2025-06-09T13:58:10.036707+0800  Level 20: 检测到数据滞后: BEAMXUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:58:10.036873+0800  Level 20: BEAMXUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:58:10.037018+0800  Level 20: 检测到数据滞后: AIUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:58:10.037169+0800  Level 20: 将发送滞后告警: AIUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:58:10.037314+0800  Level 20: 数据滞后已恢复正常: COOKIEUSDT.BINANCE
2025-06-09T13:58:10.037456+0800  Level 20: 数据滞后已恢复正常: LAYERUSDT.BINANCE
2025-06-09T13:58:10.037601+0800  Level 20: 已发送合并告警邮件，包含 0 个缺失合约和 9 个滞后合约
2025-06-09T13:59:10.091851+0800  Level 20: 开始检查数据完整性
2025-06-09T13:59:10.092229+0800  Level 20: 检测到数据滞后: ARKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:59:10.092417+0800  Level 20: ARKUSDT.BINANCE 滞后告警冷却中，还需等待 239 秒 (冷却时间: 360秒)
2025-06-09T13:59:10.092588+0800  Level 20: 数据滞后已恢复正常: 1000FLOKIUSDT.BINANCE
2025-06-09T13:59:10.092754+0800  Level 20: 数据滞后已恢复正常: CKBUSDT.BINANCE
2025-06-09T13:59:10.093621+0800  Level 20: 数据滞后已恢复正常: RSRUSDT.BINANCE
2025-06-09T13:59:10.093886+0800  Level 20: 检测到数据滞后: ONEUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:59:10.094102+0800  Level 20: 将发送滞后告警: ONEUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:59:10.094267+0800  Level 20: 检测到数据滞后: PEOPLEUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:59:10.094457+0800  Level 20: 将发送滞后告警: PEOPLEUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:59:10.094627+0800  Level 20: 检测到数据滞后: BELUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:59:10.094789+0800  Level 20: BELUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:59:10.094967+0800  Level 20: 数据滞后已恢复正常: ENSUSDT.BINANCE
2025-06-09T13:59:10.095134+0800  Level 20: 检测到数据滞后: LEVERUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:59:10.095816+0800  Level 20: 将发送滞后告警: LEVERUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:59:10.096023+0800  Level 20: 数据滞后已恢复正常: PENDLEUSDT.BINANCE
2025-06-09T13:59:10.096196+0800  Level 20: 数据滞后已恢复正常: TRUUSDT.BINANCE
2025-06-09T13:59:10.096403+0800  Level 20: 检测到数据滞后: 1000RATSUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:59:10.096586+0800  Level 20: 1000RATSUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:59:10.096757+0800  Level 20: 检测到数据滞后: 1000SATSUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:59:10.096935+0800  Level 20: 将发送滞后告警: 1000SATSUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:59:10.097090+0800  Level 20: 数据滞后已恢复正常: AAVEUSDT.BINANCE
2025-06-09T13:59:10.097247+0800  Level 20: 检测到数据滞后: ALGOUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:59:10.098121+0800  Level 20: ALGOUSDT.BINANCE 滞后告警冷却中，还需等待 239 秒 (冷却时间: 360秒)
2025-06-09T13:59:10.098335+0800  Level 20: 检测到数据滞后: TOKENUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:59:10.098499+0800  Level 20: 将发送滞后告警: TOKENUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:59:10.098660+0800  Level 20: 数据滞后已恢复正常: ZECUSDT.BINANCE
2025-06-09T13:59:10.098843+0800  Level 20: 检测到数据滞后: UXLINKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:59:10.099000+0800  Level 20: 将发送滞后告警: UXLINKUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:59:10.099145+0800  Level 20: 检测到数据滞后: MEMEUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:59:10.099278+0800  Level 20: MEMEUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:59:10.099420+0800  Level 20: 检测到数据滞后: SPELLUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:59:10.099577+0800  Level 20: SPELLUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T13:59:10.099742+0800  Level 20: 数据滞后已恢复正常: DYMUSDT.BINANCE
2025-06-09T13:59:10.099900+0800  Level 20: 数据滞后已恢复正常: BEAMXUSDT.BINANCE
2025-06-09T13:59:10.100057+0800  Level 20: 检测到数据滞后: AUCTIONUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:59:10.100195+0800  Level 20: 将发送滞后告警: AUCTIONUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:59:10.100347+0800  Level 20: 检测到数据滞后: LDOUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:59:10.100499+0800  Level 20: 将发送滞后告警: LDOUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:59:10.100648+0800  Level 20: 数据滞后已恢复正常: AIUSDT.BINANCE
2025-06-09T13:59:10.100807+0800  Level 20: 检测到数据滞后: IPUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T13:59:10.100969+0800  Level 20: 将发送滞后告警: IPUSDT.BINANCE, 连续告警次数: 1
2025-06-09T13:59:10.101123+0800  Level 20: 已发送合并告警邮件，包含 0 个缺失合约和 9 个滞后合约
2025-06-09T14:00:10.141352+0800  Level 20: 开始检查数据完整性
2025-06-09T14:00:10.143281+0800  Level 20: 检测到数据滞后: ARKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:00:10.143495+0800  Level 20: ARKUSDT.BINANCE 滞后告警冷却中，还需等待 179 秒 (冷却时间: 360秒)
2025-06-09T14:00:10.143674+0800  Level 20: 检测到数据滞后: JASMYUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:00:10.143827+0800  Level 20: 将发送滞后告警: JASMYUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:00:10.144468+0800  Level 20: 数据滞后已恢复正常: ONEUSDT.BINANCE
2025-06-09T14:00:10.144646+0800  Level 20: 数据滞后已恢复正常: PEOPLEUSDT.BINANCE
2025-06-09T14:00:10.144807+0800  Level 20: 检测到数据滞后: BELUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:00:10.144977+0800  Level 20: BELUSDT.BINANCE 滞后告警冷却中，还需等待 239 秒 (冷却时间: 360秒)
2025-06-09T14:00:10.145153+0800  Level 20: 数据滞后已恢复正常: LEVERUSDT.BINANCE
2025-06-09T14:00:10.145444+0800  Level 20: 检测到数据滞后: TRUUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:00:10.145740+0800  Level 20: TRUUSDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 180秒)
2025-06-09T14:00:10.145915+0800  Level 20: 检测到数据滞后: 1000RATSUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:00:10.146117+0800  Level 20: 1000RATSUSDT.BINANCE 滞后告警冷却中，还需等待 239 秒 (冷却时间: 360秒)
2025-06-09T14:00:10.146762+0800  Level 20: 检测到数据滞后: 1000SATSUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:00:10.146928+0800  Level 20: 1000SATSUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T14:00:10.147073+0800  Level 20: 数据滞后已恢复正常: ALGOUSDT.BINANCE
2025-06-09T14:00:10.147274+0800  Level 20: 数据滞后已恢复正常: TOKENUSDT.BINANCE
2025-06-09T14:00:10.147409+0800  Level 20: 检测到数据滞后: ZECUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:00:10.147542+0800  Level 20: 将发送滞后告警: ZECUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:00:10.147687+0800  Level 20: 检测到数据滞后: IOTAUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:00:10.148277+0800  Level 20: 将发送滞后告警: IOTAUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:00:10.148456+0800  Level 20: 数据滞后已恢复正常: UXLINKUSDT.BINANCE
2025-06-09T14:00:10.148602+0800  Level 20: 数据滞后已恢复正常: MEMEUSDT.BINANCE
2025-06-09T14:00:10.149233+0800  Level 20: 数据滞后已恢复正常: SPELLUSDT.BINANCE
2025-06-09T14:00:10.149438+0800  Level 20: 检测到数据滞后: AUCTIONUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:00:10.149575+0800  Level 20: AUCTIONUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T14:00:10.149772+0800  Level 20: 数据滞后已恢复正常: LDOUSDT.BINANCE
2025-06-09T14:00:10.149902+0800  Level 20: 检测到数据滞后: BSWUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:00:10.150057+0800  Level 20: 将发送滞后告警: BSWUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:00:10.150190+0800  Level 20: 检测到数据滞后: GOATUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:00:10.150316+0800  Level 20: 将发送滞后告警: GOATUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:00:10.150452+0800  Level 20: 数据滞后已恢复正常: IPUSDT.BINANCE
2025-06-09T14:00:10.150592+0800  Level 20: 已发送合并告警邮件，包含 0 个缺失合约和 5 个滞后合约
2025-06-09T14:01:10.202162+0800  Level 20: 开始检查数据完整性
2025-06-09T14:01:10.204252+0800  Level 20: 检测到数据滞后: ARKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:01:10.204449+0800  Level 20: ARKUSDT.BINANCE 滞后告警冷却中，还需等待 119 秒 (冷却时间: 360秒)
2025-06-09T14:01:10.204621+0800  Level 20: 数据滞后已恢复正常: JASMYUSDT.BINANCE
2025-06-09T14:01:10.204791+0800  Level 20: 检测到数据滞后: RSRUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:01:10.204951+0800  Level 20: 将发送滞后告警: RSRUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:01:10.205855+0800  Level 20: 检测到数据滞后: API3USDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:01:10.206054+0800  Level 20: 将发送滞后告警: API3USDT.BINANCE, 连续告警次数: 1
2025-06-09T14:01:10.206240+0800  Level 20: 检测到数据滞后: BELUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:01:10.206419+0800  Level 20: BELUSDT.BINANCE 滞后告警冷却中，还需等待 179 秒 (冷却时间: 360秒)
2025-06-09T14:01:10.207139+0800  Level 20: 数据滞后已恢复正常: TRUUSDT.BINANCE
2025-06-09T14:01:10.207264+0800  Level 20: 检测到数据滞后: XLMUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:01:10.207364+0800  Level 20: 将发送滞后告警: XLMUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:01:10.207448+0800  Level 20: 数据滞后已恢复正常: 1000RATSUSDT.BINANCE
2025-06-09T14:01:10.207546+0800  Level 20: 数据滞后已恢复正常: 1000SATSUSDT.BINANCE
2025-06-09T14:01:10.207629+0800  Level 20: 检测到数据滞后: XVGUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:01:10.207705+0800  Level 20: 将发送滞后告警: XVGUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:01:10.207780+0800  Level 20: 检测到数据滞后: ZECUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:01:10.207853+0800  Level 20: ZECUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T14:01:10.207925+0800  Level 20: 数据滞后已恢复正常: IOTAUSDT.BINANCE
2025-06-09T14:01:10.207998+0800  Level 20: 检测到数据滞后: SPELLUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:01:10.208069+0800  Level 20: 将发送滞后告警: SPELLUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:01:10.208153+0800  Level 20: 检测到数据滞后: DYMUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:01:10.208229+0800  Level 20: 将发送滞后告警: DYMUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:01:10.208310+0800  Level 20: 检测到数据滞后: BEAMXUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:01:10.208385+0800  Level 20: 将发送滞后告警: BEAMXUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:01:10.208456+0800  Level 20: 数据滞后已恢复正常: AUCTIONUSDT.BINANCE
2025-06-09T14:01:10.208527+0800  Level 20: 检测到数据滞后: BSWUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:01:10.208597+0800  Level 20: BSWUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T14:01:10.208670+0800  Level 20: 数据滞后已恢复正常: GOATUSDT.BINANCE
2025-06-09T14:01:10.208741+0800  Level 20: 已发送合并告警邮件，包含 0 个缺失合约和 7 个滞后合约
2025-06-09T14:02:10.256352+0800  Level 20: 开始检查数据完整性
2025-06-09T14:02:10.259195+0800  Level 20: 数据滞后已恢复正常: ARKUSDT.BINANCE
2025-06-09T14:02:10.259396+0800  Level 20: 检测到数据滞后: JASMYUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:02:10.259536+0800  Level 20: JASMYUSDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 180秒)
2025-06-09T14:02:10.259665+0800  Level 20: 数据滞后已恢复正常: RSRUSDT.BINANCE
2025-06-09T14:02:10.260846+0800  Level 20: 检测到数据滞后: PHBUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:02:10.261058+0800  Level 20: 将发送滞后告警: PHBUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:02:10.261242+0800  Level 20: 检测到数据滞后: PEOPLEUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:02:10.261807+0800  Level 20: 将发送滞后告警: PEOPLEUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:02:10.261982+0800  Level 20: 数据滞后已恢复正常: API3USDT.BINANCE
2025-06-09T14:02:10.262147+0800  Level 20: 数据滞后已恢复正常: BELUSDT.BINANCE
2025-06-09T14:02:10.262305+0800  Level 20: 检测到数据滞后: MEWUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:02:10.262435+0800  Level 20: 将发送滞后告警: MEWUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:02:10.262558+0800  Level 20: 数据滞后已恢复正常: XLMUSDT.BINANCE
2025-06-09T14:02:10.262679+0800  Level 20: 检测到数据滞后: 1000RATSUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:02:10.262801+0800  Level 20: 将发送滞后告警: 1000RATSUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:02:10.262923+0800  Level 20: 检测到数据滞后: ALGOUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:02:10.263043+0800  Level 20: 将发送滞后告警: ALGOUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:02:10.263162+0800  Level 20: 检测到数据滞后: BIGTIMEUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:02:10.263292+0800  Level 20: 将发送滞后告警: BIGTIMEUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:02:10.263417+0800  Level 20: 数据滞后已恢复正常: XVGUSDT.BINANCE
2025-06-09T14:02:10.264146+0800  Level 20: 检测到数据滞后: ZECUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:02:10.264323+0800  Level 20: ZECUSDT.BINANCE 滞后告警冷却中，还需等待 239 秒 (冷却时间: 360秒)
2025-06-09T14:02:10.264459+0800  Level 20: 检测到数据滞后: ACHUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:02:10.264586+0800  Level 20: 将发送滞后告警: ACHUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:02:10.264711+0800  Level 20: 检测到数据滞后: MEMEUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:02:10.264834+0800  Level 20: 将发送滞后告警: MEMEUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:02:10.264955+0800  Level 20: 数据滞后已恢复正常: SPELLUSDT.BINANCE
2025-06-09T14:02:10.265075+0800  Level 20: 数据滞后已恢复正常: DYMUSDT.BINANCE
2025-06-09T14:02:10.265194+0800  Level 20: 数据滞后已恢复正常: BEAMXUSDT.BINANCE
2025-06-09T14:02:10.265327+0800  Level 20: 检测到数据滞后: VANRYUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:02:10.265450+0800  Level 20: 将发送滞后告警: VANRYUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:02:10.265570+0800  Level 20: 数据滞后已恢复正常: BSWUSDT.BINANCE
2025-06-09T14:02:10.265698+0800  Level 20: 已发送合并告警邮件，包含 0 个缺失合约和 9 个滞后合约
2025-06-09T14:03:10.311662+0800  Level 20: 开始检查数据完整性
2025-06-09T14:03:10.313415+0800  Level 20: 检测到数据滞后: ARKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:03:10.313602+0800  Level 20: 将发送滞后告警: ARKUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:03:10.313773+0800  Level 20: 数据滞后已恢复正常: JASMYUSDT.BINANCE
2025-06-09T14:03:10.313929+0800  Level 20: 检测到数据滞后: RSRUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:03:10.314138+0800  Level 20: RSRUSDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 180秒)
2025-06-09T14:03:10.314694+0800  Level 20: 数据滞后已恢复正常: PHBUSDT.BINANCE
2025-06-09T14:03:10.314861+0800  Level 20: 数据滞后已恢复正常: PEOPLEUSDT.BINANCE
2025-06-09T14:03:10.315023+0800  Level 20: 检测到数据滞后: API3USDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:03:10.315173+0800  Level 20: API3USDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 180秒)
2025-06-09T14:03:10.315548+0800  Level 20: 数据滞后已恢复正常: MEWUSDT.BINANCE
2025-06-09T14:03:10.315729+0800  Level 20: 数据滞后已恢复正常: 1000RATSUSDT.BINANCE
2025-06-09T14:03:10.315883+0800  Level 20: 数据滞后已恢复正常: ALGOUSDT.BINANCE
2025-06-09T14:03:10.316035+0800  Level 20: 数据滞后已恢复正常: BIGTIMEUSDT.BINANCE
2025-06-09T14:03:10.316195+0800  Level 20: 检测到数据滞后: XVGUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:03:10.316349+0800  Level 20: XVGUSDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 180秒)
2025-06-09T14:03:10.316507+0800  Level 20: 检测到数据滞后: ZECUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:03:10.316667+0800  Level 20: ZECUSDT.BINANCE 滞后告警冷却中，还需等待 179 秒 (冷却时间: 360秒)
2025-06-09T14:03:10.316822+0800  Level 20: 检测到数据滞后: UXLINKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:03:10.316978+0800  Level 20: 将发送滞后告警: UXLINKUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:03:10.317134+0800  Level 20: 检测到数据滞后: ACHUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:03:10.317267+0800  Level 20: ACHUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T14:03:10.317416+0800  Level 20: 数据滞后已恢复正常: MEMEUSDT.BINANCE
2025-06-09T14:03:10.317570+0800  Level 20: 检测到数据滞后: DYMUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:03:10.317714+0800  Level 20: DYMUSDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 180秒)
2025-06-09T14:03:10.317865+0800  Level 20: 数据滞后已恢复正常: VANRYUSDT.BINANCE
2025-06-09T14:03:10.318031+0800  Level 20: 检测到数据滞后: BSWUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:03:10.318181+0800  Level 20: 将发送滞后告警: BSWUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:03:10.318329+0800  Level 20: 已发送合并告警邮件，包含 0 个缺失合约和 3 个滞后合约
2025-06-09T14:04:10.362465+0800  Level 20: 开始检查数据完整性
2025-06-09T14:04:10.362845+0800  Level 20: 检测到数据滞后: ARKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:04:10.363052+0800  Level 20: ARKUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T14:04:10.363224+0800  Level 20: 数据滞后已恢复正常: RSRUSDT.BINANCE
2025-06-09T14:04:10.363372+0800  Level 20: 数据滞后已恢复正常: API3USDT.BINANCE
2025-06-09T14:04:10.364063+0800  Level 20: 检测到数据滞后: PENDLEUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:04:10.364240+0800  Level 20: 将发送滞后告警: PENDLEUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:04:10.364387+0800  Level 20: 检测到数据滞后: TRUUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:04:10.364541+0800  Level 20: 将发送滞后告警: TRUUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:04:10.364697+0800  Level 20: 检测到数据滞后: CRVUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:04:10.364841+0800  Level 20: 将发送滞后告警: CRVUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:04:10.364980+0800  Level 20: 数据滞后已恢复正常: XVGUSDT.BINANCE
2025-06-09T14:04:10.365118+0800  Level 20: 检测到数据滞后: ZECUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:04:10.365709+0800  Level 20: ZECUSDT.BINANCE 滞后告警冷却中，还需等待 119 秒 (冷却时间: 360秒)
2025-06-09T14:04:10.365865+0800  Level 20: 检测到数据滞后: UXLINKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:04:10.366025+0800  Level 20: UXLINKUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T14:04:10.366222+0800  Level 20: 数据滞后已恢复正常: ACHUSDT.BINANCE
2025-06-09T14:04:10.366461+0800  Level 20: 检测到数据滞后: SPELLUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:04:10.366621+0800  Level 20: 将发送滞后告警: SPELLUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:04:10.366760+0800  Level 20: 数据滞后已恢复正常: DYMUSDT.BINANCE
2025-06-09T14:04:10.367759+0800  Level 20: 检测到数据滞后: AUCTIONUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:04:10.367937+0800  Level 20: 将发送滞后告警: AUCTIONUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:04:10.368080+0800  Level 20: 检测到数据滞后: BSWUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:04:10.368224+0800  Level 20: BSWUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T14:04:10.368363+0800  Level 20: 检测到数据滞后: LAYERUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:04:10.368509+0800  Level 20: 将发送滞后告警: LAYERUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:04:10.368646+0800  Level 20: 检测到数据滞后: IPUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:04:10.368779+0800  Level 20: 将发送滞后告警: IPUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:04:10.368912+0800  Level 20: 已发送合并告警邮件，包含 0 个缺失合约和 7 个滞后合约
2025-06-09T14:05:10.427799+0800  Level 20: 开始检查数据完整性
2025-06-09T14:05:10.428594+0800  Level 20: 检测到数据滞后: ARKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:05:10.428787+0800  Level 20: ARKUSDT.BINANCE 滞后告警冷却中，还需等待 239 秒 (冷却时间: 360秒)
2025-06-09T14:05:10.428965+0800  Level 20: 检测到数据滞后: RSRUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:05:10.429127+0800  Level 20: 将发送滞后告警: RSRUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:05:10.429620+0800  Level 20: 数据滞后已恢复正常: PENDLEUSDT.BINANCE
2025-06-09T14:05:10.429844+0800  Level 20: 数据滞后已恢复正常: TRUUSDT.BINANCE
2025-06-09T14:05:10.430034+0800  Level 20: 检测到数据滞后: 1000RATSUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:05:10.430204+0800  Level 20: 将发送滞后告警: 1000RATSUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:05:10.430360+0800  Level 20: 数据滞后已恢复正常: CRVUSDT.BINANCE
2025-06-09T14:05:10.430942+0800  Level 20: 检测到数据滞后: TOKENUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:05:10.431115+0800  Level 20: 将发送滞后告警: TOKENUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:05:10.431269+0800  Level 20: 检测到数据滞后: XVGUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:05:10.431433+0800  Level 20: 将发送滞后告警: XVGUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:05:10.431595+0800  Level 20: 数据滞后已恢复正常: ZECUSDT.BINANCE
2025-06-09T14:05:10.431739+0800  Level 20: 检测到数据滞后: UXLINKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:05:10.432013+0800  Level 20: UXLINKUSDT.BINANCE 滞后告警冷却中，还需等待 239 秒 (冷却时间: 360秒)
2025-06-09T14:05:10.432220+0800  Level 20: 数据滞后已恢复正常: SPELLUSDT.BINANCE
2025-06-09T14:05:10.432389+0800  Level 20: 检测到数据滞后: DYMUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:05:10.432560+0800  Level 20: 将发送滞后告警: DYMUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:05:10.432739+0800  Level 20: 检测到数据滞后: BEAMXUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:05:10.432904+0800  Level 20: 将发送滞后告警: BEAMXUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:05:10.433514+0800  Level 20: 数据滞后已恢复正常: AUCTIONUSDT.BINANCE
2025-06-09T14:05:10.433710+0800  Level 20: 检测到数据滞后: AIUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:05:10.433863+0800  Level 20: 将发送滞后告警: AIUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:05:10.434060+0800  Level 20: 检测到数据滞后: BSWUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:05:10.434221+0800  Level 20: BSWUSDT.BINANCE 滞后告警冷却中，还需等待 239 秒 (冷却时间: 360秒)
2025-06-09T14:05:10.434368+0800  Level 20: 数据滞后已恢复正常: LAYERUSDT.BINANCE
2025-06-09T14:05:10.434521+0800  Level 20: 数据滞后已恢复正常: IPUSDT.BINANCE
2025-06-09T14:05:10.434671+0800  Level 20: 已发送合并告警邮件，包含 0 个缺失合约和 7 个滞后合约
2025-06-09T14:06:10.494051+0800  Level 20: 开始检查数据完整性
2025-06-09T14:06:10.494418+0800  Level 20: 数据滞后已恢复正常: ARKUSDT.BINANCE
2025-06-09T14:06:10.494609+0800  Level 20: 检测到数据滞后: GALAUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:06:10.494774+0800  Level 20: 将发送滞后告警: GALAUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:06:10.494923+0800  Level 20: 检测到数据滞后: RSRUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:06:10.495065+0800  Level 20: RSRUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T14:06:10.495205+0800  Level 20: 数据滞后已恢复正常: 1000RATSUSDT.BINANCE
2025-06-09T14:06:10.495345+0800  Level 20: 检测到数据滞后: 1000SATSUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:06:10.495483+0800  Level 20: 将发送滞后告警: 1000SATSUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:06:10.496302+0800  Level 20: 数据滞后已恢复正常: TOKENUSDT.BINANCE
2025-06-09T14:06:10.496976+0800  Level 20: 数据滞后已恢复正常: XVGUSDT.BINANCE
2025-06-09T14:06:10.497151+0800  Level 20: 检测到数据滞后: SAGAUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:06:10.497287+0800  Level 20: 将发送滞后告警: SAGAUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:06:10.497458+0800  Level 20: 检测到数据滞后: ZECUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:06:10.497588+0800  Level 20: 将发送滞后告警: ZECUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:06:10.497730+0800  Level 20: 数据滞后已恢复正常: UXLINKUSDT.BINANCE
2025-06-09T14:06:10.497859+0800  Level 20: 检测到数据滞后: ACHUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:06:10.497993+0800  Level 20: 将发送滞后告警: ACHUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:06:10.498138+0800  Level 20: 数据滞后已恢复正常: DYMUSDT.BINANCE
2025-06-09T14:06:10.498414+0800  Level 20: 检测到数据滞后: BEAMXUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:06:10.498616+0800  Level 20: BEAMXUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T14:06:10.498798+0800  Level 20: 检测到数据滞后: LDOUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:06:10.498954+0800  Level 20: 将发送滞后告警: LDOUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:06:10.499121+0800  Level 20: 数据滞后已恢复正常: AIUSDT.BINANCE
2025-06-09T14:06:10.499465+0800  Level 20: 检测到数据滞后: BSWUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:06:10.499646+0800  Level 20: BSWUSDT.BINANCE 滞后告警冷却中，还需等待 179 秒 (冷却时间: 360秒)
2025-06-09T14:06:10.499825+0800  Level 20: 检测到数据滞后: COOKIEUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:06:10.499985+0800  Level 20: 将发送滞后告警: COOKIEUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:06:10.500143+0800  Level 20: 检测到数据滞后: LAYERUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:06:10.500332+0800  Level 20: LAYERUSDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 180秒)
2025-06-09T14:06:10.500511+0800  Level 20: 已发送合并告警邮件，包含 0 个缺失合约和 7 个滞后合约
2025-06-09T14:07:10.543558+0800  Level 20: 开始检查数据完整性
2025-06-09T14:07:10.543900+0800  Level 20: 数据滞后已恢复正常: GALAUSDT.BINANCE
2025-06-09T14:07:10.544064+0800  Level 20: 检测到数据滞后: RSRUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:07:10.544224+0800  Level 20: RSRUSDT.BINANCE 滞后告警冷却中，还需等待 239 秒 (冷却时间: 360秒)
2025-06-09T14:07:10.544368+0800  Level 20: 检测到数据滞后: 1000RATSUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:07:10.544498+0800  Level 20: 1000RATSUSDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 180秒)
2025-06-09T14:07:10.544623+0800  Level 20: 数据滞后已恢复正常: 1000SATSUSDT.BINANCE
2025-06-09T14:07:10.544746+0800  Level 20: 数据滞后已恢复正常: SAGAUSDT.BINANCE
2025-06-09T14:07:10.544879+0800  Level 20: 检测到数据滞后: ZECUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:07:10.545008+0800  Level 20: ZECUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T14:07:10.545137+0800  Level 20: 检测到数据滞后: UXLINKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:07:10.545262+0800  Level 20: 将发送滞后告警: UXLINKUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:07:10.545387+0800  Level 20: 检测到数据滞后: ACHUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:07:10.545510+0800  Level 20: ACHUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T14:07:10.545632+0800  Level 20: 检测到数据滞后: BEAMXUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:07:10.545917+0800  Level 20: BEAMXUSDT.BINANCE 滞后告警冷却中，还需等待 239 秒 (冷却时间: 360秒)
2025-06-09T14:07:10.546072+0800  Level 20: 数据滞后已恢复正常: LDOUSDT.BINANCE
2025-06-09T14:07:10.546199+0800  Level 20: 检测到数据滞后: BSWUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:07:10.546323+0800  Level 20: BSWUSDT.BINANCE 滞后告警冷却中，还需等待 119 秒 (冷却时间: 360秒)
2025-06-09T14:07:10.546444+0800  Level 20: 检测到数据滞后: ZEREBROUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:07:10.546565+0800  Level 20: 将发送滞后告警: ZEREBROUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:07:10.546685+0800  Level 20: 检测到数据滞后: GOATUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:07:10.546821+0800  Level 20: 将发送滞后告警: GOATUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:07:10.546947+0800  Level 20: 数据滞后已恢复正常: COOKIEUSDT.BINANCE
2025-06-09T14:07:10.547070+0800  Level 20: 检测到数据滞后: LAYERUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:07:10.547189+0800  Level 20: 将发送滞后告警: LAYERUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:07:10.547308+0800  Level 20: 检测到数据滞后: IPUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:07:10.547426+0800  Level 20: 将发送滞后告警: IPUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:07:10.547559+0800  Level 20: 已发送合并告警邮件，包含 0 个缺失合约和 5 个滞后合约
2025-06-09T14:08:10.593192+0800  Level 20: 开始检查数据完整性
2025-06-09T14:08:10.593563+0800  Level 20: 检测到数据滞后: ARKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:08:10.593760+0800  Level 20: 将发送滞后告警: ARKUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:08:10.593949+0800  Level 20: 检测到数据滞后: RSRUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:08:10.594444+0800  Level 20: RSRUSDT.BINANCE 滞后告警冷却中，还需等待 179 秒 (冷却时间: 360秒)
2025-06-09T14:08:10.595089+0800  Level 20: 检测到数据滞后: API3USDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:08:10.595294+0800  Level 20: 将发送滞后告警: API3USDT.BINANCE, 连续告警次数: 1
2025-06-09T14:08:10.595467+0800  Level 20: 检测到数据滞后: 1000RATSUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:08:10.595657+0800  Level 20: 将发送滞后告警: 1000RATSUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:08:10.595813+0800  Level 20: 检测到数据滞后: TOKENUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:08:10.595978+0800  Level 20: 将发送滞后告警: TOKENUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:08:10.596123+0800  Level 20: 检测到数据滞后: ZECUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:08:10.596277+0800  Level 20: ZECUSDT.BINANCE 滞后告警冷却中，还需等待 239 秒 (冷却时间: 360秒)
2025-06-09T14:08:10.596431+0800  Level 20: 检测到数据滞后: UXLINKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:08:10.596588+0800  Level 20: UXLINKUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T14:08:10.596737+0800  Level 20: 检测到数据滞后: ACHUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:08:10.596857+0800  Level 20: ACHUSDT.BINANCE 滞后告警冷却中，还需等待 239 秒 (冷却时间: 360秒)
2025-06-09T14:08:10.597000+0800  Level 20: 检测到数据滞后: DYMUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:08:10.597148+0800  Level 20: 将发送滞后告警: DYMUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:08:10.597387+0800  Level 20: 数据滞后已恢复正常: BEAMXUSDT.BINANCE
2025-06-09T14:08:10.597546+0800  Level 20: 检测到数据滞后: BSWUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:08:10.597704+0800  Level 20: BSWUSDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 360秒)
2025-06-09T14:08:10.597855+0800  Level 20: 数据滞后已恢复正常: ZEREBROUSDT.BINANCE
2025-06-09T14:08:10.598041+0800  Level 20: 数据滞后已恢复正常: GOATUSDT.BINANCE
2025-06-09T14:08:10.598199+0800  Level 20: 数据滞后已恢复正常: LAYERUSDT.BINANCE
2025-06-09T14:08:10.598327+0800  Level 20: 数据滞后已恢复正常: IPUSDT.BINANCE
2025-06-09T14:08:10.598448+0800  Level 20: 已发送合并告警邮件，包含 0 个缺失合约和 5 个滞后合约
2025-06-09T14:09:10.643261+0800  Level 20: 开始检查数据完整性
2025-06-09T14:09:10.643627+0800  Level 20: 数据滞后已恢复正常: ARKUSDT.BINANCE
2025-06-09T14:09:10.643817+0800  Level 20: 检测到数据滞后: GALAUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:09:10.643992+0800  Level 20: 将发送滞后告警: GALAUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:09:10.644167+0800  Level 20: 数据滞后已恢复正常: RSRUSDT.BINANCE
2025-06-09T14:09:10.644326+0800  Level 20: 检测到数据滞后: ONEUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:09:10.644491+0800  Level 20: 将发送滞后告警: ONEUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:09:10.644652+0800  Level 20: 数据滞后已恢复正常: API3USDT.BINANCE
2025-06-09T14:09:10.644989+0800  Level 20: 检测到数据滞后: 1000RATSUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:09:10.645742+0800  Level 20: 1000RATSUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T14:09:10.645920+0800  Level 20: 检测到数据滞后: 1000SATSUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:09:10.646114+0800  Level 20: 将发送滞后告警: 1000SATSUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:09:10.646268+0800  Level 20: 检测到数据滞后: TOKENUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:09:10.646429+0800  Level 20: TOKENUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T14:09:10.646598+0800  Level 20: 检测到数据滞后: SAGAUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:09:10.646756+0800  Level 20: 将发送滞后告警: SAGAUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:09:10.646897+0800  Level 20: 数据滞后已恢复正常: ZECUSDT.BINANCE
2025-06-09T14:09:10.647052+0800  Level 20: 检测到数据滞后: IOTAUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:09:10.647211+0800  Level 20: 将发送滞后告警: IOTAUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:09:10.647375+0800  Level 20: 数据滞后已恢复正常: UXLINKUSDT.BINANCE
2025-06-09T14:09:10.647500+0800  Level 20: 检测到数据滞后: LQTYUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:09:10.648169+0800  Level 20: 将发送滞后告警: LQTYUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:09:10.648346+0800  Level 20: 数据滞后已恢复正常: ACHUSDT.BINANCE
2025-06-09T14:09:10.648496+0800  Level 20: 数据滞后已恢复正常: DYMUSDT.BINANCE
2025-06-09T14:09:10.648637+0800  Level 20: 检测到数据滞后: HIGHUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:09:10.648811+0800  Level 20: 将发送滞后告警: HIGHUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:09:10.648942+0800  Level 20: 数据滞后已恢复正常: BSWUSDT.BINANCE
2025-06-09T14:09:10.649091+0800  Level 20: 检测到数据滞后: ICXUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:09:10.649291+0800  Level 20: 将发送滞后告警: ICXUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:09:10.649470+0800  Level 20: 已发送合并告警邮件，包含 0 个缺失合约和 8 个滞后合约
2025-06-09T14:10:10.694668+0800  Level 20: 开始检查数据完整性
2025-06-09T14:10:10.695045+0800  Level 20: 数据滞后已恢复正常: GALAUSDT.BINANCE
2025-06-09T14:10:10.695279+0800  Level 20: 数据滞后已恢复正常: ONEUSDT.BINANCE
2025-06-09T14:10:10.695465+0800  Level 20: 检测到数据滞后: PENDLEUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:10:10.695646+0800  Level 20: 将发送滞后告警: PENDLEUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:10:10.696437+0800  Level 20: 检测到数据滞后: TRUUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:10:10.696561+0800  Level 20: 将发送滞后告警: TRUUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:10:10.696652+0800  Level 20: 检测到数据滞后: ZENUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:10:10.696736+0800  Level 20: 将发送滞后告警: ZENUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:10:10.696817+0800  Level 20: 检测到数据滞后: 1000RATSUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:10:10.696896+0800  Level 20: 1000RATSUSDT.BINANCE 滞后告警冷却中，还需等待 239 秒 (冷却时间: 360秒)
2025-06-09T14:10:10.696974+0800  Level 20: 数据滞后已恢复正常: 1000SATSUSDT.BINANCE
2025-06-09T14:10:10.697051+0800  Level 20: 检测到数据滞后: ALGOUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:10:10.697126+0800  Level 20: 将发送滞后告警: ALGOUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:10:10.697217+0800  Level 20: 检测到数据滞后: TOKENUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:10:10.697294+0800  Level 20: TOKENUSDT.BINANCE 滞后告警冷却中，还需等待 239 秒 (冷却时间: 360秒)
2025-06-09T14:10:10.697368+0800  Level 20: 数据滞后已恢复正常: SAGAUSDT.BINANCE
2025-06-09T14:10:10.697441+0800  Level 20: 检测到数据滞后: ZECUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:10:10.697512+0800  Level 20: 将发送滞后告警: ZECUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:10:10.697584+0800  Level 20: 数据滞后已恢复正常: IOTAUSDT.BINANCE
2025-06-09T14:10:10.697655+0800  Level 20: 检测到数据滞后: UXLINKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:10:10.697727+0800  Level 20: 将发送滞后告警: UXLINKUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:10:10.697797+0800  Level 20: 数据滞后已恢复正常: LQTYUSDT.BINANCE
2025-06-09T14:10:10.697869+0800  Level 20: 检测到数据滞后: SPELLUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:10:10.697939+0800  Level 20: 将发送滞后告警: SPELLUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:10:10.698096+0800  Level 20: 数据滞后已恢复正常: HIGHUSDT.BINANCE
2025-06-09T14:10:10.698258+0800  Level 20: 数据滞后已恢复正常: ICXUSDT.BINANCE
2025-06-09T14:10:10.698414+0800  Level 20: 检测到数据滞后: LAYERUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:10:10.699020+0800  Level 20: 将发送滞后告警: LAYERUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:10:10.699223+0800  Level 20: 已发送合并告警邮件，包含 0 个缺失合约和 8 个滞后合约
2025-06-09T14:11:10.762909+0800  Level 20: 开始检查数据完整性
2025-06-09T14:11:10.763306+0800  Level 20: 检测到数据滞后: CKBUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:11:10.763511+0800  Level 20: 将发送滞后告警: CKBUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:11:10.763651+0800  Level 20: 检测到数据滞后: ENSUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:11:10.763781+0800  Level 20: 将发送滞后告警: ENSUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:11:10.763910+0800  Level 20: 数据滞后已恢复正常: PENDLEUSDT.BINANCE
2025-06-09T14:11:10.764037+0800  Level 20: 数据滞后已恢复正常: TRUUSDT.BINANCE
2025-06-09T14:11:10.764167+0800  Level 20: 数据滞后已恢复正常: ZENUSDT.BINANCE
2025-06-09T14:11:10.764824+0800  Level 20: 检测到数据滞后: 1000RATSUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:11:10.765062+0800  Level 20: 1000RATSUSDT.BINANCE 滞后告警冷却中，还需等待 179 秒 (冷却时间: 360秒)
2025-06-09T14:11:10.765233+0800  Level 20: 数据滞后已恢复正常: ALGOUSDT.BINANCE
2025-06-09T14:11:10.765406+0800  Level 20: 检测到数据滞后: TOKENUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:11:10.765560+0800  Level 20: TOKENUSDT.BINANCE 滞后告警冷却中，还需等待 179 秒 (冷却时间: 360秒)
2025-06-09T14:11:10.765717+0800  Level 20: 检测到数据滞后: ZECUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:11:10.765876+0800  Level 20: ZECUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T14:11:10.766074+0800  Level 20: 检测到数据滞后: UXLINKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:11:10.766203+0800  Level 20: UXLINKUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T14:11:10.766366+0800  Level 20: 检测到数据滞后: SPELLUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:11:10.766527+0800  Level 20: SPELLUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T14:11:10.766679+0800  Level 20: 检测到数据滞后: BEAMXUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:11:10.766852+0800  Level 20: 将发送滞后告警: BEAMXUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:11:10.767155+0800  Level 20: 检测到数据滞后: LDOUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:11:10.767355+0800  Level 20: 将发送滞后告警: LDOUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:11:10.767559+0800  Level 20: 检测到数据滞后: COOKIEUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:11:10.767716+0800  Level 20: 将发送滞后告警: COOKIEUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:11:10.767855+0800  Level 20: 检测到数据滞后: LAYERUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:11:10.767996+0800  Level 20: LAYERUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T14:11:10.768157+0800  Level 20: 已发送合并告警邮件，包含 0 个缺失合约和 5 个滞后合约
2025-06-09T14:12:10.840790+0800  Level 20: 开始检查数据完整性
2025-06-09T14:12:10.841135+0800  Level 20: 数据滞后已恢复正常: CKBUSDT.BINANCE
2025-06-09T14:12:10.841306+0800  Level 20: 数据滞后已恢复正常: ENSUSDT.BINANCE
2025-06-09T14:12:10.841451+0800  Level 20: 检测到数据滞后: MEWUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:12:10.841585+0800  Level 20: 将发送滞后告警: MEWUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:12:10.841712+0800  Level 20: 检测到数据滞后: 1000RATSUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:12:10.842559+0800  Level 20: 1000RATSUSDT.BINANCE 滞后告警冷却中，还需等待 119 秒 (冷却时间: 360秒)
2025-06-09T14:12:10.842756+0800  Level 20: 检测到数据滞后: ALGOUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:12:10.842917+0800  Level 20: ALGOUSDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 180秒)
2025-06-09T14:12:10.843088+0800  Level 20: 检测到数据滞后: NEIROETHUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:12:10.843250+0800  Level 20: 将发送滞后告警: NEIROETHUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:12:10.843425+0800  Level 20: 数据滞后已恢复正常: TOKENUSDT.BINANCE
2025-06-09T14:12:10.850265+0800  Level 20: 检测到数据滞后: ZECUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:12:10.850774+0800  Level 20: ZECUSDT.BINANCE 滞后告警冷却中，还需等待 239 秒 (冷却时间: 360秒)
2025-06-09T14:12:10.850962+0800  Level 20: 检测到数据滞后: UXLINKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:12:10.851128+0800  Level 20: UXLINKUSDT.BINANCE 滞后告警冷却中，还需等待 239 秒 (冷却时间: 360秒)
2025-06-09T14:12:10.851277+0800  Level 20: 检测到数据滞后: SPELLUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:12:10.851448+0800  Level 20: SPELLUSDT.BINANCE 滞后告警冷却中，还需等待 239 秒 (冷却时间: 360秒)
2025-06-09T14:12:10.851609+0800  Level 20: 数据滞后已恢复正常: BEAMXUSDT.BINANCE
2025-06-09T14:12:10.851757+0800  Level 20: 数据滞后已恢复正常: LDOUSDT.BINANCE
2025-06-09T14:12:10.851903+0800  Level 20: 检测到数据滞后: BSWUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:12:10.852050+0800  Level 20: 将发送滞后告警: BSWUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:12:10.852191+0800  Level 20: 检测到数据滞后: AVAAIUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:12:10.852344+0800  Level 20: 将发送滞后告警: AVAAIUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:12:10.852508+0800  Level 20: 数据滞后已恢复正常: COOKIEUSDT.BINANCE
2025-06-09T14:12:10.852659+0800  Level 20: 检测到数据滞后: LAYERUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:12:10.852811+0800  Level 20: LAYERUSDT.BINANCE 滞后告警冷却中，还需等待 239 秒 (冷却时间: 360秒)
2025-06-09T14:12:10.852978+0800  Level 20: 已发送合并告警邮件，包含 0 个缺失合约和 4 个滞后合约
2025-06-09T14:13:10.889918+0800  Level 20: 开始检查数据完整性
2025-06-09T14:13:10.890193+0800  Level 20: 检测到数据滞后: RSRUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:13:10.890446+0800  Level 20: 将发送滞后告警: RSRUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:13:10.890630+0800  Level 20: 检测到数据滞后: MEWUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:13:10.890805+0800  Level 20: MEWUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T14:13:10.890980+0800  Level 20: 检测到数据滞后: 1000RATSUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:13:10.891149+0800  Level 20: 1000RATSUSDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 360秒)
2025-06-09T14:13:10.891317+0800  Level 20: 检测到数据滞后: ALGOUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:13:10.891475+0800  Level 20: 将发送滞后告警: ALGOUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:13:10.891646+0800  Level 20: 数据滞后已恢复正常: NEIROETHUSDT.BINANCE
2025-06-09T14:13:10.892294+0800  Level 20: 检测到数据滞后: TOKENUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:13:10.892476+0800  Level 20: 将发送滞后告警: TOKENUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:13:10.892631+0800  Level 20: 检测到数据滞后: XVGUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:13:10.892823+0800  Level 20: 将发送滞后告警: XVGUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:13:10.892970+0800  Level 20: 数据滞后已恢复正常: ZECUSDT.BINANCE
2025-06-09T14:13:10.893126+0800  Level 20: 检测到数据滞后: UXLINKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:13:10.893269+0800  Level 20: UXLINKUSDT.BINANCE 滞后告警冷却中，还需等待 179 秒 (冷却时间: 360秒)
2025-06-09T14:13:10.893411+0800  Level 20: 检测到数据滞后: ACHUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:13:10.893585+0800  Level 20: 将发送滞后告警: ACHUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:13:10.893754+0800  Level 20: 检测到数据滞后: SPELLUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:13:10.893928+0800  Level 20: SPELLUSDT.BINANCE 滞后告警冷却中，还需等待 179 秒 (冷却时间: 360秒)
2025-06-09T14:13:10.894096+0800  Level 20: 数据滞后已恢复正常: BSWUSDT.BINANCE
2025-06-09T14:13:10.894242+0800  Level 20: 数据滞后已恢复正常: AVAAIUSDT.BINANCE
2025-06-09T14:13:10.894392+0800  Level 20: 数据滞后已恢复正常: LAYERUSDT.BINANCE
2025-06-09T14:13:10.894544+0800  Level 20: 检测到数据滞后: IPUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:13:10.894789+0800  Level 20: 将发送滞后告警: IPUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:13:10.894945+0800  Level 20: 已发送合并告警邮件，包含 0 个缺失合约和 6 个滞后合约
2025-06-09T14:14:10.940006+0800  Level 20: 开始检查数据完整性
2025-06-09T14:14:10.941917+0800  Level 20: 检测到数据滞后: ARKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:14:10.942121+0800  Level 20: 将发送滞后告警: ARKUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:14:10.942285+0800  Level 20: 数据滞后已恢复正常: RSRUSDT.BINANCE
2025-06-09T14:14:10.942447+0800  Level 20: 检测到数据滞后: PENDLEUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:14:10.942612+0800  Level 20: 将发送滞后告警: PENDLEUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:14:10.942783+0800  Level 20: 数据滞后已恢复正常: MEWUSDT.BINANCE
2025-06-09T14:14:10.942930+0800  Level 20: 数据滞后已恢复正常: 1000RATSUSDT.BINANCE
2025-06-09T14:14:10.943074+0800  Level 20: 数据滞后已恢复正常: ALGOUSDT.BINANCE
2025-06-09T14:14:10.943222+0800  Level 20: 检测到数据滞后: NEIROETHUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:14:10.943365+0800  Level 20: NEIROETHUSDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 180秒)
2025-06-09T14:14:10.943521+0800  Level 20: 检测到数据滞后: TOKENUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:14:10.943693+0800  Level 20: TOKENUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T14:14:10.944316+0800  Level 20: 检测到数据滞后: XVGUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:14:10.944491+0800  Level 20: XVGUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T14:14:10.944638+0800  Level 20: 检测到数据滞后: ZECUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:14:10.944813+0800  Level 20: 将发送滞后告警: ZECUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:14:10.945338+0800  Level 20: 检测到数据滞后: UXLINKUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:14:10.945510+0800  Level 20: UXLINKUSDT.BINANCE 滞后告警冷却中，还需等待 119 秒 (冷却时间: 360秒)
2025-06-09T14:14:10.945673+0800  Level 20: 数据滞后已恢复正常: ACHUSDT.BINANCE
2025-06-09T14:14:10.945830+0800  Level 20: 数据滞后已恢复正常: SPELLUSDT.BINANCE
2025-06-09T14:14:10.945992+0800  Level 20: 检测到数据滞后: DYMUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:14:10.946146+0800  Level 20: 将发送滞后告警: DYMUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:14:10.946285+0800  Level 20: 检测到数据滞后: BSWUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:14:10.946432+0800  Level 20: BSWUSDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 180秒)
2025-06-09T14:14:10.946583+0800  Level 20: 检测到数据滞后: IPUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:14:10.946745+0800  Level 20: IPUSDT.BINANCE 滞后告警冷却中，还需等待 299 秒 (冷却时间: 360秒)
2025-06-09T14:14:10.946903+0800  Level 20: 检测到数据滞后: ETHFIUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:14:10.947054+0800  Level 20: 将发送滞后告警: ETHFIUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:14:10.947195+0800  Level 20: 已发送合并告警邮件，包含 0 个缺失合约和 5 个滞后合约
2025-06-09T14:15:11.006531+0800  Level 20: 开始检查数据完整性
2025-06-09T14:15:11.006906+0800  Level 20: 数据滞后已恢复正常: ARKUSDT.BINANCE
2025-06-09T14:15:11.007098+0800  Level 20: 检测到数据滞后: CKBUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:15:11.007271+0800  Level 20: 将发送滞后告警: CKBUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:15:11.007425+0800  Level 20: 检测到数据滞后: JASMYUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:15:11.007587+0800  Level 20: 将发送滞后告警: JASMYUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:15:11.007748+0800  Level 20: 检测到数据滞后: RSRUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:15:11.007925+0800  Level 20: RSRUSDT.BINANCE 滞后告警冷却中，还需等待 59 秒 (冷却时间: 180秒)
2025-06-09T14:15:11.008258+0800  Level 20: 数据滞后已恢复正常: PENDLEUSDT.BINANCE
2025-06-09T14:15:11.008874+0800  Level 20: 检测到数据滞后: AGLDUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:15:11.009052+0800  Level 20: 将发送滞后告警: AGLDUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:15:11.009227+0800  Level 20: 检测到数据滞后: 1000SATSUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:15:11.009392+0800  Level 20: 将发送滞后告警: 1000SATSUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:15:11.009555+0800  Level 20: 数据滞后已恢复正常: NEIROETHUSDT.BINANCE
2025-06-09T14:15:11.009715+0800  Level 20: 数据滞后已恢复正常: TOKENUSDT.BINANCE
2025-06-09T14:15:11.009886+0800  Level 20: 数据滞后已恢复正常: XVGUSDT.BINANCE
2025-06-09T14:15:11.010059+0800  Level 20: 检测到数据滞后: SAGAUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:15:11.010212+0800  Level 20: 将发送滞后告警: SAGAUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:15:11.010364+0800  Level 20: 数据滞后已恢复正常: ZECUSDT.BINANCE
2025-06-09T14:15:11.010515+0800  Level 20: 检测到数据滞后: IOTAUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:15:11.010672+0800  Level 20: 将发送滞后告警: IOTAUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:15:11.010833+0800  Level 20: 数据滞后已恢复正常: UXLINKUSDT.BINANCE
2025-06-09T14:15:11.011046+0800  Level 20: 检测到数据滞后: MYROUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:15:11.011242+0800  Level 20: 将发送滞后告警: MYROUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:15:11.011410+0800  Level 20: 数据滞后已恢复正常: DYMUSDT.BINANCE
2025-06-09T14:15:11.012065+0800  Level 20: 检测到数据滞后: BSWUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:15:11.012252+0800  Level 20: 将发送滞后告警: BSWUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:15:11.012423+0800  Level 20: 检测到数据滞后: DEGENUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:15:11.012578+0800  Level 20: 将发送滞后告警: DEGENUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:15:11.012741+0800  Level 20: 检测到数据滞后: COOKIEUSDT.BINANCE, 滞后时间: 3 分钟
2025-06-09T14:15:11.012906+0800  Level 20: 将发送滞后告警: COOKIEUSDT.BINANCE, 连续告警次数: 1
2025-06-09T14:15:11.013045+0800  Level 20: 数据滞后已恢复正常: IPUSDT.BINANCE
2025-06-09T14:15:11.013134+0800  Level 20: 数据滞后已恢复正常: ETHFIUSDT.BINANCE
2025-06-09T14:15:11.013212+0800  Level 20: 已发送合并告警邮件，包含 0 个缺失合约和 10 个滞后合约
2025-06-09T14:15:18.145008+0800  Level 20: 收到信号: SIGTERM，准备关闭应用...
2025-06-09T14:15:18.268285+0800  Level 20: 数据监控器正在关闭...
2025-06-09T14:17:58.558454+0800  Level 20: 成功加载 140 条分钟线汇总数据
2025-06-09T14:17:58.558914+0800  Level 20: 数据监控器已启动
2025-06-09T14:18:58.599194+0800  Level 20: 开始检查数据完整性
2025-06-09T14:19:58.663032+0800  Level 20: 开始检查数据完整性
