# # 获取指定数据服务实例
from vnpy_binance.binance_spot_gateway import INTERVAL_VT2BINANCE, TIMEDELTA_MAP, UTC_TZ# , generate_datetime
from vnpy_evo.trader.setting import SETTINGS
from vnpy_evo.trader.database import get_database, DB_TZ
from vnpy_binance import BinanceSpotGateway
from datetime import datetime, timedelta
from time import sleep
from vnpy_evo.trader.constant import (Interval)
from vnpy_evo.trader.object import (BarData)
from vnpy_rest import RestClient, Response
from pathlib import Path
from vnpy.usertools.db_status_manager import Status
database = get_database()

# 设置合约品种
symbols = {
    "BINANCE": ['BTC','ETH','XRP','SOL','DOGE','LTC','BOME','ADA','WIF']
}
spot = [i.lower()+'usdt' for i in symbols['BINANCE'] if i not in ['DAI']]
swap = [f'{i}USDT' for i in symbols['BINANCE'] if i not in ['SHIB', 'DAI', 'FDUSD', 'WBTC']]

#symbols['BINANCE'] = spot + swap
symbols['BINANCE'] = swap

# 设置下载时间段
# time_str = (datetime.now()-timedelta(days=1))
start_date = datetime(2024, 10, 30).astimezone(DB_TZ)
#end_date = datetime(2025, 3, 18).astimezone(DB_TZ)
#start_date = (datetime.now()-timedelta(days=1)).astimezone(DB_TZ)
#end_date = (datetime.now()+timedelta(days=2)).astimezone(DB_TZ)
end_date = (datetime.now()+timedelta(days=1)).astimezone(DB_TZ)

INTERVAL = Interval.MINUTE
from datetime import datetime
from vnpy_evo.trader.constant import Exchange
from vnpy_evo.trader.object import HistoryRequest

class BinanceSpotRestAPi(RestClient):
    gateway_name = BinanceSpotGateway.default_name

    def query_history(self, req: HistoryRequest) -> list[BarData]:
        if req.symbol.islower():
            path = "/api/v3/klines"
        elif req.symbol.isupper():
            path = "/fapi/v1/klines"
        else:
            raise ValueError(f"Unknown type: {req.symbol}")

        """Query kline history data"""
        history: list[BarData] = []
        limit: int = 1000
        start_time: int = int(datetime.timestamp(req.start))

        while True:
            # Create query parameters
            usdt_symbol = req.symbol.replace('-SPOT', 'USDT').replace('-SWAP', 'USDT').upper()
            params: dict = {"symbol": usdt_symbol, "interval": INTERVAL_VT2BINANCE[req.interval], "limit": limit,
                            "startTime": start_time * 1000}

            if req.end:
                end_time: int = int(datetime.timestamp(req.end))
                params["endTime"] = end_time * 1000  # Convert to milliseconds

            resp: Response = self.request("GET", path, params=params)

            # Break the loop if request failed
            if resp.status_code // 100 != 2:
                msg: str = f"Query kline history failed, status code: {resp.status_code}, message: {resp.text}, symbol: {req.symbol}, usdt_symbol: {usdt_symbol}"
                # self.gateway.write_log(msg)
                print(msg)
                break
            else:
                data: dict = resp.json()
                if not data:
                    msg: str = f"No kline history data is received, start time: {start_time}, symbol: {req.symbol}"
                    # self.gateway.write_log(msg)
                    print(msg)
                    break

                buf: list[BarData] = []

                for row in data:
                    bar: BarData = BarData(symbol=req.symbol, exchange=req.exchange, datetime=generate_datetime(row[0]),
                                           interval=req.interval, volume=float(row[5]), turnover=float(row[7]),
                                           open_price=float(row[1]), high_price=float(row[2]), low_price=float(row[3]),
                                           close_price=float(row[4]), gateway_name=self.gateway_name)
                    buf.append(bar)

                history.extend(buf)

                begin: datetime = buf[0].datetime
                end: datetime = buf[-1].datetime
                msg: str = f"Query kline history finished, {req.symbol} - {req.interval.value}, {begin} - {end}\n\n"
                # self.gateway.write_log(msg)
                print(msg)

                if len(data) < limit:
                    break

                # Update query start time
                start_dt = bar.datetime + TIMEDELTA_MAP[req.interval]
                start_time = int(datetime.timestamp(start_dt))

            # Wait to meet request flow limit
            sleep(0.3)

        # Remove the unclosed kline
        if history:
            history.pop(-1)
        return history

def generate_datetime(timestamp: float) -> datetime:
    """Generate datetime object from Binance timestamp"""
    dt: datetime = datetime.fromtimestamp(timestamp / 1000, tz=UTC_TZ)
    return dt

# 批量下载
def query_save_data(req):
    print(f"debug query_save_data {req}")
    try:
        api = BinanceSpotRestAPi()
        api.init("https://www.binance.com")
        api.start()
        data = api.query_history(req)
        print(f"length --> {len(data)}")
        database.save_bar_data(data)
        print(f"{req.symbol}历史数据下载完成")
        api.stop()
    except Exception as e:
        print(f"下载{req.symbol}时发生错误 {e}")

# 程序长时间运行会database会失效，这里增加一个query,每20分钟执行一次
def keep_alive():
    try:
        overview_data = database.get_bar_overview()
    except Exception as e:
        print(e)

def download_data(contents=None, symbol_type="88"):
    if contents is None:
        return
    overview_data = database.get_bar_overview()
    overview_dict = {(d.symbol, d.exchange.value, d.interval): d for d in overview_data}
    
    symbols_to_download = []
    for exchange, symbols_list in contents.items():
        for symbol in symbols_list:
            # 直接添加到下载列表，不做存在性判断
            symbols_to_download.append((symbol + symbol_type, exchange))
    
    if len(symbols_to_download) > 0:
        print(f'===symbols_to_download:{symbols_to_download}')
        # 串行下载每个symbol
        for symbol, exchange in symbols_to_download:
            try:
                # Check if we need more historical data
                req = HistoryRequest(
                    symbol=symbol,
                    exchange=Exchange(exchange),
                    start=start_date.astimezone(DB_TZ),
                    interval=INTERVAL,
                    end=end_date.astimezone(DB_TZ),
                )
                query_save_data(req)
            except Exception as e:
                print(f"Error downloading symbol {symbol}: {e}")

# 增量更新
def update_data():
    end = datetime.now().astimezone(DB_TZ)
    data = database.get_bar_overview()
    for d in data:
        symbol = d.symbol
        exchange = d.exchange
        start = d.end.replace(tzinfo=DB_TZ)
        req = HistoryRequest(symbol=symbol, exchange=exchange, start=start, interval=INTERVAL, end=end, )
        query_save_data(req=req)

def download_main(contents=None):
    print(f"call download main {contents}")
    download_data(contents=contents, symbol_type="")
    
if __name__ == '__main__':
    try:
        import time
        print_date = time.strftime("%Y-%m-%d %H:%M:%S")
        print(f"{print_date}: {__file__}")
        # 每日更新
        from collections import defaultdict
        contents = defaultdict(set)
        contents["BINANCE"].add("1000FLOKIUSDT")
        contents['BINANCE'].add('BTCUSDT')
        contents['BINANCE'].add('ETHUSDT')
        contents['BINANCE'].add('XRPUSDT')
        contents['BINANCE'].add('XLMUSDT')
        contents['BINANCE'].add('ADAUSDT')
        contents['BINANCE'].add('ALGOUSDT')
        contents['BINANCE'].add('DOGEUSDT')
        contents['BINANCE'].add('TRBUSDT')
        contents['BINANCE'].add('SUSHIUSDT')
        contents['BINANCE'].add('SOLUSDT')
        contents['BINANCE'].add('AAVEUSDT')
        contents['BINANCE'].add('RSRUSDT')
        contents['BINANCE'].add('BELUSDT')
        contents['BINANCE'].add('ZENUSDT')
        contents['BINANCE'].add('HBARUSDT')
        contents['BINANCE'].add('ARUSDT')
        contents['BINANCE'].add('LPTUSDT')
        contents['BINANCE'].add('ENSUSDT')
        contents['BINANCE'].add('PEOPLEUSDT')
        contents['BINANCE'].add('API3USDT')
        contents['BINANCE'].add('APEUSDT')
        contents['BINANCE'].add('JASMYUSDT')
        contents['BINANCE'].add('SPELLUSDT')
        contents['BINANCE'].add('LDOUSDT')
        contents['BINANCE'].add('FETUSDT')
        contents['BINANCE'].add('HIGHUSDT')
        contents['BINANCE'].add('PHBUSDT')
        contents['BINANCE'].add('ACHUSDT')
        contents['BINANCE'].add('SSVUSDT')
        contents['BINANCE'].add('CKBUSDT')
        contents['BINANCE'].add('TRUUSDT')
        contents['BINANCE'].add('LQTYUSDT')
        contents['BINANCE'].add('SUIUSDT')
        contents['BINANCE'].add('1000PEPEUSDT')
        contents['BINANCE'].add('XVGUSDT')
        contents['BINANCE'].add('WLDUSDT')
        contents['BINANCE'].add('ARKMUSDT')
        contents['BINANCE'].add('MEMEUSDT')
        contents['BINANCE'].add('BEAMXUSDT')
        contents['BINANCE'].add('1000BONKUSDT')
        contents['BINANCE'].add('1000RATSUSDT')
        contents['BINANCE'].add('AUCTIONUSDT')
        contents['BINANCE'].add('AIUSDT')
        contents['BINANCE'].add('WIFUSDT')
        contents['BINANCE'].add('DYMUSDT')
        contents['BINANCE'].add('OMUSDT')
        contents['BINANCE'].add('PORTALUSDT')
        contents['BINANCE'].add('VANRYUSDT')
        contents['BINANCE'].add('ETHFIUSDT')
        contents['BINANCE'].add('POPCATUSDT')
        contents['BINANCE'].add('BSWUSDT')
        contents['BINANCE'].add('NEIROETHUSDT')
        contents['BINANCE'].add('NEIROUSDT')
        contents['BINANCE'].add('GOATUSDT')
        contents['BINANCE'].add('MOODENGUSDT')
        contents['BINANCE'].add('DEGENUSDT')
        contents['BINANCE'].add('CHILLGUYUSDT')
        contents['BINANCE'].add('VIRTUALUSDT')
        contents['BINANCE'].add('PENGUUSDT')
        contents['BINANCE'].add('AIXBTUSDT')
        contents['BINANCE'].add('FARTCOINUSDT')
        contents['BINANCE'].add('COOKIEUSDT')
        contents['BINANCE'].add('AVAAIUSDT')
        contents['BINANCE'].add('LAYERUSDT')
        contents['BINANCE'].add('IPUSDT')
        contents['BINANCE'].add('KAITOUSDT')
        download_main(contents)
    except Exception as e:
        print(e)
