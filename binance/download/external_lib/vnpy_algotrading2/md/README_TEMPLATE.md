# 算法交易模板设计说明

## 初始化配置

```python
目的：建立算法实例的基础数据结构和初始状态

# 基础属性
_count = 0                 # 算法实例计数
display_name = ""         # 显示用名称
default_setting = {}      # 默认参数配置
variables = []           # 变量名称列表

def __init__:
    # 交易相关属性
    算法引擎 = algo_engine          # 用于发单等操作
    算法名称 = algo_name           # 实例唯一标识
    交易合约 = vt_symbol          # 合约代码
    交易方向 = direction          # 买卖方向
    开平方向 = offset            # 开平标志
    目标价格 = price             # 委托价格
    目标数量 = volume            # 委托数量
    任务编号 = todo_id          # 关联的任务ID

    # 状态跟踪
    算法状态 = AlgoStatusEnum.PAUSED   # 初始为暂停状态
    成交数量 = 0                      # 已成交数量
    成交均价 = 0                      # 成交均价
    
    # 黑洞订单管理（记录由于长时间无回报而被取消的订单）
    黑洞订单记录 = {}  # vt_orderid: order
    黑洞成交量 = 0    # 黑洞订单涉及的数量
    
    # 告警管理
    告警缓存 = {}     # 用于存储最近告警{标题: 时间戳}
    告警超时 = 300    # 缓存超时时间(秒)
    
    # 活动订单跟踪
    活动订单记录 = {}  # vt_orderid: order
```

## 事件驱动主线

```python
目的：通过各类事件回调维护算法的状态和逻辑

# TICK行情处理
def update_tick(tick数据):
    if 算法处于运行状态:
        执行on_tick算法逻辑(tick数据)

# K线数据处理
def update_bar(k线数据):
    if 算法处于运行状态:
        执行on_bar算法逻辑(k线数据)

# 定时器处理
def update_timer:
    if 算法处于运行状态:
        执行on_timer算法逻辑()

# 订单状态更新
def update_order(订单数据):
    # 维护活动订单状态
    if 订单仍然活跃:
        添加到活动订单字典
    else:
        从活动订单字典移除
    
    执行on_order回调函数

# 成交回报处理
def update_trade(成交数据):
    # 判断成交方向是否需要反向计算
    if (做多方向且成交为卖出) or (做空方向且成交为买入):
        成交量变化 = -成交数量
    else:
        成交量变化 = 成交数量
    
    # 更新成交均价
    if 首次成交:
        成交均价 = 当前成交价
    else:
        原成本 = 原均价 * 原总量
        新成本 = 当前价 * 成交量变化
        最新总量 = 原总量 + 成交量变化
        if 最新总量不为0:
            成交均价 = (原成本 + 新成本) / abs(最新总量)
    
    # 更新成交量
    总成交量 += 成交量变化
    
    执行on_trade回调函数
```

## 状态管理机制

```python
目的：控制算法的运行状态转换

# 启动算法
def start:
    状态 = 运行中
    推送状态事件
    记录日志

# 停止算法
def stop:
    状态 = 已停止
    撤销所有活动委托
    推送状态事件
    记录日志

# 完成算法
def finish:
    状态 = 已完成
    撤销所有活动委托
    推送状态事件
    记录日志

# 暂停算法
def pause:
    状态 = 已暂停
    推送状态事件
    记录日志

# 恢复算法
def resume:
    状态 = 运行中
    推送状态事件
    记录日志
```

## 交易委托机制

```python
目的：提供标准化的委托下单接口

# 买入委托
def buy(价格, 数量, 委托类型=限价单, 开平=自动):
    if 算法未运行:
        返回
        
    通过引擎发送买入委托(
        算法实例=self,
        方向=LONG,
        价格=价格,
        数量=数量,
        类型=委托类型,
        开平=开平方向
    )

# 卖出委托
def sell(价格, 数量, 委托类型=限价单, 开平=自动):
    if 算法未运行:
        返回
        
    通过引擎发送卖出委托(
        算法实例=self,
        方向=SHORT,
        价格=价格,
        数量=数量,
        类型=委托类型,
        开平=开平方向
    )

# 撤销委托
def cancel_order(vt_orderid):
    通过引擎撤销指定委托

# 全部撤单
def cancel_all:
    if 有活动委托:
        for 每个委托号:
            撤销该委托
```

## 告警机制

```python
目的：实现告警消息的去重和多级发送

def send_alert(标题="", 消息="", 需要电话=False, 需要数据库=False):
    当前时间 = datetime.now()
    
    # 检查告警去重
    if 标题在告警缓存中:
        上次告警时间 = 获取缓存时间
        if (当前时间 - 上次时间).total_seconds() < 告警超时:
            返回
    
    # 更新告警缓存
    告警缓存[标题] = 当前时间
    
    # 通过引擎发送告警
    引擎.send_alert(
        算法实例=self,
        标题=标题,
        消息=消息,
        需要电话=需要电话
    )
    
    # 数据库记录
    if 不需要数据库记录:
        创建告警记录 = OrderErrorData(
            合约=交易合约,
            交易所=获取合约信息().exchange,
            错误码=0,
            错误信息=f"{标题}: {消息}",
            订单号="",
            任务号=str(任务编号),
            创建时间=当前时间,
            网关名=APP_NAME
        )
        推送告警事件(EVENT_ORDER_ERROR_RECORD)
```

## 数据流转图

```mermaid
flowchart TB
    A[事件驱动] --> B[行情数据] & C[定时事件] & D[回报处理]
    
    B --> B1[Tick] & B2[K线]
    B1 --> B3[更新行情] --> B4[算法策略]
    B2 --> B3
    
    C --> C1[定时检查] --> C2[算法策略]
    
    D --> D1[订单回报] & D2[成交回报]
    D1 --> D3[活动订单维护] --> D4[算法策略]
    D2 --> D5[成交量统计] & D6[均价计算] --> D4
    
    B4 & C2 & D4 --> E[交易信号]
    E --> F[委托管理]
    
    F --> G[委托发送] & H[撤单管理]
    H --> H1[单笔撤单] & H2[全部撤单]
    
    I[告警系统] --> I1[消息缓存] --> I2[告警去重]
    I2 --> I3[电话告警] & I4[数据库记录]
    
    J[状态管理] --> J1[启动] & J2[停止] & J3[暂停] & J4[恢复]
    J2 & J3 --> H2
