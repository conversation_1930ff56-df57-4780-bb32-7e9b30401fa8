# 算法交易引擎更新日志

## 算法交易系统增强版 (2025-05-15)

### 订单管理机制革新
1. **黑洞订单管理**
   - 增强撤单超时机制，2.5秒未收到撤单回报自动加入黑洞订单集合，发送邮件、电话、数据库告警
   - 添加黑洞订单集合（black_hole_orders）专门管理丢失订单
   - 将black_hole_volume改为property属性，实时计算黑洞成交量
   - 抽取add_to_black_hole方法，统一处理黑洞订单逻辑
   - 完善订单量计算，考虑黑洞订单对可用量的影响

2. **进程安全退出机制**
   - 支持SIGTERM(kill -15/kill)和SIGINT(Ctrl+C)信号处理
   - 收到信号时自动撤销所有活跃订单
   - 在Windows平台忽略SIGTSTP(Ctrl+Z)信号，避免进程被挂起
   - 提供gateway.cancel_all()接口用于主动撤销所有挂单
   - 支持按交易对撤单(cancel_symbol_orders)，方便上层应用调用

### 订单发送逻辑重构
1. **简化发单策略**
   - 移除复杂的反向发单和超量发单机制，避免算法内部逻辑导致持仓方向错误
   - 统一订单方向判断：始终按算法初始化时设定的方向发单
   - 订单量自动合并：当剩余量的金额小于2倍min_notional时，合并到当前订单
   - 智能处理小额订单：当订单金额小于min_notional时，offset设置为"平"，5次拒单则停止

2. **成交量计算优化**
   - 改进get_total_pending方法，准确计算所有类型订单的待成交量
   - 优化get_remaining_volume逻辑，正确处理剩余可用量

### 错误处理与告警系统
1. **差异化错误处理**
   - 实现针对不同错误码的专门处理策略：
     - -1008错误（系统过载）：允许无限重试，发送数据库告警
     - -2027错误（开仓额超限）：最多5次，发送邮件、电话、数据库告警
     - -2022错误（平仓单被拒绝）：最多5次，发送数据库告警
     - -4164错误（订单金额不足）：最多5次，发送数据库告警
   - 错误计数器自动恢复：任何订单成交时重置错误计数器

2. **告警系统升级**
   - 添加告警频率控制
      - 电话告警：所有算法单1小时内不重复发送
      - 电话邮件、数据库告警去重：同一笔算法单5分钟内不重复发送
   - 添加tick超时检查：启动后或中途5秒未收到tick停止算法,发送邮件、数据库告警

3. **任务调度友好设计**
   - 算法结束状态明确区分：正常完成(11)、部分完成(12)和异常(5)
   - 错误达到最大次数后自动停止，便于上层系统调度下一用户
   - 撤单超时和tick超时机制确保算法不会无限阻塞

### 应用场景
这些增强功能显著提高了算法交易系统在复杂市场环境下的稳定性和安全性。系统能够更加智能地处理各种异常情况，减少人工干预，尤其适用于高频交易和大额订单场景。通过精确的告警机制和友好的调度设计，上层系统可以更高效地管理多用户任务执行。进程安全退出机制确保在系统关闭时能够妥善处理所有未完成的订单，防止资金损失。

## 新增多用户分组订单分发引擎 (2025-04-21)

### 功能概述
新增订单分发引擎(DispatchEngine)，实现了一个高效的多用户分组任务分发系统，用于将父任务分发为子任务并按照用户分组规则控制执行流程。该引擎作为vnpy标准引擎集成，通过事件驱动机制运行，支持测试模式和生产模式。

### 核心功能

1. **用户分组管理**
   - 实现`UserGroupManager`类，根据用户remarks字段解析分组信息
   - 支持按组号和组内序号管理用户，实现组内顺序执行任务
   - 提供完整的用户组查询API，如获取组内用户、获取下一用户等

2. **订单分发控制**
   - 实现同一组内任务顺序执行，上一任务完成后才发送下一任务
   - 支持多组并行执行不同ref的任务，提高整体处理效率
   - 通过Redis作为任务分发媒介，实现与执行层的解耦

3. **任务状态管理**
   - 完整的任务状态跟踪：创建(10)、分发(20)、完成(11)、异常(5)
   - 超时检测机制，自动识别长时间未完成的任务
   - 定期统计和打印各组待处理任务情况

4. **测试模式支持**
   - 自动初始化测试用户数据
   - 自动生成测试订单，支持多组多用户测试场景
   - 模拟订单完成流程，便于系统功能验证

### 技术实现

1. **架构设计**
   - 采用vnpy标准引擎架构，继承BaseEngine
   - 使用事件驱动模式，通过EVENT_TIMER触发定时任务
   - 集成vnpy日志系统，提供详细的操作日志

2. **配置管理**
   - 通过vnpy的SETTINGS系统管理Redis连接配置
   - 支持从vt_setting.json读取配置参数
   - 提供合理的默认值，确保系统稳定运行

3. **数据存储**
   - 使用MySQL数据库存储任务状态和用户信息
   - 使用Redis作为任务分发通道，实现高效的任务传递
   - 完整的数据模型设计，支持复杂的任务关联关系

### 应用场景
该引擎主要用于多用户算法交易系统，可以确保同一组内的用户按照预定顺序执行相同ref的任务，同时允许不同组并行处理任务，提高整体系统效率。特别适合需要控制执行顺序的金融交易场景。

## 新增多用户分组订单分发引擎 (2025-04-21)

### 功能概述
新增订单分发引擎(DispatchEngine)，实现了一个高效的多用户分组任务分发系统，用于将父任务分发为子任务并按照用户分组规则控制执行流程。该引擎作为vnpy标准引擎集成，通过事件驱动机制运行，支持测试模式和生产模式。

### 核心功能

1. **用户分组管理**
   - 实现`UserGroupManager`类，根据用户remarks字段解析分组信息
   - 支持按组号和组内序号管理用户，实现组内顺序执行任务
   - 提供完整的用户组查询API，如获取组内用户、获取下一用户等

2. **订单分发控制**
   - 实现同一组内任务顺序执行，上一任务完成后才发送下一任务
   - 支持多组并行执行不同ref的任务，提高整体处理效率
   - 通过Redis作为任务分发媒介，实现与执行层的解耦

3. **任务状态管理**
   - 完整的任务状态跟踪：创建(10)、分发(20)、完成(11)、异常(5)
   - 超时检测机制，自动识别长时间未完成的任务
   - 定期统计和打印各组待处理任务情况

4. **测试模式支持**
   - 自动初始化测试用户数据
   - 自动生成测试订单，支持多组多用户测试场景
   - 模拟订单完成流程，便于系统功能验证

### 技术实现

1. **架构设计**
   - 采用vnpy标准引擎架构，继承BaseEngine
   - 使用事件驱动模式，通过EVENT_TIMER触发定时任务
   - 集成vnpy日志系统，提供详细的操作日志

2. **配置管理**
   - 通过vnpy的SETTINGS系统管理Redis连接配置
   - 支持从vt_setting.json读取配置参数
   - 提供合理的默认值，确保系统稳定运行

3. **数据存储**
   - 使用MySQL数据库存储任务状态和用户信息
   - 使用Redis作为任务分发通道，实现高效的任务传递
   - 完整的数据模型设计，支持复杂的任务关联关系

### 应用场景
该引擎主要用于多用户算法交易系统，可以确保同一组内的用户按照预定顺序执行相同ref的任务，同时允许不同组并行处理任务，提高整体系统效率。特别适合需要控制执行顺序的金融交易场景。

## 新增VolumeFollowSyncAlgo算法
1. 订单管理机制升级
   - 支持多个订单并发执行和跟踪
   - 引入pending_orders字典替代pending_orderids集合，支持更完整的订单信息追踪
   - 新增order_time_map记录订单发出时间
   - 优化order_cancel_time处理撤单超时逻辑

2. 订单完成检查机制
   - 新增check_all_finished方法，全面检查算法执行完成条件
   - 综合检查活跃订单、未收到回报订单、撤单超时和预期交易量
   - 在on_trade、on_order和on_timer中统一使用完成检查逻辑

3. 订单超时和价格管理
   - 新增max_order_wait参数控制订单最大等待时间
   - 增强check_and_cancel_orders逻辑，统一处理活跃订单和pending订单
   - 优化价格合理性检查，支持实时撤销不合理价格的订单

4. 成交量计算优化
   - 改进get_total_pending方法，考虑订单方向计算待成交量
   - 优化get_remaining_volume逻辑，正确处理正负方向的订单量