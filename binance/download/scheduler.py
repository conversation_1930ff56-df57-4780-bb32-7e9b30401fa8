#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
独立的PreLaqi定时任务调度器
直接操作数据库，不依赖Flask
"""

import os
import sys
import time
import signal
import threading
import logging
from datetime import datetime, timedelta
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入配置和模型
from config import Config
from models import LaqiOrder, PreLaqi, UserEquityHistory

# 创建数据库引擎和会话
engine = create_engine(Config.SQLALCHEMY_DATABASE_URI, echo=False)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 全局触发队列：保存已触发但未处理的订单
triggered_queue = []

# 全局用户权益查询时间记录：保存每个用户的上次查询时间
user_last_query_time = {}

def setup_logging():
    """设置日志"""
    if not os.path.exists('logs'):
        os.makedirs('logs')
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S',
        handlers=[
            logging.FileHandler('logs/scheduler.log', encoding='utf-8'),
            logging.StreamHandler()  # 同时输出到控制台
        ]
    )
    return logging.getLogger('scheduler')

def send_wechat_notification(message):
    """发送企业微信通知（从app.py复制）"""
    try:
        import requests
        # webhook_url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b54436b7-2384-4b54-863f-caebc34309a4"# TEST 
        webhook_url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=df6c438a-94cb-4533-adf8-8f56f3aa16e4"     
        
        data = {
            "msgtype": "text",
            "text": {
                "content": message
            }
        }
        
        response = requests.post(webhook_url, json=data, timeout=10)
        if response.status_code == 200:
            result = response.json()
            if result.get('errcode') == 0:
                print("Wechat notification sent successfully")
                return True
            else:
                print(f"Wechat notification failed: {result}")
                return False
        else:
            print(f"HTTP request failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"Error occurred when sending wechat notification: {str(e)}")
        return False

def process_pre_laqi_orders(logger):
    """处理PreLaqi订单的定时任务"""
    global triggered_queue, user_last_query_time
    session = SessionLocal()
    try:
        current_time = datetime.now()
        
        # 1. 清理队列中无效的订单（已删除、状态改变的，但不检查过期）
        valid_queue = []
        for order_id, trigger_time, trigger_value in triggered_queue:
            order = session.query(PreLaqi).filter(
                PreLaqi.id == order_id,
                PreLaqi.status == 0,  # 仍在监控状态
                PreLaqi.del_flag == 0
            ).first()
            
            if order:
                # 已在队列的订单（不检查过期时间）
                valid_queue.append((order_id, trigger_time, trigger_value))
            else:
                logger.info(f"QUEUE_REMOVE - PreLaqi#{order_id} removed from queue (deleted or status changed)")
        
        triggered_queue = valid_queue
        
        # 2. 检查新的触发条件，将新触发的订单加入队列
        active_orders = session.query(PreLaqi).filter(
            PreLaqi.status == 0,
            PreLaqi.del_flag == 0
        ).all()
        
        # 从active_orders中获取活跃用户列表，清理user_last_query_time
        active_users = {order.user for order in active_orders}
        user_last_query_time = {user: time for user, time in user_last_query_time.items() if user in active_users}
        
        for order in active_orders:
            try:
                # 检查是否过期
                if current_time > order.expiry_date:
                    order.status = 2  # Expired
                    
                    # 记录过期状态变更日志
                    type_text = "Higher than" if order.type == 0 else "Lower than"
                    logger.info(f"STATUS_CHANGE - PreLaqi#{order.id} - Status:Monitoring->Expired - User:{order.user}, Ratio:{order.ratio}, Type:{type_text}, TriggerValue:{order.trigger_value}, ExpiredAt:{order.expiry_date.strftime('%Y-%m-%d %H:%M:%S')}")
                    continue
                
                # 检查是否已在队列中
                if any(order_id == order.id for order_id, _, _ in triggered_queue):
                    continue  # 已在队列中，跳过
                
                # 获取用户最新的权益值
                # 首先获取用户最新的权益记录以确定时间范围
                latest_equity_record = session.query(UserEquityHistory).filter(
                    UserEquityHistory.username == order.user
                ).order_by(UserEquityHistory.equity_time.desc()).first()
                
                if not latest_equity_record:
                    print(f"No equity data found for user {order.user}")
                    continue
                
                latest_time = latest_equity_record.equity_time
                
                # 检查是否是首次查询该用户
                if order.user not in user_last_query_time:
                    # 初始化：使用最新权益值
                    current_value = latest_equity_record.equity_value
                    user_last_query_time[order.user] = latest_time
                else:
                    # 不是首次查询：根据订单类型在时间段内取最大或最小值
                    last_query_time = user_last_query_time[order.user]
                    
                    if order.type == 0:  # Higher than：取最大值
                        equity_in_range = session.query(UserEquityHistory.equity_value).filter(
                            UserEquityHistory.username == order.user,
                            UserEquityHistory.equity_time > last_query_time,
                        ).order_by(UserEquityHistory.equity_value.desc()).first()
                    else:  # Lower than：取最小值
                        equity_in_range = session.query(UserEquityHistory.equity_value).filter(
                            UserEquityHistory.username == order.user,
                            UserEquityHistory.equity_time > last_query_time,
                        ).order_by(UserEquityHistory.equity_value.asc()).first()
                    
                    if equity_in_range:
                        current_value = equity_in_range[0]
                    else:
                        # 如果时间段内没有新数据，使用最新值
                        current_value = latest_equity_record.equity_value
                    
                    # 更新查询时间
                    user_last_query_time[order.user] = latest_time

                # 检查触发条件
                triggered = False
                if order.type == 0:  # Higher than
                    if current_value > order.trigger_value:
                        triggered = True
                elif order.type == 1:  # Lower than
                    if current_value < order.trigger_value:
                        triggered = True
                
                if triggered:
                    # 加入触发队列
                    triggered_queue.append((order.id, current_time, current_value))
                    condition_op = ">" if order.type == 0 else "<"
                    logger.info(f"QUEUE_ADD - PreLaqi#{order.id} added to queue - User:{order.user}, Condition:{order.user}_balance({current_value}){condition_op}{order.trigger_value}")
                    print(f"PreLaqi Order {order.id} triggered and added to queue! User: {order.user}, condition: {current_value} {condition_op} {order.trigger_value}")
            
            except Exception as e:
                print(f"Error processing PreLaqi order {order.id}: {str(e)}")
                continue
        
        # 3. 处理触发队列
        if triggered_queue:
            # 检查laqi表中是否有未完成的订单 (status = 0, 1, 3) 且在12小时内创建的
            twelve_hours_ago = current_time - timedelta(hours=12)
            pending_laqi = session.query(LaqiOrder).filter(
                LaqiOrder.status.in_([0, 1, 3]),
                LaqiOrder.create_date >= twelve_hours_ago
            ).first()
            
            if pending_laqi:
                # 有未完成订单，发送通知，不创建新订单
                queue_orders = []
                for order_id, _, _ in triggered_queue:
                    order = session.query(PreLaqi).filter(PreLaqi.id == order_id).first()
                    if order:
                        queue_orders.append(order)
                
                waiting_users = [order.user for order in queue_orders]
                message = f"有排队拉齐队列\n" \
                         f"正在拉齐中: {pending_laqi.user}\n" \
                         f"排队拉齐队列: {', '.join(waiting_users)}\n" \
                         f"将在5秒后刷新"
                
                send_wechat_notification(message)
                logger.info(f"QUEUE_WAITING - Pending Laqi#{pending_laqi.id} ({pending_laqi.user}), Queue: {waiting_users}")
                print(f"Laqi queue busy, pending order: Laqi#{pending_laqi.id} ({pending_laqi.user}), queue: {waiting_users}")
            else:
                ## 判断是否需要发送结束报告(要求里没有说明，暂时不使用，防止信息过多)
                # if len(triggered_queue) == 1:
                #     need_end_report = True
                # else:
                #     need_end_report = False

                # 没有未完成订单，按优先级排序并创建一个新订单
                def get_priority(queue_item):
                    order_id, trigger_time, _ = queue_item
                    order = session.query(PreLaqi).filter(PreLaqi.id == order_id).first()
                    
                    user = order.user
                    if user == 'zhmain':
                        return (0, trigger_time)  # 最高优先级
                    elif user == 'Chen':
                        return (1, trigger_time)  # 次优先级
                    else:
                        return (2, trigger_time)  # 按触发时间排序
                
                # 按优先级排序
                triggered_queue.sort(key=get_priority)
                
                # 处理第一个（优先级最高的）
                selected_order_id, trigger_time, trigger_value = triggered_queue[0]
                selected_order = session.query(PreLaqi).filter(PreLaqi.id == selected_order_id).first()
                
                if selected_order:
                    # 创建新的LaqiOrder
                    new_laqi_order = LaqiOrder(
                        user=selected_order.user,
                        ratio=selected_order.ratio,
                        create_date=current_time,
                        update_date=current_time,
                        status=0,  # 默认状态
                        remarks=f"pre_laqi"
                    )
                    session.add(new_laqi_order)
                    session.flush()  # 获取新LaqiOrder的ID
                    
                    # 更新PreLaqi订单状态为已触发
                    selected_order.status = 1  # Triggered
                    selected_order.update_date = current_time
                    
                    # 从队列中移除已处理的订单
                    triggered_queue.pop(0)
                    
                
                    # 记录触发和创建日志
                    condition_op = ">" if selected_order.type == 0 else "<"
                    
                    logger.info(f"AUTO_CREATE_LAQI_ORDER - LaqiOrder#{new_laqi_order.id} - TriggeredBy:PreLaqi#{selected_order.id} - User:{new_laqi_order.user}, Ratio:{new_laqi_order.ratio}")
                    logger.info(f"STATUS_CHANGE - PreLaqi#{selected_order.id} - Status:Monitoring->Triggered - User:{selected_order.user}, Condition:{selected_order.user}_balance({trigger_value}){condition_op}{selected_order.trigger_value}")
                    logger.info(f"QUEUE_PROCESS - Created Laqi#{new_laqi_order.id} for {selected_order.user}, Removed from queue")
                    
                    print(f"PreLaqi Order {selected_order.id} processed! Created Laqi Order #{new_laqi_order.id} for user {selected_order.user} with ratio {selected_order.ratio}")
                    
                    # 如果还有其他等待的订单，记录日志
                    if triggered_queue:
                        remaining_orders = []
                        for order_id, _, _ in triggered_queue:
                            order = session.query(PreLaqi).filter(PreLaqi.id == order_id).first()
                            if order:
                                remaining_orders.append(order.user)
                        logger.info(f"QUEUE_REMAINING - Queue size: {len(triggered_queue)}, Users: {remaining_orders}")
                        print(f"Remaining in queue: {remaining_orders}")
                    # else:
                    #     if need_end_report:
                    #         message = f"正在拉齐中: {selected_order.user}\n" \
                    #             f"无排队拉齐队列"
                    #         send_wechat_notification(message)
        
        # 提交所有更改
        session.commit()
    except Exception as e:
        print(f"Error in process_pre_laqi_orders: {str(e)}")
        session.rollback()
    finally:
        session.close()

class PreLaqiScheduler:
    """PreLaqi定时任务调度器"""
    
    def __init__(self, logger, interval=5):
        self.logger = logger
        self.interval = interval  # 间隔秒数
        self.running = False
        self.timer = None
    
    def start(self):
        """启动调度器"""
        if self.running:
            self.logger.warning("Scheduler is already running")
            return
        
        self.running = True
        self.logger.info(f"PreLaqi scheduler started - interval:{self.interval} seconds")
        self._schedule_next()
    
    def stop(self):
        """停止调度器"""
        self.running = False
        if self.timer:
            self.timer.cancel()
        self.logger.info("PreLaqi scheduler stopped")
    
    def _schedule_next(self):
        """安排下次执行"""
        if not self.running:
            return
        
        def run_task():
            try:
                process_pre_laqi_orders(self.logger)
            except Exception as e:
                self.logger.error(f"Error occurred: {str(e)}")
            finally:
                # 继续下次调度
                self._schedule_next()
        
        self.timer = threading.Timer(self.interval, run_task)
        self.timer.daemon = True
        self.timer.start()

def main():
    logger = setup_logging()
    
    logger.info("PreLaqi_scheduler starts running")
    
    # 创建调度器
    scheduler = PreLaqiScheduler(logger, interval=5)
    
    # 定义信号处理函数
    def signal_handler(signum, frame):
        logger.info(f"Received signal {signum}, stopping scheduler...")
        scheduler.stop()
        logger.info("Scheduler stopped safely")
        sys.exit(0)
    
    # 注册信号处理
    signal.signal(signal.SIGTERM, signal_handler)  # systemctl stop
    signal.signal(signal.SIGINT, signal_handler)   # Ctrl+C
    
    try:
        scheduler.start()
        
        # 保持主线程运行
        while scheduler.running:
            time.sleep(1)
            
    except Exception as e:
        logger.error(f"Error occurred: {str(e)}")
        scheduler.stop()
        sys.exit(1)

if __name__ == '__main__':
    main() 