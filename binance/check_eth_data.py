import requests
from datetime import datetime, timedelta

import urllib.request


def get_system_proxy():
    """获取系统代理设置"""
    proxy_handler = urllib.request.ProxyHandler()
    proxies = {}

    # 从系统获取代理设置
    for protocol in ['http', 'https']:
        if proxy := proxy_handler.proxies.get(protocol):
            proxies[protocol] = proxy

    return proxies

# Define the start and end dates
start_date = datetime(2019, 12, 31)
end_date = datetime(2024, 12, 29)

# Function to check data availability
def check_data_availability(start_date, end_date):
    current_date = start_date
    missing_dates = []

    while current_date <= end_date:
        url = f"https://data.binance.vision/data/futures/um/daily/klines/ETHUSDT/1d/ETHUSDT-1d-{current_date.strftime('%Y-%m-%d')}.zip"
        response = requests.head(url, proxies=get_system_proxy())  # Use HEAD to check the status without downloading
        if response.status_code != 200:
            missing_dates.append(current_date.strftime('%Y-%m-%d'))
            print(f'{current_date} missing')
        current_date += timedelta(days=1)

    return missing_dates

# Check for missing data
missing_data = check_data_availability(start_date, end_date)

# Output the results
if missing_data:
    print("Missing data for the following dates:")
    for date in missing_data:
        print(date)
else:
    print("No missing data found.")