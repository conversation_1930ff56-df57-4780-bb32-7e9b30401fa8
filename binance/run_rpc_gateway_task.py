from time import sleep
import sys
import signal

from vnpy.event import EventEngine
from vnpy.trader.engine import MainEngine
from vnpy.trader.utility import load_json, save_json

from vnpy_rpcservice.rpc_gateway.rpc_gateway_task import RpcTaskSubGateway

class RpcTaskSubApp:
    """RPC任务订阅应用"""
    setting_filename: str = "rpc_task_sub_setting.json"
    
    def __init__(self, username: str = "user1"):
        """构造函数"""
        self.event_engine = EventEngine()
        self.main_engine = MainEngine(self.event_engine)
        self.username = username
        self.gateway_name = "RPC_TASK"
        
        self.load_setting()
        self.register_event()
        self.init_engines()
        
        self._active = True  # 添加运行状态标记
        
    def load_setting(self) -> None:
        """加载配置"""
        self.setting = load_json(self.setting_filename)
        if not self.setting:
            self.setting = {
                "主动请求地址": "tcp://127.0.0.1:20014",
                "推送订阅地址": "tcp://127.0.0.1:41002",
                "简单模式": "否"
            }
            save_json(self.setting_filename, self.setting)
            
        # 添加用户名到设置中
        self.setting["用户名"] = self.username
        
    def register_event(self) -> None:
        """注册事件监听"""
        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        # 在Windows平台上signal模块可能不完全支持
        if sys.platform != 'win32':
            # 忽略SIGTSTP信号(Ctrl+Z)，避免进程被挂起
            signal.signal(signal.SIGTSTP, signal.SIG_IGN)
            
    def _signal_handler(self, signum, frame):
        """信号处理函数"""
        sig_name = signal.Signals(signum).name
        self.main_engine.write_log(f"收到信号: {sig_name}，准备关闭应用...")
        self._active = False
        
    def init_engines(self) -> None:
        """初始化网关"""
        # 添加RPC任务订阅网关
        self.gateway = self.main_engine.add_gateway(RpcTaskSubGateway)
        
    def run(self) -> None:
        """运行应用"""
        self.main_engine.write_log(f"RPC任务订阅应用[{self.username}]启动")
        
        # 连接RPC网关
        self.gateway.connect(self.setting)
        self.main_engine.write_log(
            f"RPC任务订阅应用[{self.username}]已启动，"
            f"简单模式：{'开启' if self.gateway.simple_mode else '关闭'}"
        )
        
        self.main_engine.write_log(f"开始接收{self.username}的任务")
        
        while self._active:
            sleep(1)
            
        self.close()

    def close(self) -> None:
        """关闭应用"""
        self.main_engine.write_log("应用正在关闭...")
        self.gateway.close()
        self.main_engine.close()

def main() -> None:
    """主函数"""
    # 从命令行参数获取用户名
    username = sys.argv[1] if len(sys.argv) > 1 else "user1"
    app = RpcTaskSubApp(username)
    app.run()

if __name__ == "__main__":
    main()
