# 核查步骤：

### 0. 更新config.json，check_database是本地数据库参数，storage_database是冷备库参数

### 1. api数据下载：249行设定start参数，运行kline_download.py，全量基准数据入本地库apibardata表

### 2. 实盘数据放入实盘数据文件夹中，在1min数据入库.py中更新第27行文件路径后运行，待核查数据入本地库dbbardata表

### 3. 1min数据核查.py 18，19行输入待核查数据起始时间为参数，运行，看结果

### 4. 结果若不一致，和负责人沟通处理方法，负责人端处理完后再次发送文件至核查人端，核查人应删除之前dbbardata中的数据，或表直接删除，重复2、3、4步骤，直至数量、质量都一致

### 5. 1min结果都一致后，修改 入冷备库 文件夹中的 已有数据替换api数据.py 中的24，25行时间参数，并运行，将dbbardata数据覆盖新下载的api数据

### 6. 修改 入冷备库.py 中的24，25行时间参数，并运行，将apibardata中的规定时间的全量数据导入冷备库；若数据量太大，可以分段入库。

frp自动化尚未实现，需要手动执行命令 frpc.exe -c frpcc.toml

文件接收方式和时间还没有确定，尚未实现自动化，还需手动传参

建议这个过程中在本地数据库建的表可以用一次删一次，比较直观，不容易乱

