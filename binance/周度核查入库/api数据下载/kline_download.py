#%%
from peewee import *
from vnpy_mysql.mysql_database import DB_TZ, db
from datetime import datetime
from typing import List, Dict, Union, Optional
import pandas as pd
from tqdm import tqdm
import warnings
warnings.filterwarnings("ignore")
import time
import requests
import pytz
from tzlocal import get_localzone_name
from threading import Thread

import os
import json

path = os.path.dirname(__file__)
parent_path = os.path.dirname(path)
with open(os.path.join(parent_path,'config.json'), 'r',encoding='utf-8') as file:
    config = json.load(file)
vt_setting_file_path = os.path.join(parent_path,'.vntrader', 'vt_setting.json')
with open(vt_setting_file_path, 'r', encoding='utf-8') as vt_setting_file:
    vt_setting_data = json.load(vt_setting_file)
vt_setting_data['database.database'] = config['check_database_name']
vt_setting_data['database.host'] = config['check_host']
vt_setting_data['database.port'] = config['check_port']
vt_setting_data['database.user'] = config['check_user']
vt_setting_data['database.password'] = config['check_password']
with open(vt_setting_file_path, 'w', encoding='utf-8') as vt_setting_file:
    json.dump(vt_setting_data, vt_setting_file, indent=4)

class ApiBarData(Model):
    symbol = CharField()
    exchange = CharField()
    interval = CharField()
    datetime = DateTimeField()
    volume = DoubleField()
    turnover = DoubleField()
    open_interest = DoubleField()
    open_price = DoubleField()
    high_price = DoubleField()
    low_price = DoubleField()
    close_price = DoubleField()

    class Meta:
        database = db
        indexes = (
            (('symbol', 'exchange', 'interval', 'datetime'), True),
        )

class ApiBarOverview(Model):
    symbol = CharField()
    exchange = CharField()
    interval = CharField()
    count = IntegerField()
    start = DateTimeField()
    end = DateTimeField()

    class Meta:
        database = db
        indexes = (
            (('symbol', 'exchange', 'interval'), True),
        )


db.create_tables([ApiBarData, ApiBarOverview], safe=True)


DOWNLOAD_INTERVALS: List[Dict[str, Union[int, str]]] = [
    {"minutes": 1, "rule": "1m"},
    # {"minutes": 3, "rule": "3m"},
    # {"minutes": 5, "rule": "5m"},
    # {"minutes": 15, "rule": "15m"},
    # {"minutes": 30, "rule": "30m"},
    # {"minutes": 60, "rule": "1h"},  # 1小时及以上改用H
    # {"minutes": 120, "rule": "2h"},
    # {"minutes": 240, "rule": "4h"},
    # {"minutes": 360, "rule": "6h"},
    # {"minutes": 480, "rule": "8h"},
    # {"minutes": 720, "rule": "12h"},
    # {"minutes": 1440, "rule": "1d"},
    # {"minutes": 4320, "rule": "3d"},
]
#%%


pd.set_option('expand_frame_repr', False)
BINANCE_SPOT_LIMIT = 1000
BINANCE_FUTURE_LIMIT = 1500
LOCAL_TZ = pytz.timezone(get_localzone_name())


def generate_datetime(timestamp: float) -> datetime:
    """
    :param timestamp:
    :return:
    """
    dt = datetime.fromtimestamp(timestamp / 1000)
    dt = LOCAL_TZ.localize(dt)
    return dt


def get_binance_data(symbol: str, tradetype: str, start: str, end: str):
    """
    crawl binance exchange data
    :param symbol: BTCUSDT.
    :param exchange: spot, future_continuous, inverse_future.
    :param start_time: format :2020-1-1 or 2020-01-01 year-month-day
    :param end_time: format: 2020-1-1 or 2020-01-01 year-month-day
    :param gate_way the gateway name for binance is:BINANCE_SPOT, BINANCE_USDT, BINANCE_INVERSE
    :return:
    """

    for interval_config in DOWNLOAD_INTERVALS:
        minutes = interval_config["minutes"]
        interval_index = interval_config["rule"]
        interval = f"{minutes}m"

        if minutes >= 1440:
            interval = f"{minutes // 1440}d"

        api_url = ''
        save_symbol = symbol
        exchange = 'BINANCE'
        if tradetype == 'spot':
            # print("spot")
            limit = BINANCE_SPOT_LIMIT
            save_symbol = symbol.lower()
            gateway = 'BINANCE_SPOT'
            api_url = f'https://api.binance.com/api/v3/klines?symbol={symbol}&interval={interval_index}&limit={limit}'

        elif tradetype == 'future_continuous':
            # print('future_continuous')
            limit = BINANCE_FUTURE_LIMIT
            api_url = f'https://fapi.binance.com/fapi/v1/continuousKlines?pair={symbol}&contractType=PERPETUAL&interval={interval_index}&limit={limit}'

        elif tradetype == 'future_klines':
            # print("future_klines")
            limit = BINANCE_FUTURE_LIMIT
            api_url = f'https://fapi.binance.com/fapi/v1/klines?symbol={symbol}&interval={interval_index}&limit={limit}'

        else:
            raise Exception('the exchange name should be one of ：spot, future_continuous, future_klines')

        while True:
            try:

                start_time = int(datetime.timestamp(start)* 1000)
                end_time = int(datetime.timestamp(end)* 1000)

                latest_agg_bar: Optional[ApiBarData] = (ApiBarData
                    .select()
                    .where(
                        (ApiBarData.symbol == symbol) &
                        (ApiBarData.exchange == exchange) &
                        (ApiBarData.interval == interval)
                    )
                    .order_by(ApiBarData.datetime.desc())
                    .first())

                if latest_agg_bar:
                    start_time: datetime = latest_agg_bar.datetime.replace(tzinfo=DB_TZ)
                    start_time = int(datetime.timestamp(start_time)* 1000)

                else:
                    start_time = start_time
                url = f'{api_url}&startTime={start_time}'
                # print(interval_index)
                datas = requests.get(url=url, timeout=10, proxies=proxies).json()
                # print(datas)

                """
                [
                    [
                        1591258320000,      // open time
                        "9640.7",           // open price
                        "9642.4",           // highest price
                        "9640.6",           // lowest price
                        "9642.0",           // close price(latest price if the kline is not close)
                        "206",              // volume
                        1591258379999,      // close time
                        "2.13660389",       // turnover
                        48,                 // trade count 
                        "119",              // buy volume
                        "1.23424865",       //  buy turnover
                        "0"                 // ignore
                    ]

                """
                rows: List[Dict] = []
                # print (datas)
                for row in datas:
                    if row[0]<=end_time:
                        rows.append({
                            'symbol': save_symbol,
                            'exchange': exchange,
                            'interval': interval,
                            'datetime': generate_datetime(row[0]),
                            'volume': float(row[5]),
                            'turnover': float(row[7]),
                            'open_interest': 0,
                            'open_price': float(row[1]),
                            'high_price': float(row[2]),
                            'low_price': float(row[3]),
                            'close_price': float(row[4])
                        })

                with db.atomic():
                    for batch in chunked(rows, 100):
                        ApiBarData.insert_many(batch).on_conflict_replace().execute()

                overview: ApiBarOverview
                created: bool
                overview, created = ApiBarOverview.get_or_create(
                    symbol=symbol,
                    exchange=exchange,
                    interval=interval
                )
                aggregated_info = (ApiBarData
                    .select(
                        fn.COUNT(ApiBarData.id).alias('count'),
                        fn.MIN(ApiBarData.datetime).alias('start'),
                        fn.MAX(ApiBarData.datetime).alias('end')
                    )
                    .where(
                        (ApiBarData.symbol == symbol) &
                        (ApiBarData.exchange == exchange) &
                        (ApiBarData.interval == interval)
                    )
                    .get())

                overview.count = aggregated_info.count
                overview.start = aggregated_info.start
                overview.end = aggregated_info.end
                overview.save()

                if (datas[-1][0] > end_time) or datas[-1][6] >= (int(time.time() * 1000) - 60 * 1000):
                    break

            except Exception as error:
                time.sleep(10)
                print(error)


def download_future(symbol):

    start = datetime(2024, 11, 1, tzinfo=DB_TZ)
    end = datetime.now().astimezone(DB_TZ)
    t13 = Thread(target=get_binance_data, args=(symbol, 'future_klines', start, end))

    t13.start()
    t13.join()


if __name__ == '__main__':

    proxy_host = "127.0.0.1"
    proxy_port = 29290

    proxies = None
    if proxy_host and proxy_port:
        proxy = f'http://{proxy_host}:{proxy_port}'
        proxies = {'http': proxy, 'https': proxy}
    # weekly_symbol = ['BTCUSDT','ETHUSDT','THETAUSDT','ZRXUSDT','OMGUSDT','RLCUSDT','TRBUSDT','RUNEUSDT','SOLUSDT','STORJUSDT','BLZUSDT','AVAXUSDT','FTMUSDT','NEARUSDT','RSRUSDT','BELUSDT','SKLUSDT','GRTUSDT','UNFIUSDT','REEFUSDT','COTIUSDT','CHRUSDT','ONEUSDT','1000SHIBUSDT','BAKEUSDT','IOTXUSDT','GALAUSDT','ARUSDT','LPTUSDT','ENSUSDT','PEOPLEUSDT','DUSKUSDT','IMXUSDT','API3USDT','JASMYUSDT','INJUSDT','1000LUNCUSDT','LUNA2USDT','FETUSDT','PHBUSDT','STXUSDT','SSVUSDT','CKBUSDT','TRUUSDT','LEVERUSDT','BLURUSDT','SUIUSDT','1000PEPEUSDT','1000FLOKIUSDT','MAVUSDT','WLDUSDT','PENDLEUSDT','ARKMUSDT','YGGUSDT','SEIUSDT','ARKUSDT','BICOUSDT','MEWUSDT']
    import requests 
    symbol_list = []
    status= []
    delisted = []
    pending = []
    subtype = []
    url = "https://www.binance.com/fapi/v1/exchangeInfo" #https://developers.binance.com/docs/zh-CN/derivatives/usds-margined-futures/market-data/rest-api/Exchange-Information
    try:
        response = requests.get(url)
        datas = response.json()
        for data in datas['symbols']:
            if data['contractType'] == 'PERPETUAL':
                symbol_list.append(data['symbol'])
                subtype.append({'合约':data['symbol'],'subtype':data['underlyingSubType']})

    except ConnectionError:
        print('连接错误, 请检查vpn状态')

    symbol_list = [a for a in symbol_list if 'USDC' not in a]
    print(symbol_list)
    for symbol in tqdm(symbol_list,desc='下载进度'):
    # for symbol in symbol_list[303:]:
        print(symbol)
        download_future(symbol=symbol)
    tqdm.write('所有k线下载完成')

# %%
