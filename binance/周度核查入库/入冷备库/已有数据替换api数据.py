import pandas as pd
from datetime import datetime,timedelta
import numpy as np
from sqlalchemy import create_engine
import os
from tqdm import tqdm
import warnings
import json
from urllib.parse import quote_plus as urlquote
from typing import List, Dict
warnings.filterwarnings("ignore")
import pytz
from tzlocal import get_localzone_name
LOCAL_TZ = pytz.timezone(get_localzone_name())

path = os.path.dirname(__file__)
parent_path = os.path.dirname(path)

with open(os.path.join(parent_path,'config.json'), 'r',encoding='utf-8') as file:
    config = json.load(file)


n_1 = 1
start_date = datetime(2024,11,1).replace(hour=0, minute=0, second=0)
end_date = datetime(2025,3,19).replace(hour=11, minute=21, second=0)
# # start_date = (datetime.now() - timedelta(days=n_1)).replace(hour=14, minute=59, second=59)
# end_date = datetime.now().replace(hour=15, minute=0, second=0)

host = config['check_host']
port = config['check_port']
user = config['check_user']
password = config['check_password']
database_name = config['check_database_name']

engine = create_engine(f'mysql+pymysql://{user}:{urlquote(password)}@{host}:{port}/{database_name}')

def fetch_all(engine,start_date,end_date):
    query = """
    SELECT datetime, symbol,exchange,`interval`, open_price, high_price, low_price, close_price, volume, turnover FROM dbbardata 
    WHERE datetime BETWEEN %s AND %s 
    ORDER BY datetime
    """
    df = pd.read_sql(query, engine, params=(start_date,end_date))
    df['datetime'] = pd.to_datetime(df['datetime'])
    return df

df_all = fetch_all(engine,start_date,end_date)


vt_setting_file_path = os.path.join(parent_path,'.vntrader', 'vt_setting.json')
with open(vt_setting_file_path, 'r', encoding='utf-8') as vt_setting_file:
    vt_setting_data = json.load(vt_setting_file)
vt_setting_data['database.database'] = config['check_database_name']
vt_setting_data['database.host'] = config['check_host']
vt_setting_data['database.port'] = config['check_port']
vt_setting_data['database.user'] = config['check_user']
vt_setting_data['database.password'] = config['check_password']
with open(vt_setting_file_path, 'w', encoding='utf-8') as vt_setting_file:
    json.dump(vt_setting_data, vt_setting_file, indent=4)

from peewee import *
from vnpy_mysql.mysql_database import DB_TZ, db

class ApiBarData(Model):
    symbol = CharField()
    exchange = CharField()
    interval = CharField()
    datetime = DateTimeField()
    volume = DoubleField()
    turnover = DoubleField()
    open_interest = DoubleField()
    open_price = DoubleField()
    high_price = DoubleField()
    low_price = DoubleField()
    close_price = DoubleField()

    class Meta:
        database = db
        indexes = (
            (('symbol', 'exchange', 'interval', 'datetime'), True),
        )

db.create_tables([ApiBarData], safe=True)

def save_to_mysql(df:pd.DataFrame,bardata):

    for symbol in df['symbol'].unique():
        exchange ='BINANCE'
        interval = '1m'
        rows: List[Dict] = []
        df_sub = df[df['symbol'] == symbol]
        for index,row in df_sub.iterrows():
            rows.append({
                    'symbol':row['symbol'],
                    'exchange':exchange,
                    'datetime':row['datetime'],
                    'interval':interval,
                    'volume':float(row['volume']),
                    'turnover':float(row['turnover']),
                    'open_price':float(row['open_price']),
                    'high_price':float(row['high_price']),
                    'low_price':float(row['low_price']),
                    'close_price':float(row['close_price']),
                })
            if isinstance(rows[-1]["datetime"], str):
                rows[-1]["datetime"] = datetime.strptime(rows[-1]["datetime"], '%Y-%m-%d %H:%M:%S')
            rows[-1]["datetime"] = LOCAL_TZ.localize(rows[-1]["datetime"])

        with db.atomic():
            for batch in rows:
                bardata.insert_many(batch).on_conflict_replace().execute()

#%%
def main():
    save_to_mysql(df=df_all,bardata=ApiBarData)

if __name__ == '__main__':
    try:
        main()
    except Exception as e:
        print(e)


