#%%
import pandas as pd
from datetime import datetime,timedelta
import numpy as np
from sqlalchemy import create_engine
import os
from tqdm import tqdm
import warnings
import json
from urllib.parse import quote_plus as urlquote
from typing import List, Dict
warnings.filterwarnings("ignore")
import subprocess
import pytz
from tzlocal import get_localzone_name
LOCAL_TZ = pytz.timezone(get_localzone_name())

path = os.path.dirname(__file__)
parent_path = os.path.dirname(path)

with open(os.path.join(parent_path,'config.json'), 'r',encoding='utf-8') as file:
    config = json.load(file)

start_date = datetime(2025,1,1).replace(hour=0, minute=0, second=0)
end_date = datetime(2025,3,19).replace(hour=11, minute=0, second=0)
# # start_date = (datetime.now() - timedelta(days=n_1)).replace(hour=14, minute=59, second=59)
# end_date = datetime.now().replace(hour=15, minute=0, second=0)

host = config['check_host']
port = config['check_port']
user = config['check_user']
password = config['check_password']
database_name = config['check_database_name']

engine = create_engine(f'mysql+pymysql://{user}:{urlquote(password)}@{host}:{port}/{database_name}')

def fetch_all(engine,tablename,start_date,end_date):
    query = f"""
    SELECT datetime, symbol, exchange,`interval`, open_price, high_price, low_price, close_price, volume, turnover FROM {tablename} 
    WHERE datetime BETWEEN %s AND %s AND `interval` = '1m'
    ORDER BY datetime
    """
    df = pd.read_sql(query, engine, params=(start_date,end_date))
    df['datetime'] = pd.to_datetime(df['datetime'])
    return df

df = fetch_all(engine,'dbbardata',start_date,end_date)

#确定数据库起始日期
# start_date = df['datetime'].min().to_pydatetime() #自动对齐起始时间，如果dbbardata用一次删一次可以用这个
# end_date = df['datetime'].max().to_pydatetime()

df_all = fetch_all(engine,'apibardata',start_date,end_date)


vt_setting_file_path = os.path.join(parent_path,'.vntrader', 'vt_setting.json')
with open(vt_setting_file_path, 'r', encoding='utf-8') as vt_setting_file:
    vt_setting_data = json.load(vt_setting_file)
vt_setting_data['database.database'] = config['storage_database_name']
vt_setting_data['database.host'] = config['storage_host']
vt_setting_data['database.port'] = config['storage_port']
vt_setting_data['database.user'] = config['storage_user']
vt_setting_data['database.password'] = config['storage_password']
with open(vt_setting_file_path, 'w', encoding='utf-8') as vt_setting_file:
    json.dump(vt_setting_data, vt_setting_file, indent=4)

from peewee import *
from vnpy_mysql.mysql_database import DB_TZ, db

class dbBarData(Model):
    symbol = CharField()
    exchange = CharField()
    interval = CharField()
    datetime = DateTimeField()
    volume = DoubleField()
    turnover = DoubleField()
    open_interest = DoubleField()
    open_price = DoubleField()
    high_price = DoubleField()
    low_price = DoubleField()
    close_price = DoubleField()

    class Meta:
        database = db
        indexes = (
            (('symbol', 'exchange', 'interval', 'datetime'), True),
        )

class dbBarOverview(Model):
    symbol = CharField()
    exchange = CharField()
    interval = CharField()
    count = IntegerField()
    start = DateTimeField()
    end = DateTimeField()

    class Meta:
        database = db
        indexes = (
            (('symbol', 'exchange', 'interval'), True),
        )

db.create_tables([dbBarData, dbBarOverview], safe=True)
db.close()

from vnpy.trader.object import (BarData)
from vnpy_evo.trader.constant import Exchange,Interval
from vnpy_evo.trader.database import get_database,DB_TZ
database = get_database()

def save_to_mysql(df:pd.DataFrame):
    data: list[BarData] = []
    for symbol in df['symbol'].unique():
        data: list[BarData] = []
        df_sub = df[df['symbol'] == symbol]
        for index,row in df_sub.iterrows():
            bar: BarData = BarData(
                symbol=row['symbol'],
                exchange=Exchange(row['exchange']),
                datetime=row['datetime'],
                interval=Interval(row['interval']),
                volume=float(row['volume']),
                turnover=float(row['turnover']),
                open_price=float(row['open_price']),
                high_price=float(row['high_price']),
                low_price=float(row['low_price']),
                close_price=float(row['close_price']),
                gateway_name='BINANCE'
                )
            bar.datetime = pytz.timezone('Asia/Shanghai').localize(bar.datetime)
            data.append(bar)
        database.save_bar_data(data)
#%%
# def main():
# frpc_path = os.path.join(parent_path,'frp','frpc.exe')
# frpcc_path = os.path.join(parent_path,'frp','frpcc.toml')
# frpc_process = subprocess.Popen([frpc_path, "-c", frpcc_path])
save_to_mysql(df=df_all)
# frpc_process.terminate()

# if __name__ == '__main__':
#     try:
#         main()
#     except Exception as e:
#         print(e)


