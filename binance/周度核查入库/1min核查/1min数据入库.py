import os
import json

path = os.path.dirname(__file__)
parent_path = os.path.dirname(path)
with open(os.path.join(parent_path,'config.json'), 'r',encoding='utf-8') as file:
    config = json.load(file)
vt_setting_file_path = os.path.join(parent_path,'.vntrader', 'vt_setting.json')
with open(vt_setting_file_path, 'r', encoding='utf-8') as vt_setting_file:
    vt_setting_data = json.load(vt_setting_file)
vt_setting_data['database.database'] = config['check_database_name']
vt_setting_data['database.host'] = config['check_host']
vt_setting_data['database.port'] = config['check_port']
vt_setting_data['database.user'] = config['check_user']
vt_setting_data['database.password'] = config['check_password']
with open(vt_setting_file_path, 'w', encoding='utf-8') as vt_setting_file:
    json.dump(vt_setting_data, vt_setting_file, indent=4)

from datetime import datetime,timedelta
from typing import List, Tuple, Dict
import pandas as pd
from datetime import datetime
from peewee import *
from vnpy_mysql.mysql_database import DB_TZ, db

path = os.path.dirname(__file__)
csv_path = os.path.join(path,"实盘数据",'kline_data_20241101_0000_to_20250318_1500.csv')

class dbBarData(Model):
    symbol = CharField()
    exchange = CharField()
    interval = CharField()
    datetime = DateTimeField()
    volume = DoubleField()
    turnover = DoubleField()
    open_interest = DoubleField()
    open_price = DoubleField()
    high_price = DoubleField()
    low_price = DoubleField()
    close_price = DoubleField()

    class Meta:
        database = db
        indexes = (
            (('symbol', 'exchange', 'interval', 'datetime'), True),
        )

class dbBarOverview(Model):
    symbol = CharField()
    exchange = CharField()
    interval = CharField()
    count = IntegerField()
    start = DateTimeField()
    end = DateTimeField()

    class Meta:
        database = db
        indexes = (
            (('symbol', 'exchange', 'interval'), True),
        )

db.create_tables([dbBarData, dbBarOverview], safe=True)
db.close()
#%%
from vnpy.trader.object import (BarData)
from vnpy_evo.trader.constant import Exchange,Interval
from vnpy_evo.trader.database import get_database,DB_TZ
import pytz
database = get_database()

def save_to_mysql(df:pd.DataFrame):
    data: list[BarData] = []
    for symbol in df['symbol'].unique():
        data: list[BarData] = []
        df_sub = df[df['symbol'] == symbol]
        for index,row in df_sub.iterrows():
            bar: BarData = BarData(
                symbol=row['symbol'],
                exchange=Exchange(row['exchange']),
                datetime=row['datetime'],
                interval=Interval(row['interval']),
                volume=float(row['volume']),
                turnover=float(row['turnover']),
                open_price=float(row['open_price']),
                high_price=float(row['high_price']),
                low_price=float(row['low_price']),
                close_price=float(row['close_price']),
                gateway_name='BINANCE'
                )
            bar.datetime = pytz.timezone('Asia/Shanghai').localize(bar.datetime)
            data.append(bar)
        database.save_bar_data(data)     

def main():
    df = pd.read_csv(csv_path)
    save_to_mysql(df)

if __name__ == '__main__':
    try:
        main()
    except Exception as e:
        print(e)


# %%
