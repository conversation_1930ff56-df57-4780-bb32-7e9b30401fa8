import pymysql
import os
import json
# 配置数据库连接
path = os.path.dirname(__file__)
parent_path = os.path.dirname(path)
with open(os.path.join(parent_path,'config.json'), 'r',encoding='utf-8') as file:
    config = json.load(file)

db_config = {
    'host': config['check_host'],
    "port" : config['check_port'],
    'user': config['check_user'],
    'password': config['check_password'],
    'database': config['check_database_name']
}

# 创建 MySQL 连接
connection = pymysql.connect(**db_config)
cursor = connection.cursor()


path = os.path.dirname(__file__)

# 读取 .sql 文件内容
sql_file_path = rf'C:\Users\<USER>\Desktop\月度核查\1min核查\实盘数据\dbbardata0319.sql'

with open(sql_file_path, 'r', encoding='utf-8') as file:
    sql_script = file.read()

# 执行 .sql 文件中的所有 SQL 语句
for statement in sql_script.split(';'):
    statement = statement.strip()
    if statement:
        cursor.execute(statement)

# 提交事务并关闭连接
connection.commit()

# 关闭游标和连接
cursor.close()
connection.close()

print("SQL file imported successfully!")