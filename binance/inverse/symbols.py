import requests

# inverse symbols
# Fetch data from Binance
url = "https://www.binance.com/dapi/v1/exchangeInfo"
response = requests.get(url,proxies={"http": "http://localhost:7890", "https": "http://localhost:7890"})
data = response.json()

# Extract all symbols
symbols = [item['symbol'] for item in data['symbols']]

# Print the symbols
print(symbols)
print(len(symbols))

end_with_PERP = [item for item in symbols if item.endswith("PERP")]
print(end_with_PERP)
print(len(end_with_PERP))

not_end_with_PERP = [item for item in symbols if not item.endswith("PERP")]
print(not_end_with_PERP)
print(len(not_end_with_PERP))


# future symbols
url2 = "https://www.binance.com/fapi/v1/exchangeInfo"
response2 = requests.get(url2,proxies={"http": "http://localhost:7890", "https": "http://localhost:7890"})
data2 = response2.json()

# Extract all symbols
symbols2 = [item['symbol'] for item in data2['symbols']]
print(symbols2)
print(len(symbols2))

