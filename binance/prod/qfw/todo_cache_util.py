import redis
import logging

class TodoCacheUtil:
    def __init__(self, host='localhost', port=6379, db=0, password=None):
        self.redis_pool = redis.ConnectionPool(host=host, port=port, db=db, password=password)
        self.r = redis.Redis(connection_pool=self.redis_pool)

    def store_todo_cache(self, username, taskid, todo_info):
        todo_key = f"todos:{username}:{taskid}"
        # for key, value in todo_info.items():
        #     self.r.hset(todo_key, key, value)
        self.r.hset(todo_key, mapping=todo_info)
        # self.r.hmset(todo_key, todo_info)

    def remove_todo_cache(self, username, taskid):
        todo_key = f"todos:{username}:{taskid}"
        self.r.delete(todo_key)

    def get_user_task(self, username):
        cursor = '0'
        match = f"todos:{username}:*"
        tasks = []

        while True:
            cursor, keys = self.r.scan(cursor=cursor, match=match)
            for key in keys:
                task_info = self.r.hgetall(key)
                task_info = {k.decode('utf-8'): v.decode('utf-8') for k, v in task_info.items()}
                tasks.append(task_info)
            if cursor == 0:
                break
            # Sort the tasks by 'id' key. Note: It assumes 'id' can be converted to int for sorting.
            # If 'id' is not guaranteed to be convertible to int, additional error handling may be needed.
        sorted_tasks = sorted(tasks, key=lambda x: int(x['id']))
        return sorted_tasks

    def get_detail_task(self, todo_key):
        task_info = self.r.hgetall(todo_key)
        if task_info:
            task_info = {k.decode('utf-8'): v.decode('utf-8') for k, v in task_info.items()}
            print(task_info)
            return task_info
        else:
            logging.error(f"Task with key {todo_key} not found.")
            return {}

if __name__ == "__main__":
    # 创建TodoCacheUtil实例，假设Redis密码为"yourpassword"
    todo_util = TodoCacheUtil(password="yourpassword")
    
    # 存储待办事项
    todo_util.store_todo_cache("user1", "task1", {"content": "Finish report", "price": 100})
    
    # 获取用户的待办事项
    tasks = todo_util.get_user_task("user1")
    print(tasks)
    
    # 获取详细待办事项
    detail_task = todo_util.get_detail_task("todos:user1:task1")
    print(detail_task)
    
    # 移除待办事项
    todo_util.remove_todo_cache("user1", "task1")
