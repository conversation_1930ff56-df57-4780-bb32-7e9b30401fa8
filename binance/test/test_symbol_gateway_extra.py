import json
import time
from datetime import datetime
from pathlib import Path
from typing import Dict
import yaml
from collections import defaultdict

from vnpy_evo.event import Event, EventEngine
from vnpy_evo.trader.engine import MainEngine
from vnpy_evo.trader.object import ContractData
from vnpy_evo.trader.event import EVENT_CONTRACT

from prod.binance_linear_gateway import BinanceLinearGateway, symbol_gateway_extra

class UserGateway(BinanceLinearGateway):
    """每个用户的网关"""
    def write_log(self, msg: str) -> None:
        """重写日志方法，添加用户标识"""
        super().write_log(f"[{self.gateway_name}] {msg}")

class SymbolGatewayExtraTester:
    def __init__(self, config_path: str = "monitor_config.yaml"):
        """初始化测试器"""
        self.event_engine = EventEngine()
        self.main_engine = MainEngine(self.event_engine)
        self.users: Dict[str, UserGateway] = {}
        
        # 记录每个用户收到的合约
        self.contracts_received = defaultdict(dict)
        
        # 加载配置
        self.load_config(config_path)
        
        # 注册事件处理器
        self.event_engine.register(EVENT_CONTRACT, self.handle_contract_event)

    def load_config(self, config_path: str):
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)

            # 加载用户配置
            for username, settings in config["users"].items():
                work_dir = settings["work_dir"]
                connect_file = Path(work_dir) / ".vntrader" / "connect_binance_linear.json"

                if not connect_file.exists():
                    self.main_engine.write_log(f"找不到连接文件: {connect_file}")
                    continue

                with open(connect_file, 'r', encoding='utf-8') as f:
                    connect_config = json.load(f)

                # 创建用户网关
                gateway = UserGateway(self.event_engine, username)
                gateway.connect(connect_config)
                self.users[username] = gateway
                self.main_engine.write_log(f"用户 {username} 网关已连接")

        except Exception as e:
            self.main_engine.write_log(f"加载配置文件失败: {str(e)}")
            raise

    def handle_contract_event(self, event: Event):
        """处理合约数据事件"""
        contract: ContractData = event.data
        gateway_name = contract.gateway_name
        
        # 记录收到的合约
        self.contracts_received[gateway_name][contract.symbol] = contract
        
        # 只在收到BTCUSDT合约时打印信息
        if contract.symbol == "BTCUSDT":
            self.main_engine.write_log(f"收到用户 {gateway_name} 的 {contract.symbol} 合约，extra: {contract.extra}")

    def set_leverage_for_all_users(self):
        """为所有用户设置不同的杠杆倍数"""
        SYMBOL = "BTCUSDT"
        
        for i, (username, gateway) in enumerate(self.users.items(), 1):
            # 为每个用户设置不同的杠杆倍数
            leverage = i * 5  # 用户1设置2倍，用户2设置4倍，以此类推
            gateway.rest_api.set_leverage(SYMBOL, leverage)
            gateway.write_log(f"设置 {username} 的 {SYMBOL} 杠杆倍数为 {leverage}倍")

    def check_symbol_gateway_extra(self):
        """检查全局字典中的杠杆信息"""
        self.main_engine.write_log("\n--- 全局字典中的杠杆信息 ---")
        for symbol, gateway_info in symbol_gateway_extra.items():
            for gateway_name, info in gateway_info.items():
                self.main_engine.write_log(f"全局字典: {symbol} - {gateway_name}: {info}")

    def check_contract_extra(self):
        """检查每个用户的合约对象中的extra字段"""
        self.main_engine.write_log("\n--- 每个用户的合约对象中的extra字段 ---")
        symbol = "BTCUSDT"
        contract = self.main_engine.get_contract(f"{symbol}.BINANCE")
        if contract:
            self.main_engine.write_log(f"合约 {symbol} 信息: {contract.extra}")


    def run_test(self):
        """运行测试"""
        try:
            self.main_engine.write_log("测试开始")
            
            # 等待合约数据加载
            self.main_engine.write_log("等待合约数据加载...")
            # time.sleep(5)
            
            # 设置杠杆
            self.main_engine.write_log("设置杠杆...")
            self.set_leverage_for_all_users()
            
            # 等待杠杆设置完成
            self.main_engine.write_log("等待杠杆设置完成...")
            time.sleep(5)
            
            # 检查全局字典
            # self.check_symbol_gateway_extra()
            
            # 检查合约对象
            self.check_contract_extra()
            
            self.main_engine.write_log("测试完成")
            while True:
                time.sleep(1)
            
        except Exception as e:
            self.main_engine.write_log(f"测试过程中发生错误: {str(e)}")
        finally:
            # 关闭所有网关
            for username, gateway in self.users.items():
                gateway.close()
            self.main_engine.close()

def main():
    """主函数"""
    tester = SymbolGatewayExtraTester()
    tester.run_test()

if __name__ == "__main__":
    main()
