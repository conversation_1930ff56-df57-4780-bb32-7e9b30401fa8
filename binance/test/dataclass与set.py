from dataclasses import dataclass
from enum import Enum

class Exchange(str, Enum):
    SSE = "SSE"
    SZSE = "SZSE"

@dataclass()  # eq=True 是默认值，可省略
class SubscribeRequest:
    symbol: str
    exchange: Exchange

    def __post_init__(self) -> None:
        self.vt_symbol: str = f"{self.symbol}.{self.exchange.value}"

    def __hash__(self) -> int:
        # 基于 symbol 和 exchange 计算哈希（确保去重逻辑一致）
        return hash((self.symbol, self.exchange))


req1 = SubscribeRequest("AAPL", Exchange.SSE)
req2 = SubscribeRequest("AAPL", Exchange.SSE)
req3 = SubscribeRequest("GOOG", Exchange.SZSE)

my_set = {req1, req2, req3}

print(len(my_set))  # 输出 2 (req1 和 req2 被去重)
print(my_set)
# 输出: {SubscribeRequest(symbol='AAPL', exchange=<Exchange.SSE: 'SSE'>), SubscribeRequest(symbol='GOOG', exchange=<Exchange.SZSE: 'SZSE'>)}
print(req1 == req2) # True，==调用__eq__方法
print(req1 is req2) # False，is比较对象的内存地址
print(hash(req1) == hash(req2)) # True
