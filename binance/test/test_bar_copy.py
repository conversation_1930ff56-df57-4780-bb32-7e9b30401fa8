"""
测试脚本：验证对象复制和引用行为
测试 bar_copy 赋值是否会影响已经传递给其他线程的对象
结论：修改对象属性会改变所有引用该对象的地方，而重新赋值只改变当前变量的引用，不影响之前已经传递出去的对象引用。
"""
import time
import threading
from copy import copy
from dataclasses import dataclass
from datetime import datetime


@dataclass
class BarData:
    """模拟 BarData 类"""
    symbol: str
    datetime: datetime
    open_price: float = 0.0
    high_price: float = 0.0
    low_price: float = 0.0
    close_price: float = 0.0
    volume: float = 0.0
    turnover: float = 0.0


class EventEngine:
    """模拟事件引擎"""
    def __init__(self):
        self.handlers = {}
        self.event_queue = []
        self._active = False
        self._thread = None
        
    def register(self, event_type, handler):
        """注册事件处理函数"""
        if event_type not in self.handlers:
            self.handlers[event_type] = []
        self.handlers[event_type].append(handler)
    
    def start(self):
        """启动事件处理线程"""
        self._active = True
        self._thread = threading.Thread(target=self._run)
        self._thread.daemon = True
        self._thread.start()

    def stop(self):
        """停止事件处理线程"""
        self._active = False
        if self._thread:
            self._thread.join()

    def put(self, event_type, data):
        """放入事件队列"""
        if event_type in self.handlers:
            self.event_queue.append((event_type, data))

    def _run(self):
        """事件处理线程"""
        while self._active:
            if self.event_queue:
                event_type, data = self.event_queue.pop(0)
                self._process_event(event_type, data)
            else:
                time.sleep(0.01)  # 无事件时短暂休眠
    
    def _process_event(self, event_type, data):
        """处理事件"""
        for handler in self.handlers[event_type]:
            handler(data)
            time.sleep(1)  # 模拟处理延迟


class Gateway:
    """模拟网关类"""
    def __init__(self, event_engine):
        self.event_engine = event_engine
        self.last_bars = {}
        
    def on_event(self, event_type, bar):
        """发送事件"""
        self.event_engine.put(event_type, bar)
        
    def on_bar(self, bar):
        """处理 Bar 数据"""
        # 保存当前 bar
        self.last_bars[bar.symbol] = bar
        
        # 发送当前 bar 的事件
        bar_copy = copy(bar)
        print(f"发送 bar_copy 对象: {id(bar_copy)}, 价格: {bar_copy.close_price}")
        self.on_event("EVENT_BAR", bar_copy)
        self.on_event("EVENT_BAR1", bar_copy)

        
        # 模拟下一个循环的 bar_copy 赋值
        bar_copy.close_price = 150.0  # 修改bar_copy的属性会改已on_event走的

        bar_copy = copy(bar) # 创建新的bar_copy不会修改已on_event走的
        bar_copy.close_price = 200.0  # 修改新的 bar_copy
        print(f"新的 bar_copy 对象: {id(bar_copy)}, 修改后价格: {bar_copy.close_price}")
        self.on_event('EVENT_BAR', bar_copy)
        self.on_event("EVENT_BAR1", bar_copy)


def bar_handler(bar):
    """Bar 事件处理函数"""
    print(f"处理 bar 对象: {id(bar)}, 价格: {bar.close_price}")


def test_event_processing():
    """测试事件处理中的对象行为"""
    print("\n=== 测试事件处理 ===")
    event_engine = EventEngine()
    gateway = Gateway(event_engine)
    # 启动事件引擎
    event_engine.start()
    
    # 注册事件处理函数
    event_engine.register("EVENT_BAR", bar_handler)
    
    # 创建并处理 bar
    bar = BarData(symbol="BTC-USDT", datetime=datetime.now(), close_price=100.0)
    print(f"原始 bar 对象: {id(bar)}, 价格: {bar.close_price}")
    
    # 处理 bar
    gateway.on_bar(bar)
    
    # 等待所有线程完成
    time.sleep(5)
    # for thread in event_engine.thread_pool:
    #     thread.join()
    # 停止事件引擎
    event_engine.stop()


if __name__ == "__main__":    
    # 测试事件处理中的对象行为
    test_event_processing()
