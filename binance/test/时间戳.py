from datetime import datetime

from vnpy_evo.trader.utility import ZoneInfo

# Timezone constant
UTC_TZ = ZoneInfo("UTC")
DB_TZ = ZoneInfo("Asia/Shanghai")


def generate_datetime(timestamp: float) -> datetime:
    """Generate datetime object from a timestamp with a specific timezone"""
    dt: datetime = datetime.fromtimestamp(timestamp / 1000, tz=DB_TZ)
    return dt


# 示例用法
timestamp = 1724071219000
dt_object = generate_datetime(timestamp)
print(dt_object)
# 2024-08-19 12:40:19+00:00