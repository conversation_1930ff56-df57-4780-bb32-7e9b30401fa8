from vnpy.event import Event
from vnpy_evo.event import EventEngine
from vnpy_evo.trader.engine import MainEngine
import time
import signal
import sys

class SignalApp:
    def __init__(self):
        self.event_engine = EventEngine()
        self.main_engine = MainEngine(self.event_engine)
        
        self.register_signal()
        self._active = True
        
    def register_signal(self):
        """注册信号处理"""
        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)  # Ctrl+C
        signal.signal(signal.SIGTERM, self._signal_handler) # kill -15
        
        if sys.platform != 'win32':
            signal.signal(signal.SIGHUP, self.reload_setting)  # kill -1
            signal.signal(signal.SIGTSTP, signal.SIG_IGN)  # 忽略 Ctrl+Z

    def reload_setting(self, signum, frame):
        """收到SIGHUP信号时重新加载配置"""
        print(f"收到SIGHUP信号,重新加载配置...")
        self.main_engine.write_log("重新加载配置完成")
        
    def _signal_handler(self, signum, frame):
        """信号处理函数"""
        sig_name = signal.Signals(signum).name
        self.main_engine.write_log(f"收到信号: {sig_name}，准备关闭应用...")
        self._active = False

    def run(self):
        """运行"""
        self.main_engine.write_log("应用启动")
        while self._active:
            time.sleep(1)
        self.main_engine.write_log("应用正在关闭...")
        self.close()
    
    def close(self):
        """关闭"""
        self.main_engine.close()

def main():
    """主程序入口"""
    app = SignalApp()
    app.run()

if __name__ == "__main__":
    main()