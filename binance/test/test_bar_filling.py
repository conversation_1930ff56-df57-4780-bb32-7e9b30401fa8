from datetime import datetime, timedelta
from copy import copy
from queue import Queue
import threading
import time

# 模拟VeighNa的事件类型和数据结构
EVENT_BAR = "eBar."

class BarData:
    def __init__(self, symbol="", datetime=None, open_price=0, high_price=0, low_price=0, close_price=0, volume=0, turnover=0):
        self.symbol = symbol
        self.datetime = datetime or datetime.now()
        self.open_price = open_price
        self.high_price = high_price
        self.low_price = low_price
        self.close_price = close_price
        self.volume = volume
        self.turnover = turnover
        self.vt_symbol = f"{symbol}.BINANCE"

    def __str__(self):
        return f"Bar({self.vt_symbol}, {self.datetime}, O:{self.open_price}, H:{self.high_price}, L:{self.low_price}, C:{self.close_price}, V:{self.volume})"


class TestGateway:
    def __init__(self):
        self.last_bars = {}
        self.event_queue = Queue()
        self.consumer_thread = threading.Thread(target=self.consume_events)
        self.consumer_thread.daemon = True
        self.consumer_thread.start()
    
    def on_bar(self, bar: BarData) -> None:
        """
        Bar event push.
        """
        # 先检查时间差
        last_bar = self.last_bars.get(bar.vt_symbol)
        if last_bar:
            minutes_diff = int((bar.datetime - last_bar.datetime).total_seconds() / 60)
            if minutes_diff > 1:
                # 设置固定属性
                last_bar.volume = 0
                last_bar.turnover = 0
                last_bar.open_price = last_bar.close_price
                last_bar.high_price = last_bar.close_price
                last_bar.low_price = last_bar.close_price

                # 生成并推送缺失的bar
                for _ in range(minutes_diff-1):
                    last_bar.datetime = last_bar.datetime + timedelta(minutes=1)

                    self.write_log(f"补充缺失的bar: {last_bar.vt_symbol}, 时间: {last_bar.datetime}, 价格: {last_bar.close_price}")

                    # 发送事件时复制bar对象
                    bar_copy = copy(last_bar)
                    self.on_event(EVENT_BAR, bar_copy)
                    self.on_event(EVENT_BAR + last_bar.vt_symbol, bar_copy)

        # 更新last_bar
        self.last_bars[bar.vt_symbol] = bar

        # 发送当前bar的事件
        bar_copy = copy(bar)
        self.on_event(EVENT_BAR, bar_copy)
        self.on_event(EVENT_BAR + bar.vt_symbol, bar_copy)

    def on_event(self, event_type, data):
        """模拟事件推送"""
        self.event_queue.put((event_type, data))
        
    def consume_events(self):
        """消费事件的线程"""
        while True:
            if not self.event_queue.empty():
                event_type, data = self.event_queue.get()
                print(f"收到事件: {event_type}, 数据: {data}")
                self.event_queue.task_done()
            time.sleep(0.01)
    
    def write_log(self, msg):
        """模拟日志输出"""
        print(msg)


def simulate_market_data():
    """模拟市场数据生成器"""
    gateway = TestGateway()
    symbol = "BTCUSDT"
    
    # 生成基准时间
    base_time = datetime.now().replace(second=0, microsecond=0)
    
    # 模拟第一个bar
    bar1 = BarData(
        symbol=symbol,
        datetime=base_time,
        open_price=50000,
        high_price=50100,
        low_price=49900,
        close_price=50050,
        volume=10,
        turnover=500500
    )
    
    print(f"\n接收第一个bar: {bar1}")
    gateway.on_bar(bar1)
    
    # 模拟第二个bar (跳过一分钟，直接到第三分钟)
    # 这里应该触发缺失bar的填充
    bar2 = BarData(
        symbol=symbol,
        datetime=base_time + timedelta(minutes=3),
        open_price=50060,
        high_price=50200,
        low_price=50040,
        close_price=50150,
        volume=15,
        turnover=752250
    )
    
    print(f"\n接收第二个bar (跳过了2分钟): {bar2}")
    gateway.on_bar(bar2)
    
    # 等待事件处理完成
    time.sleep(1)
    
    # 模拟第三个bar (正常间隔)
    bar3 = BarData(
        symbol=symbol,
        datetime=base_time + timedelta(minutes=4),
        open_price=50160,
        high_price=50300,
        low_price=50100,
        close_price=50250,
        volume=12,
        turnover=603000
    )
    
    print(f"\n接收第三个bar (正常间隔): {bar3}")
    gateway.on_bar(bar3)
    
    # 等待事件处理完成
    time.sleep(1)
    
    print("\n测试完成")


if __name__ == "__main__":
    print("开始测试K线填充机制...")
    simulate_market_data()
