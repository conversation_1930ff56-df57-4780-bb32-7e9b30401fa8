import threading
import time
from copy import copy
from datetime import datetime
from enum import Enum
from typing import Dict
from loguru import logger

# 模拟所需的基础类和枚举
class Status(Enum):
    NOTTRADED = "未成交"
    PARTTRADED = "部分成交"
    ALLTRADED = "全部成交"

class OrderData:
    def __init__(self, orderid: str, traded: float = 0, status: Status = Status.NOTTRADED):
        self.orderid = orderid
        self.traded = traded
        self.status = status
        self.datetime = datetime.now()

    def __str__(self):
        return f"OrderData(orderid={self.orderid}, traded={self.traded}, status={self.status}, datetime={self.datetime})"

class Gateway:
    def __init__(self):
        self.orders: Dict[str, OrderData] = {}
        self.order_lock = threading.Lock()
        
    def get_order(self, orderid: str) -> OrderData:
        """获取订单"""
        return copy(self.orders.get(orderid, OrderData(orderid, traded=0, status=Status.NOTTRADED)))
    
    def on_order(self, order: OrderData) -> None:
        """更新订单"""
        self.orders[order.orderid] = copy(order)
        logger.info(f"订单更新 - 线程{threading.current_thread().name}: orderid={order.orderid}, traded={order.traded}, status={order.status}")
        
    def on_trade(self, orderid: str, traded_change: float) -> None:
        """处理成交回报"""
        logger.info(f"成交回报 - 线程{threading.current_thread().name}: orderid={orderid}, traded_change={traded_change}")

def simulate_rest_update(gateway: Gateway, orderid: str, use_lock: bool = True):
    """模拟REST API的订单更新"""
    def process_order():
        last_order = gateway.get_order(orderid)
        logger.info(f"获取订单 - 线程{threading.current_thread().name}: last_order={last_order}")
        
        # 模拟延迟
        time.sleep(0.2)
        
        order = OrderData(orderid, traded=552, status=Status.ALLTRADED)
        
        # Always push first order update
        if not last_order:
            logger.info(f"更新 - 线程{threading.current_thread().name}: last_order=None")
            gateway.on_order(order)
            return
            
        traded_change = order.traded - last_order.traded
        status_change = order.status != last_order.status
        
        if traded_change < 0:
            logger.info(f"跳过更新(traded_change<0) - 线程{threading.current_thread().name}: traded_change={traded_change}")
            return
            
        if traded_change == 0 and not status_change:
            logger.info(f"跳过更新(无变化) - 线程{threading.current_thread().name}: traded_change={traded_change}, status_change={status_change}")
            return
            
        logger.info(f"更新 - 线程{threading.current_thread().name}: traded_change={traded_change}, status_change={status_change}")
        
        # 先推送订单更新
        gateway.on_order(order)
        
        # 再推送成交回报
        if traded_change > 0:
            gateway.on_trade(orderid, traded_change)

    if use_lock:
        logger.info(f"线程{threading.current_thread().name} 尝试获取锁 - {time.time()}")
        with gateway.order_lock:
            logger.info(f"线程{threading.current_thread().name} 获取到锁 - {time.time()}")
            process_order()
            logger.info(f"线程{threading.current_thread().name} 即将释放锁 - {time.time()}")
        logger.info(f"线程{threading.current_thread().name} 已释放锁 - {time.time()}")
    else:
        process_order()

def simulate_ws_update(gateway: Gateway, orderid: str, use_lock: bool = True):
    """模拟WebSocket的订单更新"""
    def process_first_update():
        last_order = gateway.get_order(orderid)
        logger.info(f"获取订单 - 线程{threading.current_thread().name}: last_order={last_order}")
        
        order = OrderData(orderid, traded=0, status=Status.NOTTRADED)
        
        # Always push first order update
        if not last_order:
            logger.info(f"更新 - 线程{threading.current_thread().name}: last_order=None")
            gateway.on_order(order)
            return
            
        traded_change = order.traded - last_order.traded
        status_change = order.status != last_order.status
        
        if traded_change < 0:
            logger.info(f"跳过更新(traded_change<0) - 线程{threading.current_thread().name}: traded_change={traded_change}")
            return
            
        if traded_change == 0 and not status_change:
            logger.info(f"跳过更新(无变化) - 线程{threading.current_thread().name}: traded_change={traded_change}, status_change={status_change}")
            return
            
        logger.info(f"更新 - 线程{threading.current_thread().name}: traded_change={traded_change}, status_change={status_change}")
        
        # 先推送订单更新
        gateway.on_order(order)
        
        # 再推送成交回报
        if traded_change > 0:
            gateway.on_trade(orderid, traded_change)

    def process_second_update():
        last_order = gateway.get_order(orderid)
        logger.info(f"获取订单2 - 线程{threading.current_thread().name}: last_order={last_order}")
        
        order = OrderData(orderid, traded=552, status=Status.ALLTRADED)
        
        # Always push first order update
        if not last_order:
            logger.info(f"更新2 - 线程{threading.current_thread().name}: last_order=None")
            gateway.on_order(order)
            return
            
        traded_change = order.traded - last_order.traded
        status_change = order.status != last_order.status
        
        if traded_change < 0:
            logger.info(f"跳过更新2(traded_change<0) - 线程{threading.current_thread().name}: traded_change={traded_change}")
            return
            
        if traded_change == 0 and not status_change:
            logger.info(f"跳过更新2(无变化) - 线程{threading.current_thread().name}: traded_change={traded_change}, status_change={status_change}")
            return
            
        logger.info(f"更新2 - 线程{threading.current_thread().name}: traded_change={traded_change}, status_change={status_change}")
        
        # 先推送订单更新
        gateway.on_order(order)
        
        # 再推送成交回报
        if traded_change > 0:
            gateway.on_trade(orderid, traded_change)

    if use_lock:
        logger.info(f"线程{threading.current_thread().name} 尝试获取锁 - {time.time()}")
        with gateway.order_lock:
            logger.info(f"线程{threading.current_thread().name} 获取到锁 - {time.time()}")
            process_first_update()
            logger.info(f"线程{threading.current_thread().name} 即将释放锁 - {time.time()}")
        logger.info(f"线程{threading.current_thread().name} 已释放锁 - {time.time()}")
        
        logger.info(f"线程{threading.current_thread().name} 尝试获取锁2 - {time.time()}")
        with gateway.order_lock:
            logger.info(f"线程{threading.current_thread().name} 获取到锁2 - {time.time()}")
            process_second_update()
            logger.info(f"线程{threading.current_thread().name} 即将释放锁2 - {time.time()}")
        logger.info(f"线程{threading.current_thread().name} 已释放锁2 - {time.time()}")
    else:
        process_first_update()
        process_second_update()

def test_order_updates(use_lock: bool = True):
    """测试订单更新
    
    Args:
        use_lock (bool): 是否使用线程锁
    """
    gateway = Gateway()
    orderid = "test_order"
    
    rest_thread = threading.Thread(
        target=simulate_rest_update, 
        args=(gateway, orderid, use_lock), 
        name="REST"
    )
    ws_thread = threading.Thread(
        target=simulate_ws_update, 
        args=(gateway, orderid, use_lock), 
        name="WS"
    )
    
    # 先启动线程
    rest_thread.start()
    time.sleep(0.05)  # 确保线程先执行get_order
    
    # 再启动线程
    ws_thread.start()
    
    rest_thread.join()
    ws_thread.join()
    
    final_order = gateway.get_order(orderid)
    logger.info(f"\n最终订单状态: {final_order}")

if __name__ == "__main__":
    logger.info("\n\n=== 测试无锁情况（可能出现竞态条件）===")
    test_order_updates(use_lock=False)
    
    time.sleep(1)
    logger.info("\n\n=== 测试有锁情况 ===")
    test_order_updates(use_lock=True)