from copy import copy
import threading
import time
from enum import Enum
from loguru import logger

class Status(Enum):
    NOTTRADED = "未成交"
    PARTTRADED = "部分成交" 
    ALLTRADED = "全部成交"

class OrderData:
    """订单数据"""
    def __init__(self, orderid: str, traded: float = 0, status: Status = Status.NOTTRADED):
        self.orderid = orderid
        self.traded = traded
        self.status = status
        self.datetime = time.time()
        
    def __str__(self):
        return f"OrderData(orderid={self.orderid}, traded={self.traded}, status={self.status}, datetime={self.datetime})"

class Gateway:
    """模拟网关"""
    def __init__(self):
        self.orders = {}
        self.order_lock = threading.Lock()
        
    def get_order(self, orderid: str) -> OrderData:
        """获取订单"""
        return copy(self.orders.get(orderid, OrderData(orderid, traded=0, status=Status.NOTTRADED)))
    
    def on_order(self, order: OrderData) -> None:
        """更新订单"""
        self.orders[order.orderid] = copy(order)
        logger.info(f"订单更新 - 线程{threading.current_thread().name}: orderid={order.orderid}, traded={order.traded}, status={order.status}")
        
    def on_trade(self, orderid: str, traded_change: float) -> None:
        """处理成交回报"""
        logger.info(f"成交回报 - 线程{threading.current_thread().name}: orderid={orderid}, traded_change={traded_change}")

def simulate_rest_update(gateway: Gateway, orderid: str, use_lock: bool = True):
    """模拟REST API的订单更新"""
    def process_order():
        # 1. 获取订单
        last_order = gateway.get_order(orderid)
        logger.info(f"REST获取订单 - 线程{threading.current_thread().name}: last_order={last_order}")
        
        # 模拟网络延迟
        time.sleep(0.2)
        
        # 2. 收到成交回报(552手)
        order = OrderData(orderid, traded=552, status=Status.ALLTRADED)
        
        # 计算变化量
        traded_change = order.traded - last_order.traded
        status_change = order.status != last_order.status
        
        # 更新订单
        logger.info(f"REST更新 - 线程{threading.current_thread().name}: traded_change={traded_change}, status_change={status_change}")
        gateway.on_order(order)
        
        # 推送成交回报
        if traded_change > 0:
            gateway.on_trade(orderid, traded_change)

    if use_lock:
        logger.info(f"REST线程{threading.current_thread().name} 尝试获取锁 - {time.time()}")
        with gateway.order_lock:
            logger.info(f"REST线程{threading.current_thread().name} 获取到锁 - {time.time()}")
            process_order()
            logger.info(f"REST线程{threading.current_thread().name} 释放锁 - {time.time()}")
    else:
        process_order()

def simulate_ws_update(gateway: Gateway, orderid: str, use_lock: bool = True):
    """模拟WebSocket API的订单更新"""
    def process_order():
        # 1. 获取订单
        last_order = gateway.get_order(orderid)
        logger.info(f"WS获取订单 - 线程{threading.current_thread().name}: last_order={last_order}")
        
        # 模拟网络延迟
        time.sleep(0.1)
        
        # 2. 收到未成交状态(0手)
        order = OrderData(orderid, traded=0, status=Status.NOTTRADED)
        
        # 计算变化量
        traded_change = order.traded - last_order.traded
        status_change = order.status != last_order.status
        
        # 更新订单
        logger.info(f"WS更新1 - 线程{threading.current_thread().name}: traded_change={traded_change}, status_change={status_change}")
        gateway.on_order(order)
        
        # 推送成交回报
        if traded_change > 0:
            gateway.on_trade(orderid, traded_change)
            
        # 3. 收到全部成交状态(552手)
        time.sleep(0.1)
        order = OrderData(orderid, traded=552, status=Status.ALLTRADED)
        
        # 计算变化量
        traded_change = order.traded - last_order.traded
        status_change = order.status != last_order.status
        
        # 更新订单
        logger.info(f"WS更新2 - 线程{threading.current_thread().name}: traded_change={traded_change}, status_change={status_change}")
        gateway.on_order(order)
        
        # 推送成交回报
        if traded_change > 0:
            gateway.on_trade(orderid, traded_change)

    if use_lock:
        logger.info(f"WS线程{threading.current_thread().name} 尝试获取锁 - {time.time()}")
        with gateway.order_lock:
            logger.info(f"WS线程{threading.current_thread().name} 获取到锁 - {time.time()}")
            process_order()
            logger.info(f"WS线程{threading.current_thread().name} 释放锁 - {time.time()}")
    else:
        process_order()

def test_order_updates(use_lock: bool = True):
    """测试订单更新"""
    gateway = Gateway()
    orderid = "2506191920031000525"
    
    # 创建线程
    rest_thread = threading.Thread(
        target=simulate_rest_update,
        args=(gateway, orderid, use_lock),
        name="REST"
    )
    
    ws_thread = threading.Thread(
        target=simulate_ws_update,
        args=(gateway, orderid, use_lock),
        name="WS"
    )
    
    # 启动线程
    rest_thread.start()
    ws_thread.start()
    
    # 等待线程结束
    rest_thread.join()
    ws_thread.join()
    
    # 打印最终状态
    final_order = gateway.get_order(orderid)
    logger.info(f"\n最终订单状态: {final_order}")

if __name__ == "__main__":
    logger.info("\n=== 测试无锁情况（可能出现竞态条件）===")
    test_order_updates(use_lock=False)
    
    time.sleep(1)
    logger.info("\n=== 测试有锁情况 ===")
    test_order_updates(use_lock=True) 