# 算法交易引擎设计说明

## 初始化配置

```python
基础配置参数 = {
    "测试合约列表": ["ETHUSDT.BINANCE"],  
    "名义价值范围": (1, 100),          
    "数量除数": 3000,                 
    "测试定时间隔": 30秒,              
    "告警冷却时间": 3600秒
}

引擎初始化:
    # 核心字典表
    算法模板字典 = {}                          # 存储所有可用的算法类
    运行中算法字典 = {}                        # 存储正在运行的算法{任务号: 算法实例}
    合约算法映射 = defaultdict(set)            # 存储每个合约的算法集合
    订单号算法映射 = {}                        # 记录每个订单对应的算法实例
    持仓管理器 = PositionManager对象
    
    # 状态控制
    已处理任务集合 = set()                     # 标记已处理的任务ID
    合约就绪状态表 = {}                        # 记录每个合约是否就绪
    待启动请求表 = defaultdict(set)            # 每个合约待启动的算法集合
    
    # 系统标志
    是否正在停止 = False                       # 系统停止标志
    需要等待回报的订单集合 = set()             # 等待撤单回报
    恢复过程完成 = False                       # 系统恢复完成标志
    允许多算法 = True                          # 是否允许同合约多算法
    测试模式启用 = False                       # 是否启用测试
    定时器计数 = 0                            # 测试订单生成计数器
    上次电话告警时间 = None                    # 告警冷却控制
```

## 行情主线 - TICK数据处理

```python
目的：根据最新市场数据更新算法执行状态

# 行情数据处理
def process_tick_event(event):
    从event获取Tick数据
    从symbol_algo_map获取该合约下的算法集合
    推送Tick数据到每个算法实例.update_tick()

# K线数据处理
def process_bar_event(event):
    从event获取Bar数据
    从symbol_algo_map获取该合约下的算法集合
    推送Bar数据到每个算法实例.update_bar()

# 合约信息处理
def process_contract_event(event):
    从event获取合约信息
    
    # 如果合约未就绪
    if 未就绪:
        标记合约已就绪
        if 测试模式且是测试合约:
            记录就绪日志
            
        # 启动等待中的算法
        待启动算法集合 = 获取该合约待启动算法
        for 每个待启动算法:
            启动结果 = 尝试启动算法()
            if 启动失败:
                查询合约信息()
                终止处理
```

## 定时任务主线 - TIMER事件处理

```python
目的：定期检查系统状态，处理待执行任务

def process_timer_event(event):
    # 更新算法状态
    复制当前算法字典值避免遍历时改变
    推送定时器事件到每个算法实例
    
    # 处理待执行订单
    执行process_todo_orders()
    
    # 测试订单生成
    if 启用测试模式:
        if 定时器计数为0:
            生成测试订单()
        增加计数器
        if 超过生成间隔:
            重置计数器
```

## 交易信号主线 - 订单和成交处理

```python
# 订单状态更新
def process_order_event(event):
    从event获取订单数据
    获取关联算法实例 = 从orderid_algo_map查找
    
    if 算法存在且处于活跃状态:
        推送订单更新到算法实例
        
    # 处理系统停止时的撤单回报
    if 订单在等待撤单集合:
        if 订单已完结:
            从等待集合移除

# 成交回报处理
def process_trade_event(event):
    从event获取成交数据
    获取关联算法实例 = 从orderid_algo_map查找
    
    if 算法存在且处于活跃状态:
        推送成交数据到算法实例

# 算法状态更新
def process_algo_event(event):
    从event获取算法数据
    获取任务编号
    更新算法状态(
        任务号=todo_id,
        状态=status,
        已成交=traded,
        成交均价=traded_price,
        黑洞订单量=black_hole_volume
    )
```

## 任务恢复机制

```python
目的：系统重启时恢复未完成的算法任务

def resume_algo_orders:
    # 计算恢复范围
    截止时间 = 当前时间 - 1小时
    
    # 查询需要恢复的算法单
    运行中算法集合 = 查询(
        条件 = 状态是运行或暂停 且 
              开始时间大于截止时间
    )
    
    全部成功 = True
    for 每个算法单:
        # 获取原始任务
        任务信息 = 查询Todo记录
        if 不存在:
            记录错误并继续
            
        # 计算剩余数量
        未完成数量 = 总数量 - 已成交 - 黑洞订单
        if 无剩余:
            更新状态为已完成
            继续下一个
            
        # 标记为已处理
        添加到已处理集合
        
        # 尝试启动算法
        启动结果 = 尝试启动算法()
        if 失败:
            更新状态为暂停
            全部成功 = False
            继续下一个
            
        记录恢复日志
        
    标记恢复完成 = True
    if 不是全部成功:
        查询合约信息()
```

## 新订单处理流程

```python
目的：处理系统收到的新订单请求

def process_todo_orders:
    # 等待系统恢复完成
    if 未完成恢复:
        return
        
    # 查询新订单
    新订单集合 = 查询(
        条件 = 状态为10 且 
              不在已处理集合中
    )
    
    全部成功 = True
    for 每个订单:
        # 防重复处理
        标记为已处理
        if 已存在算法单:
            记录错误并继续
            
        # 转换交易参数
        direction = 转换买卖方向
        offset = 转换开平方向
        if 无效方向:
            记录错误并继续
            
        # 创建算法单记录
        新算法单 = 创建记录(
            任务号=订单.id,
            合约=订单.vt_symbol,
            方向=订单.direction,
            开平=订单.offset,
            价格=订单.price,
            数量=订单.real_volume,
            模板=VolumeFollowSyncAlgo,
            状态=运行中
        )
        保存算法单()
        
        # 启动算法
        启动结果 = 尝试启动算法()
        if 失败:
            更新状态为异常
            全部成功 = False
            继续下一个
        
        记录创建日志
        
    if 不是全部成功:
        查询合约信息()
```

## 安全停止机制

```python
目的：确保系统安全停止，处理所有未完成订单

def stop_all:
    if 正在停止中:
        return
        
    标记正在停止中 = True
    
    # 处理运行中算法
    for 每个运行中算法:
        记录活动订单到等待集合
        暂停算法执行
        撤销所有活动委托
        
    # 等待撤单回报
    等待次数 = 0
    while 存在等待回报的撤单 且 等待次数 < 10:
        等待1秒
        等待次数 += 1
        记录等待日志
        
    # 更新数据库状态
    将所有运行中算法改为暂停状态
    记录停止完成日志
```

## 多级告警机制

```python
目的：实现电话和邮件的差异化告警

def send_alert(算法实例, 标题="", 消息="", 需要电话=False):
    # 提取合约信息
    交易对 = 从vt_symbol分离
    交易所 = 从vt_symbol分离
    
    # 设置默认消息
    标题 = 标题 or "算法交易告警"
    消息 = 消息 or f"{交易对}在{交易所}发生异常"
    
    # 构建告警内容
    告警内容 = f"""
        标题：{标题}
        任务号：{算法.todo_id}
        时间：{当前时间}
        合约：{合约信息}
        方向：{交易方向}
        开平：{开平方向}
        价格：{委托价格}
        数量：{委托数量}
        异常：{异常信息}
    """
    
    # 电话告警
    if 需要电话 且 配置了接收地址:
        当前时间 = 获取当前时间()
        
        # 检查告警间隔(1小时)
        if 未发过告警 或 超过告警间隔:
            尝试:
                发送POST请求(
                    URL=告警地址,
                    数据={
                        'title': 标题,
                        'content': 消息,
                        'channel': 'voice'
                    }
                )
                记录上次告警时间
                记录发送成功
            如果异常:
                记录发送失败
        否则:
            记录跳过告警
            
    # 邮件告警
    尝试:
        发送邮件(标题, 告警内容)
        记录发送成功
    如果异常:
        记录发送失败
```

## 数据流转图

```mermaid
flowchart TB
    A[事件引擎] --> B[行情数据处理] & C[定时任务处理] & D[交易信号处理]
    
    B --> B1[TICK处理] & B2[BAR处理] & B3[合约信息]
    B1 --> B4[更新算法状态]
    B2 --> B4
    B3 --> B5[合约就绪处理] --> B6[启动等待算法]
    
    C --> C1[运行中算法更新] & C2[待执行订单处理] & C3[测试订单生成]
    C2 --> C4[创建算法单] --> C5[启动新算法]
    
    D --> D1[订单状态更新] & D2[成交回报处理] & D3[算法状态更新]
    D1 --> D4[撤单回报检查]
    D2 --> D5[成交量统计]
    D3 --> D6[数据库状态更新]
    
    B4 & C5 & D5 --> E[算法实例]
    E --> F[发送委托] --> G[交易接口]
    G --> D1
    
    subgraph 异常处理
        H[异常检测] --> I{告警类型}
        I --> J[邮件告警] & K[电话告警]
        K --> L[告警去重]
    end
