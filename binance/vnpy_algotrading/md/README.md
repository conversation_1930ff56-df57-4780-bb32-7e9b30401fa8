# 跟量算法设计说明

## 参数配置

```python
默认配置参数 = {
    "超价比例": 2.0,      # 在当前价格基础上的加减价百分比
    "最小名义价值倍数": 2.0, # 放大交易所最小名义价值要求的倍数
    "跟量比例": 0.2,      # 实际委托数量占检测到的市场成交量的比例
    "订单等待时间": 2.0    # 订单超过此时间（秒）将被撤销
}
```

## 行情处理主线（on_tick）

```python
目的：处理每一笔新的市场行情数据，产生跟量信号

# 首次行情处理
if tick是第一笔行情:
    记录tick时间戳 = tick.datetime
    记录当前分钟开始时间 = tick时间去除秒部分
    记录分钟首笔成交量 = tick.volume
    记录分钟末笔成交量 = tick.volume
    记录分钟首笔时间 = tick.datetime
    返回
    
# 更新时间戳
更新最新行情时间 = tick.datetime去除时区信息

# 计算实际成交量变化
if 进入新的分钟:
    if 分钟差为1 且 上一分钟K线有成交 且 首笔tick在前5秒:
        # 使用一分钟K线成交量修正tick成交量
        分钟实际成交量 = 末笔成交量 - 首笔成交量
        真实成交量变化 = 一分钟K线成交量 - 分钟实际成交量
    else:
        真实成交量变化 = 0
        
    # 更新分钟统计数据
    记录分钟首笔成交量 = tick.volume
    记录分钟首笔时间 = tick.datetime
    更新当前分钟时间戳
else:
    真实成交量变化 = tick.volume - last_tick.volume
    
# 更新末笔成交量
记录分钟末笔成交量 = tick.volume

# 处理成交量变化
if 真实成交量变化 > 0:
    # 计算成交速度
    时间间隔 = tick时间 - 上次变化时间
    累加未分配量 += 真实成交量变化
    
    if 时间间隔 > 0.5秒:
        成交速度 = 未分配量 / 时间间隔
    else:
        成交速度 = 0  # 时间间隔过短不计算速度
        
    记录日志(f"检测到成交量: {真实成交量变化}, 间隔: {时间间隔:.3f}秒, 速度: {成交速度:.3f}/秒")
```

## 定时任务主线（on_timer）

```python
目的：每0.5秒执行一次，管理订单状态并执行发单操作

# 检查行情超时（5秒）
if 有最新行情时间:
    当前时间 = datetime.now()
    if 已超过5秒未更新:
        发送数据库告警(标题="行情数据超时", 内容=f"{vt_symbol}超过5秒未收到行情")
        停止算法()
        return

# 检查基础数据
if 无最新价格 or 无合约信息 or 无最小名义价值:
    return

# 计算最小订单量
最小名义价值要求 = 最小名义价值 * 倍数 / 跟量比例
最小下单量 = 最小名义价值要求 / 最新价格
建议发单量 = 成交速度 * 定时器间隔

# 确定本次发单量
if 未分配量 <= 最小下单量:
    本次发单量 = 未分配量  # 避免剩余小单
else:
    本次发单量 = min(max(建议量, 最小下单量), 未分配量)

# 检查撤单超时订单（2.5秒）
for 每个正在撤单的订单:
    if 超过2.5秒未收到撤单回报:
        移除撤单记录
        将订单移入黑洞
        更新黑洞成交量
        发送告警(
            标题="订单撤单超时",
            内容=f"订单{orderid}撤单超2.5秒未收到回报",
            需要电话告警=True,
            需要数据库记录=True
        )

# 检查活跃订单状态
for 每个活跃订单:
    需要撤单 = False
    if 等待时间 > 最大等待时间:
        需要撤单 = True
        原因 = "等待超时"
    elif (买入价格 < 卖一价) or (卖出价格 > 买一价):
        需要撤单 = True
        原因 = "价格不合理"
        
    if 需要撤单:
        发出撤单指令()
        记录撤单时间

# 执行智能发单
if 本次发单量 > 0 且 未被冷却:
    # 计算发单参数
    实际发单量 = min(本次发单量 * 跟量比例, 剩余可用量)
    
    # 计算委托价格
    if 买入方向:
        委托价格 = 最新价 * (1 + 超价比例/100)
        检查涨停限制
    else:
        委托价格 = 最新价 * (1 - 超价比例/100)
        检查跌停限制
    
    # 发送订单
    当前名义价值 = 委托价格 * 实际发单量
    if 当前名义价值 >= 最小名义价值:
        # 检查剩余量
        剩余量 = 可用量 - 实际发单量
        if 剩余量名义价值 < 最小名义价值要求:
            发单量 = 可用量  # 合并剩余小单
            
        发送开仓订单(方向, 价格, 数量)
    else:
        发送平仓订单(方向, 价格, 数量)  # 小单直接平仓
```

## 回报处理机制

```python
# 订单回报处理
目的：跟踪订单状态变化并处理异常情况

    # 维护订单记录
    从pending集合移除
    从撤单记录移除

    if 订单不再活跃:
        清理订单时间记录
        if 检查完成():
            完成算法()
        elif 订单被拒绝:
            错误信息 = 解析拒单原因()
            
            # 记录到数据库
            创建错误记录并存入数据库(
                代码=error_code,
                信息=error_msg,
                订单号=orderid,
                任务号=todo_id
            )
            
            # 差异化处理
            if 系统过载错误(-1008):
                无限重试
            elif 开仓额超限(-2027):
                最多重试5次
                发送电话告警
                发送数据库告警
            else:  # 其他错误
                最多重试5次
                仅发送数据库告警

# 成交回报处理
目的：更新成交状态和处理冷却

    记录成交信息(订单号, 价格, 数量)

    if 处于冷却状态:
        解除冷却
        所有错误计数重置为5次

    if 检查完成():
        完成算法()
```

## 任务完成检查

```python
目的：判断算法是否可以完全结束

if 还有活跃订单:
    return False
    
if 还有未收到回报订单:
    for 每个未回报订单:
        if 正在撤单 且 超过2.5秒:
            将订单移入黑洞集合
            继续检查下一个
        else:
            return False
            
# 检查实际成交量（含黑洞订单）是否达到目标
总成交量 = 实际成交 + 黑洞成交量
return 总成交量 == 目标成交量
```
