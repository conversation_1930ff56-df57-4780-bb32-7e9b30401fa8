import sys, re, importlib
from time import sleep
from typing import Dict, Type, Union
from vnpy.event import Event, EventEngine
from vnpy.trader.engine import BaseEngine, MainEngine
from vnpy.trader.object import LogData
from .database import Todo
from .base import EVENT_ALGO_LOG
from vnpy.trader.utility import load_json, save_json
from .template_risk import RiskPlugin

APP_NAME = "AlgoRiskManager"

def pascal2snake(camel_case: str):
    """大驼峰（帕斯卡）转蛇形"""
    snake_case = re.sub(r"([A-Z])", r"_\1", camel_case)
    return snake_case.lower().strip('_')

def snake2pascal(snake_case: str):
    """蛇形转大驼峰（帕斯卡）"""
    words = snake_case.split('_')
    return ''.join(word.title() for word in words)

class AlgoRiskEngine(BaseEngine):
    """算法交易风控引擎"""
    setting_filename: str = "algo_risk_setting.json"

    def __init__(self, main_engine: MainEngine, event_engine: EventEngine) -> None:
        super().__init__(main_engine, event_engine, APP_NAME)

        self.plugin_count: int = 0
        self.plugins: Dict[str, Type[RiskPlugin]] = {}
        self.plugin_instances: Dict[str, "RiskPlugin"] = {}
        self.balance_retry_count: int = 5  # 获取账户余额重试次数

        # 加载配置
        self.load_setting()
        # 加载风控插件
        self.load_risk_template()

    def load_setting(self) -> None:
        """加载风控配置"""
        self.setting = load_json(self.setting_filename)
        if not self.setting:
            self.setting = {
                "enabled_plugins": ["SymbolRestriction", "OrderValueLimit"]
            }
            save_json(self.setting_filename, self.setting)

    def reload_setting(self) -> None:
        """重新加载配置并更新插件"""
        # 保存旧的已启用插件列表
        old_enabled = set(self.setting["enabled_plugins"])
        
        # 重新加载配置
        self.load_setting()
        new_enabled = set(self.setting["enabled_plugins"])
        
        # 处理需要移除的插件
        for name in old_enabled - new_enabled:
            self.del_risk_template(name)
            
        # 处理需要添加的插件
        for name in new_enabled - old_enabled:
            # 尝试加载插件
            try:
                # 如果是蛇形命名，转换为帕斯卡命名
                class_name = snake2pascal(name)
                module_name = f".internal_risk_plugins.{pascal2snake(class_name)}"
                module = importlib.import_module(module_name, package="vnpy_algotrading")
                plugin_class = getattr(module, class_name)
                self.add_risk_template(plugin_class)
            except Exception as e:
                self.write_log(f"加载风控插件失败: {name}, 错误: {str(e)}")
        
        # 重新加载现有插件的配置
        for plugin in self.plugin_instances.values():
            plugin.load_setting()
            
        self.write_log(f"风控插件重载完成，当前共有{self.plugin_count}个插件")

    def load_risk_template(self) -> None:
        """载入风控插件"""
        # 遍历配置中启用的插件
        for plugin_name in self.setting["enabled_plugins"]:
            try:
                # 如果是蛇形命名，转换为帕斯卡命名
                class_name = snake2pascal(plugin_name)
                module_name = f".internal_risk_plugins.{pascal2snake(class_name)}"
                
                # 尝试导入或重载模块
                try:
                    if module_name in sys.modules:
                        module = importlib.reload(sys.modules[module_name])
                    else:
                        module = importlib.import_module(module_name, package="vnpy_algotrading")
                    
                    plugin_class = getattr(module, class_name)
                    self.add_risk_template(plugin_class)
                except Exception as e:
                    self.write_log(f"加载风控插件失败: {plugin_name}, 错误: {str(e)}")
            except Exception as e:
                self.write_log(f"解析插件名称失败: {plugin_name}, 错误: {str(e)}")

        self.write_log(f"风控插件加载完成，共加载{self.plugin_count}个插件")

    def add_risk_template(self, template: Union[Type[RiskPlugin], str]) -> None:
        """添加风控插件"""
        # 如果传入的是字符串，尝试导入对应的类
        if isinstance(template, str):
            class_name = snake2pascal(template)
            module_name = f".internal_risk_plugins.{pascal2snake(class_name)}"
            try:
                module = importlib.import_module(module_name, package="vnpy_algotrading")
                template = getattr(module, class_name)
            except Exception as e:
                self.write_log(f"加载风控插件失败: {template}, 错误: {str(e)}")
                return

        # 检查是否已经添加过
        if template.__name__ in self.plugins:
            return
            
        # 记录插件类
        self.plugins[template.__name__] = template
        
        # 创建插件实例
        instance = template(self)
        self.plugin_instances[template.__name__] = instance
        self.plugin_count += 1
        
        self.write_log(f"风控插件加载成功: {template.__name__}")

    def del_risk_template(self, template_name: str) -> None:
        """删除风控插件"""
        # 如果是蛇形命名，转换为帕斯卡命名
        class_name = snake2pascal(template_name)
        
        # 移除插件实例
        if class_name in self.plugin_instances:
            del self.plugin_instances[class_name]
            self.plugin_count -= 1
            self.write_log(f"风控插件已移除: {class_name}")
            
        # 移除插件类
        if class_name in self.plugins:
            del self.plugins[class_name]

    def check_risk(self, todo: Todo) -> bool:
        """执行风控检查"""
        for name, plugin in self.plugin_instances.items():
            if not plugin.check_risk(todo):
                self.write_log(f"风控插件[{name}]拦截订单")
                return False
        return True

    def get_balance(self, gateway_name: str = "") -> float:
        """获取账户权益"""
        if not gateway_name:
            gateway_names = [name for name in self.main_engine.gateways.keys() if name != "RPC"]
            if not gateway_names:
                self.write_log("没有可用的网关")
                return 0
            gateway_name = gateway_names[0]

        # 获取vt_accountid
        vt_accountid = f"{gateway_name}.USDT"
        if not vt_accountid:
            return 0

        # 获取账户信息
        account = self.main_engine.get_account(vt_accountid)
        if not account:
            all_accounts = self.main_engine.get_all_accounts()
            if all_accounts:
                self.write_log(f"找不到账户{vt_accountid}，所有账户：{all_accounts}")
            else:
                if self.balance_retry_count <= 0:
                    msg = "获取账户余额超过最大重试次数"
                    self.write_log(msg)
                    raise RuntimeError(msg)
                    
                self.balance_retry_count -= 1
                self.write_log(f"获取账户余额重试剩余次数:{self.balance_retry_count}")
                sleep(1)
                return self.get_balance()

        return account.balance

    def write_log(self, msg: str, need_format: bool = True) -> None:
        """输出日志"""
        if need_format:
            frame = sys._getframe(1)
            func_name = frame.f_code.co_name
            line_no = frame.f_lineno
            class_name = self.__class__.__name__
            formatted_msg = f"[{class_name}.{func_name}:{line_no}] {msg}"
        else:
            formatted_msg: str = msg

        log: LogData = LogData(msg=formatted_msg, gateway_name=APP_NAME)
        event: Event = Event(EVENT_ALGO_LOG, data=log)
        self.event_engine.put(event)
