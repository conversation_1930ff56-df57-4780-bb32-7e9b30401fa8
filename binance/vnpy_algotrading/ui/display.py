NAME_DISPLAY_MAP: dict = {
    "vt_symbol": "本地代码",
    "direction": "方向",
    "price": "价格",
    "volume": "委托数量",
    "time": "执行时间（秒）",
    "interval": "每轮间隔（秒）",
    "offset": "开平",
    "status": "算法状态",
    "traded_price": "成交均价",
    "traded": "成交数量",
    "left": "剩余数量",
    "order_volume": "单笔委托",
    "timer_count": "本轮读秒",
    "total_count": "累计读秒",
    "template_name": "算法模板",
    "display_volume": "挂出数量",
    "price_add": "委托超价",
    "step_price": "网格交易间距",
    "step_volume": "网格交易数量",
    "order_type": "委托类型",
    "active_vt_symbol": "主动腿",
    "passive_vt_symbol": "被动腿",
    "spread_up": "价差上限",
    "spread_down": "价差下限",
    "max_pos": "最大持仓",
    "start_time": "开始时间",
    "end_time": "结束时间",
    "min_volume": "最小委托量",
    "max_volume": "最大委托量",
    "volume_change": "委托量变化"
}
