# vnpy_algotrading/internal_risk_plugins/symbol_restriction.py
from typing import Tuple
from vnpy.trader.utility import extract_vt_symbol

from ..template_risk import RiskPlugin
from ..database import Todo


class SymbolRestriction(RiskPlugin):
    """合约限制插件"""
    default_setting = {
        "use_white_list": True,  # True用白名单,False用黑名单
        "symbols": ["ETHUSDT", "BTCUSDT"]
    }

    def load_setting(self) -> None:
        """加载白/黑名单配置"""
        super().load_setting()
        self.use_white_list = self.setting["use_white_list"]
        self.symbols = set(self.setting["symbols"])

        list_type = "白名单" if self.use_white_list else "黑名单"
        self.write_log(f"使用{list_type}模式,限制列表:{self.symbols}")

    def check_risk(self, todo: Todo) -> Tuple[bool, str]:
        """检查合约是否在限制列表中"""
        symbol = extract_vt_symbol(todo.vt_symbol)[0]

        if self.use_white_list:
            if symbol not in self.symbols:
                msg = f"合约{symbol}不在白名单中"
                self.write_log(msg)
                return False, msg
        else:
            if symbol in self.symbols:
                msg = f"合约{symbol}在黑名单中"
                self.write_log(msg)
                return False, msg

        return True, ""