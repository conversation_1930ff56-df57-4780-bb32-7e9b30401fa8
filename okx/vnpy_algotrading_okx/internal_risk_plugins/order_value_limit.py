# vnpy_algotrading/internal_risk_plugins/order_value_limit.py

from typing import Tuple
from ..template_risk import RiskPlugin
from ..database import Todo


class OrderValueLimit(RiskPlugin):
    """委托价值限制插件"""
    default_setting = {
        "base_account_value": 1000000,  # 基准账户资金，默认100万
        "base_max_order_value": 8000    # 基准最大订单价值量，默认8000
    }

    def load_setting(self) -> None:
        """加载配置"""
        super().load_setting()
        self.base_account_value = self.setting["base_account_value"]
        self.base_max_order_value = self.setting["base_max_order_value"]
        
        # 预先计算比例系数
        self.value_ratio = self.base_max_order_value / self.base_account_value
        self.write_log(f"加载配置成功，基准账户资金:{self.base_account_value}，基准最大订单价值量:{self.base_max_order_value}")
        self.write_log(f"预计算比例系数:{self.value_ratio}")
        self.write_log(f"启动时刻账户资金：{self.get_balance()}，最大订单价值量:{self.get_balance() * self.value_ratio:.2f}")

    def check_risk(self, todo: Todo) -> Tuple[bool, str]:
        """检查委托价值是否超限"""
        # 1. 获取当前账户资金
        account_value = self.get_balance()
        if account_value <= 0:
            msg = f"无法获取账户资金，或账户资金<0，拒绝订单"
            self.write_log(msg)
            return False, msg

        # 2. 获取合约信息
        contract = self.risk_engine.main_engine.get_contract(todo.vt_symbol)
        if not contract:
            msg = f"找不到合约信息:{todo.vt_symbol}"
            self.write_log(msg)
            return False, msg

        # 3. 计算订单价值量
        order_value = todo.real_volume * todo.price * contract.size
        if order_value <= 0:
            msg = f"委托价值{order_value:.2f}不能小于等于0"
            self.write_log(msg)
            return False, msg

        # 4. 使用预计算的比例系数计算最大订单价值量
        max_order_value = account_value * self.value_ratio

        # 5. 检查是否超过限制
        if order_value > max_order_value:
            msg = f"订单价值量{order_value:.2f}超过账户限制{max_order_value:.2f}"
            self.write_log(msg)
            return False, msg

        return True, ""
