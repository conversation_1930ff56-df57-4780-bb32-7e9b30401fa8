import sys
import signal
from datetime import time, datetime
from logging import INFO
from time import sleep

from vnpy.event import EventEngine
from vnpy.trader.constant import Exchange
from vnpy.trader.engine import MainEngine
from vnpy.trader.object import SubscribeRequest,OrderRequest,Direction,OrderType,Offset,CancelRequest
from vnpy.trader.setting import SETTINGS
from vnpy.trader.utility import load_json, save_json
from vnpy.trader.event import EVENT_TICK
# sys.path.append('/opt/external_lib/prod_gateway_okx')
from prod.okx_gateway import OkxGateway
# Configure logging settings
SETTINGS["log.active"] = True
SETTINGS["log.level"] = INFO
SETTINGS["log.console"] = True
SETTINGS["log.file"] = True


def signal_handler(signum, frame):
    """Handle exit signals"""
    print("\nReceived signal to exit. Cleaning up...")
    if 'main_engine' in globals():
        main_engine.write_log("Closing connection...")
        main_engine.close()
    sys.exit(0)


def run_set_leverage():
    """Main function to set leverage for all contracts"""
    global main_engine

    # Register signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # Create event engine
    event_engine = EventEngine()
    main_engine = MainEngine(event_engine)

    # Load connection settings
    connect_filename = 'connect_okx.json'
    okx_setting = load_json(connect_filename)
    
    if not okx_setting:
        # 创建默认配置文件
        default_okx_setting = {
            "API Key": "",
            "Secret Key": "",
            "Passphrase": "",
            "Server": "REAL",  # REAL 或 DEMO
            "Proxy Host": "127.0.0.1",
            "Proxy Port": 7890
        }
        save_json("connect_okx.json", default_okx_setting)
        print("已创建默认OKX网关配置文件，请填写API信息")
        return

    # Add gateway
    okx_gateway = main_engine.add_gateway(OkxGateway)
    main_engine.write_log("OKX Gateway added successfully")

    # Connect to OKX
    main_engine.connect(okx_setting, "OKX")
    main_engine.write_log("Connecting to OKX gateway...")

    # Wait for connection
    sleep(5)

    # Read contract list from file
    try:
        with open('contract_list.txt', 'r') as f:
            contracts = [line.strip() for line in f if line.strip()]
    except FileNotFoundError:
        print("contract_list.txt file not found")
        return

    # Set leverage for all contracts
    leverage = 20
    for contract in contracts:
        # Format symbol as required
        symbol = f"{contract}_SWAP_OKX"
        try:
            main_engine.get_gateway('OKX').rest_api.set_leverage(symbol, leverage)
            main_engine.write_log(f"Set leverage for {symbol} to {leverage}")
            print(f"Set leverage for {symbol} to {leverage}")
            # Small delay to avoid rate limiting
            sleep(0.5)
        except Exception as e:
            main_engine.write_log(f"Error setting leverage for {symbol}: {str(e)}")
            print(f"Error setting leverage for {symbol}: {str(e)}")

    print("Leverage setting completed for all contracts")
    main_engine.write_log("Leverage setting completed for all contracts")

    # Close connection
    main_engine.close()


if __name__ == "__main__":
    # Add the path to the okx gateway module
    # sys.path.append('/opt/external_lib/prod_gateway_okx')
    from prod.okx_gateway import OkxGateway

    run_set_leverage()

