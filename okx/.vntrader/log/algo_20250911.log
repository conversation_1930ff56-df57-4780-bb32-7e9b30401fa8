2025-09-11T17:48:33.483117+0800  Level 20: [AlgoEngine.load_setting:130] 算法交易引擎配置加载完成：
{'VolumeFollowAlgo': {'price_add_percent': 2.0, 'min_notional_multiplier': 2.0, 'volume_ratio': 0.2}, 'VolumeFollowSyncAlgo': {'price_add_percent': 0.5, 'min_notional_multiplier': 2.0, 'volume_ratio': 0.2, 'max_order_wait': 2.0}, 'TwapAlgo': {}, 'IcebergAlgo': {}, 'SniperAlgo': {}, 'StopAlgo': {}, 'BestLimitAlgo': {}}
2025-09-11T17:48:34.775185+0800  Level 20: [AlgoEngine.resume_algo_orders:391] 恢复算法单完成
2025-09-11T17:48:34.821392+0800  Level 20: [AlgoEngine.start:159] 算法交易引擎启动完成
2025-09-11T17:48:37.402242+0800  Level 20: [AlgoEngine.process_contract_event:421] 合约就绪: BTCUSDT.BINANCE, 处理该合约的所有待启动算法
2025-09-11T17:48:37.479971+0800  Level 20: [AlgoEngine.process_contract_event:421] 合约就绪: ETHUSDT.BINANCE, 处理该合约的所有待启动算法
2025-09-11T17:56:49.460956+0800  Level 20: [AlgoEngine.load_setting:130] 算法交易引擎配置加载完成：
{'VolumeFollowAlgo': {'price_add_percent': 2.0, 'min_notional_multiplier': 2.0, 'volume_ratio': 0.2}, 'VolumeFollowSyncAlgo': {'price_add_percent': 0.5, 'min_notional_multiplier': 2.0, 'volume_ratio': 0.2, 'max_order_wait': 2.0}, 'TwapAlgo': {}, 'IcebergAlgo': {}, 'SniperAlgo': {}, 'StopAlgo': {}, 'BestLimitAlgo': {}}
2025-09-11T17:56:50.886992+0800  Level 20: [AlgoEngine.resume_algo_orders:391] 恢复算法单完成
2025-09-11T17:56:50.932070+0800  Level 20: [AlgoEngine.start:159] 算法交易引擎启动完成
2025-09-11T17:56:53.534989+0800  Level 20: [AlgoEngine.process_contract_event:421] 合约就绪: BTCUSDT.BINANCE, 处理该合约的所有待启动算法
2025-09-11T17:56:53.581487+0800  Level 20: [AlgoEngine.process_contract_event:421] 合约就绪: ETHUSDT.BINANCE, 处理该合约的所有待启动算法
2025-09-11T17:56:59.634003+0800  Level 20: [AlgoEngine.stop_all:602] 算法交易引擎安全停止完成
2025-09-11T18:04:41.936474+0800  Level 20: [AlgoEngine.load_setting:130] 算法交易引擎配置加载完成：
{'VolumeFollowAlgo': {'price_add_percent': 2.0, 'min_notional_multiplier': 2.0, 'volume_ratio': 0.2}, 'VolumeFollowSyncAlgo': {'price_add_percent': 0.5, 'min_notional_multiplier': 2.0, 'volume_ratio': 0.2, 'max_order_wait': 2.0}, 'TwapAlgo': {}, 'IcebergAlgo': {}, 'SniperAlgo': {}, 'StopAlgo': {}, 'BestLimitAlgo': {}}
2025-09-11T18:04:43.696185+0800  Level 20: [AlgoEngine.resume_algo_orders:391] 恢复算法单完成
2025-09-11T18:04:43.758960+0800  Level 20: [AlgoEngine.start:159] 算法交易引擎启动完成
2025-09-11T18:04:46.513141+0800  Level 20: [AlgoEngine.start_algo_with_retry:489] 合约未就绪，保持在等待集合中：ETHUSDT.BINANCE
