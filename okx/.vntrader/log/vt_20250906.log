2025-09-06T14:18:30.140101+0800  Level 20: 添加录制引擎
2025-09-06T14:18:30.389622+0800  INFO: Redis连接初始化成功: localhost:6380/0
2025-09-06T14:18:30.405518+0800  INFO: 已创建 2 个测试用户
2025-09-06T14:18:30.407522+0800  INFO: 测试模式已启用，将自动生成测试订单
2025-09-06T14:18:30.409518+0800  INFO: 订单分发器启动成功
2025-09-06T14:18:30.410516+0800  INFO: 分发线程已启动
2025-09-06T14:18:30.412022+0800  INFO: 订单分发器已启动，测试模式：开启
2025-09-06T14:18:30.911701+0800  Level 20: [RestApi.init:148] REST客户端初始化，地址：https://www.okx.com
2025-09-06T14:18:30.911701+0800  Level 20: [RestApi.init:152] 使用代理：http://127.0.0.1:7890
2025-09-06T14:18:30.912722+0800  Level 20: [RestApi.start:160] 创建新的事件循环
2025-09-06T14:18:30.912722+0800  Level 20: [RestApi.start_event_loop:392] 启动事件循环后台线程
2025-09-06T14:18:30.913668+0800  Level 20: [RestApi.run_event_loop:403] 事件循环开始运行
2025-09-06T14:18:30.914738+0800  Level 20: [OkxGateway.connect:524] REST API started
2025-09-06T14:18:30.914738+0800  Level 20: OKX网关添加成功
2025-09-06T14:18:31.106537+0800  Level 20: Binance网关添加成功
2025-09-06T14:18:31.107609+0800  Level 20: [RestApi._get_response:269] 创建新的TCP连接器
2025-09-06T14:18:31.122687+0800  Level 20: [RestApi._get_response:276] 创建新的客户端会话
2025-09-06T14:18:31.138245+0800  Level 20: 添加Bar生成引擎
2025-09-06T14:18:31.504970+0800  Level 20: 算法交易引擎创建成功
2025-09-06T14:18:31.566933+0800  Level 20: Binance-OKX算法交易应用已启动
2025-09-06T14:18:32.651054+0800  Level 20: [OkxGateway.on_query_contract:775] SWAP contract data received
2025-09-06T14:18:32.652573+0800  Level 20: [PublicApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-06T14:18:32.680979+0800  Level 20: [PublicApi.init:110] WebSocket客户端初始化，地址：wss://wspap.okx.com:8443/ws/v5/public?brokerId=9999
2025-09-06T14:18:32.681516+0800  Level 20: [PublicApi.run:191] 尝试连接到服务器：wss://wspap.okx.com:8443/ws/v5/public?brokerId=9999
2025-09-06T14:18:32.726276+0800  Level 20: [PublicApi.start:122] WebSocket客户端启动
2025-09-06T14:18:32.772192+0800  Level 20: [PrivateApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-06T14:18:32.802782+0800  Level 20: [PrivateApi.init:110] WebSocket客户端初始化，地址：wss://wspap.okx.com:8443/ws/v5/private?brokerId=9999
2025-09-06T14:18:32.818555+0800  Level 20: [PrivateApi.run:191] 尝试连接到服务器：wss://wspap.okx.com:8443/ws/v5/private?brokerId=9999
2025-09-06T14:18:32.878439+0800  Level 20: [PrivateApi.start:122] WebSocket客户端启动
2025-09-06T14:18:32.908718+0800  Level 20: [OkxGateway.on_query_time:603] server time: 2025-09-06 14:18:31.921000, local time: 2025-09-06 14:18:32.591685
2025-09-06T14:18:33.062435+0800  Level 20: [OkxGateway.on_query_orders:630] No open orders found
2025-09-06T14:18:34.981739+0800  Level 20: [PrivateApi.on_open:160] WebSocket连接建立成功
2025-09-06T14:18:35.011766+0800  Level 20: [OkxGateway.on_connected:1258] Private websocket API connected
2025-09-06T14:18:35.394440+0800  Level 20: [OkxGateway.on_login:1333] Private API login successful
2025-09-06T14:18:35.424895+0800  Level 20: [PublicApi.on_open:160] WebSocket连接建立成功
2025-09-06T14:18:35.456527+0800  Level 20: [OkxGateway.on_connected:1055] Public API connected
2025-09-06T14:18:40.654484+0800  INFO: 已生成2个测试订单，ref=101, BTCUSDT.BINANCE
2025-09-06T14:18:40.670033+0800  Level 20: [RecorderEngine.process_timer_event:284] bar: []
2025-09-06T14:18:40.793229+0800  Level 20: [RecorderEngine.process_timer_event:291] agg_bar: []
2025-09-06T14:18:40.808520+0800  Level 20: [RecorderEngine.process_timer_event:298] tick: []
2025-09-06T14:18:40.824109+0800  Level 20: [RecorderEngine.process_timer_event:305] order_error: []
2025-09-06T14:18:43.331076+0800  INFO: [1]分发Redis，用户:zhuser1，组:2，ref:101，BTCUSDT.BINANCE 多
2025-09-06T14:18:47.286587+0800  INFO: [1]组2，ref 101 已完成(状态:12)，发送下一个
2025-09-06T14:18:47.427165+0800  INFO: [2]分发Redis，用户:Xiey，组:2，ref:101，BTCUSDT.BINANCE 多
2025-09-06T14:18:50.626664+0800  INFO: 已生成2个测试订单，ref=102, BTCUSDT.BINANCE
2025-09-06T14:18:51.231907+0800  INFO: [4]分发Redis，用户:Xiey，组:2，ref:102，BTCUSDT.BINANCE 空
2025-09-06T14:18:51.414476+0800  Level 20: [RecorderEngine.process_timer_event:284] bar: []
2025-09-06T14:18:51.445535+0800  Level 20: [RecorderEngine.process_timer_event:291] agg_bar: []
2025-09-06T14:18:51.490587+0800  Level 20: [RecorderEngine.process_timer_event:298] tick: []
2025-09-06T14:18:51.536423+0800  Level 20: [RecorderEngine.process_timer_event:305] order_error: []
2025-09-06T14:18:57.803274+0800  WARNING: [2]分发超10秒未接收，跳至下一个
2025-09-06T14:18:58.570560+0800  WARNING: [2]分发超10秒未接收，跳至下一个
2025-09-06T14:18:59.075109+0800  WARNING: [2]分发超10秒未接收，跳至下一个
2025-09-06T14:18:59.523638+0800  WARNING: [2]分发超10秒未接收，跳至下一个
2025-09-06T14:18:59.785336+0800  WARNING: [2]分发超10秒未接收，跳至下一个
2025-09-06T14:19:00.297851+0800  WARNING: [2]分发超10秒未接收，跳至下一个
2025-09-06T14:19:00.313595+0800  Level 20:  ======1======record bar memory: 2025-09-06-141900: ETHUSDT.BINANCE 2025-09-06 14:18:00+08:00 o:4296.71 h:4298.26 l:4296.64 c:4296.96
2025-09-06T14:19:00.770001+0800  INFO: 已生成2个测试订单，ref=103, ETHUSDT.BINANCE
2025-09-06T14:19:00.936832+0800  WARNING: [2]分发超10秒未接收，跳至下一个
2025-09-06T14:19:01.137592+0800  INFO: [5]分发Redis，用户:zhuser1，组:2，ref:103，ETHUSDT.BINANCE 多
2025-09-06T14:19:01.577900+0800  INFO: [6]分发Redis，用户:Xiey，组:2，ref:103，ETHUSDT.BINANCE 空
2025-09-06T14:19:01.938230+0800  WARNING: [2]分发超10秒未接收，跳至下一个
2025-09-06T14:19:02.230544+0800  Level 20: [RecorderEngine.process_timer_event:284] bar: []
2025-09-06T14:19:02.230544+0800  WARNING: [4]分发超10秒未接收，跳至下一个
2025-09-06T14:19:02.261808+0800  Level 20: [RecorderEngine.process_timer_event:291] agg_bar: []
2025-09-06T14:19:02.376948+0800  Level 20: [RecorderEngine.process_timer_event:298] tick: []
2025-09-06T14:19:02.504658+0800  Level 20: [RecorderEngine.process_timer_event:305] order_error: []
2025-09-06T14:19:02.968937+0800  Level 20:  ======1======record bar memory: 2025-09-06-141902: ACHUSDT.BINANCE 2025-09-06 14:18:00+08:00 o:0.019303 h:0.019312 l:0.019303 c:0.019312
2025-09-06T14:19:03.198134+0800  WARNING: [2]分发超10秒未接收，跳至下一个
2025-09-06T14:19:03.489351+0800  WARNING: [4]分发超10秒未接收，跳至下一个
2025-09-06T14:19:04.184127+0800  WARNING: [2]分发超10秒未接收，跳至下一个
2025-09-06T14:19:04.444265+0800  WARNING: [4]分发超10秒未接收，跳至下一个
2025-09-06T14:19:05.022458+0800  WARNING: [2]分发超10秒未接收，跳至下一个
2025-09-06T14:19:05.481638+0800  WARNING: [4]分发超10秒未接收，跳至下一个
2025-09-06T14:19:05.944226+0800  WARNING: [2]分发超10秒未接收，跳至下一个
2025-09-06T14:19:06.252626+0800  WARNING: [4]分发超10秒未接收，跳至下一个
2025-09-06T14:19:06.515543+0800  INFO: [5]组2，ref 103 已完成(状态:12)，发送下一个
2025-09-06T14:19:06.964431+0800  WARNING: [2]分发超10秒未接收，跳至下一个
2025-09-06T14:19:07.089649+0800  WARNING: [4]分发超10秒未接收，跳至下一个
2025-09-06T14:19:07.444727+0800  WARNING: [2]分发超10秒未接收，跳至下一个
2025-09-06T14:19:07.630041+0800  WARNING: [4]分发超10秒未接收，跳至下一个
2025-09-06T14:19:08.089365+0800  WARNING: [2]分发超10秒未接收，跳至下一个
2025-09-06T14:19:08.291288+0800  WARNING: [4]分发超10秒未接收，跳至下一个
2025-09-06T14:19:08.767950+0800  WARNING: [2]分发超10秒未接收，跳至下一个
2025-09-06T14:19:08.875108+0800  WARNING: [4]分发超10秒未接收，跳至下一个
2025-09-06T14:19:09.272720+0800  WARNING: [2]分发超10秒未接收，跳至下一个
2025-09-06T14:19:09.336220+0800  WARNING: [4]分发超10秒未接收，跳至下一个
2025-09-06T14:19:09.685613+0800  WARNING: [2]分发超10秒未接收，跳至下一个
2025-09-06T14:19:09.779057+0800  WARNING: [4]分发超10秒未接收，跳至下一个
2025-09-06T14:19:10.226338+0800  WARNING: [2]分发超10秒未接收，跳至下一个
2025-09-06T14:19:10.319754+0800  WARNING: [4]分发超10秒未接收，跳至下一个
2025-09-06T14:19:10.703251+0800  WARNING: [2]分发超10秒未接收，跳至下一个
2025-09-06T14:19:10.997171+0800  WARNING: [4]分发超10秒未接收，跳至下一个
2025-09-06T14:19:11.227727+0800  INFO: 已生成2个测试订单，ref=104, ETHUSDT.BINANCE
2025-09-06T14:19:11.545666+0800  WARNING: [2]分发超10秒未接收，跳至下一个
2025-09-06T14:19:11.790884+0800  WARNING: [4]分发超10秒未接收，跳至下一个
2025-09-06T14:19:12.023180+0800  WARNING: [6]分发超10秒未接收，跳至下一个
2025-09-06T14:19:12.382691+0800  INFO: [7]分发Redis，用户:zhuser1，组:2，ref:104，ETHUSDT.BINANCE 多
2025-09-06T14:19:12.612442+0800  WARNING: [2]分发超10秒未接收，跳至下一个
2025-09-06T14:19:12.831286+0800  WARNING: [4]分发超10秒未接收，跳至下一个
2025-09-06T14:19:13.018048+0800  WARNING: [6]分发超10秒未接收，跳至下一个
2025-09-06T14:19:13.220346+0800  Level 20: [RecorderEngine.process_timer_event:284] bar: []
2025-09-06T14:19:13.235354+0800  Level 20: [RecorderEngine.process_timer_event:291] agg_bar: []
2025-09-06T14:19:13.250008+0800  Level 20: [RecorderEngine.process_timer_event:298] tick: []
2025-09-06T14:19:13.280561+0800  Level 20: [RecorderEngine.process_timer_event:305] order_error: []
2025-09-06T14:19:13.374250+0800  WARNING: [2]分发超10秒未接收，跳至下一个
2025-09-06T14:19:13.467181+0800  WARNING: [4]分发超10秒未接收，跳至下一个
2025-09-06T14:19:13.664190+0800  WARNING: [6]分发超10秒未接收，跳至下一个
2025-09-06T14:19:14.187540+0800  WARNING: [2]分发超10秒未接收，跳至下一个
2025-09-06T14:19:14.480264+0800  WARNING: [4]分发超10秒未接收，跳至下一个
2025-09-06T14:19:14.664691+0800  WARNING: [6]分发超10秒未接收，跳至下一个
2025-09-06T14:19:15.082873+0800  WARNING: [2]分发超10秒未接收，跳至下一个
2025-09-06T14:19:15.308372+0800  WARNING: [4]分发超10秒未接收，跳至下一个
2025-09-06T14:19:15.415783+0800  WARNING: [6]分发超10秒未接收，跳至下一个
2025-09-06T14:19:15.862207+0800  WARNING: [2]分发超10秒未接收，跳至下一个
2025-09-06T14:19:16.035819+0800  WARNING: [4]分发超10秒未接收，跳至下一个
2025-09-06T14:19:16.174237+0800  WARNING: [6]分发超10秒未接收，跳至下一个
2025-09-06T14:19:16.483426+0800  WARNING: [2]分发超10秒未接收，跳至下一个
2025-09-06T14:19:16.591740+0800  WARNING: [4]分发超10秒未接收，跳至下一个
2025-09-06T14:19:16.729641+0800  WARNING: [6]分发超10秒未接收，跳至下一个
2025-09-06T14:19:17.052324+0800  Level 20: [warning] ETHUSDT_SWAP_OKX traded_change rounded: 0.019999999999999997 -> 0.02
2025-09-06T14:19:17.098451+0800  Level 20: [warning] ETHUSDT_SWAP_OKX traded_change rounded: 0.010000000000000002 -> 0.01
2025-09-06T14:19:17.114026+0800  Level 20: [warning] ETHUSDT_SWAP_OKX traded_change rounded: 0.010000000000000002 -> 0.01
2025-09-06T14:19:17.189319+0800  WARNING: [2]分发超10秒未接收，跳至下一个
2025-09-06T14:19:17.295535+0800  WARNING: [4]分发超10秒未接收，跳至下一个
2025-09-06T14:19:17.494263+0800  WARNING: [6]分发超10秒未接收，跳至下一个
2025-09-06T14:19:17.865037+0800  WARNING: [2]分发超10秒未接收，跳至下一个
2025-09-06T14:19:17.973625+0800  WARNING: [4]分发超10秒未接收，跳至下一个
2025-09-06T14:19:18.218185+0800  WARNING: [6]分发超10秒未接收，跳至下一个
2025-09-06T14:19:18.341419+0800  INFO: [7]组2，ref 104 已完成(状态:12)，发送下一个
2025-09-06T14:19:18.738814+0800  Level 20: 收到信号: SIGINT，准备关闭应用...
2025-09-06T14:19:18.816407+0800  WARNING: [2]分发超10秒未接收，跳至下一个
2025-09-06T14:19:18.938343+0800  WARNING: [4]分发超10秒未接收，跳至下一个
2025-09-06T14:19:19.107753+0800  WARNING: [6]分发超10秒未接收，跳至下一个
2025-09-06T14:19:19.260660+0800  Level 20: 应用正在关闭...
2025-09-06T14:19:19.275473+0800  INFO: 订单分发器正在停止...
2025-09-06T14:19:19.289878+0800  Level 20: 收到信号: SIGINT，准备关闭应用...
2025-09-06T14:19:19.398805+0800  INFO: 订单分发器已停止
2025-09-06T14:19:19.414300+0800  INFO: 订单分发器已停止
2025-09-06T14:19:19.566222+0800  Level 20: 收到信号: SIGINT，准备关闭应用...
2025-09-06T14:19:19.917805+0800  Level 20: 收到信号: SIGINT，准备关闭应用...
2025-09-06T14:19:19.963370+0800  Level 20: [OkxGateway.on_query_orders:628] No open orders found to cancel
