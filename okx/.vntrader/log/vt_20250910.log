2025-09-10 09:59:42.486 | INFO |  | Tick event handler registered
2025-09-10 09:59:42.486 | INFO |  | okx Gateway added successfully
2025-09-10 09:59:42.486 | INFO |  | Gateway configuration loaded
2025-09-10 09:59:42.486 | INFO | OKX | [RestApi.init:148] REST客户端初始化，地址：https://www.okx.com
2025-09-10 09:59:42.487 | INFO | OKX | [RestApi.init:152] 使用代理：http://127.0.0.1:7890
2025-09-10 09:59:42.487 | INFO | OKX | [RestApi.start:160] 创建新的事件循环
2025-09-10 09:59:42.487 | INFO | OKX | [RestApi.start_event_loop:392] 启动事件循环后台线程
2025-09-10 09:59:42.487 | INFO | OKX | [RestApi.run_event_loop:403] 事件循环开始运行
2025-09-10 09:59:42.487 | INFO | OKX | [OkxGateway.connect:524] REST API started
2025-09-10 09:59:42.489 | INFO |  | Connected to okx gateway
2025-09-10 09:59:42.499 | INFO | OKX | [RestApi._get_response:269] 创建新的TCP连接器
2025-09-10 09:59:42.499 | INFO | OKX | [RestApi._get_response:276] 创建新的客户端会话
2025-09-10 09:59:42.864 | INFO | OKX | [OkxGateway.on_query_contract:775] SWAP contract data received
2025-09-10 09:59:42.864 | INFO | OKX | [PublicApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10 09:59:42.864 | INFO | OKX | [PublicApi.init:110] WebSocket客户端初始化，地址：wss://wspap.okx.com:8443/ws/v5/public?brokerId=9999
2025-09-10 09:59:42.865 | INFO | OKX | [PublicApi.run:191] 尝试连接到服务器：wss://wspap.okx.com:8443/ws/v5/public?brokerId=9999
2025-09-10 09:59:42.865 | INFO | OKX | [PublicApi.start:122] WebSocket客户端启动
2025-09-10 09:59:42.866 | INFO | OKX | [PrivateApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10 09:59:42.866 | INFO | OKX | [PrivateApi.init:110] WebSocket客户端初始化，地址：wss://wspap.okx.com:8443/ws/v5/private?brokerId=9999
2025-09-10 09:59:42.867 | INFO | OKX | [PrivateApi.run:191] 尝试连接到服务器：wss://wspap.okx.com:8443/ws/v5/private?brokerId=9999
2025-09-10 09:59:42.867 | INFO | OKX | [PrivateApi.start:122] WebSocket客户端启动
2025-09-10 09:59:42.952 | INFO | OKX | [OkxGateway.on_query_time:603] server time: 2025-09-10 09:59:42.702000, local time: 2025-09-10 09:59:42.952211
2025-09-10 09:59:43.062 | INFO | OKX | [OkxGateway.on_query_orders:630] No open orders found
2025-09-10 09:59:43.699 | INFO | OKX | [PublicApi.on_open:160] WebSocket连接建立成功
2025-09-10 09:59:43.699 | INFO | OKX | [OkxGateway.on_connected:1055] Public API connected
2025-09-10 09:59:43.702 | INFO | OKX | [PrivateApi.on_open:160] WebSocket连接建立成功
2025-09-10 09:59:43.703 | INFO | OKX | [OkxGateway.on_connected:1258] Private websocket API connected
2025-09-10 09:59:43.933 | INFO | OKX | [OkxGateway.on_login:1334] Private API login successful
2025-09-10 09:59:45.489 | INFO |  | ***Querying account and position***
2025-09-10 09:59:45.489 | INFO |  | [AccountData(gateway_name='OKX', extra=None, accountid='BTC', balance=1.********, frozen=0.0), AccountData(gateway_name='OKX', extra=None, accountid='OKB', balance=100.0, frozen=0.0), AccountData(gateway_name='OKX', extra=None, accountid='USDT', balance=4990.************, frozen=166.**************), AccountData(gateway_name='OKX', extra=None, accountid='ETH', balance=1.0, frozen=0.0)]
2025-09-10 09:59:45.489 | INFO |  | [PositionData(gateway_name='OKX', extra=None, symbol='BTCUSDT_SWAP_OKX', exchange=<Exchange.GLOBAL: 'GLOBAL'>, direction=<Direction.NET: '净'>, volume=0.04, frozen=0, price=110050.0, pnl=0.3738, yd_volume=0), PositionData(gateway_name='OKX', extra=None, symbol='ETHUSDT_SWAP_OKX', exchange=<Exchange.GLOBAL: 'GLOBAL'>, direction=<Direction.NET: '净'>, volume=1.05, frozen=0, price=4307.************, pnl=2.****************, yd_volume=0)]
2025-09-10 09:59:45.489 | INFO | OKX | [OkxGateway.subscribe:976] Failed to subscribe data, symbol not found: DOGE-USDT
2025-09-10 09:59:45.490 | INFO |  | Subscribed to DOGE-USDT.GLOBAL
2025-09-10 10:00:45.058 | INFO |  | Tick event handler registered
2025-09-10 10:00:45.059 | INFO |  | okx Gateway added successfully
2025-09-10 10:00:45.059 | INFO |  | Gateway configuration loaded
2025-09-10 10:00:45.059 | INFO | OKX | [RestApi.init:148] REST客户端初始化，地址：https://www.okx.com
2025-09-10 10:00:45.059 | INFO | OKX | [RestApi.init:152] 使用代理：http://127.0.0.1:7890
2025-09-10 10:00:45.060 | INFO | OKX | [RestApi.start:160] 创建新的事件循环
2025-09-10 10:00:45.060 | INFO | OKX | [RestApi.start_event_loop:392] 启动事件循环后台线程
2025-09-10 10:00:45.060 | INFO | OKX | [RestApi.run_event_loop:403] 事件循环开始运行
2025-09-10 10:00:45.060 | INFO | OKX | [OkxGateway.connect:524] REST API started
2025-09-10 10:00:45.060 | INFO |  | Connected to okx gateway
2025-09-10 10:00:45.071 | INFO | OKX | [RestApi._get_response:269] 创建新的TCP连接器
2025-09-10 10:00:45.072 | INFO | OKX | [RestApi._get_response:276] 创建新的客户端会话
2025-09-10 10:00:45.383 | INFO | OKX | [OkxGateway.on_query_time:603] server time: 2025-09-10 10:00:45.252000, local time: 2025-09-10 10:00:45.383552
2025-09-10 10:00:45.400 | INFO | OKX | [OkxGateway.on_query_contract:775] SWAP contract data received
2025-09-10 10:00:45.400 | INFO | OKX | [PublicApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10 10:00:45.401 | INFO | OKX | [PublicApi.init:110] WebSocket客户端初始化，地址：wss://wspap.okx.com:8443/ws/v5/public?brokerId=9999
2025-09-10 10:00:45.401 | INFO | OKX | [PublicApi.run:191] 尝试连接到服务器：wss://wspap.okx.com:8443/ws/v5/public?brokerId=9999
2025-09-10 10:00:45.402 | INFO | OKX | [PublicApi.start:122] WebSocket客户端启动
2025-09-10 10:00:45.402 | INFO | OKX | [PrivateApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10 10:00:45.402 | INFO | OKX | [PrivateApi.init:110] WebSocket客户端初始化，地址：wss://wspap.okx.com:8443/ws/v5/private?brokerId=9999
2025-09-10 10:00:45.403 | INFO | OKX | [PrivateApi.run:191] 尝试连接到服务器：wss://wspap.okx.com:8443/ws/v5/private?brokerId=9999
2025-09-10 10:00:45.403 | INFO | OKX | [PrivateApi.start:122] WebSocket客户端启动
2025-09-10 10:00:45.600 | INFO | OKX | [OkxGateway.on_query_orders:630] No open orders found
2025-09-10 10:00:45.972 | INFO | OKX | [PublicApi.on_open:160] WebSocket连接建立成功
2025-09-10 10:00:45.973 | INFO | OKX | [OkxGateway.on_connected:1055] Public API connected
2025-09-10 10:00:46.107 | INFO | OKX | [PrivateApi.on_open:160] WebSocket连接建立成功
2025-09-10 10:00:46.108 | INFO | OKX | [OkxGateway.on_connected:1258] Private websocket API connected
2025-09-10 10:00:46.320 | INFO | OKX | [OkxGateway.on_login:1334] Private API login successful
2025-09-10 10:00:48.061 | INFO |  | ***Querying account and position***
2025-09-10 10:00:48.061 | INFO |  | [AccountData(gateway_name='OKX', extra=None, accountid='BTC', balance=1.********, frozen=0.0), AccountData(gateway_name='OKX', extra=None, accountid='OKB', balance=100.0, frozen=0.0), AccountData(gateway_name='OKX', extra=None, accountid='USDT', balance=4990.************, frozen=166.*************), AccountData(gateway_name='OKX', extra=None, accountid='ETH', balance=1.0, frozen=0.0)]
2025-09-10 10:00:48.061 | INFO |  | [PositionData(gateway_name='OKX', extra=None, symbol='BTCUSDT_SWAP_OKX', exchange=<Exchange.GLOBAL: 'GLOBAL'>, direction=<Direction.NET: '净'>, volume=0.04, frozen=0, price=110050.0, pnl=0.****************, yd_volume=0), PositionData(gateway_name='OKX', extra=None, symbol='ETHUSDT_SWAP_OKX', exchange=<Exchange.GLOBAL: 'GLOBAL'>, direction=<Direction.NET: '净'>, volume=1.05, frozen=0, price=4307.************, pnl=2.****************, yd_volume=0)]
2025-09-10 10:00:48.062 | INFO | OKX | [OkxGateway.subscribe:976] Failed to subscribe data, symbol not found: DOGE-USDT
2025-09-10 10:00:48.062 | INFO |  | Subscribed to DOGE-USDT.GLOBAL
2025-09-10 10:00:53.062 | INFO | OKX | [OkxGateway.unsubscribe:1020] Failed to unsubscribe data, symbol not found: DOGE-USDT
2025-09-10 10:00:58.063 | INFO | OKX | [OkxGateway.send_order:1615] Send order failed, symbol not found: DOGE-USDT
2025-09-10 10:01:30.634 | INFO |  | Tick event handler registered
2025-09-10 10:01:30.635 | INFO |  | okx Gateway added successfully
2025-09-10 10:01:30.635 | INFO |  | Gateway configuration loaded
2025-09-10 10:01:30.636 | INFO | OKX | [RestApi.init:148] REST客户端初始化，地址：https://www.okx.com
2025-09-10 10:01:30.636 | INFO | OKX | [RestApi.init:152] 使用代理：http://127.0.0.1:7890
2025-09-10 10:01:30.637 | INFO | OKX | [RestApi.start:160] 创建新的事件循环
2025-09-10 10:01:30.637 | INFO | OKX | [RestApi.start_event_loop:392] 启动事件循环后台线程
2025-09-10 10:01:30.637 | INFO | OKX | [RestApi.run_event_loop:403] 事件循环开始运行
2025-09-10 10:01:30.638 | INFO | OKX | [OkxGateway.connect:524] REST API started
2025-09-10 10:01:30.638 | INFO |  | Connected to okx gateway
2025-09-10 10:01:30.645 | INFO | OKX | [RestApi._get_response:269] 创建新的TCP连接器
2025-09-10 10:01:30.647 | INFO | OKX | [RestApi._get_response:276] 创建新的客户端会话
2025-09-10 10:01:30.954 | INFO | OKX | [OkxGateway.on_query_time:603] server time: 2025-09-10 10:01:30.838000, local time: 2025-09-10 10:01:30.954051
2025-09-10 10:01:30.983 | INFO | OKX | [OkxGateway.on_query_contract:775] SWAP contract data received
2025-09-10 10:01:30.983 | INFO | OKX | [PublicApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10 10:01:30.984 | INFO | OKX | [PublicApi.init:110] WebSocket客户端初始化，地址：wss://wspap.okx.com:8443/ws/v5/public?brokerId=9999
2025-09-10 10:01:30.985 | INFO | OKX | [PublicApi.run:191] 尝试连接到服务器：wss://wspap.okx.com:8443/ws/v5/public?brokerId=9999
2025-09-10 10:01:30.986 | INFO | OKX | [PublicApi.start:122] WebSocket客户端启动
2025-09-10 10:01:30.986 | INFO | OKX | [PrivateApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10 10:01:30.986 | INFO | OKX | [PrivateApi.init:110] WebSocket客户端初始化，地址：wss://wspap.okx.com:8443/ws/v5/private?brokerId=9999
2025-09-10 10:01:30.986 | INFO | OKX | [PrivateApi.run:191] 尝试连接到服务器：wss://wspap.okx.com:8443/ws/v5/private?brokerId=9999
2025-09-10 10:01:30.986 | INFO | OKX | [PrivateApi.start:122] WebSocket客户端启动
2025-09-10 10:01:31.182 | INFO | OKX | [OkxGateway.on_query_orders:630] No open orders found
2025-09-10 10:01:31.556 | INFO | OKX | [PrivateApi.on_open:160] WebSocket连接建立成功
2025-09-10 10:01:31.557 | INFO | OKX | [OkxGateway.on_connected:1258] Private websocket API connected
2025-09-10 10:01:31.621 | INFO | OKX | [PublicApi.on_open:160] WebSocket连接建立成功
2025-09-10 10:01:31.622 | INFO | OKX | [OkxGateway.on_connected:1055] Public API connected
2025-09-10 10:01:31.768 | INFO | OKX | [OkxGateway.on_login:1334] Private API login successful
2025-09-10 10:01:33.638 | INFO |  | ***Querying account and position***
2025-09-10 10:01:33.638 | INFO |  | [AccountData(gateway_name='OKX', extra=None, accountid='BTC', balance=1.********, frozen=0.0), AccountData(gateway_name='OKX', extra=None, accountid='OKB', balance=100.0, frozen=0.0), AccountData(gateway_name='OKX', extra=None, accountid='USDT', balance=4990.************, frozen=166.*************), AccountData(gateway_name='OKX', extra=None, accountid='ETH', balance=1.0, frozen=0.0)]
2025-09-10 10:01:33.639 | INFO |  | [PositionData(gateway_name='OKX', extra=None, symbol='BTCUSDT_SWAP_OKX', exchange=<Exchange.GLOBAL: 'GLOBAL'>, direction=<Direction.NET: '净'>, volume=0.04, frozen=0, price=110050.0, pnl=0.****************, yd_volume=0), PositionData(gateway_name='OKX', extra=None, symbol='ETHUSDT_SWAP_OKX', exchange=<Exchange.GLOBAL: 'GLOBAL'>, direction=<Direction.NET: '净'>, volume=1.05, frozen=0, price=4307.************, pnl=2.***************, yd_volume=0)]
2025-09-10 10:01:33.639 | INFO | OKX | [OkxGateway.subscribe:976] Failed to subscribe data, symbol not found: DOGE_USDT_OKX
2025-09-10 10:01:33.639 | INFO |  | Subscribed to DOGE_USDT_OKX.GLOBAL
2025-09-10 10:01:38.638 | INFO | OKX | [OkxGateway.unsubscribe:1020] Failed to unsubscribe data, symbol not found: DOGE_USDT_OKX
2025-09-10 10:01:55.673 | INFO |  | Tick event handler registered
2025-09-10 10:01:55.674 | INFO |  | okx Gateway added successfully
2025-09-10 10:01:55.674 | INFO |  | Gateway configuration loaded
2025-09-10 10:01:55.675 | INFO | OKX | [RestApi.init:148] REST客户端初始化，地址：https://www.okx.com
2025-09-10 10:01:55.675 | INFO | OKX | [RestApi.init:152] 使用代理：http://127.0.0.1:7890
2025-09-10 10:01:55.675 | INFO | OKX | [RestApi.start:160] 创建新的事件循环
2025-09-10 10:01:55.676 | INFO | OKX | [RestApi.start_event_loop:392] 启动事件循环后台线程
2025-09-10 10:01:55.676 | INFO | OKX | [RestApi.run_event_loop:403] 事件循环开始运行
2025-09-10 10:01:55.676 | INFO | OKX | [OkxGateway.connect:524] REST API started
2025-09-10 10:01:55.677 | INFO |  | Connected to okx gateway
2025-09-10 10:01:55.685 | INFO | OKX | [RestApi._get_response:269] 创建新的TCP连接器
2025-09-10 10:01:55.686 | INFO | OKX | [RestApi._get_response:276] 创建新的客户端会话
2025-09-10 10:01:55.997 | INFO | OKX | [OkxGateway.on_query_contract:775] SWAP contract data received
2025-09-10 10:01:55.999 | INFO | OKX | [PublicApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10 10:01:55.999 | INFO | OKX | [PublicApi.init:110] WebSocket客户端初始化，地址：wss://wspap.okx.com:8443/ws/v5/public?brokerId=9999
2025-09-10 10:01:56.000 | INFO | OKX | [PublicApi.run:191] 尝试连接到服务器：wss://wspap.okx.com:8443/ws/v5/public?brokerId=9999
2025-09-10 10:01:56.000 | INFO | OKX | [PublicApi.start:122] WebSocket客户端启动
2025-09-10 10:01:56.001 | INFO | OKX | [PrivateApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10 10:01:56.001 | INFO | OKX | [PrivateApi.init:110] WebSocket客户端初始化，地址：wss://wspap.okx.com:8443/ws/v5/private?brokerId=9999
2025-09-10 10:01:56.001 | INFO | OKX | [PrivateApi.run:191] 尝试连接到服务器：wss://wspap.okx.com:8443/ws/v5/private?brokerId=9999
2025-09-10 10:01:56.002 | INFO | OKX | [PrivateApi.start:122] WebSocket客户端启动
2025-09-10 10:01:56.093 | INFO | OKX | [OkxGateway.on_query_time:603] server time: 2025-09-10 10:01:55.981000, local time: 2025-09-10 10:01:56.093562
2025-09-10 10:01:56.184 | INFO | OKX | [OkxGateway.on_query_orders:630] No open orders found
2025-09-10 10:01:56.502 | INFO | OKX | [PublicApi.on_open:160] WebSocket连接建立成功
2025-09-10 10:01:56.502 | INFO | OKX | [OkxGateway.on_connected:1055] Public API connected
2025-09-10 10:01:56.503 | INFO | OKX | [PrivateApi.on_open:160] WebSocket连接建立成功
2025-09-10 10:01:56.504 | INFO | OKX | [OkxGateway.on_connected:1258] Private websocket API connected
2025-09-10 10:01:56.698 | INFO | OKX | [OkxGateway.on_login:1334] Private API login successful
2025-09-10 10:01:58.676 | INFO |  | ***Querying account and position***
2025-09-10 10:01:58.676 | INFO |  | [AccountData(gateway_name='OKX', extra=None, accountid='BTC', balance=1.********, frozen=0.0), AccountData(gateway_name='OKX', extra=None, accountid='OKB', balance=100.0, frozen=0.0), AccountData(gateway_name='OKX', extra=None, accountid='USDT', balance=4990.************, frozen=166.**************), AccountData(gateway_name='OKX', extra=None, accountid='ETH', balance=1.0, frozen=0.0)]
2025-09-10 10:01:58.677 | INFO |  | [PositionData(gateway_name='OKX', extra=None, symbol='BTCUSDT_SWAP_OKX', exchange=<Exchange.GLOBAL: 'GLOBAL'>, direction=<Direction.NET: '净'>, volume=0.04, frozen=0, price=110050.0, pnl=0.4328, yd_volume=0), PositionData(gateway_name='OKX', extra=None, symbol='ETHUSDT_SWAP_OKX', exchange=<Exchange.GLOBAL: 'GLOBAL'>, direction=<Direction.NET: '净'>, volume=1.05, frozen=0, price=4307.************, pnl=2.***************, yd_volume=0)]
2025-09-10 10:01:58.677 | INFO | OKX | [OkxGateway.subscribe:976] Failed to subscribe data, symbol not found: DOGEUSDT_OKX
2025-09-10 10:01:58.677 | INFO |  | Subscribed to DOGEUSDT_OKX.GLOBAL
2025-09-10 10:02:03.677 | INFO | OKX | [OkxGateway.unsubscribe:1020] Failed to unsubscribe data, symbol not found: DOGEUSDT_OKX
2025-09-10 10:02:08.677 | INFO | OKX | [OkxGateway.send_order:1615] Send order failed, symbol not found: DOGEUSDT_OKX
2025-09-10 10:03:13.626 | INFO |  | Tick event handler registered
2025-09-10 10:03:13.627 | INFO |  | okx Gateway added successfully
2025-09-10 10:03:13.627 | INFO |  | Gateway configuration loaded
2025-09-10 10:03:13.628 | INFO | OKX | [RestApi.init:148] REST客户端初始化，地址：https://www.okx.com
2025-09-10 10:03:13.629 | INFO | OKX | [RestApi.init:152] 使用代理：http://127.0.0.1:7890
2025-09-10 10:03:13.629 | INFO | OKX | [RestApi.start:160] 创建新的事件循环
2025-09-10 10:03:13.629 | INFO | OKX | [RestApi.start_event_loop:392] 启动事件循环后台线程
2025-09-10 10:03:13.630 | INFO | OKX | [RestApi.run_event_loop:403] 事件循环开始运行
2025-09-10 10:03:13.630 | INFO | OKX | [OkxGateway.connect:524] REST API started
2025-09-10 10:03:13.630 | INFO |  | Connected to okx gateway
2025-09-10 10:03:13.638 | INFO | OKX | [RestApi._get_response:269] 创建新的TCP连接器
2025-09-10 10:03:13.638 | INFO | OKX | [RestApi._get_response:276] 创建新的客户端会话
2025-09-10 10:03:14.001 | INFO | OKX | [OkxGateway.on_query_time:603] server time: 2025-09-10 10:03:13.839000, local time: 2025-09-10 10:03:14.001904
2025-09-10 10:03:14.006 | INFO | OKX | [OkxGateway.on_query_contract:775] SWAP contract data received
2025-09-10 10:03:14.007 | INFO | OKX | [PublicApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10 10:03:14.007 | INFO | OKX | [PublicApi.init:110] WebSocket客户端初始化，地址：wss://wspap.okx.com:8443/ws/v5/public?brokerId=9999
2025-09-10 10:03:14.007 | INFO | OKX | [PublicApi.run:191] 尝试连接到服务器：wss://wspap.okx.com:8443/ws/v5/public?brokerId=9999
2025-09-10 10:03:14.008 | INFO | OKX | [PublicApi.start:122] WebSocket客户端启动
2025-09-10 10:03:14.008 | INFO | OKX | [PrivateApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10 10:03:14.009 | INFO | OKX | [PrivateApi.init:110] WebSocket客户端初始化，地址：wss://wspap.okx.com:8443/ws/v5/private?brokerId=9999
2025-09-10 10:03:14.009 | INFO | OKX | [PrivateApi.run:191] 尝试连接到服务器：wss://wspap.okx.com:8443/ws/v5/private?brokerId=9999
2025-09-10 10:03:14.011 | INFO | OKX | [PrivateApi.start:122] WebSocket客户端启动
2025-09-10 10:03:14.202 | INFO | OKX | [OkxGateway.on_query_orders:630] No open orders found
2025-09-10 10:03:14.530 | INFO | OKX | [PublicApi.on_open:160] WebSocket连接建立成功
2025-09-10 10:03:14.530 | INFO | OKX | [OkxGateway.on_connected:1055] Public API connected
2025-09-10 10:03:14.531 | INFO | OKX | [PrivateApi.on_open:160] WebSocket连接建立成功
2025-09-10 10:03:14.531 | INFO | OKX | [OkxGateway.on_connected:1258] Private websocket API connected
2025-09-10 10:03:14.742 | INFO | OKX | [OkxGateway.on_login:1334] Private API login successful
2025-09-10 10:03:16.629 | INFO |  | ***Querying account and position***
2025-09-10 10:03:16.630 | INFO |  | [AccountData(gateway_name='OKX', extra=None, accountid='BTC', balance=1.********, frozen=0.0), AccountData(gateway_name='OKX', extra=None, accountid='OKB', balance=100.0, frozen=0.0), AccountData(gateway_name='OKX', extra=None, accountid='USDT', balance=4990.************, frozen=166.*************), AccountData(gateway_name='OKX', extra=None, accountid='ETH', balance=1.0, frozen=0.0)]
2025-09-10 10:03:16.630 | INFO |  | [PositionData(gateway_name='OKX', extra=None, symbol='BTCUSDT_SWAP_OKX', exchange=<Exchange.GLOBAL: 'GLOBAL'>, direction=<Direction.NET: '净'>, volume=0.04, frozen=0, price=110050.0, pnl=0.****************, yd_volume=0), PositionData(gateway_name='OKX', extra=None, symbol='ETHUSDT_SWAP_OKX', exchange=<Exchange.GLOBAL: 'GLOBAL'>, direction=<Direction.NET: '净'>, volume=1.05, frozen=0, price=4307.************, pnl=2.***************, yd_volume=0)]
2025-09-10 10:03:16.630 | INFO | OKX | [OkxGateway.subscribe:1005] Subscribed to market data for DOGEUSDT_SWAP_OKX
2025-09-10 10:03:16.631 | INFO |  | Subscribed to DOGEUSDT_SWAP_OKX.GLOBAL
2025-09-10 10:03:21.630 | INFO | OKX | [OkxGateway.unsubscribe:1044] Unsubscribed from market data for DOGEUSDT_SWAP_OKX
2025-09-10 10:06:35.136 | INFO |  | Tick event handler registered
2025-09-10 10:06:35.137 | INFO |  | okx Gateway added successfully
2025-09-10 10:06:35.137 | INFO |  | Gateway configuration loaded
2025-09-10 10:06:35.138 | INFO | OKX | [RestApi.init:148] REST客户端初始化，地址：https://www.okx.com
2025-09-10 10:06:35.138 | INFO | OKX | [RestApi.init:152] 使用代理：http://127.0.0.1:7890
2025-09-10 10:06:35.138 | INFO | OKX | [RestApi.start:160] 创建新的事件循环
2025-09-10 10:06:35.139 | INFO | OKX | [RestApi.start_event_loop:392] 启动事件循环后台线程
2025-09-10 10:06:35.139 | INFO | OKX | [RestApi.run_event_loop:403] 事件循环开始运行
2025-09-10 10:06:35.140 | INFO | OKX | [OkxGateway.connect:524] REST API started
2025-09-10 10:06:35.140 | INFO |  | Connected to okx gateway
2025-09-10 10:06:35.148 | INFO | OKX | [RestApi._get_response:269] 创建新的TCP连接器
2025-09-10 10:06:35.149 | INFO | OKX | [RestApi._get_response:276] 创建新的客户端会话
2025-09-10 10:06:35.445 | INFO | OKX | [OkxGateway.on_query_time:603] server time: 2025-09-10 10:06:35.325000, local time: 2025-09-10 10:06:35.445834
2025-09-10 10:06:35.493 | INFO | OKX | [OkxGateway.on_query_contract:775] SWAP contract data received
2025-09-10 10:06:35.494 | INFO | OKX | [PublicApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10 10:06:35.496 | INFO | OKX | [PublicApi.init:110] WebSocket客户端初始化，地址：wss://wspap.okx.com:8443/ws/v5/public?brokerId=9999
2025-09-10 10:06:35.496 | INFO | OKX | [PublicApi.run:191] 尝试连接到服务器：wss://wspap.okx.com:8443/ws/v5/public?brokerId=9999
2025-09-10 10:06:35.496 | INFO | OKX | [PublicApi.start:122] WebSocket客户端启动
2025-09-10 10:06:35.496 | INFO | OKX | [PrivateApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10 10:06:35.497 | INFO | OKX | [PrivateApi.init:110] WebSocket客户端初始化，地址：wss://wspap.okx.com:8443/ws/v5/private?brokerId=9999
2025-09-10 10:06:35.498 | INFO | OKX | [PrivateApi.run:191] 尝试连接到服务器：wss://wspap.okx.com:8443/ws/v5/private?brokerId=9999
2025-09-10 10:06:35.498 | INFO | OKX | [PrivateApi.start:122] WebSocket客户端启动
2025-09-10 10:06:35.687 | INFO | OKX | [OkxGateway.on_query_orders:630] No open orders found
2025-09-10 10:06:36.033 | INFO | OKX | [PrivateApi.on_open:160] WebSocket连接建立成功
2025-09-10 10:06:36.034 | INFO | OKX | [OkxGateway.on_connected:1258] Private websocket API connected
2025-09-10 10:06:36.230 | INFO | OKX | [PublicApi.on_open:160] WebSocket连接建立成功
2025-09-10 10:06:36.231 | INFO | OKX | [OkxGateway.on_connected:1055] Public API connected
2025-09-10 10:06:36.237 | INFO | OKX | [OkxGateway.on_login:1334] Private API login successful
2025-09-10 10:06:38.139 | INFO |  | ***Querying account and position***
2025-09-10 10:06:38.140 | INFO |  | [AccountData(gateway_name='OKX', extra=None, accountid='BTC', balance=1.********, frozen=0.0), AccountData(gateway_name='OKX', extra=None, accountid='OKB', balance=100.0, frozen=0.0), AccountData(gateway_name='OKX', extra=None, accountid='USDT', balance=4990.************, frozen=166.*************), AccountData(gateway_name='OKX', extra=None, accountid='ETH', balance=1.0, frozen=0.0)]
2025-09-10 10:06:38.140 | INFO |  | [PositionData(gateway_name='OKX', extra=None, symbol='BTCUSDT_SWAP_OKX', exchange=<Exchange.GLOBAL: 'GLOBAL'>, direction=<Direction.NET: '净'>, volume=0.04, frozen=0, price=110050.0, pnl=0.****************, yd_volume=0), PositionData(gateway_name='OKX', extra=None, symbol='ETHUSDT_SWAP_OKX', exchange=<Exchange.GLOBAL: 'GLOBAL'>, direction=<Direction.NET: '净'>, volume=1.05, frozen=0, price=4307.************, pnl=2.***************, yd_volume=0)]
2025-09-10 10:06:38.140 | INFO | OKX | [OkxGateway.subscribe:1005] Subscribed to market data for DOGEUSDT_SWAP_OKX
2025-09-10 10:06:38.141 | INFO |  | Subscribed to DOGEUSDT_SWAP_OKX.GLOBAL
2025-09-10 10:06:43.140 | INFO | OKX | [OkxGateway.unsubscribe:1044] Unsubscribed from market data for DOGEUSDT_SWAP_OKX
2025-09-10 10:06:53.347 | INFO | OKX | [OkxGateway.on_cancel_order:1529] Cancel order failed, status code: 1, message: 
2025-09-10 10:11:35.752 | INFO | OKX | [OkxGateway.on_query_time:603] server time: 2025-09-10 10:11:35.626000, local time: 2025-09-10 10:11:35.752419
2025-09-10 10:16:35.711 | INFO | OKX | [OkxGateway.on_query_time:603] server time: 2025-09-10 10:16:35.602000, local time: 2025-09-10 10:16:35.711123
2025-09-10 10:21:35.806 | INFO | OKX | [OkxGateway.on_query_time:603] server time: 2025-09-10 10:21:35.636000, local time: 2025-09-10 10:21:35.806796
2025-09-10 10:26:36.014 | INFO | OKX | [OkxGateway.on_query_time:603] server time: 2025-09-10 10:26:35.871000, local time: 2025-09-10 10:26:36.014838
2025-09-10 10:31:36.101 | INFO | OKX | [OkxGateway.on_query_time:603] server time: 2025-09-10 10:31:35.976000, local time: 2025-09-10 10:31:36.101423
2025-09-10 16:57:23.431 | Level 20 | Logger | [RestApi.init:148] REST客户端初始化，地址：https://www.okx.com
2025-09-10T16:57:23.431245+0800  Level 20: [RestApi.init:148] REST客户端初始化，地址：https://www.okx.com
2025-09-10 16:57:23.438 | Level 20 | Logger | [RestApi.start:160] 创建新的事件循环
2025-09-10T16:57:23.438751+0800  Level 20: [RestApi.start:160] 创建新的事件循环
2025-09-10 16:57:23.440 | Level 20 | Logger | [RestApi.start_event_loop:392] 启动事件循环后台线程
2025-09-10T16:57:23.440760+0800  Level 20: [RestApi.start_event_loop:392] 启动事件循环后台线程
2025-09-10 16:57:23.440 | Level 20 | Logger | [RestApi.run_event_loop:403] 事件循环开始运行
2025-09-10T16:57:23.440760+0800  Level 20: [RestApi.run_event_loop:403] 事件循环开始运行
2025-09-10 16:57:23.441 | Level 20 | Logger | [OkxGateway.connect:524] REST API started
2025-09-10T16:57:23.441761+0800  Level 20: [OkxGateway.connect:524] REST API started
2025-09-10 16:57:23.459 | Level 20 | Logger | [RestApi._get_response:269] 创建新的TCP连接器
2025-09-10T16:57:23.459915+0800  Level 20: [RestApi._get_response:269] 创建新的TCP连接器
2025-09-10 16:57:23.462 | Level 20 | Logger | [RestApi._get_response:276] 创建新的客户端会话
2025-09-10T16:57:23.462913+0800  Level 20: [RestApi._get_response:276] 创建新的客户端会话
2025-09-10 16:57:23.805 | Level 20 | Logger | [OkxGateway.on_query_contract:775] SWAP contract data received
2025-09-10T16:57:23.805933+0800  Level 20: [OkxGateway.on_query_contract:775] SWAP contract data received
2025-09-10 16:57:23.806 | Level 20 | Logger | [PublicApi.init:110] WebSocket客户端初始化，地址：wss://ws.okx.com:8443/ws/v5/public
2025-09-10T16:57:23.806934+0800  Level 20: [PublicApi.init:110] WebSocket客户端初始化，地址：wss://ws.okx.com:8443/ws/v5/public
2025-09-10 16:57:23.807 | Level 20 | Logger | [PublicApi.run:191] 尝试连接到服务器：wss://ws.okx.com:8443/ws/v5/public
2025-09-10T16:57:23.807934+0800  Level 20: [PublicApi.run:191] 尝试连接到服务器：wss://ws.okx.com:8443/ws/v5/public
2025-09-10 16:57:23.808 | Level 20 | Logger | [PublicApi.start:122] WebSocket客户端启动
2025-09-10T16:57:23.808934+0800  Level 20: [PublicApi.start:122] WebSocket客户端启动
2025-09-10 16:57:23.810 | Level 20 | Logger | [PrivateApi.init:110] WebSocket客户端初始化，地址：wss://ws.okx.com:8443/ws/v5/private
2025-09-10T16:57:23.810128+0800  Level 20: [PrivateApi.init:110] WebSocket客户端初始化，地址：wss://ws.okx.com:8443/ws/v5/private
2025-09-10 16:57:23.810 | Level 20 | Logger | [PrivateApi.run:191] 尝试连接到服务器：wss://ws.okx.com:8443/ws/v5/private
2025-09-10T16:57:23.810632+0800  Level 20: [PrivateApi.run:191] 尝试连接到服务器：wss://ws.okx.com:8443/ws/v5/private
2025-09-10 16:57:23.811 | Level 20 | Logger | [PrivateApi.start:122] WebSocket客户端启动
2025-09-10T16:57:23.811637+0800  Level 20: [PrivateApi.start:122] WebSocket客户端启动
2025-09-10 16:57:23.828 | Level 20 | Logger | [OkxGateway.on_query_time:603] server time: 2025-09-10 16:57:23.373000, local time: 2025-09-10 16:57:23.827347
2025-09-10T16:57:23.828863+0800  Level 20: [OkxGateway.on_query_time:603] server time: 2025-09-10 16:57:23.373000, local time: 2025-09-10 16:57:23.827347
2025-09-10 16:57:23.923 | Level 20 | Logger | [RestApi._get_response:303] 响应内容：{"msg":"Request header OK-ACCESS-PASSPHRASE incorrect.","code":"50105"}
2025-09-10T16:57:23.923098+0800  Level 20: [RestApi._get_response:303] 响应内容：{"msg":"Request header OK-ACCESS-PASSPHRASE incorrect.","code":"50105"}
2025-09-10 16:57:23.923 | Level 20 | Logger | [RestApi._process_request:321] 请求处理失败，状态码：401
2025-09-10T16:57:23.923098+0800  Level 20: [RestApi._process_request:321] 请求处理失败，状态码：401
2025-09-10 16:57:24.213 | Level 20 | Logger | [RestApi._log_error_to_db:369] REST错误已入库，错误码：401
2025-09-10T16:57:24.213083+0800  Level 20: [RestApi._log_error_to_db:369] REST错误已入库，错误码：401
2025-09-10 16:57:24.214 | Level 20 | Logger | [RestApi.on_failed:227] RestClient on failed----------
2025-09-10T16:57:24.214042+0800  Level 20: [RestApi.on_failed:227] RestClient on failed----------
2025-09-10 16:57:24.214 | Level 20 | Logger | [RestApi.on_failed:228] request : GET /api/v5/trade/orders-pending because 401: 
headers: {'OK-ACCESS-KEY': '138a3371-73be-42c9-a718-4039091f6505', 'OK-ACCESS-SIGN': '/KqsQ9r2mDzmay1/FgDjpoczxYj1oNVScO2epICmYuc=', 'OK-ACCESS-TIMESTAMP': '2025-09-10T08:57:23.806Z', 'OK-ACCESS-PASSPHRASE': 'Dfgytr', 'Content-Type': 'application/json'}
params: None
data: 
json: None
response:{"msg":"Request header OK-ACCESS-PASSPHRASE incorrect.","code":"50105"}

2025-09-10T16:57:24.214042+0800  Level 20: [RestApi.on_failed:228] request : GET /api/v5/trade/orders-pending because 401: 
headers: {'OK-ACCESS-KEY': '138a3371-73be-42c9-a718-4039091f6505', 'OK-ACCESS-SIGN': '/KqsQ9r2mDzmay1/FgDjpoczxYj1oNVScO2epICmYuc=', 'OK-ACCESS-TIMESTAMP': '2025-09-10T08:57:23.806Z', 'OK-ACCESS-PASSPHRASE': 'Dfgytr', 'Content-Type': 'application/json'}
params: None
data: 
json: None
response:{"msg":"Request header OK-ACCESS-PASSPHRASE incorrect.","code":"50105"}

2025-09-10 16:57:24.288 | Level 20 | Logger | [RestApi._get_response:303] 响应内容：{"msg":"Request header OK-ACCESS-PASSPHRASE incorrect.","code":"50105"}
2025-09-10T16:57:24.288764+0800  Level 20: [RestApi._get_response:303] 响应内容：{"msg":"Request header OK-ACCESS-PASSPHRASE incorrect.","code":"50105"}
2025-09-10 16:57:24.289 | Level 20 | Logger | [RestApi._process_request:321] 请求处理失败，状态码：401
2025-09-10T16:57:24.289772+0800  Level 20: [RestApi._process_request:321] 请求处理失败，状态码：401
2025-09-10 16:57:24.326 | Level 20 | Logger | [RestApi._log_error_to_db:369] REST错误已入库，错误码：401
2025-09-10T16:57:24.326064+0800  Level 20: [RestApi._log_error_to_db:369] REST错误已入库，错误码：401
2025-09-10 16:57:24.326 | Level 20 | Logger | [RestApi.on_failed:227] RestClient on failed----------
2025-09-10T16:57:24.326064+0800  Level 20: [RestApi.on_failed:227] RestClient on failed----------
2025-09-10 16:57:24.327 | Level 20 | Logger | [RestApi.on_failed:228] request : POST /api/v5/account/set-position-mode because 401: 
headers: {'OK-ACCESS-KEY': '138a3371-73be-42c9-a718-4039091f6505', 'OK-ACCESS-SIGN': '8aQ0h1QUHkT3A57QGt+Ly8P+bxKl9bxdkO41dBpHNZ0=', 'OK-ACCESS-TIMESTAMP': '2025-09-10T08:57:23.480Z', 'OK-ACCESS-PASSPHRASE': 'Dfgytr', 'Content-Type': 'application/json'}
params: None
data: {"posMode": "net_mode"}
json: None
response:{"msg":"Request header OK-ACCESS-PASSPHRASE incorrect.","code":"50105"}

2025-09-10T16:57:24.327068+0800  Level 20: [RestApi.on_failed:228] request : POST /api/v5/account/set-position-mode because 401: 
headers: {'OK-ACCESS-KEY': '138a3371-73be-42c9-a718-4039091f6505', 'OK-ACCESS-SIGN': '8aQ0h1QUHkT3A57QGt+Ly8P+bxKl9bxdkO41dBpHNZ0=', 'OK-ACCESS-TIMESTAMP': '2025-09-10T08:57:23.480Z', 'OK-ACCESS-PASSPHRASE': 'Dfgytr', 'Content-Type': 'application/json'}
params: None
data: {"posMode": "net_mode"}
json: None
response:{"msg":"Request header OK-ACCESS-PASSPHRASE incorrect.","code":"50105"}

2025-09-10 16:57:24.790 | Level 20 | Logger | [PublicApi.on_open:160] WebSocket连接建立成功
2025-09-10T16:57:24.790005+0800  Level 20: [PublicApi.on_open:160] WebSocket连接建立成功
2025-09-10 16:57:24.790 | Level 20 | Logger | [OkxGateway.on_connected:1055] Public API connected
2025-09-10T16:57:24.790005+0800  Level 20: [OkxGateway.on_connected:1055] Public API connected
2025-09-10 16:57:24.802 | Level 20 | Logger | [PrivateApi.on_open:160] WebSocket连接建立成功
2025-09-10T16:57:24.802052+0800  Level 20: [PrivateApi.on_open:160] WebSocket连接建立成功
2025-09-10 16:57:24.803 | Level 20 | Logger | [OkxGateway.on_connected:1258] Private websocket API connected
2025-09-10T16:57:24.803053+0800  Level 20: [OkxGateway.on_connected:1258] Private websocket API connected
2025-09-10 16:57:25.121 | Level 20 | Logger | [OkxGateway.on_api_error:1321] Private API request failed, status code: 50110, message: Your IP ************** is not in linking trusted IP addresses.
2025-09-10T16:57:25.121038+0800  Level 20: [OkxGateway.on_api_error:1321] Private API request failed, status code: 50110, message: Your IP ************** is not in linking trusted IP addresses.
2025-09-10 16:57:25.122 | Level 20 | Logger | [PrivateApi.on_close:164] WebSocket连接断开，状态码：4001，原因：Login failed.
2025-09-10T16:57:25.122040+0800  Level 20: [PrivateApi.on_close:164] WebSocket连接断开，状态码：4001，原因：Login failed.
2025-09-10 16:57:25.175 | Level 20 | Logger | [PrivateApi._log_error_to_db:63] WebSocket错误已入库，错误码：4001
2025-09-10T16:57:25.175004+0800  Level 20: [PrivateApi._log_error_to_db:63] WebSocket错误已入库，错误码：4001
2025-09-10 16:57:25.175 | Level 20 | Logger | [OkxGateway.on_disconnected:1268] Private API disconnected, code: 4001, msg: Login failed.
2025-09-10T16:57:25.175987+0800  Level 20: [OkxGateway.on_disconnected:1268] Private API disconnected, code: 4001, msg: Login failed.
2025-09-10 16:57:43.554 | Level 20 | Logger | [RestApi.init:148] REST客户端初始化，地址：https://www.okx.com
2025-09-10T16:57:43.554547+0800  Level 20: [RestApi.init:148] REST客户端初始化，地址：https://www.okx.com
2025-09-10 16:57:43.555 | Level 20 | Logger | [RestApi.init:152] 使用代理：http://127.0.0.1:7890
2025-09-10T16:57:43.555551+0800  Level 20: [RestApi.init:152] 使用代理：http://127.0.0.1:7890
2025-09-10 16:57:43.556 | Level 20 | Logger | [RestApi.start:160] 创建新的事件循环
2025-09-10T16:57:43.556547+0800  Level 20: [RestApi.start:160] 创建新的事件循环
2025-09-10 16:57:43.557 | Level 20 | Logger | [RestApi.start_event_loop:392] 启动事件循环后台线程
2025-09-10T16:57:43.557546+0800  Level 20: [RestApi.start_event_loop:392] 启动事件循环后台线程
2025-09-10 16:57:43.558 | Level 20 | Logger | [RestApi.run_event_loop:403] 事件循环开始运行
2025-09-10T16:57:43.558548+0800  Level 20: [RestApi.run_event_loop:403] 事件循环开始运行
2025-09-10 16:57:43.559 | Level 20 | Logger | [OkxGateway.connect:524] REST API started
2025-09-10T16:57:43.559796+0800  Level 20: [OkxGateway.connect:524] REST API started
2025-09-10 16:57:43.559 | Level 20 | Logger | [RestApi._process_request:338] 请求处理异常：Timeout context manager should be used inside a task
2025-09-10T16:57:43.559796+0800  Level 20: [RestApi._process_request:338] 请求处理异常：Timeout context manager should be used inside a task
2025-09-10 16:57:43.602 | Level 20 | Logger | [RestApi._log_error_to_db:369] REST错误已入库，错误码：5
2025-09-10T16:57:43.602719+0800  Level 20: [RestApi._log_error_to_db:369] REST错误已入库，错误码：5
2025-09-10 16:57:43.604 | Level 20 | Logger | [OkxGateway.on_error:807] Exception catched by REST API: [2025-09-10T16:57:43.602719]: Unhandled RestClient Error:<class 'RuntimeError'>
request:request : GET /api/v5/public/time because terminated: 
headers: None
params: None
data: None
json: None
response:

Exception trace: 
Traceback (most recent call last):
  File "S:\OneDrive - lancely\芷瀚同步\开发\币\okx\prod\rest_client.py", line 311, in _process_request
    response: Response = await self._get_response(request)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\OneDrive - lancely\芷瀚同步\开发\币\okx\prod\rest_client.py", line 287, in _get_response
    cr: ClientResponse = await self.session.request(
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\client.py", line 622, in _request
    with timer:
         ^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\helpers.py", line 650, in __enter__
    raise RuntimeError("Timeout context manager should be used inside a task")
RuntimeError: Timeout context manager should be used inside a task

2025-09-10T16:57:43.604720+0800  Level 20: [OkxGateway.on_error:807] Exception catched by REST API: [2025-09-10T16:57:43.602719]: Unhandled RestClient Error:<class 'RuntimeError'>
request:request : GET /api/v5/public/time because terminated: 
headers: None
params: None
data: None
json: None
response:

Exception trace: 
Traceback (most recent call last):
  File "S:\OneDrive - lancely\芷瀚同步\开发\币\okx\prod\rest_client.py", line 311, in _process_request
    response: Response = await self._get_response(request)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\OneDrive - lancely\芷瀚同步\开发\币\okx\prod\rest_client.py", line 287, in _get_response
    cr: ClientResponse = await self.session.request(
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\client.py", line 622, in _request
    with timer:
         ^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\helpers.py", line 650, in __enter__
    raise RuntimeError("Timeout context manager should be used inside a task")
RuntimeError: Timeout context manager should be used inside a task

2025-09-10 16:57:43.605 | Level 20 | Logger | [RestApi._process_request:338] 请求处理异常：Timeout context manager should be used inside a task
2025-09-10T16:57:43.605721+0800  Level 20: [RestApi._process_request:338] 请求处理异常：Timeout context manager should be used inside a task
2025-09-10 16:57:43.633 | Level 20 | Logger | [RestApi._log_error_to_db:369] REST错误已入库，错误码：5
2025-09-10T16:57:43.633429+0800  Level 20: [RestApi._log_error_to_db:369] REST错误已入库，错误码：5
2025-09-10 16:57:43.635 | Level 20 | Logger | [OkxGateway.on_error:807] Exception catched by REST API: [2025-09-10T16:57:43.633429]: Unhandled RestClient Error:<class 'RuntimeError'>
request:request : GET /api/v5/public/instruments because terminated: 
headers: None
params: {'instType': 'SWAP'}
data: None
json: None
response:

Exception trace: 
Traceback (most recent call last):
  File "S:\OneDrive - lancely\芷瀚同步\开发\币\okx\prod\rest_client.py", line 311, in _process_request
    response: Response = await self._get_response(request)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\OneDrive - lancely\芷瀚同步\开发\币\okx\prod\rest_client.py", line 287, in _get_response
    cr: ClientResponse = await self.session.request(
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\client.py", line 622, in _request
    with timer:
         ^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\helpers.py", line 650, in __enter__
    raise RuntimeError("Timeout context manager should be used inside a task")
RuntimeError: Timeout context manager should be used inside a task

2025-09-10T16:57:43.635429+0800  Level 20: [OkxGateway.on_error:807] Exception catched by REST API: [2025-09-10T16:57:43.633429]: Unhandled RestClient Error:<class 'RuntimeError'>
request:request : GET /api/v5/public/instruments because terminated: 
headers: None
params: {'instType': 'SWAP'}
data: None
json: None
response:

Exception trace: 
Traceback (most recent call last):
  File "S:\OneDrive - lancely\芷瀚同步\开发\币\okx\prod\rest_client.py", line 311, in _process_request
    response: Response = await self._get_response(request)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\OneDrive - lancely\芷瀚同步\开发\币\okx\prod\rest_client.py", line 287, in _get_response
    cr: ClientResponse = await self.session.request(
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\client.py", line 622, in _request
    with timer:
         ^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\helpers.py", line 650, in __enter__
    raise RuntimeError("Timeout context manager should be used inside a task")
RuntimeError: Timeout context manager should be used inside a task

2025-09-10 16:57:43.635 | Level 20 | Logger | [RestApi._process_request:338] 请求处理异常：Timeout context manager should be used inside a task
2025-09-10T16:57:43.635429+0800  Level 20: [RestApi._process_request:338] 请求处理异常：Timeout context manager should be used inside a task
2025-09-10 16:57:43.674 | Level 20 | Logger | [RestApi._log_error_to_db:369] REST错误已入库，错误码：5
2025-09-10T16:57:43.674520+0800  Level 20: [RestApi._log_error_to_db:369] REST错误已入库，错误码：5
2025-09-10 16:57:43.675 | Level 20 | Logger | [OkxGateway.on_error:807] Exception catched by REST API: [2025-09-10T16:57:43.673519]: Unhandled RestClient Error:<class 'RuntimeError'>
request:request : POST /api/v5/account/set-position-mode because terminated: 
headers: {'OK-ACCESS-KEY': '138a3371-73be-42c9-a718-4039091f6505', 'OK-ACCESS-SIGN': 'Amfo6PmTEUF6tqe8mGgTRdEsihwk/eAqXTfwu1ltIS8=', 'OK-ACCESS-TIMESTAMP': '2025-09-10T08:57:43.180Z', 'OK-ACCESS-PASSPHRASE': 'Dfgytr', 'Content-Type': 'application/json'}
params: None
data: {"posMode": "net_mode"}
json: None
response:

Exception trace: 
Traceback (most recent call last):
  File "S:\OneDrive - lancely\芷瀚同步\开发\币\okx\prod\rest_client.py", line 311, in _process_request
    response: Response = await self._get_response(request)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\OneDrive - lancely\芷瀚同步\开发\币\okx\prod\rest_client.py", line 287, in _get_response
    cr: ClientResponse = await self.session.request(
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\client.py", line 622, in _request
    with timer:
         ^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\helpers.py", line 650, in __enter__
    raise RuntimeError("Timeout context manager should be used inside a task")
RuntimeError: Timeout context manager should be used inside a task

2025-09-10T16:57:43.675519+0800  Level 20: [OkxGateway.on_error:807] Exception catched by REST API: [2025-09-10T16:57:43.673519]: Unhandled RestClient Error:<class 'RuntimeError'>
request:request : POST /api/v5/account/set-position-mode because terminated: 
headers: {'OK-ACCESS-KEY': '138a3371-73be-42c9-a718-4039091f6505', 'OK-ACCESS-SIGN': 'Amfo6PmTEUF6tqe8mGgTRdEsihwk/eAqXTfwu1ltIS8=', 'OK-ACCESS-TIMESTAMP': '2025-09-10T08:57:43.180Z', 'OK-ACCESS-PASSPHRASE': 'Dfgytr', 'Content-Type': 'application/json'}
params: None
data: {"posMode": "net_mode"}
json: None
response:

Exception trace: 
Traceback (most recent call last):
  File "S:\OneDrive - lancely\芷瀚同步\开发\币\okx\prod\rest_client.py", line 311, in _process_request
    response: Response = await self._get_response(request)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\OneDrive - lancely\芷瀚同步\开发\币\okx\prod\rest_client.py", line 287, in _get_response
    cr: ClientResponse = await self.session.request(
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\client.py", line 622, in _request
    with timer:
         ^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\helpers.py", line 650, in __enter__
    raise RuntimeError("Timeout context manager should be used inside a task")
RuntimeError: Timeout context manager should be used inside a task

2025-09-10 16:57:54.048 | Level 20 | Logger | [RestApi.init:148] REST客户端初始化，地址：https://www.okx.com
2025-09-10T16:57:54.048531+0800  Level 20: [RestApi.init:148] REST客户端初始化，地址：https://www.okx.com
2025-09-10 16:57:54.050 | Level 20 | Logger | [RestApi.init:152] 使用代理：http://127.0.0.1:7890
2025-09-10T16:57:54.050536+0800  Level 20: [RestApi.init:152] 使用代理：http://127.0.0.1:7890
2025-09-10 16:57:54.051 | Level 20 | Logger | [RestApi.start:160] 创建新的事件循环
2025-09-10T16:57:54.051540+0800  Level 20: [RestApi.start:160] 创建新的事件循环
2025-09-10 16:57:54.052 | Level 20 | Logger | [RestApi.start_event_loop:392] 启动事件循环后台线程
2025-09-10T16:57:54.052540+0800  Level 20: [RestApi.start_event_loop:392] 启动事件循环后台线程
2025-09-10 16:57:54.053 | Level 20 | Logger | [RestApi.run_event_loop:403] 事件循环开始运行
2025-09-10T16:57:54.053540+0800  Level 20: [RestApi.run_event_loop:403] 事件循环开始运行
2025-09-10 16:57:54.054 | Level 20 | Logger | [OkxGateway.connect:524] REST API started
2025-09-10T16:57:54.054539+0800  Level 20: [OkxGateway.connect:524] REST API started
2025-09-10 16:57:54.062 | Level 20 | Logger | [RestApi._get_response:269] 创建新的TCP连接器
2025-09-10T16:57:54.062563+0800  Level 20: [RestApi._get_response:269] 创建新的TCP连接器
2025-09-10 16:57:54.064 | Level 20 | Logger | [RestApi._get_response:276] 创建新的客户端会话
2025-09-10T16:57:54.064063+0800  Level 20: [RestApi._get_response:276] 创建新的客户端会话
2025-09-10 16:57:54.213 | Level 20 | Logger | [OkxGateway.on_query_contract:775] SWAP contract data received
2025-09-10T16:57:54.213028+0800  Level 20: [OkxGateway.on_query_contract:775] SWAP contract data received
2025-09-10 16:57:54.214 | Level 20 | Logger | [PublicApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10T16:57:54.214027+0800  Level 20: [PublicApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10 16:57:54.215 | Level 20 | Logger | [PublicApi.init:110] WebSocket客户端初始化，地址：wss://ws.okx.com:8443/ws/v5/public
2025-09-10T16:57:54.215715+0800  Level 20: [PublicApi.init:110] WebSocket客户端初始化，地址：wss://ws.okx.com:8443/ws/v5/public
2025-09-10 16:57:54.217 | Level 20 | Logger | [PublicApi.run:191] 尝试连接到服务器：wss://ws.okx.com:8443/ws/v5/public
2025-09-10T16:57:54.217532+0800  Level 20: [PublicApi.run:191] 尝试连接到服务器：wss://ws.okx.com:8443/ws/v5/public
2025-09-10 16:57:54.219 | Level 20 | Logger | [PublicApi.start:122] WebSocket客户端启动
2025-09-10T16:57:54.219721+0800  Level 20: [PublicApi.start:122] WebSocket客户端启动
2025-09-10 16:57:54.219 | Level 20 | Logger | [PrivateApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10T16:57:54.219721+0800  Level 20: [PrivateApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10 16:57:54.220 | Level 20 | Logger | [PrivateApi.init:110] WebSocket客户端初始化，地址：wss://ws.okx.com:8443/ws/v5/private
2025-09-10T16:57:54.220724+0800  Level 20: [PrivateApi.init:110] WebSocket客户端初始化，地址：wss://ws.okx.com:8443/ws/v5/private
2025-09-10 16:57:54.221 | Level 20 | Logger | [PrivateApi.run:191] 尝试连接到服务器：wss://ws.okx.com:8443/ws/v5/private
2025-09-10T16:57:54.221730+0800  Level 20: [PrivateApi.run:191] 尝试连接到服务器：wss://ws.okx.com:8443/ws/v5/private
2025-09-10 16:57:54.222 | Level 20 | Logger | [PrivateApi.start:122] WebSocket客户端启动
2025-09-10T16:57:54.222770+0800  Level 20: [PrivateApi.start:122] WebSocket客户端启动
2025-09-10 16:57:54.252 | Level 20 | Logger | [OkxGateway.on_query_time:603] server time: 2025-09-10 16:57:53.837000, local time: 2025-09-10 16:57:54.251484
2025-09-10T16:57:54.252488+0800  Level 20: [OkxGateway.on_query_time:603] server time: 2025-09-10 16:57:53.837000, local time: 2025-09-10 16:57:54.251484
2025-09-10 16:57:54.261 | Level 20 | Logger | [RestApi._get_response:303] 响应内容：{"msg":"Request header OK-ACCESS-PASSPHRASE incorrect.","code":"50105"}
2025-09-10T16:57:54.261290+0800  Level 20: [RestApi._get_response:303] 响应内容：{"msg":"Request header OK-ACCESS-PASSPHRASE incorrect.","code":"50105"}
2025-09-10 16:57:54.261 | Level 20 | Logger | [RestApi._process_request:321] 请求处理失败，状态码：401
2025-09-10T16:57:54.261290+0800  Level 20: [RestApi._process_request:321] 请求处理失败，状态码：401
2025-09-10 16:57:54.282 | Level 20 | Logger | [RestApi._log_error_to_db:369] REST错误已入库，错误码：401
2025-09-10T16:57:54.282315+0800  Level 20: [RestApi._log_error_to_db:369] REST错误已入库，错误码：401
2025-09-10 16:57:54.283 | Level 20 | Logger | [RestApi.on_failed:227] RestClient on failed----------
2025-09-10T16:57:54.283316+0800  Level 20: [RestApi.on_failed:227] RestClient on failed----------
2025-09-10 16:57:54.283 | Level 20 | Logger | [RestApi.on_failed:228] request : POST /api/v5/account/set-position-mode because 401: 
headers: {'OK-ACCESS-KEY': '138a3371-73be-42c9-a718-4039091f6505', 'OK-ACCESS-SIGN': 'EKEu/lr5fCN74tbdfL1tCtNkz5Xo+WRIWjplgYCkg8Q=', 'OK-ACCESS-TIMESTAMP': '2025-09-10T08:57:54.065Z', 'OK-ACCESS-PASSPHRASE': 'Dfgytr', 'Content-Type': 'application/json'}
params: None
data: {"posMode": "net_mode"}
json: None
response:{"msg":"Request header OK-ACCESS-PASSPHRASE incorrect.","code":"50105"}

2025-09-10T16:57:54.283316+0800  Level 20: [RestApi.on_failed:228] request : POST /api/v5/account/set-position-mode because 401: 
headers: {'OK-ACCESS-KEY': '138a3371-73be-42c9-a718-4039091f6505', 'OK-ACCESS-SIGN': 'EKEu/lr5fCN74tbdfL1tCtNkz5Xo+WRIWjplgYCkg8Q=', 'OK-ACCESS-TIMESTAMP': '2025-09-10T08:57:54.065Z', 'OK-ACCESS-PASSPHRASE': 'Dfgytr', 'Content-Type': 'application/json'}
params: None
data: {"posMode": "net_mode"}
json: None
response:{"msg":"Request header OK-ACCESS-PASSPHRASE incorrect.","code":"50105"}

2025-09-10 16:57:54.285 | Level 20 | Logger | [RestApi._get_response:303] 响应内容：{"msg":"Request header OK-ACCESS-PASSPHRASE incorrect.","code":"50105"}
2025-09-10T16:57:54.285314+0800  Level 20: [RestApi._get_response:303] 响应内容：{"msg":"Request header OK-ACCESS-PASSPHRASE incorrect.","code":"50105"}
2025-09-10 16:57:54.285 | Level 20 | Logger | [RestApi._process_request:321] 请求处理失败，状态码：401
2025-09-10T16:57:54.285314+0800  Level 20: [RestApi._process_request:321] 请求处理失败，状态码：401
2025-09-10 16:57:54.291 | Level 20 | Logger | [RestApi._log_error_to_db:369] REST错误已入库，错误码：401
2025-09-10T16:57:54.291832+0800  Level 20: [RestApi._log_error_to_db:369] REST错误已入库，错误码：401
2025-09-10 16:57:54.291 | Level 20 | Logger | [RestApi.on_failed:227] RestClient on failed----------
2025-09-10T16:57:54.291832+0800  Level 20: [RestApi.on_failed:227] RestClient on failed----------
2025-09-10 16:57:54.291 | Level 20 | Logger | [RestApi.on_failed:228] request : GET /api/v5/trade/orders-pending because 401: 
headers: {'OK-ACCESS-KEY': '138a3371-73be-42c9-a718-4039091f6505', 'OK-ACCESS-SIGN': 'wI8QV1KXT/uzRbr4/Dq4KPP6qxI3YpFoddR2CV9etMQ=', 'OK-ACCESS-TIMESTAMP': '2025-09-10T08:57:54.216Z', 'OK-ACCESS-PASSPHRASE': 'Dfgytr', 'Content-Type': 'application/json'}
params: None
data: 
json: None
response:{"msg":"Request header OK-ACCESS-PASSPHRASE incorrect.","code":"50105"}

2025-09-10T16:57:54.291832+0800  Level 20: [RestApi.on_failed:228] request : GET /api/v5/trade/orders-pending because 401: 
headers: {'OK-ACCESS-KEY': '138a3371-73be-42c9-a718-4039091f6505', 'OK-ACCESS-SIGN': 'wI8QV1KXT/uzRbr4/Dq4KPP6qxI3YpFoddR2CV9etMQ=', 'OK-ACCESS-TIMESTAMP': '2025-09-10T08:57:54.216Z', 'OK-ACCESS-PASSPHRASE': 'Dfgytr', 'Content-Type': 'application/json'}
params: None
data: 
json: None
response:{"msg":"Request header OK-ACCESS-PASSPHRASE incorrect.","code":"50105"}

2025-09-10 16:57:54.412 | Level 20 | Logger | [PrivateApi.on_open:160] WebSocket连接建立成功
2025-09-10T16:57:54.412536+0800  Level 20: [PrivateApi.on_open:160] WebSocket连接建立成功
2025-09-10 16:57:54.413 | Level 20 | Logger | [OkxGateway.on_connected:1258] Private websocket API connected
2025-09-10T16:57:54.413542+0800  Level 20: [OkxGateway.on_connected:1258] Private websocket API connected
2025-09-10 16:57:54.427 | Level 20 | Logger | [PublicApi.on_open:160] WebSocket连接建立成功
2025-09-10T16:57:54.427303+0800  Level 20: [PublicApi.on_open:160] WebSocket连接建立成功
2025-09-10 16:57:54.427 | Level 20 | Logger | [OkxGateway.on_connected:1055] Public API connected
2025-09-10T16:57:54.427303+0800  Level 20: [OkxGateway.on_connected:1055] Public API connected
2025-09-10 16:57:54.452 | Level 20 | Logger | [OkxGateway.on_api_error:1321] Private API request failed, status code: 60024, message: Wrong passphrase
2025-09-10T16:57:54.452468+0800  Level 20: [OkxGateway.on_api_error:1321] Private API request failed, status code: 60024, message: Wrong passphrase
2025-09-10 16:57:54.453 | Level 20 | Logger | [PrivateApi.on_close:164] WebSocket连接断开，状态码：4001，原因：Login failed.
2025-09-10T16:57:54.453943+0800  Level 20: [PrivateApi.on_close:164] WebSocket连接断开，状态码：4001，原因：Login failed.
2025-09-10 16:57:54.464 | Level 20 | Logger | [PrivateApi._log_error_to_db:63] WebSocket错误已入库，错误码：4001
2025-09-10T16:57:54.464611+0800  Level 20: [PrivateApi._log_error_to_db:63] WebSocket错误已入库，错误码：4001
2025-09-10 16:57:54.465 | Level 20 | Logger | [OkxGateway.on_disconnected:1268] Private API disconnected, code: 4001, msg: Login failed.
2025-09-10T16:57:54.465614+0800  Level 20: [OkxGateway.on_disconnected:1268] Private API disconnected, code: 4001, msg: Login failed.
2025-09-10 16:58:20.104 | Level 20 | Logger | [RestApi.init:148] REST客户端初始化，地址：https://www.okx.com
2025-09-10T16:58:20.104880+0800  Level 20: [RestApi.init:148] REST客户端初始化，地址：https://www.okx.com
2025-09-10 16:58:20.105 | Level 20 | Logger | [RestApi.init:152] 使用代理：http://127.0.0.1:7890
2025-09-10T16:58:20.105880+0800  Level 20: [RestApi.init:152] 使用代理：http://127.0.0.1:7890
2025-09-10 16:58:20.106 | Level 20 | Logger | [RestApi.start:160] 创建新的事件循环
2025-09-10T16:58:20.106881+0800  Level 20: [RestApi.start:160] 创建新的事件循环
2025-09-10 16:58:20.106 | Level 20 | Logger | [RestApi.start_event_loop:392] 启动事件循环后台线程
2025-09-10T16:58:20.106881+0800  Level 20: [RestApi.start_event_loop:392] 启动事件循环后台线程
2025-09-10 16:58:20.106 | Level 20 | Logger | [RestApi.run_event_loop:403] 事件循环开始运行
2025-09-10T16:58:20.106881+0800  Level 20: [RestApi.run_event_loop:403] 事件循环开始运行
2025-09-10 16:58:20.107 | Level 20 | Logger | [OkxGateway.connect:524] REST API started
2025-09-10T16:58:20.107880+0800  Level 20: [OkxGateway.connect:524] REST API started
2025-09-10 16:58:20.108 | Level 20 | Logger | [RestApi._process_request:338] 请求处理异常：Timeout context manager should be used inside a task
2025-09-10T16:58:20.108879+0800  Level 20: [RestApi._process_request:338] 请求处理异常：Timeout context manager should be used inside a task
2025-09-10 16:58:20.120 | Level 20 | Logger | [RestApi._log_error_to_db:369] REST错误已入库，错误码：5
2025-09-10T16:58:20.120097+0800  Level 20: [RestApi._log_error_to_db:369] REST错误已入库，错误码：5
2025-09-10 16:58:20.122 | Level 20 | Logger | [OkxGateway.on_error:807] Exception catched by REST API: [2025-09-10T16:58:20.120097]: Unhandled RestClient Error:<class 'RuntimeError'>
request:request : GET /api/v5/public/time because terminated: 
headers: None
params: None
data: None
json: None
response:

Exception trace: 
Traceback (most recent call last):
  File "S:\OneDrive - lancely\芷瀚同步\开发\币\okx\prod\rest_client.py", line 311, in _process_request
    response: Response = await self._get_response(request)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\OneDrive - lancely\芷瀚同步\开发\币\okx\prod\rest_client.py", line 287, in _get_response
    cr: ClientResponse = await self.session.request(
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\client.py", line 622, in _request
    with timer:
         ^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\helpers.py", line 650, in __enter__
    raise RuntimeError("Timeout context manager should be used inside a task")
RuntimeError: Timeout context manager should be used inside a task

2025-09-10T16:58:20.122108+0800  Level 20: [OkxGateway.on_error:807] Exception catched by REST API: [2025-09-10T16:58:20.120097]: Unhandled RestClient Error:<class 'RuntimeError'>
request:request : GET /api/v5/public/time because terminated: 
headers: None
params: None
data: None
json: None
response:

Exception trace: 
Traceback (most recent call last):
  File "S:\OneDrive - lancely\芷瀚同步\开发\币\okx\prod\rest_client.py", line 311, in _process_request
    response: Response = await self._get_response(request)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\OneDrive - lancely\芷瀚同步\开发\币\okx\prod\rest_client.py", line 287, in _get_response
    cr: ClientResponse = await self.session.request(
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\client.py", line 622, in _request
    with timer:
         ^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\helpers.py", line 650, in __enter__
    raise RuntimeError("Timeout context manager should be used inside a task")
RuntimeError: Timeout context manager should be used inside a task

2025-09-10 16:58:20.123 | Level 20 | Logger | [RestApi._process_request:338] 请求处理异常：Timeout context manager should be used inside a task
2025-09-10T16:58:20.123095+0800  Level 20: [RestApi._process_request:338] 请求处理异常：Timeout context manager should be used inside a task
2025-09-10 16:58:20.130 | Level 20 | Logger | [RestApi._log_error_to_db:369] REST错误已入库，错误码：5
2025-09-10T16:58:20.130606+0800  Level 20: [RestApi._log_error_to_db:369] REST错误已入库，错误码：5
2025-09-10 16:58:20.132 | Level 20 | Logger | [OkxGateway.on_error:807] Exception catched by REST API: [2025-09-10T16:58:20.130606]: Unhandled RestClient Error:<class 'RuntimeError'>
request:request : GET /api/v5/public/instruments because terminated: 
headers: None
params: {'instType': 'SWAP'}
data: None
json: None
response:

Exception trace: 
Traceback (most recent call last):
  File "S:\OneDrive - lancely\芷瀚同步\开发\币\okx\prod\rest_client.py", line 311, in _process_request
    response: Response = await self._get_response(request)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\OneDrive - lancely\芷瀚同步\开发\币\okx\prod\rest_client.py", line 287, in _get_response
    cr: ClientResponse = await self.session.request(
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\client.py", line 622, in _request
    with timer:
         ^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\helpers.py", line 650, in __enter__
    raise RuntimeError("Timeout context manager should be used inside a task")
RuntimeError: Timeout context manager should be used inside a task

2025-09-10T16:58:20.132609+0800  Level 20: [OkxGateway.on_error:807] Exception catched by REST API: [2025-09-10T16:58:20.130606]: Unhandled RestClient Error:<class 'RuntimeError'>
request:request : GET /api/v5/public/instruments because terminated: 
headers: None
params: {'instType': 'SWAP'}
data: None
json: None
response:

Exception trace: 
Traceback (most recent call last):
  File "S:\OneDrive - lancely\芷瀚同步\开发\币\okx\prod\rest_client.py", line 311, in _process_request
    response: Response = await self._get_response(request)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\OneDrive - lancely\芷瀚同步\开发\币\okx\prod\rest_client.py", line 287, in _get_response
    cr: ClientResponse = await self.session.request(
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\client.py", line 622, in _request
    with timer:
         ^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\helpers.py", line 650, in __enter__
    raise RuntimeError("Timeout context manager should be used inside a task")
RuntimeError: Timeout context manager should be used inside a task

2025-09-10 16:58:20.132 | Level 20 | Logger | [RestApi._process_request:338] 请求处理异常：Timeout context manager should be used inside a task
2025-09-10T16:58:20.132609+0800  Level 20: [RestApi._process_request:338] 请求处理异常：Timeout context manager should be used inside a task
2025-09-10 16:58:20.138 | Level 20 | Logger | [RestApi._log_error_to_db:369] REST错误已入库，错误码：5
2025-09-10T16:58:20.138604+0800  Level 20: [RestApi._log_error_to_db:369] REST错误已入库，错误码：5
2025-09-10 16:58:20.139 | Level 20 | Logger | [OkxGateway.on_error:807] Exception catched by REST API: [2025-09-10T16:58:20.138604]: Unhandled RestClient Error:<class 'RuntimeError'>
request:request : POST /api/v5/account/set-position-mode because terminated: 
headers: {'OK-ACCESS-KEY': '138a3371-73be-42c9-a718-4039091f6505', 'OK-ACCESS-SIGN': 'EpLJ4qzqsjsL90YHZRoJ8fbDLb5wH9KqVUVjOtk9+/E=', 'OK-ACCESS-TIMESTAMP': '2025-09-10T08:58:19.716Z', 'OK-ACCESS-PASSPHRASE': 'Dfgytr', 'Content-Type': 'application/json'}
params: None
data: {"posMode": "net_mode"}
json: None
response:

Exception trace: 
Traceback (most recent call last):
  File "S:\OneDrive - lancely\芷瀚同步\开发\币\okx\prod\rest_client.py", line 311, in _process_request
    response: Response = await self._get_response(request)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\OneDrive - lancely\芷瀚同步\开发\币\okx\prod\rest_client.py", line 287, in _get_response
    cr: ClientResponse = await self.session.request(
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\client.py", line 622, in _request
    with timer:
         ^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\helpers.py", line 650, in __enter__
    raise RuntimeError("Timeout context manager should be used inside a task")
RuntimeError: Timeout context manager should be used inside a task

2025-09-10T16:58:20.139650+0800  Level 20: [OkxGateway.on_error:807] Exception catched by REST API: [2025-09-10T16:58:20.138604]: Unhandled RestClient Error:<class 'RuntimeError'>
request:request : POST /api/v5/account/set-position-mode because terminated: 
headers: {'OK-ACCESS-KEY': '138a3371-73be-42c9-a718-4039091f6505', 'OK-ACCESS-SIGN': 'EpLJ4qzqsjsL90YHZRoJ8fbDLb5wH9KqVUVjOtk9+/E=', 'OK-ACCESS-TIMESTAMP': '2025-09-10T08:58:19.716Z', 'OK-ACCESS-PASSPHRASE': 'Dfgytr', 'Content-Type': 'application/json'}
params: None
data: {"posMode": "net_mode"}
json: None
response:

Exception trace: 
Traceback (most recent call last):
  File "S:\OneDrive - lancely\芷瀚同步\开发\币\okx\prod\rest_client.py", line 311, in _process_request
    response: Response = await self._get_response(request)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\OneDrive - lancely\芷瀚同步\开发\币\okx\prod\rest_client.py", line 287, in _get_response
    cr: ClientResponse = await self.session.request(
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\client.py", line 622, in _request
    with timer:
         ^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\helpers.py", line 650, in __enter__
    raise RuntimeError("Timeout context manager should be used inside a task")
RuntimeError: Timeout context manager should be used inside a task

2025-09-10 16:58:34.994 | Level 20 | Logger | [RestApi.init:148] REST客户端初始化，地址：https://www.okx.com
2025-09-10T16:58:34.994016+0800  Level 20: [RestApi.init:148] REST客户端初始化，地址：https://www.okx.com
2025-09-10 16:58:34.995 | Level 20 | Logger | [RestApi.init:152] 使用代理：http://127.0.0.1:7890
2025-09-10T16:58:34.995979+0800  Level 20: [RestApi.init:152] 使用代理：http://127.0.0.1:7890
2025-09-10 16:58:34.996 | Level 20 | Logger | [RestApi.start:160] 创建新的事件循环
2025-09-10T16:58:34.996980+0800  Level 20: [RestApi.start:160] 创建新的事件循环
2025-09-10 16:58:34.996 | Level 20 | Logger | [RestApi.start_event_loop:392] 启动事件循环后台线程
2025-09-10T16:58:34.996980+0800  Level 20: [RestApi.start_event_loop:392] 启动事件循环后台线程
2025-09-10 16:58:34.997 | Level 20 | Logger | [RestApi.run_event_loop:403] 事件循环开始运行
2025-09-10T16:58:34.997979+0800  Level 20: [RestApi.run_event_loop:403] 事件循环开始运行
2025-09-10 16:58:34.997 | Level 20 | Logger | [OkxGateway.connect:524] REST API started
2025-09-10T16:58:34.997979+0800  Level 20: [OkxGateway.connect:524] REST API started
2025-09-10 16:58:35.008 | Level 20 | Logger | [RestApi._get_response:269] 创建新的TCP连接器
2025-09-10T16:58:35.008781+0800  Level 20: [RestApi._get_response:269] 创建新的TCP连接器
2025-09-10 16:58:35.008 | Level 20 | Logger | [RestApi._get_response:276] 创建新的客户端会话
2025-09-10T16:58:35.008781+0800  Level 20: [RestApi._get_response:276] 创建新的客户端会话
2025-09-10 16:58:35.174 | Level 20 | Logger | [OkxGateway.on_query_contract:775] SWAP contract data received
2025-09-10T16:58:35.174184+0800  Level 20: [OkxGateway.on_query_contract:775] SWAP contract data received
2025-09-10 16:58:35.174 | Level 20 | Logger | [PublicApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10T16:58:35.174184+0800  Level 20: [PublicApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10 16:58:35.176 | Level 20 | Logger | [PublicApi.init:110] WebSocket客户端初始化，地址：wss://ws.okx.com:8443/ws/v5/public
2025-09-10T16:58:35.176090+0800  Level 20: [PublicApi.init:110] WebSocket客户端初始化，地址：wss://ws.okx.com:8443/ws/v5/public
2025-09-10 16:58:35.176 | Level 20 | Logger | [PublicApi.run:191] 尝试连接到服务器：wss://ws.okx.com:8443/ws/v5/public
2025-09-10T16:58:35.176596+0800  Level 20: [PublicApi.run:191] 尝试连接到服务器：wss://ws.okx.com:8443/ws/v5/public
2025-09-10 16:58:35.178 | Level 20 | Logger | [PublicApi.start:122] WebSocket客户端启动
2025-09-10T16:58:35.178181+0800  Level 20: [PublicApi.start:122] WebSocket客户端启动
2025-09-10 16:58:35.178 | Level 20 | Logger | [PrivateApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10T16:58:35.178706+0800  Level 20: [PrivateApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10 16:58:35.178 | Level 20 | Logger | [PrivateApi.init:110] WebSocket客户端初始化，地址：wss://ws.okx.com:8443/ws/v5/private
2025-09-10T16:58:35.178706+0800  Level 20: [PrivateApi.init:110] WebSocket客户端初始化，地址：wss://ws.okx.com:8443/ws/v5/private
2025-09-10 16:58:35.179 | Level 20 | Logger | [PrivateApi.run:191] 尝试连接到服务器：wss://ws.okx.com:8443/ws/v5/private
2025-09-10T16:58:35.179712+0800  Level 20: [PrivateApi.run:191] 尝试连接到服务器：wss://ws.okx.com:8443/ws/v5/private
2025-09-10 16:58:35.179 | Level 20 | Logger | [PrivateApi.start:122] WebSocket客户端启动
2025-09-10T16:58:35.179712+0800  Level 20: [PrivateApi.start:122] WebSocket客户端启动
2025-09-10 16:58:35.180 | Level 20 | Logger | [RestApi._get_response:303] 响应内容：{"msg":"Request header OK-ACCESS-PASSPHRASE incorrect.","code":"50105"}
2025-09-10T16:58:35.180712+0800  Level 20: [RestApi._get_response:303] 响应内容：{"msg":"Request header OK-ACCESS-PASSPHRASE incorrect.","code":"50105"}
2025-09-10 16:58:35.182 | Level 20 | Logger | [RestApi._process_request:321] 请求处理失败，状态码：401
2025-09-10T16:58:35.182718+0800  Level 20: [RestApi._process_request:321] 请求处理失败，状态码：401
2025-09-10 16:58:35.192 | Level 20 | Logger | [RestApi._log_error_to_db:369] REST错误已入库，错误码：401
2025-09-10T16:58:35.192088+0800  Level 20: [RestApi._log_error_to_db:369] REST错误已入库，错误码：401
2025-09-10 16:58:35.192 | Level 20 | Logger | [RestApi.on_failed:227] RestClient on failed----------
2025-09-10T16:58:35.192088+0800  Level 20: [RestApi.on_failed:227] RestClient on failed----------
2025-09-10 16:58:35.193 | Level 20 | Logger | [RestApi.on_failed:228] request : POST /api/v5/account/set-position-mode because 401: 
headers: {'OK-ACCESS-KEY': '138a3371-73be-42c9-a718-4039091f6505', 'OK-ACCESS-SIGN': '2gxhiFAAggMUUlSB+Y2T7x/VtwVTHWpsMr2ZNY5py4s=', 'OK-ACCESS-TIMESTAMP': '2025-09-10T08:58:35.011Z', 'OK-ACCESS-PASSPHRASE': 'Dfgytr', 'Content-Type': 'application/json'}
params: None
data: {"posMode": "net_mode"}
json: None
response:{"msg":"Request header OK-ACCESS-PASSPHRASE incorrect.","code":"50105"}

2025-09-10T16:58:35.193088+0800  Level 20: [RestApi.on_failed:228] request : POST /api/v5/account/set-position-mode because 401: 
headers: {'OK-ACCESS-KEY': '138a3371-73be-42c9-a718-4039091f6505', 'OK-ACCESS-SIGN': '2gxhiFAAggMUUlSB+Y2T7x/VtwVTHWpsMr2ZNY5py4s=', 'OK-ACCESS-TIMESTAMP': '2025-09-10T08:58:35.011Z', 'OK-ACCESS-PASSPHRASE': 'Dfgytr', 'Content-Type': 'application/json'}
params: None
data: {"posMode": "net_mode"}
json: None
response:{"msg":"Request header OK-ACCESS-PASSPHRASE incorrect.","code":"50105"}

2025-09-10 16:58:35.239 | Level 20 | Logger | [RestApi._get_response:303] 响应内容：{"msg":"Request header OK-ACCESS-PASSPHRASE incorrect.","code":"50105"}
2025-09-10T16:58:35.239993+0800  Level 20: [RestApi._get_response:303] 响应内容：{"msg":"Request header OK-ACCESS-PASSPHRASE incorrect.","code":"50105"}
2025-09-10 16:58:35.239 | Level 20 | Logger | [RestApi._process_request:321] 请求处理失败，状态码：401
2025-09-10T16:58:35.239993+0800  Level 20: [RestApi._process_request:321] 请求处理失败，状态码：401
2025-09-10 16:58:35.248 | Level 20 | Logger | [RestApi._log_error_to_db:369] REST错误已入库，错误码：401
2025-09-10T16:58:35.248734+0800  Level 20: [RestApi._log_error_to_db:369] REST错误已入库，错误码：401
2025-09-10 16:58:35.248 | Level 20 | Logger | [RestApi.on_failed:227] RestClient on failed----------
2025-09-10T16:58:35.248734+0800  Level 20: [RestApi.on_failed:227] RestClient on failed----------
2025-09-10 16:58:35.248 | Level 20 | Logger | [RestApi.on_failed:228] request : GET /api/v5/trade/orders-pending because 401: 
headers: {'OK-ACCESS-KEY': '138a3371-73be-42c9-a718-4039091f6505', 'OK-ACCESS-SIGN': 'aHrqAQJJA5yHpo28LNDXm98hlKR3oRaye3EL5MEeEOA=', 'OK-ACCESS-TIMESTAMP': '2025-09-10T08:58:35.176Z', 'OK-ACCESS-PASSPHRASE': 'Dfgytr', 'Content-Type': 'application/json'}
params: None
data: 
json: None
response:{"msg":"Request header OK-ACCESS-PASSPHRASE incorrect.","code":"50105"}

2025-09-10T16:58:35.248734+0800  Level 20: [RestApi.on_failed:228] request : GET /api/v5/trade/orders-pending because 401: 
headers: {'OK-ACCESS-KEY': '138a3371-73be-42c9-a718-4039091f6505', 'OK-ACCESS-SIGN': 'aHrqAQJJA5yHpo28LNDXm98hlKR3oRaye3EL5MEeEOA=', 'OK-ACCESS-TIMESTAMP': '2025-09-10T08:58:35.176Z', 'OK-ACCESS-PASSPHRASE': 'Dfgytr', 'Content-Type': 'application/json'}
params: None
data: 
json: None
response:{"msg":"Request header OK-ACCESS-PASSPHRASE incorrect.","code":"50105"}

2025-09-10 16:58:35.271 | Level 20 | Logger | [OkxGateway.on_query_time:603] server time: 2025-09-10 16:58:34.852000, local time: 2025-09-10 16:58:35.271347
2025-09-10T16:58:35.271347+0800  Level 20: [OkxGateway.on_query_time:603] server time: 2025-09-10 16:58:34.852000, local time: 2025-09-10 16:58:35.271347
2025-09-10 16:58:35.370 | Level 20 | Logger | [PublicApi.on_open:160] WebSocket连接建立成功
2025-09-10T16:58:35.370255+0800  Level 20: [PublicApi.on_open:160] WebSocket连接建立成功
2025-09-10 16:58:35.371 | Level 20 | Logger | [OkxGateway.on_connected:1055] Public API connected
2025-09-10T16:58:35.371259+0800  Level 20: [OkxGateway.on_connected:1055] Public API connected
2025-09-10 16:58:36.382 | Level 20 | Logger | [PrivateApi.on_open:160] WebSocket连接建立成功
2025-09-10T16:58:36.382781+0800  Level 20: [PrivateApi.on_open:160] WebSocket连接建立成功
2025-09-10 16:58:36.382 | Level 20 | Logger | [OkxGateway.on_connected:1258] Private websocket API connected
2025-09-10T16:58:36.382781+0800  Level 20: [OkxGateway.on_connected:1258] Private websocket API connected
2025-09-10 16:58:36.685 | Level 20 | Logger | [OkxGateway.on_api_error:1321] Private API request failed, status code: 60024, message: Wrong passphrase
2025-09-10T16:58:36.685856+0800  Level 20: [OkxGateway.on_api_error:1321] Private API request failed, status code: 60024, message: Wrong passphrase
2025-09-10 16:58:36.686 | Level 20 | Logger | [PrivateApi.on_close:164] WebSocket连接断开，状态码：4001，原因：Login failed.
2025-09-10T16:58:36.686393+0800  Level 20: [PrivateApi.on_close:164] WebSocket连接断开，状态码：4001，原因：Login failed.
2025-09-10 16:58:36.698 | Level 20 | Logger | [PrivateApi._log_error_to_db:63] WebSocket错误已入库，错误码：4001
2025-09-10T16:58:36.698691+0800  Level 20: [PrivateApi._log_error_to_db:63] WebSocket错误已入库，错误码：4001
2025-09-10 16:58:36.699 | Level 20 | Logger | [OkxGateway.on_disconnected:1268] Private API disconnected, code: 4001, msg: Login failed.
2025-09-10T16:58:36.699697+0800  Level 20: [OkxGateway.on_disconnected:1268] Private API disconnected, code: 4001, msg: Login failed.
2025-09-10 17:03:35.202 | Level 20 | Logger | [RestApi.init:148] REST客户端初始化，地址：https://www.okx.com
2025-09-10T17:03:35.202737+0800  Level 20: [RestApi.init:148] REST客户端初始化，地址：https://www.okx.com
2025-09-10 17:03:35.203 | Level 20 | Logger | [RestApi.init:152] 使用代理：http://127.0.0.1:7890
2025-09-10T17:03:35.203737+0800  Level 20: [RestApi.init:152] 使用代理：http://127.0.0.1:7890
2025-09-10 17:03:35.205 | Level 20 | Logger | [RestApi.start:160] 创建新的事件循环
2025-09-10T17:03:35.205737+0800  Level 20: [RestApi.start:160] 创建新的事件循环
2025-09-10 17:03:35.206 | Level 20 | Logger | [RestApi.start_event_loop:392] 启动事件循环后台线程
2025-09-10T17:03:35.206737+0800  Level 20: [RestApi.start_event_loop:392] 启动事件循环后台线程
2025-09-10 17:03:35.206 | Level 20 | Logger | [RestApi.run_event_loop:403] 事件循环开始运行
2025-09-10T17:03:35.206737+0800  Level 20: [RestApi.run_event_loop:403] 事件循环开始运行
2025-09-10 17:03:35.207 | Level 20 | Logger | [OkxGateway.connect:524] REST API started
2025-09-10T17:03:35.207739+0800  Level 20: [OkxGateway.connect:524] REST API started
2025-09-10 17:03:35.215 | Level 20 | Logger | [RestApi._get_response:269] 创建新的TCP连接器
2025-09-10T17:03:35.215813+0800  Level 20: [RestApi._get_response:269] 创建新的TCP连接器
2025-09-10 17:03:35.216 | Level 20 | Logger | [RestApi._get_response:276] 创建新的客户端会话
2025-09-10T17:03:35.216815+0800  Level 20: [RestApi._get_response:276] 创建新的客户端会话
2025-09-10 17:03:35.420 | Level 20 | Logger | [OkxGateway.on_query_time:603] server time: 2025-09-10 17:03:34.997000, local time: 2025-09-10 17:03:35.420214
2025-09-10T17:03:35.420214+0800  Level 20: [OkxGateway.on_query_time:603] server time: 2025-09-10 17:03:34.997000, local time: 2025-09-10 17:03:35.420214
2025-09-10 17:03:35.727 | Level 20 | Logger | [OkxGateway.on_query_contract:775] SWAP contract data received
2025-09-10T17:03:35.727126+0800  Level 20: [OkxGateway.on_query_contract:775] SWAP contract data received
2025-09-10 17:03:35.728 | Level 20 | Logger | [PublicApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10T17:03:35.728127+0800  Level 20: [PublicApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10 17:03:35.730 | Level 20 | Logger | [PublicApi.init:110] WebSocket客户端初始化，地址：wss://ws.okx.com:8443/ws/v5/public
2025-09-10T17:03:35.730132+0800  Level 20: [PublicApi.init:110] WebSocket客户端初始化，地址：wss://ws.okx.com:8443/ws/v5/public
2025-09-10 17:03:35.730 | Level 20 | Logger | [PublicApi.run:191] 尝试连接到服务器：wss://ws.okx.com:8443/ws/v5/public
2025-09-10T17:03:35.730132+0800  Level 20: [PublicApi.run:191] 尝试连接到服务器：wss://ws.okx.com:8443/ws/v5/public
2025-09-10 17:03:35.730 | Level 20 | Logger | [PublicApi.start:122] WebSocket客户端启动
2025-09-10T17:03:35.730912+0800  Level 20: [PublicApi.start:122] WebSocket客户端启动
2025-09-10 17:03:35.731 | Level 20 | Logger | [PrivateApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10T17:03:35.731918+0800  Level 20: [PrivateApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10 17:03:35.732 | Level 20 | Logger | [PrivateApi.init:110] WebSocket客户端初始化，地址：wss://ws.okx.com:8443/ws/v5/private
2025-09-10T17:03:35.732481+0800  Level 20: [PrivateApi.init:110] WebSocket客户端初始化，地址：wss://ws.okx.com:8443/ws/v5/private
2025-09-10 17:03:35.732 | Level 20 | Logger | [PrivateApi.run:191] 尝试连接到服务器：wss://ws.okx.com:8443/ws/v5/private
2025-09-10T17:03:35.732481+0800  Level 20: [PrivateApi.run:191] 尝试连接到服务器：wss://ws.okx.com:8443/ws/v5/private
2025-09-10 17:03:35.732 | Level 20 | Logger | [PrivateApi.start:122] WebSocket客户端启动
2025-09-10T17:03:35.732986+0800  Level 20: [PrivateApi.start:122] WebSocket客户端启动
2025-09-10 17:03:35.784 | Level 20 | Logger | [OkxGateway.on_query_orders:630] No open orders found
2025-09-10T17:03:35.784956+0800  Level 20: [OkxGateway.on_query_orders:630] No open orders found
2025-09-10 17:03:35.994 | Level 20 | Logger | [PrivateApi.on_open:160] WebSocket连接建立成功
2025-09-10T17:03:35.994786+0800  Level 20: [PrivateApi.on_open:160] WebSocket连接建立成功
2025-09-10 17:03:35.994 | Level 20 | Logger | [OkxGateway.on_connected:1258] Private websocket API connected
2025-09-10T17:03:35.994786+0800  Level 20: [OkxGateway.on_connected:1258] Private websocket API connected
2025-09-10 17:03:36.044 | Level 20 | Logger | [OkxGateway.on_login:1334] Private API login successful
2025-09-10T17:03:36.044669+0800  Level 20: [OkxGateway.on_login:1334] Private API login successful
2025-09-10 17:03:36.205 | Level 20 | Logger | [PublicApi.on_open:160] WebSocket连接建立成功
2025-09-10T17:03:36.205777+0800  Level 20: [PublicApi.on_open:160] WebSocket连接建立成功
2025-09-10 17:03:36.205 | Level 20 | Logger | [OkxGateway.on_connected:1055] Public API connected
2025-09-10T17:03:36.205777+0800  Level 20: [OkxGateway.on_connected:1055] Public API connected
2025-09-10 19:03:12.128 | INFO |  | Tick event handler registered
2025-09-10 19:03:12.128 | INFO |  | okx Gateway added successfully
2025-09-10 19:03:12.129 | INFO |  | Gateway configuration loaded
2025-09-10 19:03:12.129 | INFO | OKX | [RestApi.init:148] REST客户端初始化，地址：https://www.okx.com
2025-09-10 19:03:12.129 | INFO | OKX | [RestApi.init:152] 使用代理：http://127.0.0.1:7890
2025-09-10 19:03:12.130 | INFO | OKX | [RestApi.start:160] 创建新的事件循环
2025-09-10 19:03:12.130 | INFO | OKX | [RestApi.start_event_loop:392] 启动事件循环后台线程
2025-09-10 19:03:12.130 | INFO | OKX | [RestApi.run_event_loop:403] 事件循环开始运行
2025-09-10 19:03:12.131 | INFO | OKX | [OkxGateway.connect:526] REST API started
2025-09-10 19:03:12.131 | INFO |  | Connected to okx gateway
2025-09-10 19:03:12.142 | INFO | OKX | [RestApi._get_response:269] 创建新的TCP连接器
2025-09-10 19:03:12.142 | INFO | OKX | [RestApi._get_response:276] 创建新的客户端会话
2025-09-10 19:03:12.602 | INFO | OKX | [OkxGateway.on_query_time:605] server time: 2025-09-10 19:03:12.015000, local time: 2025-09-10 19:03:12.602777
2025-09-10 19:03:12.709 | INFO | OKX | [OkxGateway.on_query_contract:777] SWAP contract data received
2025-09-10 19:03:12.709 | INFO | OKX | [PublicApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10 19:03:12.710 | INFO | OKX | [PublicApi.init:110] WebSocket客户端初始化，地址：wss://wspap.okx.com:8443/ws/v5/public?brokerId=9999
2025-09-10 19:03:12.710 | INFO | OKX | [PublicApi.run:191] 尝试连接到服务器：wss://wspap.okx.com:8443/ws/v5/public?brokerId=9999
2025-09-10 19:03:12.710 | INFO | OKX | [PublicApi.start:122] WebSocket客户端启动
2025-09-10 19:03:12.710 | INFO | OKX | [OkxGateway.start_subscribe_thread:987] OKX订阅流控线程已启动
2025-09-10 19:03:12.711 | INFO | OKX | [PrivateApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10 19:03:12.711 | INFO | OKX | [PrivateApi.init:110] WebSocket客户端初始化，地址：wss://wspap.okx.com:8443/ws/v5/private?brokerId=9999
2025-09-10 19:03:12.711 | INFO | OKX | [PrivateApi.run:191] 尝试连接到服务器：wss://wspap.okx.com:8443/ws/v5/private?brokerId=9999
2025-09-10 19:03:12.711 | INFO | OKX | [PrivateApi.start:122] WebSocket客户端启动
2025-09-10 19:03:12.871 | INFO | OKX | [OkxGateway.on_query_orders:632] No open orders found
2025-09-10 19:03:13.396 | INFO | OKX | [PublicApi.on_open:160] WebSocket连接建立成功
2025-09-10 19:03:13.396 | INFO | OKX | [OkxGateway.on_connected:1210] Public API connected
2025-09-10 19:03:13.404 | INFO | OKX | [PrivateApi.on_open:160] WebSocket连接建立成功
2025-09-10 19:03:13.405 | INFO | OKX | [OkxGateway.on_connected:1413] Private websocket API connected
2025-09-10 19:03:13.599 | INFO | OKX | [OkxGateway.on_login:1489] Private API login successful
2025-09-10 19:03:15.132 | INFO |  | ***Querying account and position***
2025-09-10 19:03:15.132 | INFO |  | [AccountData(gateway_name='OKX', extra=None, accountid='BTC', balance=1.********, frozen=0.0), AccountData(gateway_name='OKX', extra=None, accountid='OKB', balance=100.0, frozen=0.0), AccountData(gateway_name='OKX', extra=None, accountid='USDT', balance=4998.************, frozen=967.*************), AccountData(gateway_name='OKX', extra=None, accountid='ETH', balance=1.0, frozen=0.0)]
2025-09-10 19:03:15.132 | INFO |  | [PositionData(gateway_name='OKX', extra=None, symbol='DOGEUSDT_SWAP_OKX', exchange=<Exchange.GLOBAL: 'GLOBAL'>, direction=<Direction.NET: '净'>, volume=10.0, frozen=0, price=0.2395, pnl=9.***************, yd_volume=0), PositionData(gateway_name='OKX', extra=None, symbol='BTCUSDT_SWAP_OKX', exchange=<Exchange.GLOBAL: 'GLOBAL'>, direction=<Direction.NET: '净'>, volume=0.04, frozen=0, price=110050.0, pnl=0.8588, yd_volume=0), PositionData(gateway_name='OKX', extra=None, symbol='ETHUSDT_SWAP_OKX', exchange=<Exchange.GLOBAL: 'GLOBAL'>, direction=<Direction.NET: '净'>, volume=1.05, frozen=0, price=4307.************, pnl=2.****************, yd_volume=0)]
2025-09-10 19:03:15.132 | INFO | OKX | [OkxGateway.subscribe:1165] Added subscription request to queue for DOGEUSDT_SWAP_OKX
2025-09-10 19:03:15.132 | INFO |  | Subscribed to DOGEUSDT_SWAP_OKX.GLOBAL
2025-09-10 19:03:15.219 | INFO | OKX | [OkxGateway._subscribe_process:1041] 订阅流控线程异常：<class 'TypeError'> - unhashable type: 'dict'
Traceback (most recent call last):
  File "S:\OneDrive - lancely\芷瀚同步\开发\币\okx\prod\okx_gateway.py", line 1022, in _subscribe_process
    subscribe_set = set(subscribe_requests)
                    ^^^^^^^^^^^^^^^^^^^^^^^
TypeError: unhashable type: 'dict'

2025-09-10 19:03:20.132 | INFO | OKX | [OkxGateway.unsubscribe:1199] Added unsubscription request to queue for DOGEUSDT_SWAP_OKX
2025-09-10 19:03:20.136 | INFO | OKX | [OkxGateway._subscribe_process:1041] 订阅流控线程异常：<class 'TypeError'> - unhashable type: 'dict'
Traceback (most recent call last):
  File "S:\OneDrive - lancely\芷瀚同步\开发\币\okx\prod\okx_gateway.py", line 1023, in _subscribe_process
    unsubscribe_set = set(unsubscribe_requests)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: unhashable type: 'dict'

2025-09-10 19:03:24.533 | INFO |  | Closing connection...
2025-09-10 20:44:26.488 | INFO |  | Tick event handler registered
2025-09-10 20:44:26.489 | INFO |  | okx Gateway added successfully
2025-09-10 20:44:26.489 | INFO |  | Gateway configuration loaded
2025-09-10 20:44:26.490 | INFO | OKX | [RestApi.init:148] REST客户端初始化，地址：https://www.okx.com
2025-09-10 20:44:26.490 | INFO | OKX | [RestApi.init:152] 使用代理：http://127.0.0.1:7890
2025-09-10 20:44:26.490 | INFO | OKX | [RestApi.start:160] 创建新的事件循环
2025-09-10 20:44:26.491 | INFO | OKX | [RestApi.start_event_loop:392] 启动事件循环后台线程
2025-09-10 20:44:26.491 | INFO | OKX | [RestApi.run_event_loop:403] 事件循环开始运行
2025-09-10 20:44:26.492 | INFO | OKX | [OkxGateway.connect:526] REST API started
2025-09-10 20:44:26.492 | INFO |  | Connected to okx gateway
2025-09-10 20:44:26.501 | INFO | OKX | [RestApi._get_response:269] 创建新的TCP连接器
2025-09-10 20:44:26.502 | INFO | OKX | [RestApi._get_response:276] 创建新的客户端会话
2025-09-10 20:44:27.364 | INFO | OKX | [OkxGateway.on_query_time:605] server time: 2025-09-10 20:44:26.666000, local time: 2025-09-10 20:44:27.364453
2025-09-10 20:44:27.787 | INFO | OKX | [OkxGateway.on_query_contract:777] SWAP contract data received
2025-09-10 20:44:27.788 | INFO | OKX | [PublicApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10 20:44:27.788 | INFO | OKX | [PublicApi.init:110] WebSocket客户端初始化，地址：wss://wspap.okx.com:8443/ws/v5/public?brokerId=9999
2025-09-10 20:44:27.788 | INFO | OKX | [PublicApi.run:191] 尝试连接到服务器：wss://wspap.okx.com:8443/ws/v5/public?brokerId=9999
2025-09-10 20:44:27.790 | INFO | OKX | [PublicApi.start:122] WebSocket客户端启动
2025-09-10 20:44:27.790 | INFO | OKX | [OkxGateway.start_subscribe_thread:995] OKX订阅流控线程已启动
2025-09-10 20:44:27.791 | INFO | OKX | [PrivateApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10 20:44:27.791 | INFO | OKX | [PrivateApi.init:110] WebSocket客户端初始化，地址：wss://wspap.okx.com:8443/ws/v5/private?brokerId=9999
2025-09-10 20:44:27.791 | INFO | OKX | [PrivateApi.run:191] 尝试连接到服务器：wss://wspap.okx.com:8443/ws/v5/private?brokerId=9999
2025-09-10 20:44:27.792 | INFO | OKX | [PrivateApi.start:122] WebSocket客户端启动
2025-09-10 20:44:27.944 | INFO | OKX | [PrivateApi.on_open:160] WebSocket连接建立成功
2025-09-10 20:44:27.944 | INFO | OKX | [OkxGateway.on_connected:1502] Private websocket API connected
2025-09-10 20:44:27.946 | INFO | OKX | [PublicApi.on_open:160] WebSocket连接建立成功
2025-09-10 20:44:27.946 | INFO | OKX | [OkxGateway.on_connected:1231] Public API connected
2025-09-10 20:44:27.960 | INFO | OKX | [OkxGateway.on_query_orders:632] No open orders found
2025-09-10 20:44:27.989 | INFO | OKX | [OkxGateway.on_login:1578] Private API login successful
2025-09-10 20:44:29.492 | INFO |  | ***Querying account and position***
2025-09-10 20:44:29.492 | INFO |  | [AccountData(gateway_name='OKX', extra=None, accountid='BTC', balance=1.********, frozen=0.0), AccountData(gateway_name='OKX', extra=None, accountid='OKB', balance=100.0, frozen=0.0), AccountData(gateway_name='OKX', extra=None, accountid='USDT', balance=5026.************, frozen=977.35266), AccountData(gateway_name='OKX', extra=None, accountid='ETH', balance=1.0, frozen=0.0)]
2025-09-10 20:44:29.492 | INFO |  | [PositionData(gateway_name='OKX', extra=None, symbol='DOGEUSDT_SWAP_OKX', exchange=<Exchange.GLOBAL: 'GLOBAL'>, direction=<Direction.NET: '净'>, volume=10.0, frozen=0, price=0.2395, pnl=32.***************, yd_volume=0), PositionData(gateway_name='OKX', extra=None, symbol='BTCUSDT_SWAP_OKX', exchange=<Exchange.GLOBAL: 'GLOBAL'>, direction=<Direction.NET: '净'>, volume=0.04, frozen=0, price=110050.0, pnl=1.****************, yd_volume=0), PositionData(gateway_name='OKX', extra=None, symbol='ETHUSDT_SWAP_OKX', exchange=<Exchange.GLOBAL: 'GLOBAL'>, direction=<Direction.NET: '净'>, volume=1.05, frozen=0, price=4307.************, pnl=7.***************, yd_volume=0)]
2025-09-10 20:44:29.493 | INFO | OKX | [OkxGateway.subscribe:1186] Added subscription request to queue for DOGEUSDT_SWAP_OKX
2025-09-10 20:44:29.493 | INFO |  | Subscribed to DOGEUSDT_SWAP_OKX.GLOBAL
2025-09-10 20:44:29.496 | INFO | OKX | [OkxGateway._send_subscribe_packet:1133] 发送订阅请求：2个频道
2025-09-10 20:44:34.492 | INFO | OKX | [OkxGateway.unsubscribe:1220] Added unsubscription request to queue for DOGEUSDT_SWAP_OKX
2025-09-10 20:44:34.609 | INFO | OKX | [OkxGateway.on_depth:1362] 收到未订阅的depth: DOGE-USDT-SWAP
2025-09-10 20:44:34.731 | INFO | OKX | [OkxGateway.on_ticker:1304] 收到未订阅的ticker: DOGE-USDT-SWAP
2025-09-10 20:44:34.909 | INFO | OKX | [OkxGateway.on_depth:1362] 收到未订阅的depth: DOGE-USDT-SWAP
2025-09-10 20:44:35.391 | INFO |  | Closing connection...
2025-09-10 20:47:39.789 | Level 20 | Logger | [OkxGateway.enable_log_mode:1000] 日志模式已启用，将跟踪ticker和depth推送信息
2025-09-10T20:47:39.789777+0800  Level 20: [OkxGateway.enable_log_mode:1000] 日志模式已启用，将跟踪ticker和depth推送信息
2025-09-10 20:49:06.666 | Level 20 | Logger | [OkxGateway.enable_log_mode:1000] 日志模式已启用，将跟踪ticker和depth推送信息
2025-09-10T20:49:06.666522+0800  Level 20: [OkxGateway.enable_log_mode:1000] 日志模式已启用，将跟踪ticker和depth推送信息
2025-09-10 20:49:10.562 | Level 20 | Logger | [RestApi.init:148] REST客户端初始化，地址：https://www.okx.com
2025-09-10T20:49:10.562741+0800  Level 20: [RestApi.init:148] REST客户端初始化，地址：https://www.okx.com
2025-09-10 20:49:10.563 | Level 20 | Logger | [RestApi.init:152] 使用代理：http://127.0.0.1:7890
2025-09-10T20:49:10.563742+0800  Level 20: [RestApi.init:152] 使用代理：http://127.0.0.1:7890
2025-09-10 20:49:10.564 | Level 20 | Logger | [RestApi.start:160] 创建新的事件循环
2025-09-10T20:49:10.564776+0800  Level 20: [RestApi.start:160] 创建新的事件循环
2025-09-10 20:49:10.564 | Level 20 | Logger | [RestApi.start_event_loop:392] 启动事件循环后台线程
2025-09-10T20:49:10.564776+0800  Level 20: [RestApi.start_event_loop:392] 启动事件循环后台线程
2025-09-10 20:49:10.565 | Level 20 | Logger | [RestApi.run_event_loop:403] 事件循环开始运行
2025-09-10T20:49:10.565788+0800  Level 20: [RestApi.run_event_loop:403] 事件循环开始运行
2025-09-10 20:49:10.566 | Level 20 | Logger | [OkxGateway.connect:526] REST API started
2025-09-10T20:49:10.566739+0800  Level 20: [OkxGateway.connect:526] REST API started
2025-09-10 20:49:10.576 | Level 20 | Logger | [RestApi._get_response:269] 创建新的TCP连接器
2025-09-10T20:49:10.576336+0800  Level 20: [RestApi._get_response:269] 创建新的TCP连接器
2025-09-10 20:49:10.577 | Level 20 | Logger | [RestApi._get_response:276] 创建新的客户端会话
2025-09-10T20:49:10.577336+0800  Level 20: [RestApi._get_response:276] 创建新的客户端会话
2025-09-10 20:49:10.941 | Level 20 | Logger | [OkxGateway.on_query_time:605] server time: 2025-09-10 20:49:10.287000, local time: 2025-09-10 20:49:10.941495
2025-09-10T20:49:10.941495+0800  Level 20: [OkxGateway.on_query_time:605] server time: 2025-09-10 20:49:10.287000, local time: 2025-09-10 20:49:10.941495
2025-09-10 20:49:11.019 | Level 20 | Logger | [OkxGateway.on_query_contract:777] SWAP contract data received
2025-09-10T20:49:11.019747+0800  Level 20: [OkxGateway.on_query_contract:777] SWAP contract data received
2025-09-10 20:49:11.020 | Level 20 | Logger | [PublicApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10T20:49:11.020746+0800  Level 20: [PublicApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10 20:49:11.023 | Level 20 | Logger | [PublicApi.init:110] WebSocket客户端初始化，地址：wss://wspap.okx.com:8443/ws/v5/public?brokerId=9999
2025-09-10T20:49:11.023169+0800  Level 20: [PublicApi.init:110] WebSocket客户端初始化，地址：wss://wspap.okx.com:8443/ws/v5/public?brokerId=9999
2025-09-10 20:49:11.025 | Level 20 | Logger | [PublicApi.run:191] 尝试连接到服务器：wss://wspap.okx.com:8443/ws/v5/public?brokerId=9999
2025-09-10T20:49:11.025275+0800  Level 20: [PublicApi.run:191] 尝试连接到服务器：wss://wspap.okx.com:8443/ws/v5/public?brokerId=9999
2025-09-10 20:49:11.025 | Level 20 | Logger | [PublicApi.start:122] WebSocket客户端启动
2025-09-10T20:49:11.025275+0800  Level 20: [PublicApi.start:122] WebSocket客户端启动
2025-09-10 20:49:11.026 | Level 20 | Logger | [OkxGateway.start_subscribe_thread:995] OKX订阅流控线程已启动
2025-09-10T20:49:11.026283+0800  Level 20: [OkxGateway.start_subscribe_thread:995] OKX订阅流控线程已启动
2025-09-10 20:49:11.026 | Level 20 | Logger | [PrivateApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10T20:49:11.026283+0800  Level 20: [PrivateApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10 20:49:11.027 | Level 20 | Logger | [PrivateApi.init:110] WebSocket客户端初始化，地址：wss://wspap.okx.com:8443/ws/v5/private?brokerId=9999
2025-09-10T20:49:11.027043+0800  Level 20: [PrivateApi.init:110] WebSocket客户端初始化，地址：wss://wspap.okx.com:8443/ws/v5/private?brokerId=9999
2025-09-10 20:49:11.027 | Level 20 | Logger | [PrivateApi.run:191] 尝试连接到服务器：wss://wspap.okx.com:8443/ws/v5/private?brokerId=9999
2025-09-10T20:49:11.027043+0800  Level 20: [PrivateApi.run:191] 尝试连接到服务器：wss://wspap.okx.com:8443/ws/v5/private?brokerId=9999
2025-09-10 20:49:11.029 | Level 20 | Logger | [PrivateApi.start:122] WebSocket客户端启动
2025-09-10T20:49:11.029416+0800  Level 20: [PrivateApi.start:122] WebSocket客户端启动
2025-09-10 20:49:11.170 | Level 20 | Logger | [PrivateApi.on_open:160] WebSocket连接建立成功
2025-09-10T20:49:11.170560+0800  Level 20: [PrivateApi.on_open:160] WebSocket连接建立成功
2025-09-10 20:49:11.171 | Level 20 | Logger | [OkxGateway.on_connected:1502] Private websocket API connected
2025-09-10T20:49:11.171408+0800  Level 20: [OkxGateway.on_connected:1502] Private websocket API connected
2025-09-10 20:49:11.172 | Level 20 | Logger | [PublicApi.on_open:160] WebSocket连接建立成功
2025-09-10T20:49:11.172416+0800  Level 20: [PublicApi.on_open:160] WebSocket连接建立成功
2025-09-10 20:49:11.173 | Level 20 | Logger | [OkxGateway.on_connected:1231] Public API connected
2025-09-10T20:49:11.173463+0800  Level 20: [OkxGateway.on_connected:1231] Public API connected
2025-09-10 20:49:11.191 | Level 20 | Logger | [OkxGateway.on_query_orders:632] No open orders found
2025-09-10T20:49:11.191413+0800  Level 20: [OkxGateway.on_query_orders:632] No open orders found
2025-09-10 20:49:11.222 | Level 20 | Logger | [OkxGateway.on_login:1578] Private API login successful
2025-09-10T20:49:11.222536+0800  Level 20: [OkxGateway.on_login:1578] Private API login successful
2025-09-10 20:50:29.753 | Level 20 | Logger | [OkxGateway.enable_log_mode:1000] 日志模式已启用，将跟踪ticker和depth推送信息
2025-09-10T20:50:29.753656+0800  Level 20: [OkxGateway.enable_log_mode:1000] 日志模式已启用，将跟踪ticker和depth推送信息
2025-09-10 20:50:35.576 | Level 20 | Logger | [RestApi.init:148] REST客户端初始化，地址：https://www.okx.com
2025-09-10T20:50:35.576321+0800  Level 20: [RestApi.init:148] REST客户端初始化，地址：https://www.okx.com
2025-09-10 20:50:35.577 | Level 20 | Logger | [RestApi.init:152] 使用代理：http://127.0.0.1:7890
2025-09-10T20:50:35.577324+0800  Level 20: [RestApi.init:152] 使用代理：http://127.0.0.1:7890
2025-09-10 20:50:35.578 | Level 20 | Logger | [RestApi.start:160] 创建新的事件循环
2025-09-10T20:50:35.578321+0800  Level 20: [RestApi.start:160] 创建新的事件循环
2025-09-10 20:50:35.579 | Level 20 | Logger | [RestApi.start_event_loop:392] 启动事件循环后台线程
2025-09-10T20:50:35.579321+0800  Level 20: [RestApi.start_event_loop:392] 启动事件循环后台线程
2025-09-10 20:50:35.580 | Level 20 | Logger | [RestApi.run_event_loop:403] 事件循环开始运行
2025-09-10T20:50:35.580323+0800  Level 20: [RestApi.run_event_loop:403] 事件循环开始运行
2025-09-10 20:50:35.580 | Level 20 | Logger | [OkxGateway.connect:526] REST API started
2025-09-10T20:50:35.580323+0800  Level 20: [OkxGateway.connect:526] REST API started
2025-09-10 20:50:35.590 | Level 20 | Logger | [RestApi._get_response:269] 创建新的TCP连接器
2025-09-10T20:50:35.590092+0800  Level 20: [RestApi._get_response:269] 创建新的TCP连接器
2025-09-10 20:50:35.591 | Level 20 | Logger | [RestApi._get_response:276] 创建新的客户端会话
2025-09-10T20:50:35.591092+0800  Level 20: [RestApi._get_response:276] 创建新的客户端会话
2025-09-10 20:50:36.008 | Level 20 | Logger | [OkxGateway.on_query_contract:777] SWAP contract data received
2025-09-10T20:50:36.008721+0800  Level 20: [OkxGateway.on_query_contract:777] SWAP contract data received
2025-09-10 20:50:36.009 | Level 20 | Logger | [PublicApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10T20:50:36.009800+0800  Level 20: [PublicApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10 20:50:36.010 | Level 20 | Logger | [PublicApi.init:110] WebSocket客户端初始化，地址：wss://wspap.okx.com:8443/ws/v5/public?brokerId=9999
2025-09-10T20:50:36.010797+0800  Level 20: [PublicApi.init:110] WebSocket客户端初始化，地址：wss://wspap.okx.com:8443/ws/v5/public?brokerId=9999
2025-09-10 20:50:36.012 | Level 20 | Logger | [PublicApi.run:191] 尝试连接到服务器：wss://wspap.okx.com:8443/ws/v5/public?brokerId=9999
2025-09-10T20:50:36.012318+0800  Level 20: [PublicApi.run:191] 尝试连接到服务器：wss://wspap.okx.com:8443/ws/v5/public?brokerId=9999
2025-09-10 20:50:36.012 | Level 20 | Logger | [PublicApi.start:122] WebSocket客户端启动
2025-09-10T20:50:36.012877+0800  Level 20: [PublicApi.start:122] WebSocket客户端启动
2025-09-10 20:50:36.013 | Level 20 | Logger | [OkxGateway.start_subscribe_thread:995] OKX订阅流控线程已启动
2025-09-10T20:50:36.013413+0800  Level 20: [OkxGateway.start_subscribe_thread:995] OKX订阅流控线程已启动
2025-09-10 20:50:36.014 | Level 20 | Logger | [PrivateApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10T20:50:36.014251+0800  Level 20: [PrivateApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10 20:50:36.014 | Level 20 | Logger | [PrivateApi.init:110] WebSocket客户端初始化，地址：wss://wspap.okx.com:8443/ws/v5/private?brokerId=9999
2025-09-10T20:50:36.014768+0800  Level 20: [PrivateApi.init:110] WebSocket客户端初始化，地址：wss://wspap.okx.com:8443/ws/v5/private?brokerId=9999
2025-09-10 20:50:36.014 | Level 20 | Logger | [PrivateApi.run:191] 尝试连接到服务器：wss://wspap.okx.com:8443/ws/v5/private?brokerId=9999
2025-09-10T20:50:36.014768+0800  Level 20: [PrivateApi.run:191] 尝试连接到服务器：wss://wspap.okx.com:8443/ws/v5/private?brokerId=9999
2025-09-10 20:50:36.015 | Level 20 | Logger | [PrivateApi.start:122] WebSocket客户端启动
2025-09-10T20:50:36.015482+0800  Level 20: [PrivateApi.start:122] WebSocket客户端启动
2025-09-10 20:50:36.028 | Level 20 | Logger | [OkxGateway.on_query_time:605] server time: 2025-09-10 20:50:35.344000, local time: 2025-09-10 20:50:36.027653
2025-09-10T20:50:36.028214+0800  Level 20: [OkxGateway.on_query_time:605] server time: 2025-09-10 20:50:35.344000, local time: 2025-09-10 20:50:36.027653
2025-09-10 20:50:36.164 | Level 20 | Logger | [PublicApi.on_open:160] WebSocket连接建立成功
2025-09-10T20:50:36.164670+0800  Level 20: [PublicApi.on_open:160] WebSocket连接建立成功
2025-09-10 20:50:36.165 | Level 20 | Logger | [OkxGateway.on_connected:1231] Public API connected
2025-09-10T20:50:36.165843+0800  Level 20: [OkxGateway.on_connected:1231] Public API connected
2025-09-10 20:50:36.169 | Level 20 | Logger | [OkxGateway.on_query_orders:632] No open orders found
2025-09-10T20:50:36.169963+0800  Level 20: [OkxGateway.on_query_orders:632] No open orders found
2025-09-10 20:50:36.177 | Level 20 | Logger | [PrivateApi.on_open:160] WebSocket连接建立成功
2025-09-10T20:50:36.177141+0800  Level 20: [PrivateApi.on_open:160] WebSocket连接建立成功
2025-09-10 20:50:36.177 | Level 20 | Logger | [OkxGateway.on_connected:1502] Private websocket API connected
2025-09-10T20:50:36.177635+0800  Level 20: [OkxGateway.on_connected:1502] Private websocket API connected
2025-09-10 20:50:36.225 | Level 20 | Logger | [OkxGateway.on_login:1578] Private API login successful
2025-09-10T20:50:36.225742+0800  Level 20: [OkxGateway.on_login:1578] Private API login successful
2025-09-10 21:13:35.174 | Level 20 | Logger | [OkxGateway.enable_log_mode:1000] 日志模式已启用，将跟踪ticker和depth推送信息
2025-09-10T21:13:35.174923+0800  Level 20: [OkxGateway.enable_log_mode:1000] 日志模式已启用，将跟踪ticker和depth推送信息
2025-09-10 21:13:38.319 | Level 20 | Logger | [RestApi.init:148] REST客户端初始化，地址：https://www.okx.com
2025-09-10T21:13:38.319566+0800  Level 20: [RestApi.init:148] REST客户端初始化，地址：https://www.okx.com
2025-09-10 21:13:38.319 | Level 20 | Logger | [RestApi.init:152] 使用代理：http://127.0.0.1:7890
2025-09-10T21:13:38.319566+0800  Level 20: [RestApi.init:152] 使用代理：http://127.0.0.1:7890
2025-09-10 21:13:38.321 | Level 20 | Logger | [RestApi.start:160] 创建新的事件循环
2025-09-10T21:13:38.321071+0800  Level 20: [RestApi.start:160] 创建新的事件循环
2025-09-10 21:13:38.323 | Level 20 | Logger | [RestApi.start_event_loop:392] 启动事件循环后台线程
2025-09-10T21:13:38.323075+0800  Level 20: [RestApi.start_event_loop:392] 启动事件循环后台线程
2025-09-10 21:13:38.324 | Level 20 | Logger | [RestApi.run_event_loop:403] 事件循环开始运行
2025-09-10T21:13:38.324080+0800  Level 20: [RestApi.run_event_loop:403] 事件循环开始运行
2025-09-10 21:13:38.325 | Level 20 | Logger | [OkxGateway.connect:526] REST API started
2025-09-10T21:13:38.325078+0800  Level 20: [OkxGateway.connect:526] REST API started
2025-09-10 21:13:38.334 | Level 20 | Logger | [RestApi._get_response:269] 创建新的TCP连接器
2025-09-10T21:13:38.334078+0800  Level 20: [RestApi._get_response:269] 创建新的TCP连接器
2025-09-10 21:13:38.336 | Level 20 | Logger | [RestApi._get_response:276] 创建新的客户端会话
2025-09-10T21:13:38.336087+0800  Level 20: [RestApi._get_response:276] 创建新的客户端会话
2025-09-10 21:13:38.717 | Level 20 | Logger | [OkxGateway.on_query_time:605] server time: 2025-09-10 21:13:38.042000, local time: 2025-09-10 21:13:38.717449
2025-09-10T21:13:38.717448+0800  Level 20: [OkxGateway.on_query_time:605] server time: 2025-09-10 21:13:38.042000, local time: 2025-09-10 21:13:38.717449
2025-09-10 21:13:38.802 | Level 20 | Logger | [OkxGateway.on_query_contract:777] SWAP contract data received
2025-09-10T21:13:38.802238+0800  Level 20: [OkxGateway.on_query_contract:777] SWAP contract data received
2025-09-10 21:13:38.804 | Level 20 | Logger | [PublicApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10T21:13:38.804237+0800  Level 20: [PublicApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10 21:13:38.805 | Level 20 | Logger | [PublicApi.init:110] WebSocket客户端初始化，地址：wss://wspap.okx.com:8443/ws/v5/public?brokerId=9999
2025-09-10T21:13:38.805704+0800  Level 20: [PublicApi.init:110] WebSocket客户端初始化，地址：wss://wspap.okx.com:8443/ws/v5/public?brokerId=9999
2025-09-10 21:13:38.806 | Level 20 | Logger | [PublicApi.run:191] 尝试连接到服务器：wss://wspap.okx.com:8443/ws/v5/public?brokerId=9999
2025-09-10T21:13:38.806473+0800  Level 20: [PublicApi.run:191] 尝试连接到服务器：wss://wspap.okx.com:8443/ws/v5/public?brokerId=9999
2025-09-10 21:13:38.807 | Level 20 | Logger | [PublicApi.start:122] WebSocket客户端启动
2025-09-10T21:13:38.807478+0800  Level 20: [PublicApi.start:122] WebSocket客户端启动
2025-09-10 21:13:38.808 | Level 20 | Logger | [OkxGateway.start_subscribe_thread:995] OKX订阅流控线程已启动
2025-09-10T21:13:38.808857+0800  Level 20: [OkxGateway.start_subscribe_thread:995] OKX订阅流控线程已启动
2025-09-10 21:13:38.809 | Level 20 | Logger | [PrivateApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10T21:13:38.809881+0800  Level 20: [PrivateApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10 21:13:38.810 | Level 20 | Logger | [PrivateApi.init:110] WebSocket客户端初始化，地址：wss://wspap.okx.com:8443/ws/v5/private?brokerId=9999
2025-09-10T21:13:38.810884+0800  Level 20: [PrivateApi.init:110] WebSocket客户端初始化，地址：wss://wspap.okx.com:8443/ws/v5/private?brokerId=9999
2025-09-10 21:13:38.811 | Level 20 | Logger | [PrivateApi.run:191] 尝试连接到服务器：wss://wspap.okx.com:8443/ws/v5/private?brokerId=9999
2025-09-10T21:13:38.811873+0800  Level 20: [PrivateApi.run:191] 尝试连接到服务器：wss://wspap.okx.com:8443/ws/v5/private?brokerId=9999
2025-09-10 21:13:38.812 | Level 20 | Logger | [PrivateApi.start:122] WebSocket客户端启动
2025-09-10T21:13:38.812871+0800  Level 20: [PrivateApi.start:122] WebSocket客户端启动
2025-09-10 21:13:38.995 | Level 20 | Logger | [OkxGateway.on_query_orders:632] No open orders found
2025-09-10T21:13:38.995334+0800  Level 20: [OkxGateway.on_query_orders:632] No open orders found
2025-09-10 21:13:39.137 | Level 20 | Logger | [PrivateApi.on_open:160] WebSocket连接建立成功
2025-09-10T21:13:39.137694+0800  Level 20: [PrivateApi.on_open:160] WebSocket连接建立成功
2025-09-10 21:13:39.138 | Level 20 | Logger | [OkxGateway.on_connected:1502] Private websocket API connected
2025-09-10T21:13:39.138712+0800  Level 20: [OkxGateway.on_connected:1502] Private websocket API connected
2025-09-10 21:13:39.146 | Level 20 | Logger | [PublicApi.on_open:160] WebSocket连接建立成功
2025-09-10T21:13:39.146809+0800  Level 20: [PublicApi.on_open:160] WebSocket连接建立成功
2025-09-10 21:13:39.147 | Level 20 | Logger | [OkxGateway.on_connected:1231] Public API connected
2025-09-10T21:13:39.147809+0800  Level 20: [OkxGateway.on_connected:1231] Public API connected
2025-09-10 21:13:39.189 | Level 20 | Logger | [OkxGateway.on_login:1578] Private API login successful
2025-09-10T21:13:39.189783+0800  Level 20: [OkxGateway.on_login:1578] Private API login successful
2025-09-10 23:38:12.736 | Level 20 | Logger | [OkxGateway.enable_log_mode:1000] 日志模式已启用，将跟踪ticker和depth推送信息
2025-09-10T23:38:12.736389+0800  Level 20: [OkxGateway.enable_log_mode:1000] 日志模式已启用，将跟踪ticker和depth推送信息
2025-09-10 23:38:49.275 | Level 20 | Logger | [RestApi.init:148] REST客户端初始化，地址：https://www.okx.com
2025-09-10T23:38:49.275934+0800  Level 20: [RestApi.init:148] REST客户端初始化，地址：https://www.okx.com
2025-09-10 23:38:49.277 | Level 20 | Logger | [RestApi.init:152] 使用代理：http://127.0.0.1:7890
2025-09-10T23:38:49.277931+0800  Level 20: [RestApi.init:152] 使用代理：http://127.0.0.1:7890
2025-09-10 23:38:49.278 | Level 20 | Logger | [RestApi.start:160] 创建新的事件循环
2025-09-10T23:38:49.278932+0800  Level 20: [RestApi.start:160] 创建新的事件循环
2025-09-10 23:38:49.279 | Level 20 | Logger | [RestApi.start_event_loop:392] 启动事件循环后台线程
2025-09-10T23:38:49.279932+0800  Level 20: [RestApi.start_event_loop:392] 启动事件循环后台线程
2025-09-10 23:38:49.280 | Level 20 | Logger | [RestApi.run_event_loop:403] 事件循环开始运行
2025-09-10T23:38:49.280932+0800  Level 20: [RestApi.run_event_loop:403] 事件循环开始运行
2025-09-10 23:38:49.281 | Level 20 | Logger | [OkxGateway.connect:526] REST API started
2025-09-10T23:38:49.281932+0800  Level 20: [OkxGateway.connect:526] REST API started
2025-09-10 23:38:49.289 | Level 20 | Logger | [RestApi._get_response:269] 创建新的TCP连接器
2025-09-10T23:38:49.289932+0800  Level 20: [RestApi._get_response:269] 创建新的TCP连接器
2025-09-10 23:38:49.291 | Level 20 | Logger | [RestApi._get_response:276] 创建新的客户端会话
2025-09-10T23:38:49.291825+0800  Level 20: [RestApi._get_response:276] 创建新的客户端会话
2025-09-10 23:38:49.775 | Level 20 | Logger | [RestApi._get_response:303] 响应内容：{"msg":"Your IP *************** is not included in your API key's 5830298e-84a1-4cb5-bfa6-7bd17a61b481 IP whitelist.","code":"50110"}
2025-09-10T23:38:49.775527+0800  Level 20: [RestApi._get_response:303] 响应内容：{"msg":"Your IP *************** is not included in your API key's 5830298e-84a1-4cb5-bfa6-7bd17a61b481 IP whitelist.","code":"50110"}
2025-09-10 23:38:49.776 | Level 20 | Logger | [RestApi._process_request:321] 请求处理失败，状态码：401
2025-09-10T23:38:49.776533+0800  Level 20: [RestApi._process_request:321] 请求处理失败，状态码：401
2025-09-10 23:38:49.842 | Level 20 | Logger | [RestApi._log_error_to_db:369] REST错误已入库，错误码：401
2025-09-10T23:38:49.842983+0800  Level 20: [RestApi._log_error_to_db:369] REST错误已入库，错误码：401
2025-09-10 23:38:49.843 | Level 20 | Logger | [RestApi.on_failed:227] RestClient on failed----------
2025-09-10T23:38:49.843985+0800  Level 20: [RestApi.on_failed:227] RestClient on failed----------
2025-09-10 23:38:49.847 | Level 20 | Logger | [RestApi.on_failed:228] request : POST /api/v5/account/set-position-mode because 401: 
headers: {'OK-ACCESS-KEY': '5830298e-84a1-4cb5-bfa6-7bd17a61b481', 'OK-ACCESS-SIGN': 'tAWq/KTtnC8+GprWCIIflUr9Blz+wtFrZ/q9B4Q7tjE=', 'OK-ACCESS-TIMESTAMP': '2025-09-10T15:38:49.292Z', 'OK-ACCESS-PASSPHRASE': 'Dfgytrb88$', 'Content-Type': 'application/json', 'x-simulated-trading': '1'}
params: None
data: {"posMode": "net_mode"}
json: None
response:{"msg":"Your IP *************** is not included in your API key's 5830298e-84a1-4cb5-bfa6-7bd17a61b481 IP whitelist.","code":"50110"}

2025-09-10T23:38:49.847990+0800  Level 20: [RestApi.on_failed:228] request : POST /api/v5/account/set-position-mode because 401: 
headers: {'OK-ACCESS-KEY': '5830298e-84a1-4cb5-bfa6-7bd17a61b481', 'OK-ACCESS-SIGN': 'tAWq/KTtnC8+GprWCIIflUr9Blz+wtFrZ/q9B4Q7tjE=', 'OK-ACCESS-TIMESTAMP': '2025-09-10T15:38:49.292Z', 'OK-ACCESS-PASSPHRASE': 'Dfgytrb88$', 'Content-Type': 'application/json', 'x-simulated-trading': '1'}
params: None
data: {"posMode": "net_mode"}
json: None
response:{"msg":"Your IP *************** is not included in your API key's 5830298e-84a1-4cb5-bfa6-7bd17a61b481 IP whitelist.","code":"50110"}

2025-09-10 23:38:49.848 | Level 20 | Logger | [OkxGateway.on_query_contract:777] SWAP contract data received
2025-09-10T23:38:49.848983+0800  Level 20: [OkxGateway.on_query_contract:777] SWAP contract data received
2025-09-10 23:38:49.850 | Level 20 | Logger | [PublicApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10T23:38:49.850809+0800  Level 20: [PublicApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10 23:38:49.851 | Level 20 | Logger | [PublicApi.init:110] WebSocket客户端初始化，地址：wss://wspap.okx.com:8443/ws/v5/public?brokerId=9999
2025-09-10T23:38:49.851948+0800  Level 20: [PublicApi.init:110] WebSocket客户端初始化，地址：wss://wspap.okx.com:8443/ws/v5/public?brokerId=9999
2025-09-10 23:38:49.852 | Level 20 | Logger | [PublicApi.run:191] 尝试连接到服务器：wss://wspap.okx.com:8443/ws/v5/public?brokerId=9999
2025-09-10T23:38:49.852457+0800  Level 20: [PublicApi.run:191] 尝试连接到服务器：wss://wspap.okx.com:8443/ws/v5/public?brokerId=9999
2025-09-10 23:38:49.853 | Level 20 | Logger | [PublicApi.start:122] WebSocket客户端启动
2025-09-10T23:38:49.853884+0800  Level 20: [PublicApi.start:122] WebSocket客户端启动
2025-09-10 23:38:49.854 | Level 20 | Logger | [OkxGateway.start_subscribe_thread:995] OKX订阅流控线程已启动
2025-09-10T23:38:49.854887+0800  Level 20: [OkxGateway.start_subscribe_thread:995] OKX订阅流控线程已启动
2025-09-10 23:38:49.855 | Level 20 | Logger | [PrivateApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10T23:38:49.855887+0800  Level 20: [PrivateApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10 23:38:49.856 | Level 20 | Logger | [PrivateApi.init:110] WebSocket客户端初始化，地址：wss://wspap.okx.com:8443/ws/v5/private?brokerId=9999
2025-09-10T23:38:49.856887+0800  Level 20: [PrivateApi.init:110] WebSocket客户端初始化，地址：wss://wspap.okx.com:8443/ws/v5/private?brokerId=9999
2025-09-10 23:38:49.856 | Level 20 | Logger | [PrivateApi.run:191] 尝试连接到服务器：wss://wspap.okx.com:8443/ws/v5/private?brokerId=9999
2025-09-10T23:38:49.856887+0800  Level 20: [PrivateApi.run:191] 尝试连接到服务器：wss://wspap.okx.com:8443/ws/v5/private?brokerId=9999
2025-09-10 23:38:49.857 | Level 20 | Logger | [PrivateApi.start:122] WebSocket客户端启动
2025-09-10T23:38:49.857887+0800  Level 20: [PrivateApi.start:122] WebSocket客户端启动
2025-09-10 23:38:49.857 | Level 20 | Logger | [OkxGateway.on_query_time:605] server time: 2025-09-10 23:38:48.967000, local time: 2025-09-10 23:38:49.850296
2025-09-10T23:38:49.857887+0800  Level 20: [OkxGateway.on_query_time:605] server time: 2025-09-10 23:38:48.967000, local time: 2025-09-10 23:38:49.850296
2025-09-10 23:38:50.022 | Level 20 | Logger | [RestApi._get_response:303] 响应内容：{"msg":"Your IP *************** is not included in your API key's 5830298e-84a1-4cb5-bfa6-7bd17a61b481 IP whitelist.","code":"50110"}
2025-09-10T23:38:50.022555+0800  Level 20: [RestApi._get_response:303] 响应内容：{"msg":"Your IP *************** is not included in your API key's 5830298e-84a1-4cb5-bfa6-7bd17a61b481 IP whitelist.","code":"50110"}
2025-09-10 23:38:50.022 | Level 20 | Logger | [RestApi._process_request:321] 请求处理失败，状态码：401
2025-09-10T23:38:50.022555+0800  Level 20: [RestApi._process_request:321] 请求处理失败，状态码：401
2025-09-10 23:38:50.030 | Level 20 | Logger | [RestApi._log_error_to_db:369] REST错误已入库，错误码：401
2025-09-10T23:38:50.030308+0800  Level 20: [RestApi._log_error_to_db:369] REST错误已入库，错误码：401
2025-09-10 23:38:50.030 | Level 20 | Logger | [RestApi.on_failed:227] RestClient on failed----------
2025-09-10T23:38:50.030308+0800  Level 20: [RestApi.on_failed:227] RestClient on failed----------
2025-09-10 23:38:50.031 | Level 20 | Logger | [RestApi.on_failed:228] request : GET /api/v5/trade/orders-pending because 401: 
headers: {'OK-ACCESS-KEY': '5830298e-84a1-4cb5-bfa6-7bd17a61b481', 'OK-ACCESS-SIGN': '8umeULxdALE1E/rFn8fjKSEPUFWXhs3m3LyScbevLDY=', 'OK-ACCESS-TIMESTAMP': '2025-09-10T15:38:48.967Z', 'OK-ACCESS-PASSPHRASE': 'Dfgytrb88$', 'Content-Type': 'application/json', 'x-simulated-trading': '1'}
params: None
data: 
json: None
response:{"msg":"Your IP *************** is not included in your API key's 5830298e-84a1-4cb5-bfa6-7bd17a61b481 IP whitelist.","code":"50110"}

2025-09-10T23:38:50.031310+0800  Level 20: [RestApi.on_failed:228] request : GET /api/v5/trade/orders-pending because 401: 
headers: {'OK-ACCESS-KEY': '5830298e-84a1-4cb5-bfa6-7bd17a61b481', 'OK-ACCESS-SIGN': '8umeULxdALE1E/rFn8fjKSEPUFWXhs3m3LyScbevLDY=', 'OK-ACCESS-TIMESTAMP': '2025-09-10T15:38:48.967Z', 'OK-ACCESS-PASSPHRASE': 'Dfgytrb88$', 'Content-Type': 'application/json', 'x-simulated-trading': '1'}
params: None
data: 
json: None
response:{"msg":"Your IP *************** is not included in your API key's 5830298e-84a1-4cb5-bfa6-7bd17a61b481 IP whitelist.","code":"50110"}

2025-09-10 23:38:50.133 | Level 20 | Logger | [PrivateApi.on_open:160] WebSocket连接建立成功
2025-09-10T23:38:50.133834+0800  Level 20: [PrivateApi.on_open:160] WebSocket连接建立成功
2025-09-10 23:38:50.134 | Level 20 | Logger | [OkxGateway.on_connected:1502] Private websocket API connected
2025-09-10T23:38:50.134494+0800  Level 20: [OkxGateway.on_connected:1502] Private websocket API connected
2025-09-10 23:38:50.184 | Level 20 | Logger | [OkxGateway.on_api_error:1565] Private API request failed, status code: 50110, message: Your IP 2400:8a20:123:34:be24:11ff:fe8a:c5f2 is not in linking trusted IP addresses.
2025-09-10T23:38:50.184967+0800  Level 20: [OkxGateway.on_api_error:1565] Private API request failed, status code: 50110, message: Your IP 2400:8a20:123:34:be24:11ff:fe8a:c5f2 is not in linking trusted IP addresses.
2025-09-10 23:38:50.185 | Level 20 | Logger | [PrivateApi.on_close:164] WebSocket连接断开，状态码：4001，原因：Login failed.
2025-09-10T23:38:50.185474+0800  Level 20: [PrivateApi.on_close:164] WebSocket连接断开，状态码：4001，原因：Login failed.
2025-09-10 23:38:50.205 | Level 20 | Logger | [PrivateApi._log_error_to_db:63] WebSocket错误已入库，错误码：4001
2025-09-10T23:38:50.205481+0800  Level 20: [PrivateApi._log_error_to_db:63] WebSocket错误已入库，错误码：4001
2025-09-10 23:38:50.205 | Level 20 | Logger | [OkxGateway.on_disconnected:1512] Private API disconnected, code: 4001, msg: Login failed.
2025-09-10T23:38:50.205481+0800  Level 20: [OkxGateway.on_disconnected:1512] Private API disconnected, code: 4001, msg: Login failed.
2025-09-10 23:38:50.387 | Level 20 | Logger | [PublicApi.on_open:160] WebSocket连接建立成功
2025-09-10T23:38:50.387680+0800  Level 20: [PublicApi.on_open:160] WebSocket连接建立成功
2025-09-10 23:38:50.388 | Level 20 | Logger | [OkxGateway.on_connected:1231] Public API connected
2025-09-10T23:38:50.388687+0800  Level 20: [OkxGateway.on_connected:1231] Public API connected
2025-09-10 23:39:29.981 | Level 20 | Logger | [OkxGateway.enable_log_mode:1000] 日志模式已启用，将跟踪ticker和depth推送信息
2025-09-10T23:39:29.981637+0800  Level 20: [OkxGateway.enable_log_mode:1000] 日志模式已启用，将跟踪ticker和depth推送信息
2025-09-10 23:39:36.979 | Level 20 | Logger | [RestApi.init:148] REST客户端初始化，地址：https://www.okx.com
2025-09-10T23:39:36.979027+0800  Level 20: [RestApi.init:148] REST客户端初始化，地址：https://www.okx.com
2025-09-10 23:39:36.979 | Level 20 | Logger | [RestApi.init:152] 使用代理：http://127.0.0.1:7890
2025-09-10T23:39:36.979994+0800  Level 20: [RestApi.init:152] 使用代理：http://127.0.0.1:7890
2025-09-10 23:39:36.980 | Level 20 | Logger | [RestApi.start:160] 创建新的事件循环
2025-09-10T23:39:36.980509+0800  Level 20: [RestApi.start:160] 创建新的事件循环
2025-09-10 23:39:36.981 | Level 20 | Logger | [RestApi.start_event_loop:392] 启动事件循环后台线程
2025-09-10T23:39:36.981516+0800  Level 20: [RestApi.start_event_loop:392] 启动事件循环后台线程
2025-09-10 23:39:36.981 | Level 20 | Logger | [RestApi.run_event_loop:403] 事件循环开始运行
2025-09-10T23:39:36.981516+0800  Level 20: [RestApi.run_event_loop:403] 事件循环开始运行
2025-09-10 23:39:36.983 | Level 20 | Logger | [OkxGateway.connect:526] REST API started
2025-09-10T23:39:36.983147+0800  Level 20: [OkxGateway.connect:526] REST API started
2025-09-10 23:39:36.992 | Level 20 | Logger | [RestApi._get_response:269] 创建新的TCP连接器
2025-09-10T23:39:36.992938+0800  Level 20: [RestApi._get_response:269] 创建新的TCP连接器
2025-09-10 23:39:36.992 | Level 20 | Logger | [RestApi._get_response:276] 创建新的客户端会话
2025-09-10T23:39:36.992938+0800  Level 20: [RestApi._get_response:276] 创建新的客户端会话
2025-09-10 23:39:37.430 | Level 20 | Logger | [RestApi._get_response:303] 响应内容：{"msg":"Your IP *************** is not included in your API key's 5830298e-84a1-4cb5-bfa6-7bd17a61b481 IP whitelist.","code":"50110"}
2025-09-10T23:39:37.430075+0800  Level 20: [RestApi._get_response:303] 响应内容：{"msg":"Your IP *************** is not included in your API key's 5830298e-84a1-4cb5-bfa6-7bd17a61b481 IP whitelist.","code":"50110"}
2025-09-10 23:39:37.430 | Level 20 | Logger | [RestApi._process_request:321] 请求处理失败，状态码：401
2025-09-10T23:39:37.430632+0800  Level 20: [RestApi._process_request:321] 请求处理失败，状态码：401
2025-09-10 23:39:37.459 | Level 20 | Logger | [RestApi._log_error_to_db:369] REST错误已入库，错误码：401
2025-09-10T23:39:37.459896+0800  Level 20: [RestApi._log_error_to_db:369] REST错误已入库，错误码：401
2025-09-10 23:39:37.460 | Level 20 | Logger | [RestApi.on_failed:227] RestClient on failed----------
2025-09-10T23:39:37.460907+0800  Level 20: [RestApi.on_failed:227] RestClient on failed----------
2025-09-10 23:39:37.460 | Level 20 | Logger | [RestApi.on_failed:228] request : POST /api/v5/account/set-position-mode because 401: 
headers: {'OK-ACCESS-KEY': '5830298e-84a1-4cb5-bfa6-7bd17a61b481', 'OK-ACCESS-SIGN': 'WDQF5ccOvlg+xv7VDoVZVeVMV+rC/hs6mvv07u3GyKA=', 'OK-ACCESS-TIMESTAMP': '2025-09-10T15:39:36.994Z', 'OK-ACCESS-PASSPHRASE': 'Dfgytrb88$', 'Content-Type': 'application/json'}
params: None
data: {"posMode": "net_mode"}
json: None
response:{"msg":"Your IP *************** is not included in your API key's 5830298e-84a1-4cb5-bfa6-7bd17a61b481 IP whitelist.","code":"50110"}

2025-09-10T23:39:37.460907+0800  Level 20: [RestApi.on_failed:228] request : POST /api/v5/account/set-position-mode because 401: 
headers: {'OK-ACCESS-KEY': '5830298e-84a1-4cb5-bfa6-7bd17a61b481', 'OK-ACCESS-SIGN': 'WDQF5ccOvlg+xv7VDoVZVeVMV+rC/hs6mvv07u3GyKA=', 'OK-ACCESS-TIMESTAMP': '2025-09-10T15:39:36.994Z', 'OK-ACCESS-PASSPHRASE': 'Dfgytrb88$', 'Content-Type': 'application/json'}
params: None
data: {"posMode": "net_mode"}
json: None
response:{"msg":"Your IP *************** is not included in your API key's 5830298e-84a1-4cb5-bfa6-7bd17a61b481 IP whitelist.","code":"50110"}

2025-09-10 23:39:37.511 | Level 20 | Logger | [OkxGateway.on_query_contract:777] SWAP contract data received
2025-09-10T23:39:37.511553+0800  Level 20: [OkxGateway.on_query_contract:777] SWAP contract data received
2025-09-10 23:39:37.512 | Level 20 | Logger | [PublicApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10T23:39:37.512554+0800  Level 20: [PublicApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10 23:39:37.513 | Level 20 | Logger | [PublicApi.init:110] WebSocket客户端初始化，地址：wss://ws.okx.com:8443/ws/v5/public
2025-09-10T23:39:37.513556+0800  Level 20: [PublicApi.init:110] WebSocket客户端初始化，地址：wss://ws.okx.com:8443/ws/v5/public
2025-09-10 23:39:37.514 | Level 20 | Logger | [PublicApi.run:191] 尝试连接到服务器：wss://ws.okx.com:8443/ws/v5/public
2025-09-10T23:39:37.514692+0800  Level 20: [PublicApi.run:191] 尝试连接到服务器：wss://ws.okx.com:8443/ws/v5/public
2025-09-10 23:39:37.515 | Level 20 | Logger | [PublicApi.start:122] WebSocket客户端启动
2025-09-10T23:39:37.515921+0800  Level 20: [PublicApi.start:122] WebSocket客户端启动
2025-09-10 23:39:37.516 | Level 20 | Logger | [OkxGateway.start_subscribe_thread:995] OKX订阅流控线程已启动
2025-09-10T23:39:37.516817+0800  Level 20: [OkxGateway.start_subscribe_thread:995] OKX订阅流控线程已启动
2025-09-10 23:39:37.516 | Level 20 | Logger | [PrivateApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10T23:39:37.516817+0800  Level 20: [PrivateApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10 23:39:37.516 | Level 20 | Logger | [PrivateApi.init:110] WebSocket客户端初始化，地址：wss://ws.okx.com:8443/ws/v5/private
2025-09-10T23:39:37.516817+0800  Level 20: [PrivateApi.init:110] WebSocket客户端初始化，地址：wss://ws.okx.com:8443/ws/v5/private
2025-09-10 23:39:37.516 | Level 20 | Logger | [PrivateApi.run:191] 尝试连接到服务器：wss://ws.okx.com:8443/ws/v5/private
2025-09-10T23:39:37.516817+0800  Level 20: [PrivateApi.run:191] 尝试连接到服务器：wss://ws.okx.com:8443/ws/v5/private
2025-09-10 23:39:37.517 | Level 20 | Logger | [PrivateApi.start:122] WebSocket客户端启动
2025-09-10T23:39:37.517822+0800  Level 20: [PrivateApi.start:122] WebSocket客户端启动
2025-09-10 23:39:37.704 | Level 20 | Logger | [RestApi._get_response:303] 响应内容：{"msg":"Your IP *************** is not included in your API key's 5830298e-84a1-4cb5-bfa6-7bd17a61b481 IP whitelist.","code":"50110"}
2025-09-10T23:39:37.704269+0800  Level 20: [RestApi._get_response:303] 响应内容：{"msg":"Your IP *************** is not included in your API key's 5830298e-84a1-4cb5-bfa6-7bd17a61b481 IP whitelist.","code":"50110"}
2025-09-10 23:39:37.704 | Level 20 | Logger | [RestApi._process_request:321] 请求处理失败，状态码：401
2025-09-10T23:39:37.704269+0800  Level 20: [RestApi._process_request:321] 请求处理失败，状态码：401
2025-09-10 23:39:37.712 | Level 20 | Logger | [RestApi._log_error_to_db:369] REST错误已入库，错误码：401
2025-09-10T23:39:37.712276+0800  Level 20: [RestApi._log_error_to_db:369] REST错误已入库，错误码：401
2025-09-10 23:39:37.712 | Level 20 | Logger | [RestApi.on_failed:227] RestClient on failed----------
2025-09-10T23:39:37.712276+0800  Level 20: [RestApi.on_failed:227] RestClient on failed----------
2025-09-10 23:39:37.712 | Level 20 | Logger | [RestApi.on_failed:228] request : GET /api/v5/trade/orders-pending because 401: 
headers: {'OK-ACCESS-KEY': '5830298e-84a1-4cb5-bfa6-7bd17a61b481', 'OK-ACCESS-SIGN': '1oMgxkt45b3qLmRUkGBCNkg7Psg8mhJAK8gl4tgHdGA=', 'OK-ACCESS-TIMESTAMP': '2025-09-10T15:39:37.515Z', 'OK-ACCESS-PASSPHRASE': 'Dfgytrb88$', 'Content-Type': 'application/json'}
params: None
data: 
json: None
response:{"msg":"Your IP *************** is not included in your API key's 5830298e-84a1-4cb5-bfa6-7bd17a61b481 IP whitelist.","code":"50110"}

2025-09-10T23:39:37.712276+0800  Level 20: [RestApi.on_failed:228] request : GET /api/v5/trade/orders-pending because 401: 
headers: {'OK-ACCESS-KEY': '5830298e-84a1-4cb5-bfa6-7bd17a61b481', 'OK-ACCESS-SIGN': '1oMgxkt45b3qLmRUkGBCNkg7Psg8mhJAK8gl4tgHdGA=', 'OK-ACCESS-TIMESTAMP': '2025-09-10T15:39:37.515Z', 'OK-ACCESS-PASSPHRASE': 'Dfgytrb88$', 'Content-Type': 'application/json'}
params: None
data: 
json: None
response:{"msg":"Your IP *************** is not included in your API key's 5830298e-84a1-4cb5-bfa6-7bd17a61b481 IP whitelist.","code":"50110"}

2025-09-10 23:39:37.818 | Level 20 | Logger | [PrivateApi.on_open:160] WebSocket连接建立成功
2025-09-10T23:39:37.818606+0800  Level 20: [PrivateApi.on_open:160] WebSocket连接建立成功
2025-09-10 23:39:37.819 | Level 20 | Logger | [OkxGateway.on_connected:1502] Private websocket API connected
2025-09-10T23:39:37.819611+0800  Level 20: [OkxGateway.on_connected:1502] Private websocket API connected
2025-09-10 23:39:37.860 | Level 20 | Logger | [PublicApi.on_open:160] WebSocket连接建立成功
2025-09-10T23:39:37.860329+0800  Level 20: [PublicApi.on_open:160] WebSocket连接建立成功
2025-09-10 23:39:37.860 | Level 20 | Logger | [OkxGateway.on_connected:1231] Public API connected
2025-09-10T23:39:37.860329+0800  Level 20: [OkxGateway.on_connected:1231] Public API connected
2025-09-10 23:39:37.869 | Level 20 | Logger | [OkxGateway.on_api_error:1565] Private API request failed, status code: 50110, message: Your IP 2400:8a20:123:34:be24:11ff:fe8a:c5f2 is not in linking trusted IP addresses.
2025-09-10T23:39:37.869754+0800  Level 20: [OkxGateway.on_api_error:1565] Private API request failed, status code: 50110, message: Your IP 2400:8a20:123:34:be24:11ff:fe8a:c5f2 is not in linking trusted IP addresses.
2025-09-10 23:39:37.871 | Level 20 | Logger | [PrivateApi.on_close:164] WebSocket连接断开，状态码：4001，原因：Login failed.
2025-09-10T23:39:37.871014+0800  Level 20: [PrivateApi.on_close:164] WebSocket连接断开，状态码：4001，原因：Login failed.
2025-09-10 23:39:37.883 | Level 20 | Logger | [PrivateApi._log_error_to_db:63] WebSocket错误已入库，错误码：4001
2025-09-10T23:39:37.883812+0800  Level 20: [PrivateApi._log_error_to_db:63] WebSocket错误已入库，错误码：4001
2025-09-10 23:39:37.885 | Level 20 | Logger | [OkxGateway.on_disconnected:1512] Private API disconnected, code: 4001, msg: Login failed.
2025-09-10T23:39:37.885508+0800  Level 20: [OkxGateway.on_disconnected:1512] Private API disconnected, code: 4001, msg: Login failed.
2025-09-10 23:39:38.422 | Level 20 | Logger | [OkxGateway.on_query_time:605] server time: 2025-09-10 23:39:37.589000, local time: 2025-09-10 23:39:38.422026
2025-09-10T23:39:38.422025+0800  Level 20: [OkxGateway.on_query_time:605] server time: 2025-09-10 23:39:37.589000, local time: 2025-09-10 23:39:38.422026
2025-09-10 23:40:55.107 | Level 20 | Logger | [OkxGateway.enable_log_mode:1000] 日志模式已启用，将跟踪ticker和depth推送信息
2025-09-10T23:40:55.107265+0800  Level 20: [OkxGateway.enable_log_mode:1000] 日志模式已启用，将跟踪ticker和depth推送信息
2025-09-10 23:41:00.188 | Level 20 | Logger | [RestApi.init:148] REST客户端初始化，地址：https://www.okx.com
2025-09-10T23:41:00.188049+0800  Level 20: [RestApi.init:148] REST客户端初始化，地址：https://www.okx.com
2025-09-10 23:41:00.188 | Level 20 | Logger | [RestApi.init:152] 使用代理：http://127.0.0.1:7890
2025-09-10T23:41:00.188049+0800  Level 20: [RestApi.init:152] 使用代理：http://127.0.0.1:7890
2025-09-10 23:41:00.189 | Level 20 | Logger | [RestApi.start:160] 创建新的事件循环
2025-09-10T23:41:00.189056+0800  Level 20: [RestApi.start:160] 创建新的事件循环
2025-09-10 23:41:00.190 | Level 20 | Logger | [RestApi.start_event_loop:392] 启动事件循环后台线程
2025-09-10T23:41:00.190051+0800  Level 20: [RestApi.start_event_loop:392] 启动事件循环后台线程
2025-09-10 23:41:00.190 | Level 20 | Logger | [RestApi.run_event_loop:403] 事件循环开始运行
2025-09-10T23:41:00.190051+0800  Level 20: [RestApi.run_event_loop:403] 事件循环开始运行
2025-09-10 23:41:00.191 | Level 20 | Logger | [OkxGateway.connect:526] REST API started
2025-09-10T23:41:00.191052+0800  Level 20: [OkxGateway.connect:526] REST API started
2025-09-10 23:41:00.199 | Level 20 | Logger | [RestApi._get_response:269] 创建新的TCP连接器
2025-09-10T23:41:00.199737+0800  Level 20: [RestApi._get_response:269] 创建新的TCP连接器
2025-09-10 23:41:00.200 | Level 20 | Logger | [RestApi._get_response:276] 创建新的客户端会话
2025-09-10T23:41:00.200737+0800  Level 20: [RestApi._get_response:276] 创建新的客户端会话
2025-09-10 23:41:00.619 | Level 20 | Logger | [OkxGateway.on_query_time:605] server time: 2025-09-10 23:40:59.795000, local time: 2025-09-10 23:41:00.619047
2025-09-10T23:41:00.619046+0800  Level 20: [OkxGateway.on_query_time:605] server time: 2025-09-10 23:40:59.795000, local time: 2025-09-10 23:41:00.619047
2025-09-10 23:41:00.624 | Level 20 | Logger | [OkxGateway.on_query_contract:777] SWAP contract data received
2025-09-10T23:41:00.624019+0800  Level 20: [OkxGateway.on_query_contract:777] SWAP contract data received
2025-09-10 23:41:00.624 | Level 20 | Logger | [PublicApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10T23:41:00.624019+0800  Level 20: [PublicApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10 23:41:00.625 | Level 20 | Logger | [PublicApi.init:110] WebSocket客户端初始化，地址：wss://ws.okx.com:8443/ws/v5/public
2025-09-10T23:41:00.625019+0800  Level 20: [PublicApi.init:110] WebSocket客户端初始化，地址：wss://ws.okx.com:8443/ws/v5/public
2025-09-10 23:41:00.626 | Level 20 | Logger | [PublicApi.run:191] 尝试连接到服务器：wss://ws.okx.com:8443/ws/v5/public
2025-09-10T23:41:00.626514+0800  Level 20: [PublicApi.run:191] 尝试连接到服务器：wss://ws.okx.com:8443/ws/v5/public
2025-09-10 23:41:00.628 | Level 20 | Logger | [PublicApi.start:122] WebSocket客户端启动
2025-09-10T23:41:00.628106+0800  Level 20: [PublicApi.start:122] WebSocket客户端启动
2025-09-10 23:41:00.629 | Level 20 | Logger | [OkxGateway.start_subscribe_thread:995] OKX订阅流控线程已启动
2025-09-10T23:41:00.629169+0800  Level 20: [OkxGateway.start_subscribe_thread:995] OKX订阅流控线程已启动
2025-09-10 23:41:00.629 | Level 20 | Logger | [PrivateApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10T23:41:00.629693+0800  Level 20: [PrivateApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10 23:41:00.630 | Level 20 | Logger | [PrivateApi.init:110] WebSocket客户端初始化，地址：wss://ws.okx.com:8443/ws/v5/private
2025-09-10T23:41:00.630215+0800  Level 20: [PrivateApi.init:110] WebSocket客户端初始化，地址：wss://ws.okx.com:8443/ws/v5/private
2025-09-10 23:41:00.631 | Level 20 | Logger | [PrivateApi.run:191] 尝试连接到服务器：wss://ws.okx.com:8443/ws/v5/private
2025-09-10T23:41:00.631264+0800  Level 20: [PrivateApi.run:191] 尝试连接到服务器：wss://ws.okx.com:8443/ws/v5/private
2025-09-10 23:41:00.631 | Level 20 | Logger | [PrivateApi.start:122] WebSocket客户端启动
2025-09-10T23:41:00.631787+0800  Level 20: [PrivateApi.start:122] WebSocket客户端启动
2025-09-10 23:41:00.655 | Level 20 | Logger | [RestApi._get_response:303] 响应内容：{"msg":"Your IP *************** is not included in your API key's 5830298e-84a1-4cb5-bfa6-7bd17a61b481 IP whitelist.","code":"50110"}
2025-09-10T23:41:00.655994+0800  Level 20: [RestApi._get_response:303] 响应内容：{"msg":"Your IP *************** is not included in your API key's 5830298e-84a1-4cb5-bfa6-7bd17a61b481 IP whitelist.","code":"50110"}
2025-09-10 23:41:00.655 | Level 20 | Logger | [RestApi._process_request:321] 请求处理失败，状态码：401
2025-09-10T23:41:00.655994+0800  Level 20: [RestApi._process_request:321] 请求处理失败，状态码：401
2025-09-10 23:41:00.687 | Level 20 | Logger | [RestApi._log_error_to_db:369] REST错误已入库，错误码：401
2025-09-10T23:41:00.687420+0800  Level 20: [RestApi._log_error_to_db:369] REST错误已入库，错误码：401
2025-09-10 23:41:00.688 | Level 20 | Logger | [RestApi.on_failed:227] RestClient on failed----------
2025-09-10T23:41:00.688428+0800  Level 20: [RestApi.on_failed:227] RestClient on failed----------
2025-09-10 23:41:00.688 | Level 20 | Logger | [RestApi.on_failed:228] request : POST /api/v5/account/set-position-mode because 401: 
headers: {'OK-ACCESS-KEY': '5830298e-84a1-4cb5-bfa6-7bd17a61b481', 'OK-ACCESS-SIGN': 'oUxBAO0DEDeigXKJlKefRNJeygyg7VpV3P3EgkSX3U8=', 'OK-ACCESS-TIMESTAMP': '2025-09-10T15:41:00.203Z', 'OK-ACCESS-PASSPHRASE': 'Dfgytrb88$', 'Content-Type': 'application/json'}
params: None
data: {"posMode": "net_mode"}
json: None
response:{"msg":"Your IP *************** is not included in your API key's 5830298e-84a1-4cb5-bfa6-7bd17a61b481 IP whitelist.","code":"50110"}

2025-09-10T23:41:00.688428+0800  Level 20: [RestApi.on_failed:228] request : POST /api/v5/account/set-position-mode because 401: 
headers: {'OK-ACCESS-KEY': '5830298e-84a1-4cb5-bfa6-7bd17a61b481', 'OK-ACCESS-SIGN': 'oUxBAO0DEDeigXKJlKefRNJeygyg7VpV3P3EgkSX3U8=', 'OK-ACCESS-TIMESTAMP': '2025-09-10T15:41:00.203Z', 'OK-ACCESS-PASSPHRASE': 'Dfgytrb88$', 'Content-Type': 'application/json'}
params: None
data: {"posMode": "net_mode"}
json: None
response:{"msg":"Your IP *************** is not included in your API key's 5830298e-84a1-4cb5-bfa6-7bd17a61b481 IP whitelist.","code":"50110"}

2025-09-10 23:41:00.808 | Level 20 | Logger | [RestApi._get_response:303] 响应内容：{"msg":"Your IP *************** is not included in your API key's 5830298e-84a1-4cb5-bfa6-7bd17a61b481 IP whitelist.","code":"50110"}
2025-09-10T23:41:00.808440+0800  Level 20: [RestApi._get_response:303] 响应内容：{"msg":"Your IP *************** is not included in your API key's 5830298e-84a1-4cb5-bfa6-7bd17a61b481 IP whitelist.","code":"50110"}
2025-09-10 23:41:00.808 | Level 20 | Logger | [RestApi._process_request:321] 请求处理失败，状态码：401
2025-09-10T23:41:00.808440+0800  Level 20: [RestApi._process_request:321] 请求处理失败，状态码：401
2025-09-10 23:41:00.821 | Level 20 | Logger | [RestApi._log_error_to_db:369] REST错误已入库，错误码：401
2025-09-10T23:41:00.821018+0800  Level 20: [RestApi._log_error_to_db:369] REST错误已入库，错误码：401
2025-09-10 23:41:00.822 | Level 20 | Logger | [RestApi.on_failed:227] RestClient on failed----------
2025-09-10T23:41:00.822025+0800  Level 20: [RestApi.on_failed:227] RestClient on failed----------
2025-09-10 23:41:00.823 | Level 20 | Logger | [RestApi.on_failed:228] request : GET /api/v5/trade/orders-pending because 401: 
headers: {'OK-ACCESS-KEY': '5830298e-84a1-4cb5-bfa6-7bd17a61b481', 'OK-ACCESS-SIGN': 'rPSA0T7CKT2usPEUzXxn89jG2aJgP0W8HSR5juRVe5Y=', 'OK-ACCESS-TIMESTAMP': '2025-09-10T15:40:59.803Z', 'OK-ACCESS-PASSPHRASE': 'Dfgytrb88$', 'Content-Type': 'application/json'}
params: None
data: 
json: None
response:{"msg":"Your IP *************** is not included in your API key's 5830298e-84a1-4cb5-bfa6-7bd17a61b481 IP whitelist.","code":"50110"}

2025-09-10T23:41:00.823026+0800  Level 20: [RestApi.on_failed:228] request : GET /api/v5/trade/orders-pending because 401: 
headers: {'OK-ACCESS-KEY': '5830298e-84a1-4cb5-bfa6-7bd17a61b481', 'OK-ACCESS-SIGN': 'rPSA0T7CKT2usPEUzXxn89jG2aJgP0W8HSR5juRVe5Y=', 'OK-ACCESS-TIMESTAMP': '2025-09-10T15:40:59.803Z', 'OK-ACCESS-PASSPHRASE': 'Dfgytrb88$', 'Content-Type': 'application/json'}
params: None
data: 
json: None
response:{"msg":"Your IP *************** is not included in your API key's 5830298e-84a1-4cb5-bfa6-7bd17a61b481 IP whitelist.","code":"50110"}

2025-09-10 23:41:01.824 | Level 20 | Logger | [PrivateApi.on_open:160] WebSocket连接建立成功
2025-09-10T23:41:01.824882+0800  Level 20: [PrivateApi.on_open:160] WebSocket连接建立成功
2025-09-10 23:41:01.825 | Level 20 | Logger | [OkxGateway.on_connected:1502] Private websocket API connected
2025-09-10T23:41:01.825194+0800  Level 20: [OkxGateway.on_connected:1502] Private websocket API connected
2025-09-10 23:41:01.854 | Level 20 | Logger | [PublicApi.on_open:160] WebSocket连接建立成功
2025-09-10T23:41:01.854013+0800  Level 20: [PublicApi.on_open:160] WebSocket连接建立成功
2025-09-10 23:41:01.855 | Level 20 | Logger | [OkxGateway.on_connected:1231] Public API connected
2025-09-10T23:41:01.855016+0800  Level 20: [OkxGateway.on_connected:1231] Public API connected
2025-09-10 23:41:02.172 | Level 20 | Logger | [OkxGateway.on_api_error:1565] Private API request failed, status code: 50110, message: Your IP *************** is not in linking trusted IP addresses.
2025-09-10T23:41:02.172579+0800  Level 20: [OkxGateway.on_api_error:1565] Private API request failed, status code: 50110, message: Your IP *************** is not in linking trusted IP addresses.
2025-09-10 23:41:02.174 | Level 20 | Logger | [PrivateApi.on_close:164] WebSocket连接断开，状态码：4001，原因：Login failed.
2025-09-10T23:41:02.174734+0800  Level 20: [PrivateApi.on_close:164] WebSocket连接断开，状态码：4001，原因：Login failed.
2025-09-10 23:41:02.186 | Level 20 | Logger | [PrivateApi._log_error_to_db:63] WebSocket错误已入库，错误码：4001
2025-09-10T23:41:02.186600+0800  Level 20: [PrivateApi._log_error_to_db:63] WebSocket错误已入库，错误码：4001
2025-09-10 23:41:02.188 | Level 20 | Logger | [OkxGateway.on_disconnected:1512] Private API disconnected, code: 4001, msg: Login failed.
2025-09-10T23:41:02.188602+0800  Level 20: [OkxGateway.on_disconnected:1512] Private API disconnected, code: 4001, msg: Login failed.
2025-09-10 23:41:38.706 | Level 20 | Logger | [OkxGateway.enable_log_mode:1000] 日志模式已启用，将跟踪ticker和depth推送信息
2025-09-10T23:41:38.706511+0800  Level 20: [OkxGateway.enable_log_mode:1000] 日志模式已启用，将跟踪ticker和depth推送信息
2025-09-10 23:41:42.258 | Level 20 | Logger | [RestApi.init:148] REST客户端初始化，地址：https://www.okx.com
2025-09-10T23:41:42.258645+0800  Level 20: [RestApi.init:148] REST客户端初始化，地址：https://www.okx.com
2025-09-10 23:41:42.259 | Level 20 | Logger | [RestApi.init:152] 使用代理：http://127.0.0.1:7890
2025-09-10T23:41:42.259654+0800  Level 20: [RestApi.init:152] 使用代理：http://127.0.0.1:7890
2025-09-10 23:41:42.260 | Level 20 | Logger | [RestApi.start:160] 创建新的事件循环
2025-09-10T23:41:42.260651+0800  Level 20: [RestApi.start:160] 创建新的事件循环
2025-09-10 23:41:42.260 | Level 20 | Logger | [RestApi.start_event_loop:392] 启动事件循环后台线程
2025-09-10T23:41:42.260651+0800  Level 20: [RestApi.start_event_loop:392] 启动事件循环后台线程
2025-09-10 23:41:42.261 | Level 20 | Logger | [RestApi.run_event_loop:403] 事件循环开始运行
2025-09-10T23:41:42.261650+0800  Level 20: [RestApi.run_event_loop:403] 事件循环开始运行
2025-09-10 23:41:42.262 | Level 20 | Logger | [OkxGateway.connect:526] REST API started
2025-09-10T23:41:42.262652+0800  Level 20: [OkxGateway.connect:526] REST API started
2025-09-10 23:41:42.270 | Level 20 | Logger | [RestApi._get_response:269] 创建新的TCP连接器
2025-09-10T23:41:42.270650+0800  Level 20: [RestApi._get_response:269] 创建新的TCP连接器
2025-09-10 23:41:42.271 | Level 20 | Logger | [RestApi._get_response:276] 创建新的客户端会话
2025-09-10T23:41:42.271652+0800  Level 20: [RestApi._get_response:276] 创建新的客户端会话
2025-09-10 23:41:42.662 | Level 20 | Logger | [OkxGateway.on_query_time:605] server time: 2025-09-10 23:41:41.849000, local time: 2025-09-10 23:41:42.662190
2025-09-10T23:41:42.662189+0800  Level 20: [OkxGateway.on_query_time:605] server time: 2025-09-10 23:41:41.849000, local time: 2025-09-10 23:41:42.662190
2025-09-10 23:41:42.666 | Level 20 | Logger | [RestApi._get_response:303] 响应内容：{"msg":"Your IP *************** is not included in your API key's 5830298e-84a1-4cb5-bfa6-7bd17a61b481 IP whitelist.","code":"50110"}
2025-09-10T23:41:42.666817+0800  Level 20: [RestApi._get_response:303] 响应内容：{"msg":"Your IP *************** is not included in your API key's 5830298e-84a1-4cb5-bfa6-7bd17a61b481 IP whitelist.","code":"50110"}
2025-09-10 23:41:42.667 | Level 20 | Logger | [RestApi._process_request:321] 请求处理失败，状态码：401
2025-09-10T23:41:42.667767+0800  Level 20: [RestApi._process_request:321] 请求处理失败，状态码：401
2025-09-10 23:41:42.678 | Level 20 | Logger | [RestApi._log_error_to_db:369] REST错误已入库，错误码：401
2025-09-10T23:41:42.678828+0800  Level 20: [RestApi._log_error_to_db:369] REST错误已入库，错误码：401
2025-09-10 23:41:42.678 | Level 20 | Logger | [RestApi.on_failed:227] RestClient on failed----------
2025-09-10T23:41:42.678828+0800  Level 20: [RestApi.on_failed:227] RestClient on failed----------
2025-09-10 23:41:42.678 | Level 20 | Logger | [RestApi.on_failed:228] request : POST /api/v5/account/set-position-mode because 401: 
headers: {'OK-ACCESS-KEY': '5830298e-84a1-4cb5-bfa6-7bd17a61b481', 'OK-ACCESS-SIGN': 'IcP4z9WQgyIV9ftyBjBNuHtFhp/BN6jFtgRWe2lsAcU=', 'OK-ACCESS-TIMESTAMP': '2025-09-10T15:41:42.273Z', 'OK-ACCESS-PASSPHRASE': 'Dfgytrb88$', 'Content-Type': 'application/json'}
params: None
data: {"posMode": "net_mode"}
json: None
response:{"msg":"Your IP *************** is not included in your API key's 5830298e-84a1-4cb5-bfa6-7bd17a61b481 IP whitelist.","code":"50110"}

2025-09-10T23:41:42.678828+0800  Level 20: [RestApi.on_failed:228] request : POST /api/v5/account/set-position-mode because 401: 
headers: {'OK-ACCESS-KEY': '5830298e-84a1-4cb5-bfa6-7bd17a61b481', 'OK-ACCESS-SIGN': 'IcP4z9WQgyIV9ftyBjBNuHtFhp/BN6jFtgRWe2lsAcU=', 'OK-ACCESS-TIMESTAMP': '2025-09-10T15:41:42.273Z', 'OK-ACCESS-PASSPHRASE': 'Dfgytrb88$', 'Content-Type': 'application/json'}
params: None
data: {"posMode": "net_mode"}
json: None
response:{"msg":"Your IP *************** is not included in your API key's 5830298e-84a1-4cb5-bfa6-7bd17a61b481 IP whitelist.","code":"50110"}

2025-09-10 23:41:42.730 | Level 20 | Logger | [OkxGateway.on_query_contract:777] SWAP contract data received
2025-09-10T23:41:42.730404+0800  Level 20: [OkxGateway.on_query_contract:777] SWAP contract data received
2025-09-10 23:41:42.730 | Level 20 | Logger | [PublicApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10T23:41:42.730404+0800  Level 20: [PublicApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10 23:41:42.731 | Level 20 | Logger | [PublicApi.init:110] WebSocket客户端初始化，地址：wss://ws.okx.com:8443/ws/v5/public
2025-09-10T23:41:42.731405+0800  Level 20: [PublicApi.init:110] WebSocket客户端初始化，地址：wss://ws.okx.com:8443/ws/v5/public
2025-09-10 23:41:42.733 | Level 20 | Logger | [PublicApi.run:191] 尝试连接到服务器：wss://ws.okx.com:8443/ws/v5/public
2025-09-10T23:41:42.733654+0800  Level 20: [PublicApi.run:191] 尝试连接到服务器：wss://ws.okx.com:8443/ws/v5/public
2025-09-10 23:41:42.733 | Level 20 | Logger | [PublicApi.start:122] WebSocket客户端启动
2025-09-10T23:41:42.733973+0800  Level 20: [PublicApi.start:122] WebSocket客户端启动
2025-09-10 23:41:42.734 | Level 20 | Logger | [OkxGateway.start_subscribe_thread:995] OKX订阅流控线程已启动
2025-09-10T23:41:42.734478+0800  Level 20: [OkxGateway.start_subscribe_thread:995] OKX订阅流控线程已启动
2025-09-10 23:41:42.734 | Level 20 | Logger | [PrivateApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10T23:41:42.734995+0800  Level 20: [PrivateApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10 23:41:42.734 | Level 20 | Logger | [PrivateApi.init:110] WebSocket客户端初始化，地址：wss://ws.okx.com:8443/ws/v5/private
2025-09-10T23:41:42.734995+0800  Level 20: [PrivateApi.init:110] WebSocket客户端初始化，地址：wss://ws.okx.com:8443/ws/v5/private
2025-09-10 23:41:42.736 | Level 20 | Logger | [PrivateApi.run:191] 尝试连接到服务器：wss://ws.okx.com:8443/ws/v5/private
2025-09-10T23:41:42.736557+0800  Level 20: [PrivateApi.run:191] 尝试连接到服务器：wss://ws.okx.com:8443/ws/v5/private
2025-09-10 23:41:42.737 | Level 20 | Logger | [PrivateApi.start:122] WebSocket客户端启动
2025-09-10T23:41:42.737086+0800  Level 20: [PrivateApi.start:122] WebSocket客户端启动
2025-09-10 23:41:42.904 | Level 20 | Logger | [RestApi._get_response:303] 响应内容：{"msg":"Your IP *************** is not included in your API key's 5830298e-84a1-4cb5-bfa6-7bd17a61b481 IP whitelist.","code":"50110"}
2025-09-10T23:41:42.904126+0800  Level 20: [RestApi._get_response:303] 响应内容：{"msg":"Your IP *************** is not included in your API key's 5830298e-84a1-4cb5-bfa6-7bd17a61b481 IP whitelist.","code":"50110"}
2025-09-10 23:41:42.904 | Level 20 | Logger | [RestApi._process_request:321] 请求处理失败，状态码：401
2025-09-10T23:41:42.904126+0800  Level 20: [RestApi._process_request:321] 请求处理失败，状态码：401
2025-09-10 23:41:42.912 | Level 20 | Logger | [RestApi._log_error_to_db:369] REST错误已入库，错误码：401
2025-09-10T23:41:42.912131+0800  Level 20: [RestApi._log_error_to_db:369] REST错误已入库，错误码：401
2025-09-10 23:41:42.912 | Level 20 | Logger | [RestApi.on_failed:227] RestClient on failed----------
2025-09-10T23:41:42.912131+0800  Level 20: [RestApi.on_failed:227] RestClient on failed----------
2025-09-10 23:41:42.912 | Level 20 | Logger | [RestApi.on_failed:228] request : GET /api/v5/trade/orders-pending because 401: 
headers: {'OK-ACCESS-KEY': '5830298e-84a1-4cb5-bfa6-7bd17a61b481', 'OK-ACCESS-SIGN': 'ji75afOBRVOdFR1uWHdT3dAxTdqCdVKj6ZADS+RHDYg=', 'OK-ACCESS-TIMESTAMP': '2025-09-10T15:41:41.919Z', 'OK-ACCESS-PASSPHRASE': 'Dfgytrb88$', 'Content-Type': 'application/json'}
params: None
data: 
json: None
response:{"msg":"Your IP *************** is not included in your API key's 5830298e-84a1-4cb5-bfa6-7bd17a61b481 IP whitelist.","code":"50110"}

2025-09-10T23:41:42.912131+0800  Level 20: [RestApi.on_failed:228] request : GET /api/v5/trade/orders-pending because 401: 
headers: {'OK-ACCESS-KEY': '5830298e-84a1-4cb5-bfa6-7bd17a61b481', 'OK-ACCESS-SIGN': 'ji75afOBRVOdFR1uWHdT3dAxTdqCdVKj6ZADS+RHDYg=', 'OK-ACCESS-TIMESTAMP': '2025-09-10T15:41:41.919Z', 'OK-ACCESS-PASSPHRASE': 'Dfgytrb88$', 'Content-Type': 'application/json'}
params: None
data: 
json: None
response:{"msg":"Your IP *************** is not included in your API key's 5830298e-84a1-4cb5-bfa6-7bd17a61b481 IP whitelist.","code":"50110"}

2025-09-10 23:41:43.474 | Level 20 | Logger | [PublicApi.on_error:177] WebSocket连接异常：[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)
Traceback (most recent call last):
  File "S:\Envs\evo\Lib\site-packages\websocket\_app.py", line 484, in setSock
    self.sock.connect(
  File "S:\Envs\evo\Lib\site-packages\websocket\_core.py", line 256, in connect
    self.sock, addrs = connect(
                       ^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\websocket\_http.py", line 151, in connect
    sock = _ssl_socket(sock, options.sslopt, hostname)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\websocket\_http.py", line 311, in _ssl_socket
    sock = _wrap_sni_socket(sock, sslopt, hostname, check_hostname)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\websocket\_http.py", line 281, in _wrap_sni_socket
    return context.wrap_socket(
           ^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\ssl.py", line 455, in wrap_socket
    return self.sslsocket_class._create(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\ssl.py", line 1041, in _create
    self.do_handshake()
  File "S:\Envs\evo\Lib\ssl.py", line 1319, in do_handshake
    self._sslobj.do_handshake()
ssl.SSLEOFError: [SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)

2025-09-10T23:41:43.474049+0800  Level 20: [PublicApi.on_error:177] WebSocket连接异常：[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)
Traceback (most recent call last):
  File "S:\Envs\evo\Lib\site-packages\websocket\_app.py", line 484, in setSock
    self.sock.connect(
  File "S:\Envs\evo\Lib\site-packages\websocket\_core.py", line 256, in connect
    self.sock, addrs = connect(
                       ^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\websocket\_http.py", line 151, in connect
    sock = _ssl_socket(sock, options.sslopt, hostname)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\websocket\_http.py", line 311, in _ssl_socket
    sock = _wrap_sni_socket(sock, sslopt, hostname, check_hostname)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\websocket\_http.py", line 281, in _wrap_sni_socket
    return context.wrap_socket(
           ^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\ssl.py", line 455, in wrap_socket
    return self.sslsocket_class._create(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\ssl.py", line 1041, in _create
    self.do_handshake()
  File "S:\Envs\evo\Lib\ssl.py", line 1319, in do_handshake
    self._sslobj.do_handshake()
ssl.SSLEOFError: [SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)

2025-09-10 23:41:43.486 | Level 20 | Logger | [PublicApi._log_error_to_db:63] WebSocket错误已入库，错误码：5
2025-09-10T23:41:43.486795+0800  Level 20: [PublicApi._log_error_to_db:63] WebSocket错误已入库，错误码：5
2025-09-10 23:41:43.487 | Level 20 | Logger | [PublicApi.on_error:177] WebSocket连接异常：PublicApi.on_error() missing 2 required positional arguments: 'value' and 'tb'
Traceback (most recent call last):
  File "S:\Envs\evo\Lib\site-packages\websocket\_app.py", line 484, in setSock
    self.sock.connect(
  File "S:\Envs\evo\Lib\site-packages\websocket\_core.py", line 256, in connect
    self.sock, addrs = connect(
                       ^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\websocket\_http.py", line 151, in connect
    sock = _ssl_socket(sock, options.sslopt, hostname)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\websocket\_http.py", line 311, in _ssl_socket
    sock = _wrap_sni_socket(sock, sslopt, hostname, check_hostname)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\websocket\_http.py", line 281, in _wrap_sni_socket
    return context.wrap_socket(
           ^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\ssl.py", line 455, in wrap_socket
    return self.sslsocket_class._create(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\ssl.py", line 1041, in _create
    self.do_handshake()
  File "S:\Envs\evo\Lib\ssl.py", line 1319, in do_handshake
    self._sslobj.do_handshake()
ssl.SSLEOFError: [SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "S:\Envs\evo\Lib\site-packages\websocket\_app.py", line 682, in _callback
    callback(self, *args)
  File "S:\OneDrive - lancely\芷瀚同步\开发\币\okx\prod\websocket_client2.py", line 186, in on_error
    self.on_error(e)
TypeError: PublicApi.on_error() missing 2 required positional arguments: 'value' and 'tb'

2025-09-10T23:41:43.487761+0800  Level 20: [PublicApi.on_error:177] WebSocket连接异常：PublicApi.on_error() missing 2 required positional arguments: 'value' and 'tb'
Traceback (most recent call last):
  File "S:\Envs\evo\Lib\site-packages\websocket\_app.py", line 484, in setSock
    self.sock.connect(
  File "S:\Envs\evo\Lib\site-packages\websocket\_core.py", line 256, in connect
    self.sock, addrs = connect(
                       ^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\websocket\_http.py", line 151, in connect
    sock = _ssl_socket(sock, options.sslopt, hostname)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\websocket\_http.py", line 311, in _ssl_socket
    sock = _wrap_sni_socket(sock, sslopt, hostname, check_hostname)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\websocket\_http.py", line 281, in _wrap_sni_socket
    return context.wrap_socket(
           ^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\ssl.py", line 455, in wrap_socket
    return self.sslsocket_class._create(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\ssl.py", line 1041, in _create
    self.do_handshake()
  File "S:\Envs\evo\Lib\ssl.py", line 1319, in do_handshake
    self._sslobj.do_handshake()
ssl.SSLEOFError: [SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "S:\Envs\evo\Lib\site-packages\websocket\_app.py", line 682, in _callback
    callback(self, *args)
  File "S:\OneDrive - lancely\芷瀚同步\开发\币\okx\prod\websocket_client2.py", line 186, in on_error
    self.on_error(e)
TypeError: PublicApi.on_error() missing 2 required positional arguments: 'value' and 'tb'

2025-09-10 23:41:43.497 | Level 20 | Logger | [PublicApi._log_error_to_db:63] WebSocket错误已入库，错误码：5
2025-09-10T23:41:43.497765+0800  Level 20: [PublicApi._log_error_to_db:63] WebSocket错误已入库，错误码：5
2025-09-10 23:41:43.498 | Level 20 | Logger | [PublicApi.on_close:164] WebSocket连接断开，状态码：None，原因：None
2025-09-10T23:41:43.498765+0800  Level 20: [PublicApi.on_close:164] WebSocket连接断开，状态码：None，原因：None
2025-09-10 23:41:43.498 | Level 20 | Logger | [OkxGateway.on_disconnected:1243] Public API disconnected, code: None, msg: None
2025-09-10T23:41:43.498765+0800  Level 20: [OkxGateway.on_disconnected:1243] Public API disconnected, code: None, msg: None
2025-09-10 23:41:44.849 | Level 20 | Logger | [PrivateApi.on_open:160] WebSocket连接建立成功
2025-09-10T23:41:44.849805+0800  Level 20: [PrivateApi.on_open:160] WebSocket连接建立成功
2025-09-10 23:41:44.850 | Level 20 | Logger | [OkxGateway.on_connected:1502] Private websocket API connected
2025-09-10T23:41:44.850776+0800  Level 20: [OkxGateway.on_connected:1502] Private websocket API connected
2025-09-10 23:41:45.230 | Level 20 | Logger | [OkxGateway.on_api_error:1565] Private API request failed, status code: 50110, message: Your IP ************ is not in linking trusted IP addresses.
2025-09-10T23:41:45.230156+0800  Level 20: [OkxGateway.on_api_error:1565] Private API request failed, status code: 50110, message: Your IP ************ is not in linking trusted IP addresses.
2025-09-10 23:41:45.230 | Level 20 | Logger | [PrivateApi.on_close:164] WebSocket连接断开，状态码：4001，原因：Login failed.
2025-09-10T23:41:45.230659+0800  Level 20: [PrivateApi.on_close:164] WebSocket连接断开，状态码：4001，原因：Login failed.
2025-09-10 23:41:45.240 | Level 20 | Logger | [PrivateApi._log_error_to_db:63] WebSocket错误已入库，错误码：4001
2025-09-10T23:41:45.240609+0800  Level 20: [PrivateApi._log_error_to_db:63] WebSocket错误已入库，错误码：4001
2025-09-10 23:41:45.242 | Level 20 | Logger | [OkxGateway.on_disconnected:1512] Private API disconnected, code: 4001, msg: Login failed.
2025-09-10T23:41:45.242643+0800  Level 20: [OkxGateway.on_disconnected:1512] Private API disconnected, code: 4001, msg: Login failed.
2025-09-10 23:42:12.516 | Level 20 | Logger | [OkxGateway.enable_log_mode:1000] 日志模式已启用，将跟踪ticker和depth推送信息
2025-09-10T23:42:12.516483+0800  Level 20: [OkxGateway.enable_log_mode:1000] 日志模式已启用，将跟踪ticker和depth推送信息
2025-09-10 23:42:15.708 | Level 20 | Logger | [RestApi.init:148] REST客户端初始化，地址：https://www.okx.com
2025-09-10T23:42:15.708300+0800  Level 20: [RestApi.init:148] REST客户端初始化，地址：https://www.okx.com
2025-09-10 23:42:15.709 | Level 20 | Logger | [RestApi.init:152] 使用代理：http://127.0.0.1:7890
2025-09-10T23:42:15.709292+0800  Level 20: [RestApi.init:152] 使用代理：http://127.0.0.1:7890
2025-09-10 23:42:15.710 | Level 20 | Logger | [RestApi.start:160] 创建新的事件循环
2025-09-10T23:42:15.710290+0800  Level 20: [RestApi.start:160] 创建新的事件循环
2025-09-10 23:42:15.710 | Level 20 | Logger | [RestApi.start_event_loop:392] 启动事件循环后台线程
2025-09-10T23:42:15.710290+0800  Level 20: [RestApi.start_event_loop:392] 启动事件循环后台线程
2025-09-10 23:42:15.711 | Level 20 | Logger | [RestApi.run_event_loop:403] 事件循环开始运行
2025-09-10T23:42:15.711294+0800  Level 20: [RestApi.run_event_loop:403] 事件循环开始运行
2025-09-10 23:42:15.711 | Level 20 | Logger | [OkxGateway.connect:526] REST API started
2025-09-10T23:42:15.711294+0800  Level 20: [OkxGateway.connect:526] REST API started
2025-09-10 23:42:15.722 | Level 20 | Logger | [RestApi._get_response:269] 创建新的TCP连接器
2025-09-10T23:42:15.722295+0800  Level 20: [RestApi._get_response:269] 创建新的TCP连接器
2025-09-10 23:42:15.723 | Level 20 | Logger | [RestApi._get_response:276] 创建新的客户端会话
2025-09-10T23:42:15.723295+0800  Level 20: [RestApi._get_response:276] 创建新的客户端会话
2025-09-10 23:42:17.775 | Level 20 | Logger | [RestApi._process_request:338] 请求处理异常：Cannot connect to host 127.0.0.1:7890 ssl:default [Connect call failed ('127.0.0.1', 7890)]
2025-09-10T23:42:17.775279+0800  Level 20: [RestApi._process_request:338] 请求处理异常：Cannot connect to host 127.0.0.1:7890 ssl:default [Connect call failed ('127.0.0.1', 7890)]
2025-09-10 23:42:17.785 | Level 20 | Logger | [RestApi._log_error_to_db:369] REST错误已入库，错误码：5
2025-09-10T23:42:17.785185+0800  Level 20: [RestApi._log_error_to_db:369] REST错误已入库，错误码：5
2025-09-10 23:42:17.788 | Level 20 | Logger | [OkxGateway.on_error:809] Exception catched by REST API: [2025-09-10T23:42:17.785185]: Unhandled RestClient Error:<class 'aiohttp.client_exceptions.ClientProxyConnectionError'>
request:request : POST /api/v5/account/set-position-mode because terminated: 
headers: {'OK-ACCESS-KEY': '5830298e-84a1-4cb5-bfa6-7bd17a61b481', 'OK-ACCESS-SIGN': 'yPo0JjjjNxsWJ+G/2g/pXbo9DMW/pRGe5rY2hty7qbQ=', 'OK-ACCESS-TIMESTAMP': '2025-09-10T15:42:15.724Z', 'OK-ACCESS-PASSPHRASE': 'Dfgytrb88$', 'Content-Type': 'application/json'}
params: None
data: {"posMode": "net_mode"}
json: None
response:

Exception trace: 
Traceback (most recent call last):
  File "S:\Envs\evo\Lib\site-packages\aiohttp\connector.py", line 1268, in _wrap_create_connection
    sock = await aiohappyeyeballs.start_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohappyeyeballs\impl.py", line 122, in start_connection
    raise first_exception
  File "S:\Envs\evo\Lib\site-packages\aiohappyeyeballs\impl.py", line 73, in start_connection
    sock = await _connect_sock(
           ^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohappyeyeballs\impl.py", line 208, in _connect_sock
    await loop.sock_connect(sock, address)
  File "S:\Envs\evo\Lib\asyncio\selector_events.py", line 651, in sock_connect
    return await fut
           ^^^^^^^^^
  File "S:\Envs\evo\Lib\asyncio\selector_events.py", line 691, in _sock_connect_cb
    raise OSError(err, f'Connect call failed {address}')
ConnectionRefusedError: [Errno 10061] Connect call failed ('127.0.0.1', 7890)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "S:\OneDrive - lancely\芷瀚同步\开发\币\okx\prod\rest_client.py", line 311, in _process_request
    response: Response = await self._get_response(request)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\OneDrive - lancely\芷瀚同步\开发\币\okx\prod\rest_client.py", line 287, in _get_response
    cr: ClientResponse = await self.session.request(
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\client.py", line 725, in _connect_and_send_request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\connector.py", line 642, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\connector.py", line 1207, in _create_connection
    _, proto = await self._create_proxy_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\connector.py", line 1606, in _create_proxy_connection
    transport, proto = await self._create_direct_connection(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\connector.py", line 1581, in _create_direct_connection
    raise last_exc
  File "S:\Envs\evo\Lib\site-packages\aiohttp\connector.py", line 1550, in _create_direct_connection
    transp, proto = await self._wrap_create_connection(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\connector.py", line 1291, in _wrap_create_connection
    raise client_error(req.connection_key, exc) from exc
aiohttp.client_exceptions.ClientProxyConnectionError: Cannot connect to host 127.0.0.1:7890 ssl:default [Connect call failed ('127.0.0.1', 7890)]

2025-09-10T23:42:17.788186+0800  Level 20: [OkxGateway.on_error:809] Exception catched by REST API: [2025-09-10T23:42:17.785185]: Unhandled RestClient Error:<class 'aiohttp.client_exceptions.ClientProxyConnectionError'>
request:request : POST /api/v5/account/set-position-mode because terminated: 
headers: {'OK-ACCESS-KEY': '5830298e-84a1-4cb5-bfa6-7bd17a61b481', 'OK-ACCESS-SIGN': 'yPo0JjjjNxsWJ+G/2g/pXbo9DMW/pRGe5rY2hty7qbQ=', 'OK-ACCESS-TIMESTAMP': '2025-09-10T15:42:15.724Z', 'OK-ACCESS-PASSPHRASE': 'Dfgytrb88$', 'Content-Type': 'application/json'}
params: None
data: {"posMode": "net_mode"}
json: None
response:

Exception trace: 
Traceback (most recent call last):
  File "S:\Envs\evo\Lib\site-packages\aiohttp\connector.py", line 1268, in _wrap_create_connection
    sock = await aiohappyeyeballs.start_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohappyeyeballs\impl.py", line 122, in start_connection
    raise first_exception
  File "S:\Envs\evo\Lib\site-packages\aiohappyeyeballs\impl.py", line 73, in start_connection
    sock = await _connect_sock(
           ^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohappyeyeballs\impl.py", line 208, in _connect_sock
    await loop.sock_connect(sock, address)
  File "S:\Envs\evo\Lib\asyncio\selector_events.py", line 651, in sock_connect
    return await fut
           ^^^^^^^^^
  File "S:\Envs\evo\Lib\asyncio\selector_events.py", line 691, in _sock_connect_cb
    raise OSError(err, f'Connect call failed {address}')
ConnectionRefusedError: [Errno 10061] Connect call failed ('127.0.0.1', 7890)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "S:\OneDrive - lancely\芷瀚同步\开发\币\okx\prod\rest_client.py", line 311, in _process_request
    response: Response = await self._get_response(request)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\OneDrive - lancely\芷瀚同步\开发\币\okx\prod\rest_client.py", line 287, in _get_response
    cr: ClientResponse = await self.session.request(
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\client.py", line 725, in _connect_and_send_request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\connector.py", line 642, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\connector.py", line 1207, in _create_connection
    _, proto = await self._create_proxy_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\connector.py", line 1606, in _create_proxy_connection
    transport, proto = await self._create_direct_connection(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\connector.py", line 1581, in _create_direct_connection
    raise last_exc
  File "S:\Envs\evo\Lib\site-packages\aiohttp\connector.py", line 1550, in _create_direct_connection
    transp, proto = await self._wrap_create_connection(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\connector.py", line 1291, in _wrap_create_connection
    raise client_error(req.connection_key, exc) from exc
aiohttp.client_exceptions.ClientProxyConnectionError: Cannot connect to host 127.0.0.1:7890 ssl:default [Connect call failed ('127.0.0.1', 7890)]

2025-09-10 23:42:17.788 | Level 20 | Logger | [RestApi._process_request:338] 请求处理异常：Cannot connect to host 127.0.0.1:7890 ssl:default [Connect call failed ('127.0.0.1', 7890)]
2025-09-10T23:42:17.788186+0800  Level 20: [RestApi._process_request:338] 请求处理异常：Cannot connect to host 127.0.0.1:7890 ssl:default [Connect call failed ('127.0.0.1', 7890)]
2025-09-10 23:42:17.794 | Level 20 | Logger | [RestApi._log_error_to_db:369] REST错误已入库，错误码：5
2025-09-10T23:42:17.794741+0800  Level 20: [RestApi._log_error_to_db:369] REST错误已入库，错误码：5
2025-09-10 23:42:17.795 | Level 20 | Logger | [OkxGateway.on_error:809] Exception catched by REST API: [2025-09-10T23:42:17.793697]: Unhandled RestClient Error:<class 'aiohttp.client_exceptions.ClientProxyConnectionError'>
request:request : GET /api/v5/public/time because terminated: 
headers: None
params: None
data: None
json: None
response:

Exception trace: 
Traceback (most recent call last):
  File "S:\Envs\evo\Lib\site-packages\aiohttp\connector.py", line 1268, in _wrap_create_connection
    sock = await aiohappyeyeballs.start_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohappyeyeballs\impl.py", line 122, in start_connection
    raise first_exception
  File "S:\Envs\evo\Lib\site-packages\aiohappyeyeballs\impl.py", line 73, in start_connection
    sock = await _connect_sock(
           ^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohappyeyeballs\impl.py", line 208, in _connect_sock
    await loop.sock_connect(sock, address)
  File "S:\Envs\evo\Lib\asyncio\selector_events.py", line 651, in sock_connect
    return await fut
           ^^^^^^^^^
  File "S:\Envs\evo\Lib\asyncio\selector_events.py", line 691, in _sock_connect_cb
    raise OSError(err, f'Connect call failed {address}')
ConnectionRefusedError: [Errno 10061] Connect call failed ('127.0.0.1', 7890)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "S:\OneDrive - lancely\芷瀚同步\开发\币\okx\prod\rest_client.py", line 311, in _process_request
    response: Response = await self._get_response(request)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\OneDrive - lancely\芷瀚同步\开发\币\okx\prod\rest_client.py", line 287, in _get_response
    cr: ClientResponse = await self.session.request(
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\client.py", line 725, in _connect_and_send_request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\connector.py", line 642, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\connector.py", line 1207, in _create_connection
    _, proto = await self._create_proxy_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\connector.py", line 1606, in _create_proxy_connection
    transport, proto = await self._create_direct_connection(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\connector.py", line 1581, in _create_direct_connection
    raise last_exc
  File "S:\Envs\evo\Lib\site-packages\aiohttp\connector.py", line 1550, in _create_direct_connection
    transp, proto = await self._wrap_create_connection(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\connector.py", line 1291, in _wrap_create_connection
    raise client_error(req.connection_key, exc) from exc
aiohttp.client_exceptions.ClientProxyConnectionError: Cannot connect to host 127.0.0.1:7890 ssl:default [Connect call failed ('127.0.0.1', 7890)]

2025-09-10T23:42:17.795739+0800  Level 20: [OkxGateway.on_error:809] Exception catched by REST API: [2025-09-10T23:42:17.793697]: Unhandled RestClient Error:<class 'aiohttp.client_exceptions.ClientProxyConnectionError'>
request:request : GET /api/v5/public/time because terminated: 
headers: None
params: None
data: None
json: None
response:

Exception trace: 
Traceback (most recent call last):
  File "S:\Envs\evo\Lib\site-packages\aiohttp\connector.py", line 1268, in _wrap_create_connection
    sock = await aiohappyeyeballs.start_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohappyeyeballs\impl.py", line 122, in start_connection
    raise first_exception
  File "S:\Envs\evo\Lib\site-packages\aiohappyeyeballs\impl.py", line 73, in start_connection
    sock = await _connect_sock(
           ^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohappyeyeballs\impl.py", line 208, in _connect_sock
    await loop.sock_connect(sock, address)
  File "S:\Envs\evo\Lib\asyncio\selector_events.py", line 651, in sock_connect
    return await fut
           ^^^^^^^^^
  File "S:\Envs\evo\Lib\asyncio\selector_events.py", line 691, in _sock_connect_cb
    raise OSError(err, f'Connect call failed {address}')
ConnectionRefusedError: [Errno 10061] Connect call failed ('127.0.0.1', 7890)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "S:\OneDrive - lancely\芷瀚同步\开发\币\okx\prod\rest_client.py", line 311, in _process_request
    response: Response = await self._get_response(request)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\OneDrive - lancely\芷瀚同步\开发\币\okx\prod\rest_client.py", line 287, in _get_response
    cr: ClientResponse = await self.session.request(
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\client.py", line 725, in _connect_and_send_request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\connector.py", line 642, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\connector.py", line 1207, in _create_connection
    _, proto = await self._create_proxy_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\connector.py", line 1606, in _create_proxy_connection
    transport, proto = await self._create_direct_connection(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\connector.py", line 1581, in _create_direct_connection
    raise last_exc
  File "S:\Envs\evo\Lib\site-packages\aiohttp\connector.py", line 1550, in _create_direct_connection
    transp, proto = await self._wrap_create_connection(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\connector.py", line 1291, in _wrap_create_connection
    raise client_error(req.connection_key, exc) from exc
aiohttp.client_exceptions.ClientProxyConnectionError: Cannot connect to host 127.0.0.1:7890 ssl:default [Connect call failed ('127.0.0.1', 7890)]

2025-09-10 23:42:17.796 | Level 20 | Logger | [RestApi._process_request:338] 请求处理异常：Cannot connect to host 127.0.0.1:7890 ssl:default [Connect call failed ('127.0.0.1', 7890)]
2025-09-10T23:42:17.796696+0800  Level 20: [RestApi._process_request:338] 请求处理异常：Cannot connect to host 127.0.0.1:7890 ssl:default [Connect call failed ('127.0.0.1', 7890)]
2025-09-10 23:42:17.803 | Level 20 | Logger | [RestApi._log_error_to_db:369] REST错误已入库，错误码：5
2025-09-10T23:42:17.803726+0800  Level 20: [RestApi._log_error_to_db:369] REST错误已入库，错误码：5
2025-09-10 23:42:17.804 | Level 20 | Logger | [OkxGateway.on_error:809] Exception catched by REST API: [2025-09-10T23:42:17.802730]: Unhandled RestClient Error:<class 'aiohttp.client_exceptions.ClientProxyConnectionError'>
request:request : GET /api/v5/public/instruments because terminated: 
headers: None
params: {'instType': 'SWAP'}
data: None
json: None
response:

Exception trace: 
Traceback (most recent call last):
  File "S:\Envs\evo\Lib\site-packages\aiohttp\connector.py", line 1268, in _wrap_create_connection
    sock = await aiohappyeyeballs.start_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohappyeyeballs\impl.py", line 122, in start_connection
    raise first_exception
  File "S:\Envs\evo\Lib\site-packages\aiohappyeyeballs\impl.py", line 73, in start_connection
    sock = await _connect_sock(
           ^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohappyeyeballs\impl.py", line 208, in _connect_sock
    await loop.sock_connect(sock, address)
  File "S:\Envs\evo\Lib\asyncio\selector_events.py", line 651, in sock_connect
    return await fut
           ^^^^^^^^^
  File "S:\Envs\evo\Lib\asyncio\selector_events.py", line 691, in _sock_connect_cb
    raise OSError(err, f'Connect call failed {address}')
ConnectionRefusedError: [Errno 10061] Connect call failed ('127.0.0.1', 7890)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "S:\OneDrive - lancely\芷瀚同步\开发\币\okx\prod\rest_client.py", line 311, in _process_request
    response: Response = await self._get_response(request)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\OneDrive - lancely\芷瀚同步\开发\币\okx\prod\rest_client.py", line 287, in _get_response
    cr: ClientResponse = await self.session.request(
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\client.py", line 725, in _connect_and_send_request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\connector.py", line 642, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\connector.py", line 1207, in _create_connection
    _, proto = await self._create_proxy_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\connector.py", line 1606, in _create_proxy_connection
    transport, proto = await self._create_direct_connection(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\connector.py", line 1581, in _create_direct_connection
    raise last_exc
  File "S:\Envs\evo\Lib\site-packages\aiohttp\connector.py", line 1550, in _create_direct_connection
    transp, proto = await self._wrap_create_connection(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\connector.py", line 1291, in _wrap_create_connection
    raise client_error(req.connection_key, exc) from exc
aiohttp.client_exceptions.ClientProxyConnectionError: Cannot connect to host 127.0.0.1:7890 ssl:default [Connect call failed ('127.0.0.1', 7890)]

2025-09-10T23:42:17.804696+0800  Level 20: [OkxGateway.on_error:809] Exception catched by REST API: [2025-09-10T23:42:17.802730]: Unhandled RestClient Error:<class 'aiohttp.client_exceptions.ClientProxyConnectionError'>
request:request : GET /api/v5/public/instruments because terminated: 
headers: None
params: {'instType': 'SWAP'}
data: None
json: None
response:

Exception trace: 
Traceback (most recent call last):
  File "S:\Envs\evo\Lib\site-packages\aiohttp\connector.py", line 1268, in _wrap_create_connection
    sock = await aiohappyeyeballs.start_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohappyeyeballs\impl.py", line 122, in start_connection
    raise first_exception
  File "S:\Envs\evo\Lib\site-packages\aiohappyeyeballs\impl.py", line 73, in start_connection
    sock = await _connect_sock(
           ^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohappyeyeballs\impl.py", line 208, in _connect_sock
    await loop.sock_connect(sock, address)
  File "S:\Envs\evo\Lib\asyncio\selector_events.py", line 651, in sock_connect
    return await fut
           ^^^^^^^^^
  File "S:\Envs\evo\Lib\asyncio\selector_events.py", line 691, in _sock_connect_cb
    raise OSError(err, f'Connect call failed {address}')
ConnectionRefusedError: [Errno 10061] Connect call failed ('127.0.0.1', 7890)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "S:\OneDrive - lancely\芷瀚同步\开发\币\okx\prod\rest_client.py", line 311, in _process_request
    response: Response = await self._get_response(request)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\OneDrive - lancely\芷瀚同步\开发\币\okx\prod\rest_client.py", line 287, in _get_response
    cr: ClientResponse = await self.session.request(
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\client.py", line 770, in _request
    resp = await handler(req)
           ^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\client.py", line 725, in _connect_and_send_request
    conn = await self._connector.connect(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\connector.py", line 642, in connect
    proto = await self._create_connection(req, traces, timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\connector.py", line 1207, in _create_connection
    _, proto = await self._create_proxy_connection(req, traces, timeout)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\connector.py", line 1606, in _create_proxy_connection
    transport, proto = await self._create_direct_connection(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\connector.py", line 1581, in _create_direct_connection
    raise last_exc
  File "S:\Envs\evo\Lib\site-packages\aiohttp\connector.py", line 1550, in _create_direct_connection
    transp, proto = await self._wrap_create_connection(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "S:\Envs\evo\Lib\site-packages\aiohttp\connector.py", line 1291, in _wrap_create_connection
    raise client_error(req.connection_key, exc) from exc
aiohttp.client_exceptions.ClientProxyConnectionError: Cannot connect to host 127.0.0.1:7890 ssl:default [Connect call failed ('127.0.0.1', 7890)]

2025-09-10 23:42:53.395 | Level 20 | Logger | [OkxGateway.enable_log_mode:1000] 日志模式已启用，将跟踪ticker和depth推送信息
2025-09-10T23:42:53.395183+0800  Level 20: [OkxGateway.enable_log_mode:1000] 日志模式已启用，将跟踪ticker和depth推送信息
2025-09-10 23:42:56.602 | Level 20 | Logger | [RestApi.init:148] REST客户端初始化，地址：https://www.okx.com
2025-09-10T23:42:56.602628+0800  Level 20: [RestApi.init:148] REST客户端初始化，地址：https://www.okx.com
2025-09-10 23:42:56.603 | Level 20 | Logger | [RestApi.init:152] 使用代理：http://127.0.0.1:7890
2025-09-10T23:42:56.603753+0800  Level 20: [RestApi.init:152] 使用代理：http://127.0.0.1:7890
2025-09-10 23:42:56.604 | Level 20 | Logger | [RestApi.start:160] 创建新的事件循环
2025-09-10T23:42:56.604753+0800  Level 20: [RestApi.start:160] 创建新的事件循环
2025-09-10 23:42:56.605 | Level 20 | Logger | [RestApi.start_event_loop:392] 启动事件循环后台线程
2025-09-10T23:42:56.605751+0800  Level 20: [RestApi.start_event_loop:392] 启动事件循环后台线程
2025-09-10 23:42:56.605 | Level 20 | Logger | [RestApi.run_event_loop:403] 事件循环开始运行
2025-09-10T23:42:56.605751+0800  Level 20: [RestApi.run_event_loop:403] 事件循环开始运行
2025-09-10 23:42:56.606 | Level 20 | Logger | [OkxGateway.connect:526] REST API started
2025-09-10T23:42:56.606753+0800  Level 20: [OkxGateway.connect:526] REST API started
2025-09-10 23:42:56.616 | Level 20 | Logger | [RestApi._get_response:269] 创建新的TCP连接器
2025-09-10T23:42:56.616375+0800  Level 20: [RestApi._get_response:269] 创建新的TCP连接器
2025-09-10 23:42:56.617 | Level 20 | Logger | [RestApi._get_response:276] 创建新的客户端会话
2025-09-10T23:42:56.617078+0800  Level 20: [RestApi._get_response:276] 创建新的客户端会话
2025-09-10 23:42:56.779 | Level 20 | Logger | [RestApi._get_response:303] 响应内容：{"msg":"Your IP ************* is not included in your API key's 5830298e-84a1-4cb5-bfa6-7bd17a61b481 IP whitelist.","code":"50110"}
2025-09-10T23:42:56.779432+0800  Level 20: [RestApi._get_response:303] 响应内容：{"msg":"Your IP ************* is not included in your API key's 5830298e-84a1-4cb5-bfa6-7bd17a61b481 IP whitelist.","code":"50110"}
2025-09-10 23:42:56.780 | Level 20 | Logger | [RestApi._process_request:321] 请求处理失败，状态码：401
2025-09-10T23:42:56.780439+0800  Level 20: [RestApi._process_request:321] 请求处理失败，状态码：401
2025-09-10 23:42:56.789 | Level 20 | Logger | [RestApi._log_error_to_db:369] REST错误已入库，错误码：401
2025-09-10T23:42:56.789942+0800  Level 20: [RestApi._log_error_to_db:369] REST错误已入库，错误码：401
2025-09-10 23:42:56.791 | Level 20 | Logger | [RestApi.on_failed:227] RestClient on failed----------
2025-09-10T23:42:56.791950+0800  Level 20: [RestApi.on_failed:227] RestClient on failed----------
2025-09-10 23:42:56.791 | Level 20 | Logger | [RestApi.on_failed:228] request : POST /api/v5/account/set-position-mode because 401: 
headers: {'OK-ACCESS-KEY': '5830298e-84a1-4cb5-bfa6-7bd17a61b481', 'OK-ACCESS-SIGN': 'I1bv9kLgH6QQSJK3i5jo/ZvS0Uxk61hyBQRDKQDx0b8=', 'OK-ACCESS-TIMESTAMP': '2025-09-10T15:42:56.618Z', 'OK-ACCESS-PASSPHRASE': 'Dfgytrb88$', 'Content-Type': 'application/json'}
params: None
data: {"posMode": "net_mode"}
json: None
response:{"msg":"Your IP ************* is not included in your API key's 5830298e-84a1-4cb5-bfa6-7bd17a61b481 IP whitelist.","code":"50110"}

2025-09-10T23:42:56.791950+0800  Level 20: [RestApi.on_failed:228] request : POST /api/v5/account/set-position-mode because 401: 
headers: {'OK-ACCESS-KEY': '5830298e-84a1-4cb5-bfa6-7bd17a61b481', 'OK-ACCESS-SIGN': 'I1bv9kLgH6QQSJK3i5jo/ZvS0Uxk61hyBQRDKQDx0b8=', 'OK-ACCESS-TIMESTAMP': '2025-09-10T15:42:56.618Z', 'OK-ACCESS-PASSPHRASE': 'Dfgytrb88$', 'Content-Type': 'application/json'}
params: None
data: {"posMode": "net_mode"}
json: None
response:{"msg":"Your IP ************* is not included in your API key's 5830298e-84a1-4cb5-bfa6-7bd17a61b481 IP whitelist.","code":"50110"}

2025-09-10 23:42:56.811 | Level 20 | Logger | [OkxGateway.on_query_contract:777] SWAP contract data received
2025-09-10T23:42:56.811341+0800  Level 20: [OkxGateway.on_query_contract:777] SWAP contract data received
2025-09-10 23:42:56.813 | Level 20 | Logger | [PublicApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10T23:42:56.813016+0800  Level 20: [PublicApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10 23:42:56.813 | Level 20 | Logger | [PublicApi.init:110] WebSocket客户端初始化，地址：wss://ws.okx.com:8443/ws/v5/public
2025-09-10T23:42:56.813521+0800  Level 20: [PublicApi.init:110] WebSocket客户端初始化，地址：wss://ws.okx.com:8443/ws/v5/public
2025-09-10 23:42:56.814 | Level 20 | Logger | [PublicApi.run:191] 尝试连接到服务器：wss://ws.okx.com:8443/ws/v5/public
2025-09-10T23:42:56.814622+0800  Level 20: [PublicApi.run:191] 尝试连接到服务器：wss://ws.okx.com:8443/ws/v5/public
2025-09-10 23:42:56.815 | Level 20 | Logger | [PublicApi.start:122] WebSocket客户端启动
2025-09-10T23:42:56.815126+0800  Level 20: [PublicApi.start:122] WebSocket客户端启动
2025-09-10 23:42:56.816 | Level 20 | Logger | [OkxGateway.start_subscribe_thread:995] OKX订阅流控线程已启动
2025-09-10T23:42:56.816131+0800  Level 20: [OkxGateway.start_subscribe_thread:995] OKX订阅流控线程已启动
2025-09-10 23:42:56.816 | Level 20 | Logger | [PrivateApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10T23:42:56.816965+0800  Level 20: [PrivateApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-10 23:42:56.817 | Level 20 | Logger | [PrivateApi.init:110] WebSocket客户端初始化，地址：wss://ws.okx.com:8443/ws/v5/private
2025-09-10T23:42:56.817593+0800  Level 20: [PrivateApi.init:110] WebSocket客户端初始化，地址：wss://ws.okx.com:8443/ws/v5/private
2025-09-10 23:42:56.818 | Level 20 | Logger | [PrivateApi.run:191] 尝试连接到服务器：wss://ws.okx.com:8443/ws/v5/private
2025-09-10T23:42:56.818098+0800  Level 20: [PrivateApi.run:191] 尝试连接到服务器：wss://ws.okx.com:8443/ws/v5/private
2025-09-10 23:42:56.819 | Level 20 | Logger | [PrivateApi.start:122] WebSocket客户端启动
2025-09-10T23:42:56.819078+0800  Level 20: [PrivateApi.start:122] WebSocket客户端启动
2025-09-10 23:42:56.872 | Level 20 | Logger | [RestApi._get_response:303] 响应内容：{"msg":"Your IP ************* is not included in your API key's 5830298e-84a1-4cb5-bfa6-7bd17a61b481 IP whitelist.","code":"50110"}
2025-09-10T23:42:56.872164+0800  Level 20: [RestApi._get_response:303] 响应内容：{"msg":"Your IP ************* is not included in your API key's 5830298e-84a1-4cb5-bfa6-7bd17a61b481 IP whitelist.","code":"50110"}
2025-09-10 23:42:56.872 | Level 20 | Logger | [RestApi._process_request:321] 请求处理失败，状态码：401
2025-09-10T23:42:56.872164+0800  Level 20: [RestApi._process_request:321] 请求处理失败，状态码：401
2025-09-10 23:42:56.879 | Level 20 | Logger | [RestApi._log_error_to_db:369] REST错误已入库，错误码：401
2025-09-10T23:42:56.879791+0800  Level 20: [RestApi._log_error_to_db:369] REST错误已入库，错误码：401
2025-09-10 23:42:56.880 | Level 20 | Logger | [RestApi.on_failed:227] RestClient on failed----------
2025-09-10T23:42:56.880788+0800  Level 20: [RestApi.on_failed:227] RestClient on failed----------
2025-09-10 23:42:56.880 | Level 20 | Logger | [RestApi.on_failed:228] request : GET /api/v5/trade/orders-pending because 401: 
headers: {'OK-ACCESS-KEY': '5830298e-84a1-4cb5-bfa6-7bd17a61b481', 'OK-ACCESS-SIGN': 'tCAVNcy1Q1qBaSFAPONYIjlWXzy8W6A2jgtNVPABf+M=', 'OK-ACCESS-TIMESTAMP': '2025-09-10T15:42:56.816Z', 'OK-ACCESS-PASSPHRASE': 'Dfgytrb88$', 'Content-Type': 'application/json'}
params: None
data: 
json: None
response:{"msg":"Your IP ************* is not included in your API key's 5830298e-84a1-4cb5-bfa6-7bd17a61b481 IP whitelist.","code":"50110"}

2025-09-10T23:42:56.880788+0800  Level 20: [RestApi.on_failed:228] request : GET /api/v5/trade/orders-pending because 401: 
headers: {'OK-ACCESS-KEY': '5830298e-84a1-4cb5-bfa6-7bd17a61b481', 'OK-ACCESS-SIGN': 'tCAVNcy1Q1qBaSFAPONYIjlWXzy8W6A2jgtNVPABf+M=', 'OK-ACCESS-TIMESTAMP': '2025-09-10T15:42:56.816Z', 'OK-ACCESS-PASSPHRASE': 'Dfgytrb88$', 'Content-Type': 'application/json'}
params: None
data: 
json: None
response:{"msg":"Your IP ************* is not included in your API key's 5830298e-84a1-4cb5-bfa6-7bd17a61b481 IP whitelist.","code":"50110"}

2025-09-10 23:42:56.916 | Level 20 | Logger | [OkxGateway.on_query_time:605] server time: 2025-09-10 23:42:56.155000, local time: 2025-09-10 23:42:56.916567
2025-09-10T23:42:56.916566+0800  Level 20: [OkxGateway.on_query_time:605] server time: 2025-09-10 23:42:56.155000, local time: 2025-09-10 23:42:56.916567
2025-09-10 23:42:57.035 | Level 20 | Logger | [PublicApi.on_open:160] WebSocket连接建立成功
2025-09-10T23:42:57.035124+0800  Level 20: [PublicApi.on_open:160] WebSocket连接建立成功
2025-09-10 23:42:57.036 | Level 20 | Logger | [OkxGateway.on_connected:1231] Public API connected
2025-09-10T23:42:57.036594+0800  Level 20: [OkxGateway.on_connected:1231] Public API connected
2025-09-10 23:42:57.037 | Level 20 | Logger | [PrivateApi.on_open:160] WebSocket连接建立成功
2025-09-10T23:42:57.037600+0800  Level 20: [PrivateApi.on_open:160] WebSocket连接建立成功
2025-09-10 23:42:57.038 | Level 20 | Logger | [OkxGateway.on_connected:1502] Private websocket API connected
2025-09-10T23:42:57.038139+0800  Level 20: [OkxGateway.on_connected:1502] Private websocket API connected
2025-09-10 23:42:57.087 | Level 20 | Logger | [OkxGateway.on_api_error:1565] Private API request failed, status code: 50110, message: Your IP ************* is not in linking trusted IP addresses.
2025-09-10T23:42:57.087368+0800  Level 20: [OkxGateway.on_api_error:1565] Private API request failed, status code: 50110, message: Your IP ************* is not in linking trusted IP addresses.
2025-09-10 23:42:57.088 | Level 20 | Logger | [PrivateApi.on_close:164] WebSocket连接断开，状态码：4001，原因：Login failed.
2025-09-10T23:42:57.088149+0800  Level 20: [PrivateApi.on_close:164] WebSocket连接断开，状态码：4001，原因：Login failed.
2025-09-10 23:42:57.108 | Level 20 | Logger | [PrivateApi._log_error_to_db:63] WebSocket错误已入库，错误码：4001
2025-09-10T23:42:57.108564+0800  Level 20: [PrivateApi._log_error_to_db:63] WebSocket错误已入库，错误码：4001
2025-09-10 23:42:57.109 | Level 20 | Logger | [OkxGateway.on_disconnected:1512] Private API disconnected, code: 4001, msg: Login failed.
2025-09-10T23:42:57.109551+0800  Level 20: [OkxGateway.on_disconnected:1512] Private API disconnected, code: 4001, msg: Login failed.
2025-09-10 23:47:56.968 | Level 20 | Logger | [OkxGateway.on_query_time:605] server time: 2025-09-10 23:47:56.199000, local time: 2025-09-10 23:47:56.968410
2025-09-10T23:47:56.968410+0800  Level 20: [OkxGateway.on_query_time:605] server time: 2025-09-10 23:47:56.199000, local time: 2025-09-10 23:47:56.968410
