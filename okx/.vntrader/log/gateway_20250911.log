2025-09-11T17:48:34.153883+0800  Level 20: [BinanceLinearGateway.__init__:194] Gateway Version: 2025-4-18 22:45:30
2025-09-11T17:48:34.154883+0800  Level 20: [BinanceLinearRestApi.init:148] REST客户端初始化，地址：https://fapi.binance.com
2025-09-11T17:48:34.155884+0800  Level 20: [BinanceLinearRestApi.init:152] 使用代理：http://127.0.0.1:7890
2025-09-11T17:48:34.155884+0800  Level 20: [BinanceLinearRestApi.start:160] 创建新的事件循环
2025-09-11T17:48:34.156883+0800  Level 20: [BinanceLinearRestApi.start_event_loop:392] 启动事件循环后台线程
2025-09-11T17:48:34.157886+0800  Level 20: [BinanceLinearRestApi.run_event_loop:403] 事件循环开始运行
2025-09-11T17:48:34.158885+0800  Level 20: [BinanceLinearRestApi.connect:555] REST API started
2025-09-11T17:48:34.158885+0800  Level 20: [BinanceLinearDataWebsocketApi.init:107] 使用代理：http://127.0.0.1:7890
2025-09-11T17:48:34.160885+0800  Level 20: [BinanceLinearDataWebsocketApi.init:111] WebSocket客户端初始化，地址：wss://fstream.binance.com/stream
2025-09-11T17:48:34.168134+0800  Level 20: [BinanceLinearDataWebsocketApi.run:192] 尝试连接到服务器：wss://fstream.binance.com/stream
2025-09-11T17:48:34.230377+0800  Level 20: [BinanceLinearDataWebsocketApi.start:123] WebSocket客户端启动
2025-09-11T17:48:34.262352+0800  Level 20: [BinanceLinearDataWebsocketApi.start_subscribe_thread:1971] 订阅处理线程已启动
2025-09-11T17:48:34.434584+0800  Level 20: [BinanceLinearRestApi._get_response:269] 创建新的TCP连接器
2025-09-11T17:48:34.495637+0800  Level 20: [BinanceLinearRestApi._get_response:276] 创建新的客户端会话
2025-09-11T17:48:36.498350+0800  Level 20: [BinanceLinearDataWebsocketApi.on_open:161] WebSocket连接建立成功
2025-09-11T17:48:36.559933+0800  Level 20: [BinanceLinearDataWebsocketApi.on_connected:2077] Data Websocket API is connected
2025-09-11T17:48:36.744959+0800  Level 20: [BinanceLinearRestApi.on_start_user_stream:1329] 连接trade_ws_api
2025-09-11T17:48:36.806659+0800  Level 20: [BinanceLinearTradeWebsocketApi.init:107] 使用代理：http://127.0.0.1:7890
2025-09-11T17:48:36.869835+0800  Level 20: [BinanceLinearTradeWebsocketApi.init:111] WebSocket客户端初始化，地址：wss://fstream.binance.com/ws/w76rig8BisE0at6ibFI1bgYfRLPd2fN4Bt1bsTanLeRbXKqWCX9CBigsvauP1jIN
2025-09-11T17:48:36.901839+0800  Level 20: [BinanceLinearTradeWebsocketApi.run:192] 尝试连接到服务器：wss://fstream.binance.com/ws/w76rig8BisE0at6ibFI1bgYfRLPd2fN4Bt1bsTanLeRbXKqWCX9CBigsvauP1jIN
2025-09-11T17:48:36.934541+0800  Level 20: [BinanceLinearTradeWebsocketApi.start:123] WebSocket客户端启动
2025-09-11T17:48:37.373303+0800  Level 20: [BinanceLinearRestApi.on_query_contract:1029] Available contracts data is received
2025-09-11T17:48:37.386455+0800  Level 20: [BinanceLinearRestApi.on_query_orders:983] Open orders data is received
2025-09-11T17:48:38.876424+0800  Level 20: [BinanceLinearTradeWebsocketApi.on_open:161] WebSocket连接建立成功
2025-09-11T17:48:38.907938+0800  Level 20: [BinanceLinearTradeWebsocketApi.on_connected:1771] Trade Websocket API is connected
2025-09-11T17:56:50.064474+0800  Level 20: [BinanceLinearGateway.__init__:194] Gateway Version: 2025-4-18 22:45:30
2025-09-11T17:56:50.065458+0800  Level 20: [BinanceLinearRestApi.init:148] REST客户端初始化，地址：https://fapi.binance.com
2025-09-11T17:56:50.066464+0800  Level 20: [BinanceLinearRestApi.init:152] 使用代理：http://127.0.0.1:7890
2025-09-11T17:56:50.067459+0800  Level 20: [BinanceLinearRestApi.start:160] 创建新的事件循环
2025-09-11T17:56:50.067459+0800  Level 20: [BinanceLinearRestApi.start_event_loop:392] 启动事件循环后台线程
2025-09-11T17:56:50.068461+0800  Level 20: [BinanceLinearRestApi.run_event_loop:403] 事件循环开始运行
2025-09-11T17:56:50.069458+0800  Level 20: [BinanceLinearRestApi.connect:555] REST API started
2025-09-11T17:56:50.069458+0800  Level 20: [BinanceLinearDataWebsocketApi.init:107] 使用代理：http://127.0.0.1:7890
2025-09-11T17:56:50.144613+0800  Level 20: [BinanceLinearDataWebsocketApi.init:111] WebSocket客户端初始化，地址：wss://fstream.binance.com/stream
2025-09-11T17:56:50.175635+0800  Level 20: [BinanceLinearDataWebsocketApi.run:192] 尝试连接到服务器：wss://fstream.binance.com/stream
2025-09-11T17:56:50.175635+0800  Level 20: [BinanceLinearDataWebsocketApi.start:123] WebSocket客户端启动
2025-09-11T17:56:50.191315+0800  Level 20: [BinanceLinearDataWebsocketApi.start_subscribe_thread:1971] 订阅处理线程已启动
2025-09-11T17:56:50.421399+0800  Level 20: [BinanceLinearRestApi._get_response:269] 创建新的TCP连接器
2025-09-11T17:56:50.453850+0800  Level 20: [BinanceLinearRestApi._get_response:276] 创建新的客户端会话
2025-09-11T17:56:52.761576+0800  Level 20: [BinanceLinearDataWebsocketApi.on_open:161] WebSocket连接建立成功
2025-09-11T17:56:52.792127+0800  Level 20: [BinanceLinearDataWebsocketApi.on_connected:2077] Data Websocket API is connected
2025-09-11T17:56:53.099451+0800  Level 20: [BinanceLinearRestApi.on_start_user_stream:1329] 连接trade_ws_api
2025-09-11T17:56:53.130423+0800  Level 20: [BinanceLinearTradeWebsocketApi.init:107] 使用代理：http://127.0.0.1:7890
2025-09-11T17:56:53.192013+0800  Level 20: [BinanceLinearTradeWebsocketApi.init:111] WebSocket客户端初始化，地址：wss://fstream.binance.com/ws/w76rig8BisE0at6ibFI1bgYfRLPd2fN4Bt1bsTanLeRbXKqWCX9CBigsvauP1jIN
2025-09-11T17:56:53.239466+0800  Level 20: [BinanceLinearTradeWebsocketApi.run:192] 尝试连接到服务器：wss://fstream.binance.com/ws/w76rig8BisE0at6ibFI1bgYfRLPd2fN4Bt1bsTanLeRbXKqWCX9CBigsvauP1jIN
2025-09-11T17:56:53.284911+0800  Level 20: [BinanceLinearTradeWebsocketApi.start:123] WebSocket客户端启动
2025-09-11T17:56:53.395780+0800  Level 20: [BinanceLinearRestApi.on_query_contract:1029] Available contracts data is received
2025-09-11T17:56:54.742350+0800  Level 20: [BinanceLinearRestApi.on_query_orders:983] Open orders data is received
2025-09-11T17:56:55.174642+0800  Level 20: [BinanceLinearTradeWebsocketApi.on_open:161] WebSocket连接建立成功
2025-09-11T17:56:55.190521+0800  Level 20: [BinanceLinearTradeWebsocketApi.on_connected:1771] Trade Websocket API is connected
2025-09-11T17:57:00.856833+0800  Level 20: [BinanceLinearRestApi.stop:166] REST客户端停止中
2025-09-11T17:57:00.903481+0800  Level 20: [BinanceLinearRestApi.stop:169] 事件循环已停止
2025-09-11T17:57:01.059396+0800  Level 20: [BinanceLinearTradeWebsocketApi.on_close:165] WebSocket连接断开，状态码：None，原因：None
2025-09-11T17:57:01.120745+0800  Level 20: [BinanceLinearTradeWebsocketApi.on_disconnected:1918] Trade Websocket API is disconnected, code: None, msg: None
2025-09-11T17:57:01.166898+0800  Level 20: [BinanceLinearTradeWebsocketApi.stop:135] WebSocket客户端停止中
2025-09-11T17:57:01.214394+0800  Level 20: [BinanceLinearDataWebsocketApi.stop_subscribe_thread:1979] 订阅处理线程已停止
2025-09-11T17:57:01.449388+0800  Level 20: [BinanceLinearDataWebsocketApi.on_close:165] WebSocket连接断开，状态码：None，原因：None
2025-09-11T17:57:01.479670+0800  Level 20: [BinanceLinearDataWebsocketApi.on_disconnected:2389] Data Websocket API is disconnected, code: None, msg: None
2025-09-11T17:57:01.542838+0800  Level 20: [BinanceLinearDataWebsocketApi.stop:135] WebSocket客户端停止中
2025-09-11T18:04:42.669376+0800  Level 20: [BinanceLinearGateway.__init__:194] Gateway Version: 2025-4-18 22:45:30
2025-09-11T18:04:42.672374+0800  Level 20: [BinanceLinearRestApi.init:148] REST客户端初始化，地址：https://fapi.binance.com
2025-09-11T18:04:42.673377+0800  Level 20: [BinanceLinearRestApi.init:152] 使用代理：http://127.0.0.1:7890
2025-09-11T18:04:42.674378+0800  Level 20: [BinanceLinearRestApi.start:160] 创建新的事件循环
2025-09-11T18:04:42.676379+0800  Level 20: [BinanceLinearRestApi.start_event_loop:392] 启动事件循环后台线程
2025-09-11T18:04:42.677378+0800  Level 20: [BinanceLinearRestApi.run_event_loop:403] 事件循环开始运行
2025-09-11T18:04:42.685953+0800  Level 20: [BinanceLinearRestApi.connect:555] REST API started
2025-09-11T18:04:42.700963+0800  Level 20: [BinanceLinearDataWebsocketApi.init:107] 使用代理：http://127.0.0.1:7890
2025-09-11T18:04:42.763520+0800  Level 20: [BinanceLinearDataWebsocketApi.init:111] WebSocket客户端初始化，地址：wss://fstream.binance.com/stream
2025-09-11T18:04:42.794952+0800  Level 20: [BinanceLinearDataWebsocketApi.run:192] 尝试连接到服务器：wss://fstream.binance.com/stream
2025-09-11T18:04:42.840907+0800  Level 20: [BinanceLinearDataWebsocketApi.start:123] WebSocket客户端启动
2025-09-11T18:04:42.872042+0800  Level 20: [BinanceLinearDataWebsocketApi.start_subscribe_thread:1971] 订阅处理线程已启动
2025-09-11T18:04:42.933947+0800  Level 20: [BinanceLinearRestApi._get_response:269] 创建新的TCP连接器
2025-09-11T18:04:42.979271+0800  Level 20: [BinanceLinearRestApi._get_response:276] 创建新的客户端会话
