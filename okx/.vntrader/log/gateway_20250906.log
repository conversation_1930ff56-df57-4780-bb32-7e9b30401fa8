2025-09-06T14:18:30.916987+0800  Level 20: [BinanceLinearGateway.__init__:194] Gateway Version: 2025-4-18 22:45:30
2025-09-06T14:18:30.917969+0800  Level 20: [BinanceLinearRestApi.init:148] REST客户端初始化，地址：https://fapi.binance.com
2025-09-06T14:18:30.917969+0800  Level 20: [BinanceLinearRestApi.init:152] 使用代理：http://127.0.0.1:7890
2025-09-06T14:18:30.917969+0800  Level 20: [BinanceLinearRestApi.start:160] 创建新的事件循环
2025-09-06T14:18:30.918966+0800  Level 20: [BinanceLinearRestApi.start_event_loop:392] 启动事件循环后台线程
2025-09-06T14:18:30.919972+0800  Level 20: [BinanceLinearRestApi.run_event_loop:403] 事件循环开始运行
2025-09-06T14:18:30.920480+0800  Level 20: [BinanceLinearRestApi.connect:555] REST API started
2025-09-06T14:18:30.920480+0800  Level 20: [BinanceLinearDataWebsocketApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-06T14:18:30.939805+0800  Level 20: [BinanceLinearDataWebsocketApi.init:110] WebSocket客户端初始化，地址：wss://fstream.binance.com/stream
2025-09-06T14:18:31.015502+0800  Level 20: [BinanceLinearDataWebsocketApi.run:191] 尝试连接到服务器：wss://fstream.binance.com/stream
2025-09-06T14:18:31.030604+0800  Level 20: [BinanceLinearDataWebsocketApi.start:122] WebSocket客户端启动
2025-09-06T14:18:31.076505+0800  Level 20: [BinanceLinearDataWebsocketApi.start_subscribe_thread:1971] 订阅处理线程已启动
2025-09-06T14:18:31.152377+0800  Level 20: [BinanceLinearRestApi._get_response:269] 创建新的TCP连接器
2025-09-06T14:18:31.214508+0800  Level 20: [BinanceLinearRestApi._get_response:276] 创建新的客户端会话
2025-09-06T14:18:33.031394+0800  Level 20: [BinanceLinearDataWebsocketApi.on_open:160] WebSocket连接建立成功
2025-09-06T14:18:33.046774+0800  Level 20: [BinanceLinearDataWebsocketApi.on_connected:2077] Data Websocket API is connected
2025-09-06T14:18:33.309475+0800  Level 20: [BinanceLinearRestApi.on_query_contract:1029] Available contracts data is received
2025-09-06T14:18:33.339432+0800  Level 20: [BinanceLinearRestApi.on_start_user_stream:1329] 连接trade_ws_api
2025-09-06T14:18:33.354022+0800  Level 20: [BinanceLinearTradeWebsocketApi.init:106] 使用代理：http://127.0.0.1:7890
2025-09-06T14:18:33.399302+0800  Level 20: [BinanceLinearTradeWebsocketApi.init:110] WebSocket客户端初始化，地址：wss://fstream.binance.com/ws/bmdvd2lsiJ7voOJ1PwRMuz8vlC1oGOMnq9omnQ5MVgFvy5BSO41jIQhldzyHD5OL
2025-09-06T14:18:33.626411+0800  Level 20: [BinanceLinearTradeWebsocketApi.run:191] 尝试连接到服务器：wss://fstream.binance.com/ws/bmdvd2lsiJ7voOJ1PwRMuz8vlC1oGOMnq9omnQ5MVgFvy5BSO41jIQhldzyHD5OL
2025-09-06T14:18:33.763157+0800  Level 20: [BinanceLinearTradeWebsocketApi.start:122] WebSocket客户端启动
2025-09-06T14:18:33.899454+0800  Level 20: [BinanceLinearRestApi.on_query_orders:983] Open orders data is received
2025-09-06T14:18:35.073898+0800  Level 20: [BinanceLinearTradeWebsocketApi.on_open:160] WebSocket连接建立成功
2025-09-06T14:18:35.104299+0800  Level 20: [BinanceLinearTradeWebsocketApi.on_connected:1771] Trade Websocket API is connected
2025-09-06T14:18:35.887459+0800  Level 20: [BinanceLinearDataWebsocketApi.subscribe:2147] 添加订阅请求到队列：['btcusdt@ticker', 'btcusdt@depth5', 'btcusdt@kline_1m']
2025-09-06T14:18:35.917923+0800  Level 20: [BinanceLinearDataWebsocketApi.subscribe:2147] 添加订阅请求到队列：['achusdt@ticker', 'achusdt@depth5', 'achusdt@kline_1m']
2025-09-06T14:18:35.964028+0800  Level 20: [BinanceLinearDataWebsocketApi.subscribe:2147] 添加订阅请求到队列：['ethusdt@ticker', 'ethusdt@depth5', 'ethusdt@kline_1m']
2025-09-06T14:18:35.978654+0800  Level 20: [BinanceLinearDataWebsocketApi._subscribe_process:2011] 准备发送订阅请求，总数：9
2025-09-06T14:18:36.025905+0800  Level 20: [BinanceLinearDataWebsocketApi._subscribe_process:2023] 发送订阅请求批次：['btcusdt@kline_1m', 'achusdt@depth5', 'btcusdt@ticker', 'ethusdt@ticker', 'ethusdt@depth5', 'btcusdt@depth5', 'ethusdt@kline_1m', 'achusdt@ticker', 'achusdt@kline_1m']
2025-09-06T14:18:43.930568+0800  Level 20: [BinanceLinearDataWebsocketApi.subscribe:2147] 添加订阅请求到队列：['btcusdt@ticker', 'btcusdt@depth5', 'btcusdt@kline_1m']
2025-09-06T14:18:44.054119+0800  Level 20: [BinanceLinearDataWebsocketApi._subscribe_process:2011] 准备发送订阅请求，总数：3
2025-09-06T14:18:44.084335+0800  Level 20: [BinanceLinearDataWebsocketApi._subscribe_process:2023] 发送订阅请求批次：['btcusdt@ticker', 'btcusdt@depth5', 'btcusdt@kline_1m']
2025-09-06T14:18:46.919735+0800  Level 20: [BinanceLinearDataWebsocketApi.unsubscribe:2164] 添加退订请求到队列：['btcusdt@ticker', 'btcusdt@depth5', 'btcusdt@kline_1m']
2025-09-06T14:18:47.241737+0800  Level 20: [BinanceLinearDataWebsocketApi.on_packet:2206] 收到未订阅的tick: btcusdt
2025-09-06T14:18:47.256575+0800  Level 20: [BinanceLinearDataWebsocketApi._subscribe_process:2028] 准备发送退订请求，总数：3
2025-09-06T14:18:47.332931+0800  Level 20: [BinanceLinearDataWebsocketApi._subscribe_process:2039] 发送退订请求批次：['btcusdt@ticker', 'btcusdt@depth5', 'btcusdt@kline_1m']
2025-09-06T14:19:00.296840+0800  Level 20: [BinanceLinearDataWebsocketApi.on_packet:2375] 当前时间：2025-09-06 14:19:00.281759+08:00，收到K线时间：2025-09-06 14:18:00+08:00，K线已收尾：True，symbol：ethusdt，o:4296.71，h:4298.26，l:4296.64，c:4296.96
2025-09-06T14:19:01.665170+0800  Level 20: [BinanceLinearDataWebsocketApi.subscribe:2147] 添加订阅请求到队列：['ethusdt@ticker', 'ethusdt@depth5', 'ethusdt@kline_1m']
2025-09-06T14:19:01.831729+0800  Level 20: [BinanceLinearDataWebsocketApi._subscribe_process:2011] 准备发送订阅请求，总数：3
2025-09-06T14:19:01.847033+0800  Level 20: [BinanceLinearDataWebsocketApi._subscribe_process:2023] 发送订阅请求批次：['ethusdt@ticker', 'ethusdt@kline_1m', 'ethusdt@depth5']
2025-09-06T14:19:02.752545+0800  Level 20: [BinanceLinearDataWebsocketApi.on_packet:2375] 当前时间：2025-09-06 14:19:02.599994+08:00，收到K线时间：2025-09-06 14:18:00+08:00，K线已收尾：True，symbol：achusdt，o:0.0193030，h:0.0193120，l:0.0193030，c:0.0193120
2025-09-06T14:19:05.388601+0800  Level 20: [BinanceLinearDataWebsocketApi.unsubscribe:2164] 添加退订请求到队列：['ethusdt@ticker', 'ethusdt@depth5', 'ethusdt@kline_1m']
2025-09-06T14:19:05.653573+0800  Level 20: [BinanceLinearDataWebsocketApi.on_packet:2206] 收到未订阅的tick: ethusdt
2025-09-06T14:19:05.665718+0800  Level 20: [BinanceLinearDataWebsocketApi._subscribe_process:2028] 准备发送退订请求，总数：3
2025-09-06T14:19:05.696055+0800  Level 20: [BinanceLinearDataWebsocketApi._subscribe_process:2039] 发送退订请求批次：['ethusdt@ticker', 'ethusdt@kline_1m', 'ethusdt@depth5']
2025-09-06T14:19:13.358730+0800  Level 20: [BinanceLinearDataWebsocketApi.subscribe:2147] 添加订阅请求到队列：['ethusdt@ticker', 'ethusdt@depth5', 'ethusdt@kline_1m']
2025-09-06T14:19:13.450877+0800  Level 20: [BinanceLinearDataWebsocketApi._subscribe_process:2011] 准备发送订阅请求，总数：3
2025-09-06T14:19:13.466176+0800  Level 20: [BinanceLinearDataWebsocketApi._subscribe_process:2023] 发送订阅请求批次：['ethusdt@ticker', 'ethusdt@kline_1m', 'ethusdt@depth5']
2025-09-06T14:19:17.773561+0800  Level 20: [BinanceLinearDataWebsocketApi.unsubscribe:2164] 添加退订请求到队列：['ethusdt@ticker', 'ethusdt@depth5', 'ethusdt@kline_1m']
2025-09-06T14:19:18.004007+0800  Level 20: [BinanceLinearDataWebsocketApi._subscribe_process:2028] 准备发送退订请求，总数：3
2025-09-06T14:19:18.050983+0800  Level 20: [BinanceLinearDataWebsocketApi._subscribe_process:2039] 发送退订请求批次：['ethusdt@ticker', 'ethusdt@kline_1m', 'ethusdt@depth5']
2025-09-06T14:19:18.051777+0800  Level 20: [BinanceLinearDataWebsocketApi.on_packet:2206] 收到未订阅的tick: ethusdt
2025-09-06T14:19:19.414300+0800  Level 20: [BinanceLinearRestApi.stop:166] REST客户端停止中
2025-09-06T14:19:19.504824+0800  Level 20: [BinanceLinearRestApi.stop:169] 事件循环已停止
2025-09-06T14:19:19.612568+0800  Level 20: [BinanceLinearTradeWebsocketApi.on_close:164] WebSocket连接断开，状态码：None，原因：None
2025-09-06T14:19:19.656914+0800  Level 20: [BinanceLinearTradeWebsocketApi.on_disconnected:1918] Trade Websocket API is disconnected, code: None, msg: None
2025-09-06T14:19:19.703824+0800  Level 20: [BinanceLinearTradeWebsocketApi.stop:134] WebSocket客户端停止中
2025-09-06T14:19:19.779638+0800  Level 20: [BinanceLinearDataWebsocketApi.stop_subscribe_thread:1979] 订阅处理线程已停止
2025-09-06T14:19:19.825126+0800  Level 20: [BinanceLinearDataWebsocketApi.on_close:164] WebSocket连接断开，状态码：None，原因：None
2025-09-06T14:19:19.854634+0800  Level 20: [BinanceLinearDataWebsocketApi.on_disconnected:2389] Data Websocket API is disconnected, code: None, msg: None
2025-09-06T14:19:19.948360+0800  Level 20: [BinanceLinearDataWebsocketApi.stop:134] WebSocket客户端停止中
