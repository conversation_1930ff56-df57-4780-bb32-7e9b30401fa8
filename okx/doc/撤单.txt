## OKX

WS / 下单
只有当您的账户有足够的资金才能下单。一旦下单，您的账户资金将在订单生命周期内被冻结。被冻结的资金以及数量取决于订单指定的类型和参数


服务地址
/ws/v5/private (需要登录)

限速：60次/2s
跟单交易带单员带单产品的限速：4次/2s
限速规则（期权以外）：User ID + Instrument ID
限速规则（只限期权）：User ID + Instrument Family
该接口限速同时受到 子账户限速 及 基于成交比率的子账户限速 限速规则的影响。

 同`下单` REST API 共享限速
请求示例

{
    "id": "1512",
    "op": "order",
    "args": [{
        "side": "buy",
        "instId": "BTC-USDT",
        "tdMode": "isolated",
        "ordType": "market",
        "sz": "100"
    }]
}
请求参数
参数名	类型	是否必须	描述
id	String	是	消息的唯一标识
用户提供，返回参数中会返回以便于找到相应的请求。
字母（区分大小写）与数字的组合，可以是纯字母、纯数字且长度必须要在1-32位之间。
op	String	是	操作
order
args	Array of objects	是	请求参数
> instId	String	是	产品ID，如 BTC-USDT
> tdMode	String	是	交易模式
保证金模式 isolated：逐仓 cross：全仓
非保证金模式 cash：现金
spot_isolated：现货逐仓(仅适用于现货带单) ，现货带单时，tdMode 的值需要指定为spot_isolated
> ccy	String	否	保证金币种，适用于逐仓杠杆及合约模式下的全仓杠杆订单
> clOrdId	String	否	由用户设置的订单ID
字母（区分大小写）与数字的组合，可以是纯字母、纯数字且长度要在1-32位之间。
> tag	String	否	订单标签
字母（区分大小写）与数字的组合，可以是纯字母、纯数字且长度要在1-16位之间。
> side	String	是	订单方向，buy sell
> posSide	String	否	持仓方向
在买卖模式下，默认 net
在开平仓模式下必填，且仅可选择 long 或 short，仅适用于交割/永续
> ordType	String	是	订单类型
market：市价单，仅适用于币币/杠杆/交割/永续
limit：限价单
post_only：只做maker单
fok：全部成交或立即取消
ioc：立即成交并取消剩余
optimal_limit_ioc：市价委托立即成交并取消剩余（仅适用交割、永续）
mmp：做市商保护(仅适用于组合保证金账户模式下的期权订单)
mmp_and_post_only：做市商保护且只做maker单(仅适用于组合保证金账户模式下的期权订单)
> sz	String	是	委托数量
> px	String	可选	委托价格，仅适用于limit、post_only、fok、ioc、mmp、mmp_and_post_only类型的订单
期权下单时，px/pxUsd/pxVol 只能填一个
> pxUsd	String	可选	以USD价格进行期权下单
仅适用于期权
期权下单时 px/pxUsd/pxVol 必填一个，且只能填一个
> pxVol	String	可选	以隐含波动率进行期权下单，例如 1 代表 100%
仅适用于期权
期权下单时 px/pxUsd/pxVol 必填一个，且只能填一个
> reduceOnly	Boolean	否	是否只减仓，true 或 false，默认false
仅适用于币币杠杆，以及买卖模式下的交割/永续
仅适用于合约模式和跨币种保证金模式
> tgtCcy	String	否	币币市价单委托数量sz的单位
base_ccy: 交易货币 ；quote_ccy：计价货币
仅适用于币币市价订单
默认买单为quote_ccy，卖单为base_ccy
> banAmend	Boolean	否	是否禁止币币市价改单，true 或 false，默认false
为true时，余额不足时，系统不会改单，下单会失败，仅适用于币币市价单
> pxAmendType	String	否	订单价格修正类型
0：当px超出价格限制时，不允许系统修改订单价格
1：当px超出价格限制时，允许系统将价格修改为限制范围内的最优值
默认值为0
> stpMode	String	否	自成交保护模式
cancel_maker,cancel_taker, cancel_both
Cancel both不支持FOK

默认使用账户层面的acctStpMode进行下单，该字段的默认值为cancel_maker，用户可通过母账户登录网页修改该配置；用户亦可以通过下单接口的stpMode参数指定订单的STP模式。
expTime	String	否	请求有效截止时间。Unix时间戳的毫秒数格式，如 1597026383085
tradeQuoteCcy	String	否	用于交易的计价币种。仅适用于币币。
默认值为 instId 的计价币种，比如：对于 BTC-USD，默认取 USD。
成功返回示例

{
    "id": "1512",
    "op": "order",
    "data": [{
        "clOrdId": "",
        "ordId": "12345689",
        "tag": "",
        "ts":"1695190491421",
        "sCode": "0",
        "sMsg": ""
    }],
    "code": "0",
    "msg": "",
    "inTime": "1695190491421339",
    "outTime": "1695190491423240"
}
失败返回示例

{
    "id": "1512",
    "op": "order",
    "data": [{
        "clOrdId": "",
        "ordId": "",
        "tag": "",
        "ts":"1695190491421",
        "sCode": "5XXXX",
        "sMsg": "not exist"
    }],
    "code": "1",
    "msg": "",
    "inTime": "1695190491421339",
    "outTime": "1695190491423240"
}
格式错误返回示例

{
    "id": "1512",
    "op": "order",
    "data": [],
    "code": "60013",
    "msg": "Invalid args",
    "inTime": "1695190491421339",
    "outTime": "1695190491423240"
}
返回参数
参数名	类型	描述
id	String	消息的唯一标识
op	String	操作
order
code	String	代码
msg	String	消息
data	Array of objects	请求成功后返回的数据
> ordId	String	订单ID
> clOrdId	String	由用户设置的订单ID
> tag	String	订单标签
> ts	String	系统完成订单请求处理的时间戳，Unix时间戳的毫秒数格式，如 1597026383085
> sCode	String	订单状态码，0 代表成功
> sMsg	String	订单状态消息
inTime	String	WebSocket 网关接收请求时的时间戳，Unix时间戳的微秒数格式，如 1597026383085123
outTime	String	WebSocket 网关发送响应时的时间戳，Unix时间戳的微秒数格式，如 1597026383085123
 tdMode
交易模式，下单时需要指定
现货模式：
- 币币和期权买方：cash
合约模式：
- 逐仓杠杆：isolated
- 全仓杠杆：cross
- 币币：cash
- 全仓交割/永续/期权：cross
- 逐仓交割/永续/期权：isolated
跨币种保证金模式：
- 逐仓杠杆：isolated
- 全仓币币：cross
- 全仓交割/永续/期权：cross
- 逐仓交割/永续/期权：isolated
组合保证金模式：
- 逐仓杠杆：isolated
- 全仓币币：cross
- 全仓交割/永续/期权：cross
- 逐仓交割/永续/期权：isolated
 clOrdId
clOrdId是用户自定义的唯一ID用来识别订单。如果在请求参数中传入了，那它一定会在返回参数内，并且可以用于查询订单，撤销订单，修改订单等接口。 clOrdId不能与当前所有的挂单的clOrdId重复
 posSide
持仓方向，买卖模式下此参数非必填，如果填写仅可以选择net；在开平仓模式下必填，且仅可选择 long 或 short。
开平仓模式下，side和posSide需要进行组合
开多：买入开多（side 填写 buy； posSide 填写 long ）
开空：卖出开空（side 填写 sell； posSide 填写 short ）
平多：卖出平多（side 填写 sell；posSide 填写 long ）
平空：买入平空（side 填写 buy； posSide 填写 short ）
组合保证金模式：交割和永续仅支持买卖模式
 ordType
订单类型，创建新订单时必须指定，您指定的订单类型将影响需要哪些订单参数和撮合系统如何执行您的订单，以下是有效的ordType：
普通委托：
limit：限价单，要求指定sz 和 px
market：市价单，币币和币币杠杆，是市价委托吃单；交割合约和永续合约，是自动以最高买/最低卖价格委托，遵循限价机制；期权合约不支持市价委托；由于市价委托无法确定成交价格，为确保有足够的资产买入设定数量的交易币种，会多冻结5%的计价币资产
高级委托：
post_only：限价委托，在下单那一刻只做maker，如果该笔订单的任何部分会吃掉当前挂单深度，则该订单将被全部撤销。
fok：限价委托，全部成交或立即取消，如果无法全部成交该笔订单，则该订单将被全部撤销。
ioc：限价委托，立即成交并取消剩余，立即按照委托价格撮合成交，并取消该订单剩余未完成数量，不会在深度列表上展示委托数量。
optimal_limit_ioc：市价委托，立即成交并取消剩余，仅适用于交割合约和永续合约。
 sz
交易数量，表示要购买或者出售的数量。
当币币/币币杠杆以限价买入和卖出时，指交易货币数量。
当币币杠杆以市价买入时，指计价货币的数量。
当币币杠杆以市价卖出时，指交易货币的数量。
对于币币市价单，单位由 tgtCcy 决定
当交割、永续、期权买入和卖出时，指合约张数。
 reduceOnly
只减仓，下单时，此参数设置为 true 时，表示此笔订单具有减仓属性，只会减少持仓数量，不会增加新的持仓仓位
对于同一杠杆产品，所有反方向挂单的币数加上当前只减仓下单数量，不能超过仓位资产；负债还完后，如果还有剩余的委托数量，不会反向开仓，而是会进行币币交易。
对于同一交割/永续产品，当前只减仓下单张数，加上价格时间优先于当前只减仓下单的只减仓挂单张数总和，不能超过持仓数量
仅适用于`合约模式`和`跨币种保证金模式`
仅适用于`币币杠杆`，以及买卖模式下的`交割/永续`
注意：交割和永续合约在开平仓模式下，所有的平仓单都有只减仓逻辑，不受该字段传值的影响。
 tgtCcy
市价单委托数量`sz`的单位：仅适用于币币市价下单交易。
交易货币：base_ccy
计价货币：quote_ccy
您在使用交易货币买入或者计价货币卖出时，请知晓：
1.如果您输入的数量大于当前可买或者可卖的数量，系统将按照您的最大可买或者可卖数量帮您完成交易，如果您希望按照指定数量成交，那您可以尝试使用限价单，等待市场价格波动到锁定的余额可以买入或卖出您指定的数量。
2.如果您输入的数量不大于当前可买或者可卖的数量，那当市场价格波动过大时，锁定的余额可能没办法买入您输入的交易货币数量或卖出您输入的计价货币数量，为保证您的交易体验，我们基于【能买多少买多少】或者【能卖多少卖多少】的原则，更改下单的数量帮您完成交易。此外，我们将尽量多锁定一点余额来规避更改下单数量的情况。
2.1 交易币买入例子：
以市价下单 买入 10个LTC为例，用户可买为11个，此时 10 < 11，挂单成功。当LTC-USDT的市价为200，用户被锁定余额为3,000 USDT，200*10 < 3,000，最终成交10个LTC； 若市场波动过大，LTC-USDT的市价为400，此时400*10 > 3,000，当用户被锁定的余额不够买入下单指定的交易货币数量时，系統使用用户被锁定的最大余额3,000 USDT下单买入，最终成交 3,000/400 = 7.5个 LTC。
2.2 计价币卖出例子：
以市价下单 卖出 1,000USDT为例，用户可卖为1,200USDT，1,000 < 1,200，挂单成功。LTC-USDT的市价为200，用户被锁定的余额为6个LTC，最终成交5个LTC； 若市场波动过大，LTC-USDT的市价为100，100*6 < 1,000，当用户被锁定的余额不够卖出下单指定的计价货币数量时，系統使用用户被锁定的最大余额6个LTC下单，最终成交 6 * 100 = 600 USDT。
 px
期权下单时，委托价格需为 tickSz 的整数倍。
当不为整数倍时，取值规则以tickSz取 0.0005 为例：
当委托价格对0.0005的余数大于0.00025或者委托价格小于0.0005时，向上取；
当委托价格对0.0005的余数小于等于0.00025，且委托价格大于0.0005时，向下取。
 强制自成交保护
交易系统会以母账户维度实施强制自成交保护，同一母账户下所有账户，包括母账户本身和所有子账户，都无法进行自成交。默认使用账户层面的acctStpMode进行下单，该字段的默认值为`cancel_maker`，用户可通过母账户登录网页修改该配置；用户亦可以通过下单接口的stpMode参数指定订单的STP模式。用户亦可以通过下单接口的stpMode参数指定订单的STP模式。
强制自成交保护不会导致延迟。
有三种STP模式。STP模式始终基于taker订单中的配置。
1.Cancel Maker：这是默认的STP模式，系统撤Maker订单以防止自成交。然后，taker订单会基于深度继续和下一个订单成交。
2.Cancel Taker：撤Taker订单以防止自成交。如果用户的Maker订单不是深度里第一个订单，Taker订单会被部分成交，然后撤单。FOK订单会确保完全成交和自成交保护。
3.Cancel Both：撤Taker和Maker订单以防止自成交。如果用户的Maker订单不是深度里第一个订单，Taker订单会被部分成交，然后Taker订单的剩余数量和第一个自我Maker订单被取消。此模式不支持FOK订单。

GET / 获取未成交订单列表
获取当前账户下所有未成交订单信息

限速：60次/2s
限速规则：User ID
权限：读取
HTTP请求
GET /api/v5/trade/orders-pending

请求示例

GET /api/v5/trade/orders-pending?ordType=post_only,fok,ioc&instType=SPOT

请求参数
参数名	类型	是否必须	描述
instType	String	否	产品类型
SPOT：币币
MARGIN：币币杠杆
SWAP：永续合约
FUTURES：交割合约
OPTION：期权
instFamily	String	否	交易品种
适用于交割/永续/期权
instId	String	否	产品ID，如 BTC-USDT
ordType	String	否	订单类型
market：市价单
limit：限价单
post_only：只做maker单
fok：全部成交或立即取消
ioc：立即成交并取消剩余
optimal_limit_ioc：市价委托立即成交并取消剩余（仅适用交割、永续）
mmp：做市商保护(仅适用于组合保证金账户模式下的期权订单)
mmp_and_post_only：做市商保护且只做maker单(仅适用于组合保证金账户模式下的期权订单)
op_fok：期权简选（全部成交或立即取消）
state	String	否	订单状态
live：等待成交
partially_filled：部分成交
after	String	否	请求此ID之前（更旧的数据）的分页内容，传的值为对应接口的ordId
before	String	否	请求此ID之后（更新的数据）的分页内容，传的值为对应接口的ordId
limit	String	否	返回结果的数量，最大为100，默认100条
返回结果

{
    "code": "0",
    "data": [
        {
            "accFillSz": "0",
            "algoClOrdId": "",
            "algoId": "",
            "attachAlgoClOrdId": "",
            "attachAlgoOrds": [],
            "avgPx": "",
            "cTime": "1724733617998",
            "cancelSource": "",
            "cancelSourceReason": "",
            "category": "normal",
            "ccy": "",
            "clOrdId": "",
            "fee": "0",
            "feeCcy": "BTC",
            "fillPx": "",
            "fillSz": "0",
            "fillTime": "",
            "instId": "BTC-USDT",
            "instType": "SPOT",
            "isTpLimit": "false",
            "lever": "",
            "linkedAlgoOrd": {
                "algoId": ""
            },
            "ordId": "1752588852617379840",
            "ordType": "post_only",
            "pnl": "0",
            "posSide": "net",
            "px": "13013.5",
            "pxType": "",
            "pxUsd": "",
            "pxVol": "",
            "quickMgnType": "",
            "rebate": "0",
            "rebateCcy": "USDT",
            "reduceOnly": "false",
            "side": "buy",
            "slOrdPx": "",
            "slTriggerPx": "",
            "slTriggerPxType": "",
            "source": "",
            "state": "live",
            "stpId": "",
            "stpMode": "cancel_maker",
            "sz": "0.001",
            "tag": "",
            "tdMode": "cash",
            "tgtCcy": "",
            "tpOrdPx": "",
            "tpTriggerPx": "",
            "tpTriggerPxType": "",
            "tradeId": "",
            ”tradeQuoteCcy“: "USDT",
            "uTime": "1724733617998"
        }
    ],
    "msg": ""
}
返回参数
参数名	类型	描述
instType	String	产品类型
instId	String	产品ID
tgtCcy	String	币币市价单委托数量sz的单位
base_ccy: 交易货币 ；quote_ccy：计价货币
仅适用于币币市价订单
默认买单为quote_ccy，卖单为base_ccy
ccy	String	保证金币种，适用于逐仓杠杆及合约模式下的全仓杠杆订单
ordId	String	订单ID
clOrdId	String	客户自定义订单ID
tag	String	订单标签
px	String	委托价格，对于期权，以币(如BTC, ETH)为单位
pxUsd	String	期权价格，以USD为单位
仅适用于期权，其他业务线返回空字符串""
pxVol	String	期权订单的隐含波动率
仅适用于期权，其他业务线返回空字符串""
pxType	String	期权的价格类型
px：代表按价格下单，单位为币 (请求参数 px 的数值单位是BTC或ETH)
pxVol：代表按pxVol下单
pxUsd：代表按照pxUsd下单，单位为USD (请求参数px 的数值单位是USD)
sz	String	委托数量
pnl	String	收益(不包括手续费)
适用于有成交的平仓订单，其他情况均为0
ordType	String	订单类型
market：市价单
limit：限价单
post_only：只做maker单
fok：全部成交或立即取消
ioc：立即成交并取消剩余
optimal_limit_ioc：市价委托立即成交并取消剩余（仅适用交割、永续）
mmp：做市商保护(仅适用于组合保证金账户模式下的期权订单)
mmp_and_post_only：做市商保护且只做maker单(仅适用于组合保证金账户模式下的期权订单)
op_fok：期权简选（全部成交或立即取消）
side	String	订单方向
posSide	String	持仓方向
tdMode	String	交易模式
accFillSz	String	累计成交数量
fillPx	String	最新成交价格。如果还没成交，系统返回""。
tradeId	String	最新成交ID
fillSz	String	最新成交数量
fillTime	String	最新成交时间
avgPx	String	成交均价。如果还没成交，系统返回0。
state	String	订单状态
live：等待成交
partially_filled：部分成交
lever	String	杠杆倍数，0.01到125之间的数值，仅适用于 币币杠杆/交割/永续
attachAlgoClOrdId	String	下单附带止盈止损时，客户自定义的策略订单ID
tpTriggerPx	String	止盈触发价
tpTriggerPxType	String	止盈触发价类型
last：最新价格
index：指数价格
mark：标记价格
slTriggerPx	String	止损触发价
slTriggerPxType	String	止损触发价类型
last：最新价格
index：指数价格
mark：标记价格
slOrdPx	String	止损委托价
tpOrdPx	String	止盈委托价
attachAlgoOrds	Array of objects	下单附带止盈止损信息
> attachAlgoId	String	附带止盈止损的订单ID，改单时，可用来标识该笔附带止盈止损订单。下止盈止损委托单时，该值不会传给 algoId
> attachAlgoClOrdId	String	下单附带止盈止损时，客户自定义的策略订单ID
> tpOrdKind	String	止盈订单类型
condition: 条件单
limit: 限价单
> tpTriggerPx	String	止盈触发价
> tpTriggerPxType	String	止盈触发价类型
last：最新价格
index：指数价格
mark：标记价格
> tpOrdPx	String	止盈委托价
> slTriggerPx	String	止损触发价
> slTriggerPxType	String	止损触发价类型
last：最新价格
index：指数价格
mark：标记价格
> slOrdPx	String	止损委托价
> sz	String	张数。仅适用于“多笔止盈”的止盈订单
> failCode	String	委托失败的错误码，默认为"",
委托失败时有值，如 51020
> failReason	String	委托失败的原因，默认为""
委托失败时有值
> amendPxOnTriggerType	String	是否启用开仓价止损，仅适用于分批止盈的止损订单
0：不开启，默认值
1：开启
linkedAlgoOrd	Object	止损订单信息，仅适用于包含限价止盈单的双向止盈止损订单，触发后生成的普通订单
> algoId	String	策略订单唯一标识
stpId	String	自成交保护ID
如果自成交保护不适用则返回""（已弃用）
stpMode	String	自成交保护模式
feeCcy	String	手续费币种
对于现货和杠杆的挂单卖单，表示计价币种。其他情况下，表示收取手续费的币种
fee	String	手续费与返佣
对于现货和杠杆（除挂单卖单外）：平台收取的累计手续费，为负数，如：-0.01
对于现货和杠杆的挂单卖单、交割、永续和期权：累计手续费和返佣（现货和杠杆挂单卖单始终以计价币种计算）
rebateCcy	String	返佣币种
对于现货和杠杆的挂单卖单，表示交易币种。其他情况下，表示支付返佣的币种
rebate	String	返佣金额
对于现货和杠杆（除挂单卖单外）：平台对达到交易等级要求的用户提供的挂单奖励，无返佣时返回""
对于现货和杠杆的挂单卖单：以计价币种计算的累计手续费和返佣金额
source	String	订单来源
6：计划委托策略触发后的生成的普通单
7：止盈止损策略触发后的生成的普通单
13：策略委托单触发后的生成的普通单
25：移动止盈止损策略触发后的生成的普通单
34: 追逐限价委托生成的普通单
category	String	订单种类
normal：普通委托
reduceOnly	String	是否只减仓，true 或 false
quickMgnType	String	一键借币类型，仅适用于杠杆逐仓的一键借币模式
manual：手动，auto_borrow：自动借币，auto_repay：自动还币
algoClOrdId	String	客户自定义策略订单ID。策略订单触发，且策略单有algoClOrdId是有值，否则为"",
algoId	String	策略委托单ID，策略订单触发时有值，否则为""
isTpLimit	String	是否为限价止盈，true 或 false.
uTime	String	订单状态更新时间，Unix时间戳的毫秒数格式，如 1597026383085
cTime	String	订单创建时间，Unix时间戳的毫秒数格式，如 1597026383085
cancelSource	String	订单取消来源的原因枚举值代码
cancelSourceReason	String	订单取消来源的对应具体原因
tradeQuoteCcy	String	用于交易的计价币种。

WS / 批量撤单
批量进行撤单操作，每次可批量撤销不同类型的产品，最多撤销20个

服务地址
/ws/v5/private (需要登录)

限速：300个/2s
限速规则（期权以外）：User ID + Instrument ID
限速规则（只限期权）：User ID + Instrument Family
 与其他限速按接口调用次数不同，该接口限速按订单的总个数限速。如果单次批量请求中只有一个元素，则算在单个`撤单`限速中。
 同`批量撤单` REST API 共享限速
请求示例

{
    "id": "1515",
    "op": "batch-cancel-orders",
    "args": [{
        "instId": "BTC-USDT",
        "ordId": "2517748157541376"
    }, {
        "instId": "LTC-USDT",
        "ordId": "2517748155771904"
    }]
}
请求参数
参数	类型	是否必须	描述
id	String	是	消息的唯一标识
用户提供，返回参数中会返回以便于找到相应的请求。
字母（区分大小写）与数字的组合，可以是纯字母、纯数字且长度必须要在1-32位之间。
op	String	是	支持的业务操作，如 batch-cancel-orders
args	Array of objects	是	请求参数
> instId	String	是	产品ID
> ordId	String	可选	订单ID
ordId和clOrdId必须传一个，若传两个，以ordId 为主
> clOrdId	String	可选	用户提供的订单ID
字母（区分大小写）与数字的组合，可以是纯字母、纯数字，且长度要在1-32位之间。
全部成功返回示例

{
    "id": "1515",
    "op": "batch-cancel-orders",
    "data": [{
        "clOrdId": "oktswap6",
        "ordId": "2517748157541376",
        "ts": "1695190491421",
        "sCode": "0",
        "sMsg": ""
    }, {
        "clOrdId": "oktswap7",
        "ordId": "2517748155771904",
        "ts": "1695190491421",
        "sCode": "0",
        "sMsg": ""
    }],
    "code": "0",
    "msg": "",
    "inTime": "1695190491421339",
    "outTime": "1695190491423240"
}
部分成功的返回示例

{
    "id": "1515",
    "op": "batch-cancel-orders",
    "data": [{
        "clOrdId": "oktswap6",
        "ordId": "2517748157541376",
        "ts": "1695190491421",
        "sCode": "0",
        "sMsg": ""
    }, {
        "clOrdId": "oktswap7",
        "ordId": "2517748155771904",
        "ts": "1695190491421",
        "sCode": "5XXXX",
        "sMsg": "order not exist"
    }],
    "code": "2",
    "msg": "",
    "inTime": "1695190491421339",
    "outTime": "1695190491423240"
}
全部失败的返回示例

{
    "id": "1515",
    "op": "batch-cancel-orders",
    "data": [{
        "clOrdId": "oktswap6",
        "ordId": "2517748157541376",
        "ts": "1695190491421",
        "sCode": "5XXXX",
        "sMsg": "order not exist"
    }, {
        "clOrdId": "oktswap7",
        "ordId": "2517748155771904",
        "ts": "1695190491421",
        "sCode": "5XXXX",
        "sMsg": "order not exist"
    }],
    "code": "1",
    "msg": "",
    "inTime": "1695190491421339",
    "outTime": "1695190491423240"
}
格式错误示例

{
    "id": "1515",
    "op": "batch-cancel-orders",
    "data": [],
    "code": "60013",
    "msg": "Invalid args",
    "inTime": "1695190491421339",
    "outTime": "1695190491423240"
}
返回参数
参数	类型	描述
id	String	消息的唯一标识
op	String	业务操作
code	String	代码
msg	String	消息
data	Array of objects	请求成功后返回的数据
> ordId	String	订单ID
> clOrdId	String	由用户设置的订单ID
> ts	String	系统完成订单请求处理的时间戳，Unix时间戳的毫秒数格式，如 1597026383085
> sCode	String	订单状态码，0 代表成功
> sMsg	String	订单状态消息
inTime	String	WebSocket 网关接收请求时的时间戳，Unix时间戳的微秒数格式，如 1597026383085123
outTime	String	WebSocket 网关发送响应时的时间戳，Unix时间戳的微秒数格式，如 1597026383085123



## binance
撤销全部订单(TRADE)
接口描述
撤销全部订单 (TRADE)

HTTP请求
DELETE /fapi/v1/allOpenOrders

请求权重
1

请求参数
名称	类型	是否必需	描述
symbol	STRING	YES	交易对
recvWindow	LONG	NO	
timestamp	LONG	YES	
响应示例
{
	"code": 200, 
	"msg": "The operation of cancel all open order is done."
}