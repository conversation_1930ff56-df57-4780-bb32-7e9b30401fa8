# OKX 成交量转换为 Binance 格式 - 实现总结

## 概述

本文档总结了将 OKX 成交量和成交额数据转换为 Binance 格式的更改，适用于 USDT 永续合约。实现采用简化方法，不使用额外的工具函数。

## 核心转换规则

### 成交量映射
- **Binance volume** = OKX `volCcy` (基础货币成交量，如 BTC)
- **Binance turnover** = OKX `volCcyQuote` (计价货币成交量，如 USDT)

### 合约转换公式
对于 USDT 永续合约：`contracts * ctVal = coins`
- OKX 报告合约张数 (张数)
- Binance 报告币种数量 (BTC for BTC-USDT)
- ctVal 是合约价值 (如 BTC-USDT-SWAP 每张合约 0.01 BTC)
- 使用 `format_float()` 处理乘法后的精度问题

## 实现详情

### 1. 简化直接转换

实现使用直接转换逻辑，不使用额外的工具函数，在需要的地方应用 `format_float()` 进行精度处理。

### 2. Ticker 数据处理更新

**文件**: `okx/prod/okx_gateway.py` - `on_ticker()` 方法

**修改前**:
```python
tick.volume = float(d["vol24h"])      # Contracts
tick.turnover = float(d["volCcy24h"]) # Coins
```

**修改后**:
```python
# Use OKX volCcy24h for volume (coins like Binance) and volCcyQuote24h for turnover (USDT like Binance)
tick.volume = float(d.get("volCcy24h", "0"))      # Coins (like Binance)
tick.turnover = float(d.get("volCcyQuote24h", "0")) # Quote currency (like Binance)
```

### 3. 历史K线数据处理更新

**文件**: `okx/prod/okx_gateway.py` - `query_history()` 方法

**修改前**:
```python
ts, op, hp, lp, cp, volume, turnover, _, _ = row
# volume was contracts, turnover was coins
```

**修改后**:
```python
ts, op, hp, lp, cp, vol_contracts, vol_coins, vol_quote, _ = row
# Use OKX volCcy for volume (coins like Binance) and volCcyQuote for turnover (USDT like Binance)
volume = float(vol_coins) if vol_coins else 0.0      # Coins (like Binance)
turnover = float(vol_quote) if vol_quote else 0.0    # Quote currency (like Binance)
```

### 4. 订单数据处理更新

**文件**: `okx/prod/okx_gateway.py` - `parse_order_data()` 方法

**修改前**:
```python
traded=float(data["accFillSz"]),  # Contracts
volume=float(data["sz"]),         # Contracts
```

**修改后**:
```python
# Convert OKX order volumes from contracts to coins for Binance compatibility
order_volume_coins = float(format_float(float(data["sz"]) * contract.size))
traded_volume_coins = float(format_float(float(data["accFillSz"]) * contract.size))
traded=traded_volume_coins,  # Coins
volume=order_volume_coins,   # Coins
```

### 5. 持仓数据处理更新

**文件**: `okx/prod/okx_gateway.py` - `on_position()` 方法

**修改前**:
```python
pos: float = float(d.get("pos", "0"))  # Contracts
volume=pos,
```

**修改后**:
```python
# Convert OKX position volume from contracts to coins for Binance compatibility
pos_contracts: float = float(d.get("pos", "0"))
pos_coins: float = float(format_float(pos_contracts * contract.size))
volume=pos_coins,  # Coins
```

### 6. 发单处理更新

**文件**: `okx/prod/okx_gateway.py` - `send_order()` 方法

**修改前**:
```python
"sz": str(req.volume)  # 币数量 (对 OKX 来说是错误的)
```

**修改后**:
```python
# Convert volume from coins to contracts for OKX API
# req.volume is in coins (like Binance), but OKX sz needs contracts
volume_contracts = round_to(req.volume / contract.size, contract.min_volume)

args: dict = {
    # ... other fields ...
    "sz": str(volume_contracts)  # 合约张数 (对 OKX 来说是正确的)
}
```

**说明**:
- 传入的 `req.volume` 是币数量概念（如 BTC），符合 Binance 格式
- 发送给 OKX 的 `sz` 需要是张数概念，所以需要除以 `contract.size` 转换
- 使用 `format_float()` 处理除法后的精度问题

## 数据字段映射

| 场景 | Binance 字段 | 含义 | OKX 字段 | 含义             | 换算关系 |
|------|-------------|------|----------|----------------|----------|
| K线 (成交量) | volume | 成交量 (BTC) | vol | 张数 (contracts) | 合约张数 (换算用合约面值，不直接对应 Binance) |
| | turnover | 成交额 (USDT) | volCcy | 成交量 (BTC)      | ≈ Binance volume |
| | | | volCcyQuote | 成交额 (USDT)     | ≈ Binance turnover |
| 持仓 (OI) | - | - | oi | 持仓量 (张)        | 合约张数 |
| | | | oiCcy | 持仓量 (BTC)      | 持仓的 BTC 数量 |
| | | | oiUsd | 持仓量 (USD)      | 持仓的美元价值 |
| 委托 (Orders) | - | - | sz | 委托数量 (张)       | 合约张数 |
| 持仓 (Positions) | - | - | pos | 持仓数量 (张)       | 合约张数 |
| | | | posCcy | 持仓数量 (币种，杠杆用)  | 币本位才有用 |
| 行情 (Ticker 24h) | volume (24h) | 24h 成交量 (BTC) | vol24h | 24h 成交量 (张)    | 合约张数 |
| | quoteVolume (24h) | 24h 成交额 (USDT) | volCcy24h | 24h 成交量 (BTC)  | ≈ Binance volume(24h) |
| | | | volCcyQuote (24h) | 24h 成交额 (USDT) | ≈ Binance quoteVolume(24h) |

## 测试

创建了 `okx/test_volume_conversion.py` 来验证实现：

- ✅ Ticker 成交量转换测试
- ✅ 持仓成交量转换测试
- ✅ 订单成交量转换测试
- ✅ 边界情况和精度测试
- ✅ 发单成交量转换测试（币数量 → 合约张数 → 币数量的往返转换）

所有测试均成功通过。

## 转换示例

对于 BTC-USDT-SWAP，ctVal = 0.01：

**OKX 数据**:
- vol24h: "1000" (合约张数)
- volCcy24h: "10.0" (BTC)
- volCcyQuote24h: "600000.0" (USDT)

**转换为 Binance 格式**:
- volume: 10.0 (BTC) - 使用 volCcy24h
- turnover: 600000.0 (USDT) - 使用 volCcyQuote24h

**持仓示例**:
- OKX pos: "50" (合约张数)
- 转换后: 0.5 BTC (50 * 0.01)

## 优势

1. **一致性**: OKX 数据现在与 Binance 格式匹配
2. **简洁性**: 直接转换逻辑，无需额外工具函数
3. **精确性**: 使用 `format_float()` 处理浮点精度问题
4. **全面性**: 涵盖 ticker、历史、订单和持仓数据
5. **经过测试**: 所有转换逻辑都经过充分测试

## 修改的文件

- `okx/prod/okx_gateway.py` - 主要实现
- `okx/test_volume_conversion.py` - 测试套件 (新增)
- `okx/doc/volume_conversion_summary.md` - 本文档 (新增)

## 注意事项

- 识别了持仓量处理但未实现，因为代码库中当前未使用
- 转换保持向后兼容性
- 所有成交量字段现在表示实际币种数量而不是合约张数
- 成交额字段现在表示计价货币数量 (USDT 永续为 USDT)
- 使用 `format_float()` 处理与 `contract.size` 相乘时的 Python 浮点精度问题
- 简化实现，不使用额外工具函数，以提高可维护性
- 所有涉及 `contract.size` 乘除法后都使用正确的精度处理方法
- 算法中的 `round_to` 操作先转换为张数概念再处理，确保精度计算正确
- **重要**：`traded_change` 计算使用张数概念避免浮点减法精度问题

### 7. 算法文件中的 Round_to 处理更新

**文件**: `okx/vnpy_algotrading_okx/algos/volume_follow_sync_algo.py`

**关键修改**:
- `check_all_finished()` 方法中的完成判断
- `on_tick()` 方法中的订单量计算
- `add_to_black_hole()` 方法中的黑洞量处理

**修改原则**:
```python
# 错误的做法：直接对币数量进行 round_to
volume_result = round_to(volume_coins, contract.min_volume)

# 正确的做法：先转换为张数概念再 round_to，最后转换回币数量用 format_float
volume_contracts = round_to(volume_coins / contract.size, contract.min_volume)
volume_result = float(format_float(volume_contracts * contract.size))
```

**精度处理原则**:
- **除法操作** (`/ contract.size`): 用 `round_to(*, contract.min_volume)`
  - 转换为张数概念，需要合约精度约束
- **乘法操作** (`* contract.size`): 用 `float(format_float(*))`
  - 转换为币数量，只需要处理浮点精度，不应该用合约精度约束
- **发单特例**: `round_to(coins / contract.size, contract.min_volume)`
  - 发单时需要确保发送的张数符合合约精度要求
