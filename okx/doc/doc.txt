## 查看持仓信息
获取该账户下拥有实际持仓的信息。账户为买卖模式会显示净持仓（net），账户为开平仓模式下会分别返回开多（long）或开空（short）的仓位。按照仓位创建时间倒序排列。

限速：10次/2s
限速规则：User ID
权限：读取
HTTP请求
GET /api/v5/account/positions

请求示例

# 查看BTC-USDT的持仓信息
GET /api/v5/account/positions?instId=BTC-USDT

请求参数
参数名	类型	是否必须	描述
instType	String	否	产品类型
MARGIN：币币杠杆
SWAP：永续合约
FUTURES：交割合约
OPTION：期权
instType和instId同时传入的时候会校验instId与instType是否一致。
instId	String	否	交易产品ID，如：BTC-USDT-SWAP
支持多个instId查询（不超过10个），半角逗号分隔
posId	String	否	持仓ID
支持多个posId查询（不超过20个）。
存在有效期的属性，自最近一次完全平仓算起，满30天 posId 以及整个仓位会被清除。
 如果该 instId 拥有过仓位且当前持仓量为0，传 instId 时，如果当前存在有效的posId，会返回仓位信息，如果当前不存在有效的 posId 时，不会返回仓位信息；不传 instId 时，仓位信息不返回。
 逐仓交易设置中，如果设置为自主划转模式，逐仓转入保证金后，会生成一个持仓量为0的仓位
返回结果

{
    "code": "0",
    "data": [
        {
            "adl": "1",
            "availPos": "0.00190433573",
            "avgPx": "62961.4",
            "baseBal": "",
            "baseBorrowed": "",
            "baseInterest": "",
            "bePx": "",
            "bizRefId": "",
            "bizRefType": "",
            "cTime": "1724740225685",
            "ccy": "BTC",
            "clSpotInUseAmt": "",
            "closeOrderAlgo": [],
            "deltaBS": "",
            "deltaPA": "",
            "fee": "",
            "fundingFee": "",
            "gammaBS": "",
            "gammaPA": "",
            "idxPx": "62890.5",
            "imr": "",
            "instId": "BTC-USDT",
            "instType": "MARGIN",
            "interest": "0",
            "last": "62892.9",
            "lever": "5",
            "liab": "-99.9998177776581948",
            "liabCcy": "USDT",
            "liqPenalty": "",
            "liqPx": "53615.448336593756",
            "margin": "0.000317654",
            "markPx": "62891.9",
            "maxSpotInUseAmt": "",
            "mgnMode": "isolated",
            "mgnRatio": "9.404143929947395",
            "mmr": "0.0000318005395854",
            "notionalUsd": "119.756628017499",
            "optVal": "",
            "pendingCloseOrdLiabVal": "0",
            "pnl": "",
            "pos": "0.00190433573",
            "posCcy": "BTC",
            "posId": "1752810569801498626",
            "posSide": "net",
            "quoteBal": "",
            "quoteBorrowed": "",
            "quoteInterest": "",
            "realizedPnl": "",
            "spotInUseAmt": "",
            "spotInUseCcy": "",
            "thetaBS": "",
            "thetaPA": "",
            "tradeId": "785524470",
            "uTime": "1724742632153",
            "upl": "-0.0000033452492717",
            "uplLastPx": "-0.0000033199677697",
            "uplRatio": "-0.0105311101755551",
            "uplRatioLastPx": "-0.0104515220008934",
            "usdPx": "",
            "vegaBS": "",
            "vegaPA": "",
            "nonSettleAvgPx":"",
            "settledPnl":""
        }
    ],
    "msg": ""
}
返回参数
参数名	类型	描述
instType	String	产品类型
mgnMode	String	保证金模式
cross：全仓
isolated：逐仓
posId	String	持仓ID
posSide	String	持仓方向
long：开平仓模式开多，pos为正
short：开平仓模式开空，pos为正
net：买卖模式（交割/永续/期权：pos为正代表开多，pos为负代表开空。币币杠杆时，pos均为正，posCcy为交易货币时，代表开多；posCcy为计价货币时，代表开空。）
pos	String	持仓数量，逐仓自主划转模式下，转入保证金后会产生pos为0的仓位
baseBal	String	交易币余额，适用于 币币杠杆（逐仓一键借币模式）（已弃用）
quoteBal	String	计价币余额 ，适用于 币币杠杆（逐仓一键借币模式）（已弃用）
baseBorrowed	String	交易币已借，适用于 币币杠杆（逐仓一键借币模式）（已弃用）
baseInterest	String	交易币计息，适用于 币币杠杆（逐仓一键借币模式）（已弃用）
quoteBorrowed	String	计价币已借，适用于 币币杠杆（逐仓一键借币模式）（已弃用）
quoteInterest	String	计价币计息，适用于 币币杠杆（逐仓一键借币模式）（已弃用）
posCcy	String	仓位资产币种，仅适用于币币杠杆仓位
availPos	String	可平仓数量，适用于 币币杠杆，期权
对于杠杆仓位，平仓时，杠杆还清负债后，余下的部分会视为币币交易，如果想要减少币币交易的数量，可通过"获取最大可用数量"接口获取只减仓的可用数量。
avgPx	String	开仓均价
会随结算周期变化，特别是在交割合约全仓模式下，结算时开仓均价会更新为结算价格，同时新增头寸也会改变开仓均价。
nonSettleAvgPx	String	未结算均价
不受结算影响的加权开仓价格，仅在新增头寸时更新，和开仓均价的主要区别在于是否受到结算影响。
仅适用于全仓交割
upl	String	未实现收益（以标记价格计算）
uplRatio	String	未实现收益率（以标记价格计算
uplLastPx	String	以最新成交价格计算的未实现收益，主要做展示使用，实际值还是 upl
uplRatioLastPx	String	以最新成交价格计算的未实现收益率
instId	String	产品ID，如 BTC-USDT-SWAP
lever	String	杠杆倍数，不适用于期权以及组合保证金模式下的全仓仓位
liqPx	String	预估强平价
不适用于期权
markPx	String	最新标记价格
imr	String	初始保证金，仅适用于全仓
margin	String	保证金余额，可增减，仅适用于逐仓
mgnRatio	String	维持保证金率
mmr	String	维持保证金
liab	String	负债额，仅适用于币币杠杆
liabCcy	String	负债币种，仅适用于币币杠杆
interest	String	利息，已经生成的未扣利息
tradeId	String	最新成交ID
optVal	String	期权市值，仅适用于期权
pendingCloseOrdLiabVal	String	逐仓杠杆负债对应平仓挂单的数量
notionalUsd	String	以美金价值为单位的持仓数量
adl	String	信号区
分为5档，从1到5，数字越小代表adl强度越弱
仅适用于交割/永续/期权
ccy	String	占用保证金的币种
last	String	最新成交价
idxPx	String	最新指数价格
usdPx	String	保证金币种的市场最新美金价格 仅适用于期权
bePx	String	盈亏平衡价
deltaBS	String	美金本位持仓仓位delta，仅适用于期权
deltaPA	String	币本位持仓仓位delta，仅适用于期权
gammaBS	String	美金本位持仓仓位gamma，仅适用于期权
gammaPA	String	币本位持仓仓位gamma，仅适用于期权
thetaBS	String	美金本位持仓仓位theta，仅适用于期权
thetaPA	String	币本位持仓仓位theta，仅适用于期权
vegaBS	String	美金本位持仓仓位vega，仅适用于期权
vegaPA	String	币本位持仓仓位vega，仅适用于期权
spotInUseAmt	String	现货对冲占用数量
适用于组合保证金模式
spotInUseCcy	String	现货对冲占用币种，如 BTC
适用于组合保证金模式
clSpotInUseAmt	String	用户自定义现货占用数量
适用于组合保证金模式
maxSpotInUseAmt	String	系统计算得到的最大可能现货占用数量
适用于组合保证金模式
realizedPnl	String	已实现收益
仅适用于交割/永续/期权
realizedPnl=pnl+fee+fundingFee+liqPenalty+settledPnl
settledPnl	String	已结算收益
仅适用于全仓交割
pnl	String	平仓订单累计收益额(不包括手续费)
fee	String	累计手续费金额，正数代表平台返佣 ，负数代表平台扣除
fundingFee	String	累计资金费用
liqPenalty	String	累计爆仓罚金，有值时为负数。
closeOrderAlgo	Array of objects	平仓策略委托订单。调用策略委托下单，且closeFraction=1 时，该数组才会有值。
> algoId	String	策略委托单ID
> slTriggerPx	String	止损触发价
> slTriggerPxType	String	止损触发价类型
last：最新价格
index：指数价格
mark：标记价格
> tpTriggerPx	String	止盈触发价
> tpTriggerPxType	String	止盈触发价类型
last：最新价格
index：指数价格
mark：标记价格
> closeFraction	String	策略委托触发时，平仓的百分比。1 代表100%
cTime	String	持仓创建时间，Unix时间戳的毫秒数格式，如 1597026383085
uTime	String	最近一次持仓更新时间，Unix时间戳的毫秒数格式，如 1597026383085
bizRefId	String	外部业务id，如 体验券id
bizRefType	String	外部业务类型
 PM账户下，持仓的 IMR MMR的数据是后端服务以ristUnit为最小粒度重新计算，相同riskUnit全仓仓位的imr和mmr返回值相同。

## 设置账户模式
账户模式的首次设置，需要在网页或手机app上进行。若用户计划在持有仓位的情况下切换账户模式，应该先调用预设置接口进行必要的预设置，再调用预检查接口获取不匹配信息、保证金校验等相关信息，最后调用账户模式切换接口进行账户模式切换。

限速：5次/2s
限速规则：User ID
权限：交易
HTTP请求
POST /api/v5/account/set-account-level

请求示例

POST /api/v5/account/set-account-level
body
{
    "acctLv":"1"
}
请求参数
参数名	类型	是否必须	描述
acctLv	String	是	账户模式
1: 现货模式
2: 合约模式
3: 跨币种保证金模式
4: 组合保证金模式
返回结果

{
    "code":"0",
    "msg":"",
    "data" :[
          {
            "acctLv":"1"
          }
    ]
}
返回参数
参数名	类型	描述
acctLv	String	账户模式

## 设置杠杆倍数

一个产品可以有如下10种杠杆倍数的设置场景：


在逐仓交易模式下，设置币币杠杆的杠杆倍数（币对层面）；
现货模式账户已开通借币功能，在全仓交易模式下，设置币币杠杆的杠杆倍数（币种层面）；
合约模式账户在全仓交易模式下，设置币币杠杆的杠杆倍数（币对层面）；
跨币种保证金模式账户在全仓交易模式下，设置币币杠杆的杠杆倍数（币种层面）；
组合保证金模式账户在全仓交易模式下，设置币币杠杆的杠杆倍数（币种层面）；
在全仓交易模式下，设置交割的杠杆倍数（指数层面）；
在逐仓交易模式、买卖持仓模式下，设置交割的杠杆倍数（合约层面）；
在逐仓交易模式、开平仓持仓模式下，设置交割的杠杆倍数（合约与持仓方向层面）；
在全仓交易模式下，设置永续的杠杆倍数（合约层面）；
在逐仓交易模式、买卖持仓模式下，设置永续的杠杆倍数（合约层面）；
在逐仓交易模式、开平仓持仓模式下，设置永续的杠杆倍数（合约与持仓方向层面）；
注意请求参数 posSide 仅在交割/永续的开平仓持仓模式下才需要填写（参见场景8和11）。
请参阅右侧对应的每个案例的请求示例。

限速：20次/2s
限速规则：User ID
权限：交易
HTTP请求
POST /api/v5/account/set-leverage

请求示例

# 1.在`逐仓`交易模式下，设置`币币杠杆`的杠杆倍数（币对层面）
POST /api/v5/account/set-leverage
body
{
    "instId":"BTC-USDT",
    "lever":"5",
    "mgnMode":"isolated"
}

# 2.`现货模式`账户已开通借币功能，在`全仓`交易模式下，设置`币币杠杆`的杠杆倍数（币种层面）
POST /api/v5/account/set-leverage
body
{
    "ccy":"BTC",
    "lever":"5",
    "mgnMode":"cross"
}


# 3.`合约模式`账户在`全仓`交易模式下，设置`币币杠杆`的杠杆倍数（币对层面）
POST /api/v5/account/set-leverage
body
{
    "instId":"BTC-USDT",
    "lever":"5",
    "mgnMode":"cross"
}

# 4.`跨币种保证金模式`账户在`全仓`交易模式下，设置`币币杠杆`的杠杆倍数（币种层面）
POST /api/v5/account/set-leverage
body
{
    "ccy":"BTC",
    "lever":"5",
    "mgnMode":"cross"
}

# 5. `组合保证金模式`账户在`全仓`交易模式下，设置`币币杠杆`的杠杆倍数（币种层面）
POST /api/v5/account/set-leverage
body
{
    "ccy":"BTC",
    "lever":"5",
    "mgnMode":"cross"
}

# 6.在`全仓`交易模式下，设置`交割`的杠杆倍数（指数层面）
POST /api/v5/account/set-leverage
body
{
    "instId":"BTC-USDT-200802",
    "lever":"5",
    "mgnMode":"cross"
}

# 7.在`逐仓`交易模式、`买卖`持仓模式下，设置`交割`的杠杆倍数（合约层面）
POST /api/v5/account/set-leverage
body
{
    "instId":"BTC-USDT-200802",
    "lever":"5",
    "mgnMode":"isolated"
}

# 8.在`逐仓`交易模式、`开平仓`持仓模式下，设置`交割`的杠杆倍数（合约与头寸层面）
POST /api/v5/account/set-leverage
body
{
    "instId":"BTC-USDT-200802",
    "lever":"5",
    "posSide":"long",
    "mgnMode":"isolated"
}

# 9.在`全仓`交易模式下，设置`永续`的杠杆倍数（合约层面）
POST /api/v5/account/set-leverage
body
{
    "instId":"BTC-USDT-SWAP",
    "lever":"5",
    "mgnMode":"cross"
}

# 10.在`逐仓`交易模式、`买卖`持仓模式下，设置`永续`的杠杆倍数（合约层面）
POST /api/v5/account/set-leverage
body
{
    "instId":"BTC-USDT-SWAP",
    "lever":"5",
    "mgnMode":"isolated"
}

# 11.在`逐仓`交易模式、`开平仓`持仓模式下，设置`永续`的杠杆倍数（合约与头寸层面）
POST /api/v5/account/set-leverage
body
{
    "instId":"BTC-USDT-SWAP",
    "lever":"5",
    "posSide":"long",
    "mgnMode":"isolated"
}
请求参数
参数名	类型	是否必须	描述
instId	String	可选	产品ID：币对、合约
仅适用于现货模式/跨币种保证金模式/组合保证金模式的全仓交割永续，合约模式的全仓币币杠杆交割永续 以及逐仓。
且在适用场景下必填。
ccy	String	可选	保证金币种，用于设置开启自动借币模式下币种维度的杠杆。
仅适用于现货模式/跨币种保证金模式/组合保证金模式的全仓币币杠杆。
且在适用场景下必填。
lever	String	是	杠杆倍数
mgnMode	String	是	保证金模式
isolated：逐仓
cross：全仓
如果ccy有效传值，该参数值只能为cross。
posSide	String	可选	持仓方向
long：开平仓模式开多
short：开平仓模式开空
仅适用于逐仓交割/永续
在开平仓模式且保证金模式为逐仓条件下必填
返回结果

{
    "code": "0",
    "msg": "",
    "data": [{
        "lever": "30",
        "mgnMode": "isolated",
        "instId": "BTC-USDT-SWAP",
        "posSide": "long"
    }]
}
返回参数
参数名	类型	描述
lever	String	杠杆倍数
mgnMode	String	保证金模式
isolated：逐仓
cross：全仓
instId	String	产品ID
posSide	String	持仓方向
 当希望在指数层面设置交割/永续的全仓杠杆倍数时，传入任意产品ID 和保证金模式（全仓）即可。
 组合保证金账户下交割和永续的全仓不能调整杠杆倍数。

## 获取杠杆倍数
限速：20次/2s
限速规则：User ID
权限：读取
HTTP请求
GET /api/v5/account/leverage-info

请求示例

GET /api/v5/account/leverage-info?instId=BTC-USDT-SWAP&mgnMode=cross

请求参数
参数名	类型	是否必须	描述
instId	String	可选	产品ID
支持多个instId查询，半角逗号分隔。instId个数不超过20个。
ccy	String	可选	币种，用于币种维度的杠杆。
仅适用于现货模式/跨币种保证金模式/组合保证金模式的全仓币币杠杆。
支持多ccy查询，半角逗号分隔。ccy个数不超过20个。
mgnMode	String	是	保证金模式
isolated：逐仓
cross：全仓
返回结果

{
    "code": "0",
    "msg": "",
    "data": [{
        "ccy":"",
        "instId": "BTC-USDT-SWAP",
        "mgnMode": "cross",
        "posSide": "long",
        "lever": "10"
    },{
        "ccy":"",
        "instId": "BTC-USDT-SWAP",
        "mgnMode": "cross",
        "posSide": "short",
        "lever": "10"
    }]
}
返回参数
参数名	类型	描述
instId	String	产品ID
ccy	String	币种，用于币种维度的杠杆。
仅适用于现货模式/跨币种保证金模式/组合保证金模式的全仓币币杠杆。
mgnMode	String	保证金模式
posSide	String	持仓方向
long：开平仓模式开多
short：开平仓模式开空
net：买卖模式
开平仓模式下会返回两个方向的杠杆倍数
lever	String	杠杆倍数
 组合保证金账户下交割和永续的全仓不能获取杠杆倍数。

REST 请求验证
发起请求
所有REST私有请求头都必须包含以下内容：

OK-ACCESS-KEY字符串类型的APIKey。

OK-ACCESS-SIGN使用HMAC SHA256哈希函数获得哈希值，再使用Base-64编码（请参阅签名）。

OK-ACCESS-TIMESTAMP发起请求的时间（UTC），如：2020-12-08T09:08:57.715Z

OK-ACCESS-PASSPHRASE您在创建API密钥时指定的Passphrase。

所有请求都应该含有application/json类型内容，并且是有效的JSON。

签名
生成签名

OK-ACCESS-SIGN的请求头是对timestamp + method + requestPath + body字符串（+表示字符串连接），以及SecretKey，使用HMAC SHA256方法加密，通过Base-64编码输出而得到的。

如：sign=CryptoJS.enc.Base64.stringify(CryptoJS.HmacSHA256(timestamp + 'GET' + '/api/v5/account/balance?ccy=BTC', SecretKey))

其中，timestamp的值与OK-ACCESS-TIMESTAMP请求头相同，为ISO格式，如2020-12-08T09:08:57.715Z。

method是请求方法，字母全部大写：GET/POST。

requestPath是请求接口路径。如：/api/v5/account/balance

body是指请求主体的字符串，如果请求没有主体（通常为GET请求）则body可省略。如：{"instId":"BTC-USDT","lever":"5","mgnMode":"isolated"}

 GET请求参数是算作requestPath，不算body
SecretKey为用户申请APIKey时所生成。如：22582BD0CFF14C41EDBF1AB98506286D

WebSocket
概述
WebSocket是HTML5一种新的协议（Protocol）。它实现了用户端与服务器全双工通信， 使得数据可以快速地双向传播。通过一次简单的握手就可以建立用户端和服务器连接， 服务器根据业务规则可以主动推送信息给用户端。其优点如下：

用户端和服务器进行数据传输时，请求头信息比较小，大概2个字节。
用户端和服务器皆可以主动地发送数据给对方。
不需要多次创建TCP请求和销毁，节约宽带和服务器的资源。
 强烈建议开发者使用WebSocket API获取市场行情和买卖深度等信息。
连接
连接限制：3 次/秒 (基于IP)

当订阅公有频道时，使用公有服务的地址；当订阅私有频道时，使用私有服务的地址

请求限制：

每个连接 对于 订阅/取消订阅/登录 请求的总次数限制为 480 次/小时

如果出现网络问题，系统会自动断开连接

如果连接成功后30s未订阅或订阅后30s内服务器未向用户推送数据，系统会自动断开连接

为了保持连接有效且稳定，建议您进行以下操作：

1. 每次接收到消息后，用户设置一个定时器，定时N秒，N 小于30。

2. 如果定时器被触发（N 秒内没有收到新消息），发送字符串 'ping'。

3. 期待一个文字字符串'pong'作为回应。如果在 N秒内未收到，请发出错误或重新连接。

连接限制
子账户维度，订阅每个 WebSocket 频道的最大连接数为 30 个。每个 WebSocket 连接都由唯一的 connId 标识。



受此限制的 WebSocket 频道如下：

订单频道
账户频道
持仓频道
账户余额和持仓频道
爆仓风险预警推送频道
账户greeks频道
若用户通过不同的请求参数在同一个 WebSocket 连接下订阅同一个频道，如使用 {"channel": "orders", "instType": "ANY"} 和 {"channel": "orders", "instType": "SWAP"}，只算为一次连接。若用户使用相同或不同的 WebSocket 连接订阅上述频道，如订单频道和账户频道。在该两个频道之间，计数不会累计，因为它们被视作不同的频道。简言之，系统计算每个频道对应的 WebSocket 连接数量。



新链接订阅频道时，平台将对该订阅返回channel-conn-count的消息同步链接数量。

链接数量更新

{
    "event":"channel-conn-count",
    "channel":"orders",
    "connCount": "2",
    "connId":"abcd1234"
}



当超出限制时，一般最新订阅的链接会收到拒绝。用户会先收到平时的订阅成功信息然后收到channel-conn-count-error消息，代表平台终止了这个链接的订阅。在异常场景下平台会终止已订阅的现有链接。

链接数量限制报错

{
    "event": "channel-conn-count-error",
    "channel": "orders",
    "connCount": "20",
    "connId":"a4d3ae55"
}



通过 WebSocket 进行的订单操作，例如下单、修改和取消订单，不会受到此改动影响。


订阅
订阅说明

请求格式说明

WebSocket 频道分成两类： 公共频道 和 私有频道

公共频道无需登录，包括行情频道，K线频道，交易数据频道，资金费率频道，限价范围频道，深度数据频道，标记价格频道等。

私有频道需登录，包括用户账户频道，用户交易频道，用户持仓频道等。

用户可以选择订阅一个或者多个频道，多个频道总长度不能超过 64 KB。

以下是一个请求参数的例子。每一个频道的请求参数的要求都不一样。请根据每一个频道的需求来订阅频道。

请求示例

请求参数

参数	类型	是否必须	描述
op	String	是	操作，subscribe
args	Array of objects	是	请求订阅的频道列表
> channel	String	是	频道名
> instType	String	否	产品类型
SPOT：币币
MARGIN：币币杠杆
SWAP：永续
FUTURES：交割
OPTION：期权
ANY：全部
> instFamily	String	否	交易品种
适用于交割/永续/期权
> instId	String	否	产品ID
返回示例

{
    "event": "subscribe",
    "arg": {
        "channel": "tickers",
        "instId": "BTC-USDT"
    },
    "connId": "accb8e21"
}
返回参数

参数	类型	是否必须	描述
event	String	是	事件，subscribe error
arg	Object	否	订阅的频道
> channel	String	是	频道名
> instType	String	否	产品类型
SPOT：币币
MARGIN：币币杠杆
SWAP：永续
FUTURES：交割
OPTION：期权
ANY：全部
> instFamily	String	否	交易品种
适用于交割/永续/期权
> instId	String	否	产品ID
code	String	否	错误码
msg	String	否	错误消息
connId	String	是	WebSocket连接ID
取消订阅
可以取消一个或者多个频道

请求格式说明

请求示例

请求参数

参数	类型	是否必须	描述
op	String	是	操作，unsubscribe
args	Array of objects	是	取消订阅的频道列表
> channel	String	是	频道名
> instType	String	否	产品类型
SPOT：币币
MARGIN：币币杠杆
SWAP：永续合约
FUTURES：交割合约
OPTION：期权
ANY：全部
> instFamily	String	否	交易品种
适用于交割/永续/期权
> instId	String	否	产品ID
返回示例

{
    "event": "unsubscribe",
    "arg": {
        "channel": "tickers",
        "instId": "BTC-USDT"
    },
    "connId": "d0b44253"
}
返回参数

参数	类型	是否必须	描述
event	String	是	事件，unsubscribe error
arg	Object	否	取消订阅的频道
> channel	String	是	频道名
> instType	String	否	产品类型
SPOT：币币
MARGIN：币币杠杆
SWAP：永续合约
FUTURES：交割合约
OPTION：期权
ANY：全部
> instFamily	String	否	交易品种
适用于交割/永续/期权
> instId	String	否	产品ID
code	String	否	错误码
msg	String	否	错误消息
connId	String	是	WebSocket连接ID


WS / 深度频道
获取深度数据。在提前挂单阶段，best ask的价格有机会低于best bid。books是400档频道，books5是5档频道， bbo-tbt是先1档后实时推送的频道，books-l2-tbt是先400档后实时推送的频道，books50-l2-tbt是先50档后实时推的频道；

books 首次推400档快照数据，以后增量推送，每100毫秒推送一次变化的数据
books5 首次推5档快照数据，以后定量推送，每100毫秒当5档快照数据有变化推送一次5档数据
bbo-tbt 首次推1档快照数据，以后定量推送，每10毫秒当1档快照数据有变化推送一次1档数据
books-l2-tbt 首次推400档快照数据，以后增量推送，每10毫秒推送一次变化的数据
books50-l2-tbt 首次推50档快照数据，以后增量推送，每10毫秒推送一次变化的数据
单个连接、交易产品维度，深度频道的推送顺序固定为：bbo-tbt -> books-l2-tbt -> books50-l2-tbt -> books -> books5。
在相同连接下，用户将无法为相同交易产品同时订阅 books-l2-tbt 以及 books50-l2-tbt/books频道
更多细节，请参阅更新日志 2024-07-17
 books-l2-tbt400档深度频道，只允许交易手续费等级VIP5及以上的API用户订阅。
books50-l2-tbt50档深度频道，只允许交易手续费等级VIP4及以上的API用户订阅.


