已处理
    账户相关
        app下载
            安卓
                https://www.okx.com/zh-hans/download

            苹果
                APP STORE
                ｜ https://www.okx.com/zh-hans/help/how-to-download-okx-app-on-iphone
                    外区id备用 https://www.binance.com/zh-CN/downloadTips



        开户
            https://www.okx.com/zh-hans

        api对接
            服务端
                文档
                    以发单为例
                        https://www.okx.com/docs-v5/zh/?language=python#order-book-trading-trade-post-place-order

                    注：币安赵长鹏曾是欧易的CTO


            客户端
                vnpy通用架构（已有）
                    https://github.com/veighna-global/vnpy_okx/blob/main/vnpy_okx/okx_gateway.py



        子账户
            币安、欧易均支持

        社群
            https://www.okx.com/zh-hans/community


    数据源相关
        币安
            历史数据源
            实时行情源
            信号源


    开发相关
        账户模式
            设置为合约模式
                账户模式的首次设置，需要在网页或手机app上进行。
                    为了方便您的交易体验，请在开始交易前设置适当的账户模式。
                    交易账户交易系统提供四个账户模式，分别为现货模式、合约模式、跨币种保证金模式以及组合保证金模式。


            否则
                错误码 51010
                    You can't complete this request under your current account mode
                    ｜ 当前账户模式不支持此操作
                        这个跟您的账户模式有关系，如果您要下单现货杠杆或合约，需要调整为除现货模式外的其他3种模式！您可以通过接口：https://www.okx.com/docs-v5/zh/#trading-account-rest-api-set-position-mode 设置，也可以在网页或 App 上设置；注意：首次设置必须要在页面或 App 上设置：交易页面右上角设置图标-账户模式。




        欧易与币安官方合约映射
            永续期货合约列表
                币安
                    https://www.binance.com/fapi/v1/exchangeInfo

                欧易
                    https://www.okx.com/api/v5/public/instruments?instType=SWAP


            统计
                Binance 永续合约数量: 521
                OKX 永续合约数量: 261
                两个交易所交叉的永续合约数量: 219

            抽查BTCUSDT
                欧易网页链接为btc-usdt-swap，显示为BTCUSDT，发单为BTC-USDT（使用websocket发单。币安websocket、rest均可发单，目前使用rest）
                    https://www.okx.com/zh-hans/trade-swap/btc-usdt-swap

                币安均为btcusdt
                    https://www.binance.com/zh-CN/futures/BTCUSDT




    网关创建与升级
        依赖库升级
            从 vnpy 升级到 vnpy_evo 框架
            使用 Binance 的底层 REST 和 WebSocket 客户端，提高稳定性和兼容性

        交易所常量统一化
            新增统一国际交易所常量
                "S:\Envs\evo\Lib\site-packages\vnpy_evo\trader\constant.py"
                ｜ GLOBAL = "GLOBAL"
                vnpy新国际版已将交易所字段统一置为GLOBAL，将实际交易所移至symbol，如
                    OKX（使用最新设计）
                        BTCUSDT_SWAP_OKX.GLOBAL
                        ｜ 原始为BTC-USDT与SWAP

                    BINANCE（稳定性考虑，不跟随升级）
                        BTCUSDT_SWAP_BINANCE.GLOBAL
                        ｜ 原始为BTCUSDT与PERPETUAL




        订单处理优化
            修复成交回报重复计算问题

        功能添加
            退订
            定时同步本地与服务器时间，API调用时调整时间差
            启动时设置账户模式为净持仓

        错误处理改进
            添加未订阅数据的安全检查

        日志系统增强
            添加调用者信息到日志中（类名、方法名、行号）


    底层连接升级
        底层rest、websocket添加日志跨网关自适应

    模拟账号开立与测试
        开立
            登录欧易账户—>交易—>模拟交易—>个人中心—>创建模拟盘APIKey—>开始模拟交易

        行情订阅

        交易

    错误码解析
        https://www.okx.com/docs-v5/zh/?language=python#error-code
            REST 
                REST API 错误码从 50000 到 59999
                    公共
                        错误码从 50000 到 53999
                            通用类
                            | 错误码 | HTTP 状态码 | 错误提示 |
                            | --- | --- | --- |
                            | 0 | 200 |  |
                            | 1 | 200 | 操作全部失败 |
                            | 2 | 200 | 批量操作部分成功 |




            WebSocket
                WebSocket 错误消息均为英文，中文错误消息仅供参考
                公共
                    错误码从 60000 到 64002


            关闭帧
                | 状态码 | 文案 |
                | --- | --- |
                | 1009 | 用户订阅请求过大 |
                | 4001 | 登录失败 |
                | 4002 | 参数不合法 |
                | 4003 | 登录账户多于100个 |
                | 4004 | 空闲超时30秒 |
                | 4005 | 写缓冲区满 |
                | 4006 | 异常场景关闭 |
                | 4007 | API key已更新或删除，请重新连接 |
                | 4008 | 总订阅频道数量超过最大限制 |
                | 4009 | 该连接订阅频道数超限制 |

            与币安1008错误代码不重合


    流控相关
        不订阅行情，无需处理
            websocket
                每个连接 对于 订阅/取消订阅/登录 请求的总次数限制为 480 次/小时
                ｜ 约8次/分钟



    撤单回报处理
        与币安不同，无需处理撤单回报
            撤单返回sCode等于0不能严格认为该订单已经被撤销，只表示您的撤单请求被系统服务器所接受，撤单结果以订单频道推送的状态或者查询订单状态为准


    合约参数相关
        51121 Order quantity must be a mult IP le of the lot size.
            在 API 中合约下单是以张为单位的，必须是该币对最小下单数量的倍数，最小下单数量可以通过产品接口：https://www.okx.com/docs-v5/zh/#public-data-rest-api-get-instruments 的minSz 字段获取。



待处理
    开发相关
        合约信息替换与映射
            网关层面
            算法模块层面

        发单通道替换


