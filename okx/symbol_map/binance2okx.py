#!/usr/bin/env python
# -*- coding: utf-8 -*-
'''
@File     :  binance2okx.py
@Time     :  2025/9/3 16:28
<AUTHOR>  Lance
@Project  :  okx
@Desc     :  None
@Version  :  1.0
'''
def name_binance2okx(symbol: str) -> str:
    """
    将 Binance Futures 合约代码映射到 OKX 合约代码
    规则：
    1. BTCUSDT (USDT本位永续) -> BTC-USDT-SWAP
    2. BTCUSD_PERP (币本位永续) -> BTC-USD-SWAP
    3. BTCUSD_YYMMDD (币本位交割合约) -> BTC-USD-YYMMDD
    """
    # USDT本位永续
    if symbol.endswith("USDT"):
        return f"{symbol[:-4]}-USDT-SWAP"

    # 币本位永续
    if symbol.endswith("_PERP"):
        return f"{symbol[:-5]}-USD-SWAP"

    # 币本位交割合约
    if "_" in symbol:
        base, date = symbol.split("_")
        return f"{base}-USD-{date}"

    raise ValueError(f"无法识别的 Binance 合约代码: {symbol}")


# 示例
print(name_binance2okx("BTCUSDT"))       # BTC-USDT-SWAP
print(name_binance2okx("BTCUSD_PERP"))   # BTC-USD-SWAP
print(name_binance2okx("BTCUSD_240927")) # BTC-USD-240927
