"""
比较Binance和OKX永续合约的参数差异
包括最小下单手数、最小下单价值量、最小价格跳动等
同时分析两个交易所的永续合约覆盖情况
"""

import json
import os
from typing import Optional, Dict, Any, Set, Tuple, List
from decimal import Decimal
import pandas as pd

# 导入下载合约信息模块
from download_contracts import download_all_contracts


def get_binance_perpetuals(data: Optional[Dict[str, Any]]) -> Set[str]:
    """获取Binance永续合约列表 - 只获取USDT永续合约"""
    if not data or 'symbols' not in data:
        return set()
    
    perpetuals = set()
    for symbol in data['symbols']:
        # Binance期货的永续合约没有到期日期，只要USDT结算的
        if (symbol.get('contractType') == 'PERPETUAL' and 
            symbol.get('status') == 'TRADING' and
            symbol.get('quoteAsset') == 'USDT'):
            perpetuals.add(symbol['symbol'])
    
    return perpetuals


def get_okx_perpetuals(data: Optional[Dict[str, Any]]) -> Set[str]:
    """获取OKX永续合约列表 - 只获取USDT永续合约"""
    if not data or 'data' not in data:
        return set()
    
    perpetuals = set()
    for instrument in data['data']:
        # OKX的永续合约instType为SWAP，且没有到期日，只要USDT结算的
        if (instrument.get('instType') == 'SWAP' and 
            instrument.get('state') == 'live' and
            instrument.get('settleCcy') == 'USDT'):
            perpetuals.add(instrument['instId'])
    
    return perpetuals



def load_json_file(filename: str) -> Optional[Dict[str, Any]]:
    """加载JSON文件"""
    # 只从data目录加载
    data_path = os.path.join('data', filename)
    
    try:
        with open(data_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"文件 {data_path} 不存在")
        return None
    except json.JSONDecodeError as e:
        print(f"JSON解析错误 in {data_path}: {e}")
        return None


def get_binance_perpetuals_params(data: Optional[Dict[str, Any]]) -> Dict[str, Dict]:
    """获取Binance永续合约参数 - 只获取USDT永续合约"""
    if not data or 'symbols' not in data:
        return {}
    
    perpetuals = {}
    for symbol in data['symbols']:
        # Binance期货的永续合约没有到期日期，只要USDT结算的
        if (symbol.get('contractType') == 'PERPETUAL' and 
            symbol.get('status') == 'TRADING' and
            symbol.get('quoteAsset') == 'USDT'):
            
            symbol_name = symbol['symbol']
            
            # 提取过滤器信息
            price_filter = {}
            lot_size_filter = {}
            min_notional_filter = {}
            
            for f in symbol.get('filters', []):
                if f['filterType'] == 'PRICE_FILTER':
                    price_filter = f
                elif f['filterType'] == 'LOT_SIZE':
                    lot_size_filter = f
                elif f['filterType'] == 'MIN_NOTIONAL':
                    min_notional_filter = f
            
            perpetuals[symbol_name] = {
                'symbol': symbol_name,
                # 价格过滤器
                'tickSize': float(price_filter.get('tickSize', 0)) if price_filter.get('tickSize') else 0,
                # 数量过滤器
                'stepSize': float(lot_size_filter.get('stepSize', 0)) if lot_size_filter.get('stepSize') else 0,
                'minQty': float(lot_size_filter.get('minQty', 0)) if lot_size_filter.get('minQty') else 0,
                # 最小名义价值
                'minNotional': float(min_notional_filter.get('notional', 0)) if min_notional_filter.get('notional') else 0,
            }
    
    return perpetuals


def get_okx_perpetuals_params(data: Optional[Dict[str, Any]]) -> Dict[str, Dict]:
    """获取OKX永续合约参数 - 只获取USDT永续合约"""
    if not data or 'data' not in data:
        return {}
    
    perpetuals = {}
    for instrument in data['data']:
        # OKX的永续合约instType为SWAP，且没有到期日，只要USDT结算的
        if (instrument.get('instType') == 'SWAP' and 
            instrument.get('state') == 'live' and
            instrument.get('settleCcy') == 'USDT'):
            
            inst_id = instrument['instId']
            
            perpetuals[inst_id] = {
                'instId': inst_id,
                # 价格相关
                'tickSz': float(instrument.get('tickSz', 0)) if instrument.get('tickSz') else 0,  # 下单价格精度
                # 数量相关
                'lotSz': float(instrument.get('lotSz', 0)) if instrument.get('lotSz') else 0,  # 下单数量精度
                'minSz': float(instrument.get('minSz', 0)) if instrument.get('minSz') else 0,  # 最小下单数量
            }
    
    return perpetuals


def normalize_symbol_for_comparison(symbol: str, exchange: str) -> str:
    """标准化交易对名称用于比较 - 只处理USDT合约"""
    if exchange == 'binance':
        # Binance格式如: BTCUSDT -> BTC-USDT，只处理USDT
        if symbol.endswith('USDT'):
            base = symbol[:-4]
            return f"{base}-USDT"
        return symbol
    elif exchange == 'okx':
        # OKX格式如: BTC-USDT-SWAP -> BTC-USDT
        if '-SWAP' in symbol:
            return symbol.replace('-SWAP', '')
        return symbol


def find_common_perpetuals_with_params(binance_params: Dict[str, Dict], okx_params: Dict[str, Dict]) -> Dict[str, Dict]:
    """找出两个交易所共同的永续合约及其参数"""
    # 标准化交易对名称
    binance_normalized = {}
    for symbol, params in binance_params.items():
        norm_symbol = normalize_symbol_for_comparison(symbol, 'binance')
        binance_normalized[norm_symbol] = {**params, 'original_symbol': symbol}
    
    okx_normalized = {}
    for inst_id, params in okx_params.items():
        norm_symbol = normalize_symbol_for_comparison(inst_id, 'okx')
        okx_normalized[norm_symbol] = {**params, 'original_symbol': inst_id}
    
    # 找出共同的合约
    common_contracts = {}
    for norm_symbol in binance_normalized.keys():
        if norm_symbol in okx_normalized:
            common_contracts[norm_symbol] = {
                'binance': binance_normalized[norm_symbol],
                'okx': okx_normalized[norm_symbol]
            }
    
    return common_contracts


def calculate_decimal_precision(value_str: str) -> int:
    """计算小数精度位数"""
    try:
        # 使用Decimal来准确计算精度
        from decimal import Decimal
        decimal_val = Decimal(str(value_str))
        
        # 获取小数部分的位数
        sign, digits, exponent = decimal_val.as_tuple()
        
        if exponent >= 0:
            return 0
        else:
            return abs(exponent)
    except:
        # 备用方法：直接从字符串计算
        if '.' in str(value_str):
            return len(str(value_str).split('.')[-1])
        else:
            return 0


def normalize_symbol_for_coverage(symbol: str, exchange: str) -> str:
    """标准化交易对名称用于覆盖情况分析"""
    if exchange == 'binance':
        # Binance格式如: BTCUSDT -> BTC-USDT
        if symbol.endswith('USDT'):
            base = symbol[:-4]
            return f"{base}-USDT"
        elif symbol.endswith('BUSD'):
            base = symbol[:-4]
            return f"{base}-BUSD"
        elif symbol.endswith('USDC'):
            base = symbol[:-4]
            return f"{base}-USDC"
        return symbol
    elif exchange == 'okx':
        # OKX格式如: BTC-USDT-SWAP -> BTC-USDT
        if '-SWAP' in symbol:
            return symbol.replace('-SWAP', '')
        return symbol


def create_coverage_sheet(writer, binance_perpetuals: Set[str], okx_perpetuals: Set[str], 
                         common_contracts: Dict[str, Dict]) -> None:
    """创建永续合约覆盖情况sheet"""
    
    # 标准化所有交易对名称
    binance_normalized = {}
    for symbol in binance_perpetuals:
        norm_symbol = normalize_symbol_for_coverage(symbol, 'binance')
        binance_normalized[norm_symbol] = symbol
    
    okx_normalized = {}
    for symbol in okx_perpetuals:
        norm_symbol = normalize_symbol_for_coverage(symbol, 'okx')
        okx_normalized[norm_symbol] = symbol
    
    # 获取所有标准化交易对
    all_normalized = set(binance_normalized.keys()) | set(okx_normalized.keys())
    
    # 创建覆盖情况数据
    coverage_data = []
    for norm_symbol in sorted(all_normalized):
        binance_symbol = binance_normalized.get(norm_symbol, '')
        okx_symbol = okx_normalized.get(norm_symbol, '')
        
        # 判断覆盖情况
        if binance_symbol and okx_symbol:
            coverage_status = '两个交易所都有'
        elif binance_symbol:
            coverage_status = '仅Binance有'
        elif okx_symbol:
            coverage_status = '仅OKX有'
        else:
            coverage_status = '未知'
        
        coverage_data.append({
            '标准化交易对': norm_symbol,
            'Binance交易对': binance_symbol,
            'OKX交易对': okx_symbol,
            '覆盖情况': coverage_status
        })
    
    coverage_df = pd.DataFrame(coverage_data)
    coverage_df.to_excel(writer, sheet_name='永续合约覆盖情况', index=False)


def load_ticker_data() -> tuple[Dict[str, Dict], Dict[str, Dict]]:
    """加载盘口价格数据"""
    binance_tickers = {}
    okx_tickers = {}
    
    # 加载Binance盘口数据
    binance_file = 'data/binance_bookTicker.json'
    try:
        with open(binance_file, 'r', encoding='utf-8') as f:
            binance_data = json.load(f)
            for ticker in binance_data:
                symbol = ticker['symbol']
                bid_price = float(ticker['bidPrice'])
                ask_price = float(ticker['askPrice'])
                mid_price = (bid_price + ask_price) / 2
                binance_tickers[symbol] = {
                    'bid_price': bid_price,
                    'ask_price': ask_price,
                    'mid_price': mid_price
                }
    except (FileNotFoundError, json.JSONDecodeError):
        print("无法加载Binance盘口数据")
    
    # 加载OKX盘口数据
    okx_file = 'data/okx_bookTicker.json'
    try:
        with open(okx_file, 'r', encoding='utf-8') as f:
            okx_data = json.load(f)
            if 'data' in okx_data:
                for ticker in okx_data['data']:
                    inst_id = ticker['instId']
                    bid_price = float(ticker['bidPx']) if ticker['bidPx'] else 0
                    ask_price = float(ticker['askPx']) if ticker['askPx'] else 0
                    if bid_price > 0 and ask_price > 0:
                        mid_price = (bid_price + ask_price) / 2
                        okx_tickers[inst_id] = {
                            'bid_price': bid_price,
                            'ask_price': ask_price,
                            'mid_price': mid_price
                        }
    except (FileNotFoundError, json.JSONDecodeError):
        print("无法加载OKX盘口数据")
    
    return binance_tickers, okx_tickers


def compare_contract_parameters(common_contracts: Dict[str, Dict]) -> List[Dict]:
    """比较合约参数并找出差异"""
    differences = []
    
    # 加载盘口价格数据
    print("   加载盘口价格数据...")
    binance_tickers, okx_tickers = load_ticker_data()
    print(f"   Binance盘口数据: {len(binance_tickers)}个, OKX盘口数据: {len(okx_tickers)}个")
    
    for norm_symbol, contracts in common_contracts.items():
        binance = contracts['binance']
        okx = contracts['okx']
        
        diff_info = {
            'normalized_symbol': norm_symbol,
            'binance_symbol': binance['original_symbol'],
            'okx_symbol': okx['original_symbol'],
            'differences': []
        }
        
        # 比较最小价格跳动 (tickSize)
        binance_tick = binance['tickSize']
        okx_tick = okx['tickSz']
        if binance_tick != okx_tick:
            diff_info['differences'].append({
                'parameter': '最小价格跳动',
                'binance_value': binance_tick,
                'okx_value': okx_tick,
                'binance_field': 'tickSize',
                'okx_field': 'tickSz'
            })
        
        # 比较最小下单数量
        binance_min_qty = binance['minQty']
        okx_min_qty = okx['minSz']
        if binance_min_qty != okx_min_qty:
            diff_info['differences'].append({
                'parameter': '最小下单数量',
                'binance_value': binance_min_qty,
                'okx_value': okx_min_qty,
                'binance_field': 'minQty',
                'okx_field': 'minSz'
            })
        
        # 移除下单数量精度对比，只保留最小下单数量对比
        
        # 比较最小名义价值 (使用盘口中间价计算OKX的最小名义价值)
        binance_min_notional = binance['minNotional']
        
        # 获取OKX盘口中间价
        okx_ticker = okx_tickers.get(okx['original_symbol'])
        if okx_ticker and okx_ticker['mid_price'] > 0:
            # OKX最小名义价值 = minSz * 盘口中间价
            okx_min_value = okx['minSz'] * okx_ticker['mid_price']
            calculation_method = f"minSz({okx['minSz']}) * 中间价({okx_ticker['mid_price']:.6f})"
        else:
            # 如果没有盘口数据，跳过此合约的最小名义价值比较
            okx_min_value = None
            calculation_method = "无盘口数据，无法计算"
        
        if binance_min_notional > 0 and okx_min_value is not None:  # 只有当两边都有有效数据时才比较
            diff_info['differences'].append({
                'parameter': '最小名义价值',
                'binance_value': binance_min_notional,
                'okx_value': okx_min_value,
                'binance_field': 'minNotional',
                'okx_field': calculation_method,
                'note': 'OKX通过最小数量*盘口中间价计算'
            })
        
        
        # 只保留有差异的合约
        if diff_info['differences']:
            differences.append(diff_info)
    
    return differences


def save_comparison_result(differences: List[Dict], common_contracts: Dict[str, Dict], 
                          binance_perpetuals: Set[str], okx_perpetuals: Set[str]) -> None:
    """保存比较结果到Excel文件的不同sheet"""
    
    # 确保data目录存在
    os.makedirs('data', exist_ok=True)
    
    # 保存Excel格式
    save_to_excel(differences, common_contracts, binance_perpetuals, okx_perpetuals)


def save_to_excel(differences: List[Dict], common_contracts: Dict[str, Dict], 
                  binance_perpetuals: Set[str], okx_perpetuals: Set[str]) -> None:
    """保存比较结果到Excel文件的不同sheet"""
    
    try:
        # 加载盘口价格数据用于计算
        binance_tickers, okx_tickers = load_ticker_data()
        
        with pd.ExcelWriter('data/contract_params_comparison.xlsx', engine='openpyxl') as writer:
            
            # Sheet 1: 概览统计
            summary_data = {
                '统计项目': [
                    'Binance永续合约总数',
                    'OKX永续合约总数',
                    '交叉合约总数',
                    '存在参数差异的合约',
                    '参数完全相同的合约',
                    '差异比例(%)' # 有参数差异的合约数 / 共同合约总数 × 100%
                ],
                '数值': [
                    len(binance_perpetuals),
                    len(okx_perpetuals),
                    len(common_contracts),
                    len(differences),
                    len(common_contracts) - len(differences),
                    f"{(len(differences) / len(common_contracts) * 100):.1f}%" if common_contracts else "0%"
                ]
            }
            summary_df = pd.DataFrame(summary_data)
            summary_df.to_excel(writer, sheet_name='概览统计', index=False)
            
            # Sheet 2: 永续合约覆盖情况
            create_coverage_sheet(writer, binance_perpetuals, okx_perpetuals, common_contracts)
            
            # Sheet 3: 所有合约参数对比（包含差异分析）
            all_contracts_data = []
            
            # 创建差异映射
            diff_map = {}
            for diff in differences:
                diff_map[diff['normalized_symbol']] = diff['differences']
            
            for norm_symbol, contracts in common_contracts.items():
                binance = contracts['binance']
                okx = contracts['okx']
                
                # 获取Binance盘口中间价
                binance_ticker = binance_tickers.get(binance['original_symbol'])
                binance_mid_price = binance_ticker['mid_price'] if binance_ticker else 0
                
                # 获取OKX盘口中间价
                okx_ticker = okx_tickers.get(okx['original_symbol'])
                okx_mid_price = okx_ticker['mid_price'] if okx_ticker else 0
                okx_min_notional = okx['minSz'] * okx_mid_price if okx_mid_price > 0 else None
                
                # 计算价差比例 (Binance - OKX) / OKX * 100%
                price_diff_ratio = 0
                if binance_mid_price > 0 and okx_mid_price > 0:
                    price_diff_ratio = (binance_mid_price - okx_mid_price) / okx_mid_price * 100
                
                # 计算各参数差异
                tick_diff = ""
                qty_diff = ""
                notional_diff = ""
                
                contract_diffs = diff_map.get(norm_symbol, [])
                for diff in contract_diffs:
                    if diff['parameter'] == '最小价格跳动':
                        ratio = diff['binance_value'] / diff['okx_value'] if diff['okx_value'] > 0 else float('inf')
                        tick_diff = f"{ratio:.2e}" if ratio != float('inf') else "∞"
                    elif diff['parameter'] == '最小下单数量':
                        ratio = diff['binance_value'] / diff['okx_value'] if diff['okx_value'] > 0 else float('inf')
                        qty_diff = f"{ratio:.2e}" if ratio != float('inf') else "∞"
                    elif diff['parameter'] == '最小名义价值':
                        ratio = diff['binance_value'] / diff['okx_value'] if diff['okx_value'] > 0 else float('inf')
                        notional_diff = f"{ratio:.2e}" if ratio != float('inf') else "∞"
                
                row = {
                    '标准化交易对': norm_symbol,
                    'Binance交易对': binance['original_symbol'],
                    'OKX交易对': okx['original_symbol'],
                    'Binance盘口中间价': binance_mid_price,
                    'OKX盘口中间价': okx_mid_price,
                    '价差比例(%)': price_diff_ratio,
                    'Binance最小价格跳动': binance['tickSize'],
                    'OKX最小价格跳动': okx['tickSz'],
                    '价格跳动差异': tick_diff,
                    'Binance最小下单数量': binance['minQty'],
                    'OKX最小下单数量': okx['minSz'],
                    '下单数量差异': qty_diff,
                    'Binance最小名义价值': binance['minNotional'],
                    'OKX最小名义价值': okx_min_notional if okx_min_notional is not None else '无盘口数据',
                    '名义价值差异': notional_diff,
                }
                all_contracts_data.append(row)
            
            all_contracts_df = pd.DataFrame(all_contracts_data)
            all_contracts_df.to_excel(writer, sheet_name='所有合约参数', index=False)
        
        print("Excel文件已保存到: data/contract_params_comparison.xlsx")
        
    except ImportError:
        print("警告: 无法导入pandas或openpyxl，请安装: pip install pandas openpyxl")
        print("Excel文件保存失败，但JSON文件已正常保存")
    except Exception as e:
        print(f"保存Excel文件时出错: {e}")
        print("JSON文件已正常保存")


def main():
    print("=== Binance和OKX永续合约分析 ===\n")
    
    # 优先下载最新数据
    print("正在下载最新合约数据...")
    binance_data, okx_data = download_all_contracts()
    
    # 下载失败时使用本地文件
    if binance_data is None or okx_data is None:
        print("下载失败，尝试使用本地数据文件...")
        binance_data = load_json_file('binance_exchangeInfo.json') if binance_data is None else binance_data
        okx_data = load_json_file('okx_instruments.json') if okx_data is None else okx_data
        
        if binance_data is None or okx_data is None:
            print("本地数据文件也无法加载，退出程序")
            return
    
    # 获取永续合约列表
    print("1. 解析永续合约列表...")
    binance_perpetuals = get_binance_perpetuals(binance_data)
    okx_perpetuals = get_okx_perpetuals(okx_data)
    print(f"   Binance永续合约: {len(binance_perpetuals)} 个")
    print(f"   OKX永续合约: {len(okx_perpetuals)} 个")
    
    # 获取合约参数
    print("2. 解析合约参数...")
    binance_params = get_binance_perpetuals_params(binance_data)
    okx_params = get_okx_perpetuals_params(okx_data)
    
    # 找出共同的合约
    print("3. 查找交叉合约...")
    common_contracts = find_common_perpetuals_with_params(binance_params, okx_params)
    print(f"   找到 {len(common_contracts)} 个交叉合约")
    
    # 比较参数
    print("4. 比较合约参数...")
    differences = compare_contract_parameters(common_contracts)
    print(f"   发现 {len(differences)} 个合约存在参数差异")
    
    # 保存结果
    print("5. 保存分析结果...")
    save_comparison_result(differences, common_contracts, binance_perpetuals, okx_perpetuals)
    
    # 显示统计信息
    print("\n=== 统计结果 ===")
    print(f"Binance永续合约总数: {len(binance_perpetuals)}")
    print(f"OKX永续合约总数: {len(okx_perpetuals)}")
    print(f"交叉合约总数: {len(common_contracts)}")
    print(f"参数完全相同: {len(common_contracts) - len(differences)}")
    print(f"存在参数差异: {len(differences)}")
    
    # 显示前5个有差异的合约
    if differences:
        print("\n=== 参数差异示例 (前5个) ===")
        for i, diff in enumerate(differences[:5]):
            print(f"\n{i+1}. {diff['normalized_symbol']}")
            print(f"   Binance: {diff['binance_symbol']}")
            print(f"   OKX: {diff['okx_symbol']}")
            print("   差异:")
            for d in diff['differences']:
                print(f"     - {d['parameter']}: Binance={d['binance_value']}, OKX={d['okx_value']}")
        
        if len(differences) > 5:
            print(f"\n... 还有 {len(differences) - 5} 个合约存在差异")
    
    print(f"\n详细结果已保存到 data/contract_params_comparison.xlsx")


if __name__ == "__main__":
    main()