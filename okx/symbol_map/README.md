# 永续合约参数比较工具

这个工具用于比较Binance和OKX两个交易所的永续合约参数差异。

## 功能特性

1. **永续合约覆盖情况分析** - 分析两个交易所的永续合约覆盖情况
2. **合约参数对比** - 详细比较交叉合约的各项参数
3. **差异分析** - 按不同参数类型分析差异情况
4. **Excel报告** - 生成包含多个sheet的详细Excel报告

## 使用方法

### 运行分析

```bash
cd okx/symbol_map
python compare_contract_params.py
```

### 输出文件

程序会生成以下文件：
- `data/contract_params_comparison.xlsx` - 详细的Excel分析报告

### Excel报告包含的Sheet

1. **概览统计** - 总体统计信息
2. **永续合约覆盖情况** - 两个交易所的合约覆盖对比
3. **所有合约参数** - 所有交叉合约的详细参数对比
4. **最小名义价值差异** - 按最小名义价值差异排序
5. **最小下单数量差异** - 按最小下单数量差异排序
6. **价格跳动差异** - 按价格跳动差异排序
7. **精度差异** - 价格和数量精度差异对比

## 依赖要求

```bash
pip install pandas openpyxl requests
```

## 文件说明

- `compare_contract_params.py` - 主程序文件
- `download_contracts.py` - 数据下载模块
- `data/` - 数据存储目录
  - `binance_exchangeInfo.json` - Binance合约信息
  - `okx_instruments.json` - OKX合约信息
  - `binance_bookTicker.json` - Binance盘口数据
  - `okx_bookTicker.json` - OKX盘口数据
  - `contract_params_comparison.xlsx` - 分析结果

## 注意事项

1. 程序会自动下载最新的合约信息和盘口数据
2. 如果网络连接有问题，可以手动下载数据文件到data目录
3. Excel文件包含多个sheet，建议使用Excel或WPS等软件查看