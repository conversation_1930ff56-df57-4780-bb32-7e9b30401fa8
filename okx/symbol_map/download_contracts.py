"""
合约信息下载模块
从 Binance 和 OKX 交易所实时下载合约信息
"""

import json
import urllib.request
import urllib.parse
import os
import ssl
from typing import Optional, Dict, Any


def get_system_proxy() -> Dict[str, str]:
    """获取系统代理设置"""
    proxy_handler = urllib.request.ProxyHandler()
    proxies = {}

    # 从系统获取代理设置
    for protocol in ['http', 'https']:
        if proxy := proxy_handler.proxies.get(protocol):
            proxies[protocol] = proxy
            os.environ[f"{protocol}_proxy"] = proxy

    return proxies


def create_opener_with_proxy() -> urllib.request.OpenerDirector:
    """创建带代理的URL opener"""
    proxies = get_system_proxy()
    
    if proxies:
        proxy_handler = urllib.request.ProxyHandler(proxies)
        print(f"使用代理: {proxies}")
    else:
        proxy_handler = urllib.request.ProxyHandler({})
        print("未检测到系统代理，使用直连")
    
    # 创建SSL上下文，忽略证书验证（如果需要）
    ssl_context = ssl.create_default_context()
    ssl_context.check_hostname = False
    ssl_context.verify_mode = ssl.CERT_NONE
    
    https_handler = urllib.request.HTTPSHandler(context=ssl_context)
    
    opener = urllib.request.build_opener(proxy_handler, https_handler)
    opener.addheaders = [
        ('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36')
    ]
    
    return opener


def download_json(url: str, timeout: int = 30) -> Optional[Dict[str, Any]]:
    """
    从指定URL下载JSON数据
    
    Args:
        url: 要下载的URL
        timeout: 超时时间（秒）
    
    Returns:
        解析后的JSON数据，失败时返回None
    """
    try:
        opener = create_opener_with_proxy()
        
        print(f"正在下载: {url}")
        
        request = urllib.request.Request(url)
        response = opener.open(request, timeout=timeout)
        
        if response.getcode() == 200:
            data = response.read().decode('utf-8')
            json_data = json.loads(data)
            print(f"下载成功，数据大小: {len(data)} 字节")
            return json_data
        else:
            print(f"下载失败，HTTP状态码: {response.getcode()}")
            return None
            
    except urllib.error.URLError as e:
        print(f"网络错误: {e}")
        return None
    except json.JSONDecodeError as e:
        print(f"JSON解析错误: {e}")
        return None
    except Exception as e:
        print(f"下载过程中发生错误: {e}")
        return None


def download_binance_contracts() -> Optional[Dict[str, Any]]:
    """
    下载 Binance 期货合约信息
    
    Returns:
        Binance 合约信息，失败时返回None
    """
    url = "https://fapi.binance.com/fapi/v1/exchangeInfo"
    return download_json(url)


def download_okx_contracts() -> Optional[Dict[str, Any]]:
    """
    下载 OKX 永续合约信息
    
    Returns:
        OKX 合约信息，失败时返回None
    """
    url = "https://www.okx.com/api/v5/public/instruments?instType=SWAP"
    return download_json(url)


def download_binance_book_ticker() -> Optional[list]:
    """
    下载 Binance 期货最新盘口价格
    
    Returns:
        Binance 盘口价格信息，失败时返回None
    """
    url = "https://fapi.binance.com/fapi/v1/ticker/bookTicker"
    return download_json(url)


def download_okx_book_ticker() -> Optional[Dict[str, Any]]:
    """
    下载 OKX 永续合约最新盘口价格
    
    Returns:
        OKX 盘口价格信息，失败时返回None
    """
    url = "https://www.okx.com/api/v5/market/tickers?instType=SWAP"
    return download_json(url)


def save_contracts_to_file(data: Dict[str, Any], filename: str) -> bool:
    """
    将合约数据保存到文件
    
    Args:
        data: 要保存的数据
        filename: 文件名
    
    Returns:
        保存是否成功
    """
    try:
        # 确保data目录存在
        os.makedirs('data', exist_ok=True)
        filepath = os.path.join('data', filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"数据已保存到: {filepath}")
        return True
    except Exception as e:
        print(f"保存文件失败: {e}")
        return False


def download_all_contracts(save_to_file: bool = True) -> tuple[Optional[Dict[str, Any]], Optional[Dict[str, Any]]]:
    """
    下载所有交易所的合约信息
    
    Args:
        save_to_file: 是否保存到文件
    
    Returns:
        (binance_data, okx_data) 元组
    """
    print("=== 开始下载合约信息 ===\n")
    
    # 下载 Binance 合约信息
    print("1. 下载 Binance 期货合约信息...")
    binance_data = download_binance_contracts()
    
    if binance_data and save_to_file:
        save_contracts_to_file(binance_data, 'binance_exchangeInfo.json')
    
    print()
    
    # 下载 OKX 合约信息
    print("2. 下载 OKX 永续合约信息...")
    okx_data = download_okx_contracts()
    
    if okx_data and save_to_file:
        save_contracts_to_file(okx_data, 'okx_instruments.json')
    
    print()
    
    # 下载 Binance 盘口价格
    print("3. 下载 Binance 期货盘口价格...")
    binance_book_ticker = download_binance_book_ticker()
    
    if binance_book_ticker and save_to_file:
        save_contracts_to_file(binance_book_ticker, 'binance_bookTicker.json')
    
    print()
    
    # 下载 OKX 盘口价格
    print("4. 下载 OKX 永续合约盘口价格...")
    okx_book_ticker = download_okx_book_ticker()
    
    if okx_book_ticker and save_to_file:
        save_contracts_to_file(okx_book_ticker, 'okx_bookTicker.json')
    
    print()
    
    # 检查下载结果
    success_count = sum([bool(binance_data), bool(okx_data), bool(binance_book_ticker), bool(okx_book_ticker)])
    if success_count == 4:
        print("✅ 所有数据下载完成")
    elif success_count >= 2:
        print("⚠️  部分数据下载成功")
    else:
        print("❌ 大部分数据下载失败")
    
    return binance_data, okx_data


if __name__ == "__main__":
    # 测试下载功能
    download_all_contracts()