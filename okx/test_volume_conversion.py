#!/usr/bin/env python3
"""
Test script for OKX volume conversion implementation.

This script tests the simplified volume conversion logic to ensure it correctly
converts OKX contract-based volumes to Binance-compatible coin-based volumes.
"""

from numpy import format_float_positional

# Mock ContractData class for testing
class MockContractData:
    def __init__(self, name, size, min_volume=0.001):
        self.name = name
        self.size = size  # ctVal
        self.min_volume = min_volume  # 最小交易量

def format_float(f: float) -> str:
    """Convert float number to string with correct precision."""
    return format_float_positional(f, trim='-')

def round_to(value: float, target: float) -> float:
    """Round value to the nearest multiple of target."""
    if target == 0:
        return 0
    return round(round(value / target) * target, 10)


def test_volume_conversion():
    """Test OKX volume conversion to Binance format."""
    print("Testing OKX volume conversion functions...")
    
    # Create a mock BTC-USDT-SWAP contract
    contract = MockContractData(
        name="BTC-USDT-SWAP",
        size=0.01  # ctVal = 0.01 BTC per contract
    )
    
    print(f"Contract: {contract.name}, ctVal: {contract.size}")
    
    # Test 1: Ticker volume conversion (simplified)
    print("\n=== Test 1: Ticker Volume Conversion ===")
    okx_vol_ccy = "10.0"  # 10 BTC
    okx_vol_ccy_quote = "600000.0"  # 600,000 USDT

    # Simplified ticker conversion logic
    binance_volume = float(okx_vol_ccy)
    binance_turnover = float(okx_vol_ccy_quote)

    print(f"OKX volCcy (BTC): {okx_vol_ccy}")
    print(f"OKX volCcyQuote (USDT): {okx_vol_ccy_quote}")
    print(f"Binance volume (BTC): {binance_volume}")
    print(f"Binance turnover (USDT): {binance_turnover}")

    # Verify results
    expected_volume = 10.0
    expected_turnover = 600000.0

    assert binance_volume == expected_volume, f"Volume mismatch: {binance_volume} != {expected_volume}"
    assert binance_turnover == expected_turnover, f"Turnover mismatch: {binance_turnover} != {expected_turnover}"
    print("✓ Ticker volume conversion test passed")
    
    # Test 2: Position volume conversion (simplified with format_float)
    print("\n=== Test 2: Position Volume Conversion ===")
    okx_pos = "50"  # 50 contracts

    # Simplified position conversion logic with format_float
    pos_contracts = float(okx_pos)
    pos_coins = float(format_float(pos_contracts * contract.size))
    expected_pos_coins = 0.5  # 50 * 0.01 = 0.5 BTC

    print(f"OKX position (contracts): {okx_pos}")
    print(f"Position in coins (BTC): {pos_coins}")
    print(f"Expected position (BTC): {expected_pos_coins}")

    assert pos_coins == expected_pos_coins, f"Position mismatch: {pos_coins} != {expected_pos_coins}"
    print("✓ Position volume conversion test passed")
    
    # Test 3: Order volume conversion (simplified with format_float)
    print("\n=== Test 3: Order Volume Conversion ===")
    okx_sz = "100"  # 100 contracts

    # Simplified order conversion logic with format_float
    order_coins = float(format_float(float(okx_sz) * contract.size))
    expected_order_coins = 1.0  # 100 * 0.01 = 1.0 BTC

    print(f"OKX order size (contracts): {okx_sz}")
    print(f"Order size in coins (BTC): {order_coins}")
    print(f"Expected order size (BTC): {expected_order_coins}")

    assert order_coins == expected_order_coins, f"Order size mismatch: {order_coins} != {expected_order_coins}"
    print("✓ Order volume conversion test passed")
    
    # Test 4: Edge cases and precision
    print("\n=== Test 4: Edge Cases and Precision ===")

    # Test with zero values
    zero_volume = float("0")
    zero_turnover = float("0")
    assert zero_volume == 0.0, f"Zero volume test failed: {zero_volume}"
    assert zero_turnover == 0.0, f"Zero turnover test failed: {zero_turnover}"

    # Test position with zero
    zero_pos = float(format_float(float("0") * contract.size))
    assert zero_pos == 0.0, f"Zero position test failed: {zero_pos}"

    # Test order with zero
    zero_order = float(format_float(float("0") * contract.size))
    assert zero_order == 0.0, f"Zero order test failed: {zero_order}"

    # Test precision with round_to
    test_value = 123.456789123456
    rounded_value = round_to(test_value, contract.min_volume)
    print(f"Original value: {test_value}")
    print(f"Rounded value: {rounded_value}")

    print("✓ Edge cases and precision test passed")
    
    # Test 5: Send order volume conversion
    print("\n=== Test 5: Send Order Volume Conversion ===")

    # Simulate send_order conversion: coins to contracts
    req_volume_coins = 1.0  # 1.0 BTC (like Binance format)
    volume_contracts = round_to(req_volume_coins / contract.size, contract.min_volume)  # 1.0 / 0.01 = 100 contracts
    expected_contracts = 100.0

    print(f"Request volume (BTC): {req_volume_coins}")
    print(f"Converted to contracts: {volume_contracts}")
    print(f"Expected contracts: {expected_contracts}")

    assert volume_contracts == expected_contracts, f"Send order conversion mismatch: {volume_contracts} != {expected_contracts}"

    # Simulate parse_order_data conversion: contracts back to coins
    okx_sz_response = str(int(volume_contracts))  # "100" (from OKX response)
    parsed_volume_coins = float(format_float(float(okx_sz_response) * contract.size))  # 100 * 0.01 = 1.0 BTC

    print(f"OKX response sz (contracts): {okx_sz_response}")
    print(f"Parsed back to coins (BTC): {parsed_volume_coins}")
    print(f"Original request volume (BTC): {req_volume_coins}")

    assert parsed_volume_coins == req_volume_coins, f"Round-trip conversion mismatch: {parsed_volume_coins} != {req_volume_coins}"
    print("✓ Send order volume conversion test passed")

    # Test 6: Round_to with contract conversion and algorithm precision
    print("\n=== Test 6: Round_to with Contract Conversion ===")

    # Test order volume conversion with format_float
    okx_sz = "123"  # 123 contracts
    order_volume_coins = float(format_float(float(okx_sz) * contract.size))
    expected_coins = 1.23  # 123 * 0.01 = 1.23

    print(f"OKX sz (contracts): {okx_sz}")
    print(f"Converted to coins with round_to: {order_volume_coins}")
    print(f"Expected coins: {expected_coins}")

    assert order_volume_coins == expected_coins, f"Round_to conversion mismatch: {order_volume_coins} != {expected_coins}"

    # Test algorithm-style conversion: coins -> contracts -> coins (with precision handling)
    req_volume = 1.2345  # 1.2345 BTC
    # Step 1: Convert to contracts and round_to
    volume_contracts = round_to(req_volume / contract.size, contract.min_volume)
    # Step 2: Convert back to coins with format_float for precision
    final_volume = float(format_float(volume_contracts * contract.size))

    print(f"Algorithm test - Request volume (BTC): {req_volume}")
    print(f"Converted to contracts: {volume_contracts}")
    print(f"Final volume (BTC): {final_volume}")

    # The final volume should be properly formatted
    expected_final = float(format_float(round_to(123.45, 0.001) * 0.01))
    assert final_volume == expected_final, f"Algorithm precision mismatch: {final_volume} != {expected_final}"
    print("✓ Round_to with contract conversion test passed")

    # Test 7: Traded change precision handling
    print("\n=== Test 7: Traded Change Precision Handling ===")

    # Simulate order traded change calculation with precision issues
    order_traded = 1.2345678  # Current order traded (coins)
    last_order_traded = 1.2345677  # Last order traded (coins)

    # Direct subtraction (problematic)
    direct_change = order_traded - last_order_traded
    print(f"Direct subtraction: {direct_change}")

    # Precision-safe calculation (like in gateway)
    order_traded_contracts = order_traded / contract.size
    last_traded_contracts = last_order_traded / contract.size
    traded_change_contracts = round_to(order_traded_contracts - last_traded_contracts, contract.min_volume)
    traded_change = float(format_float(traded_change_contracts * contract.size))

    print(f"Order traded (BTC): {order_traded}")
    print(f"Last order traded (BTC): {last_order_traded}")
    print(f"Order traded (contracts): {order_traded_contracts}")
    print(f"Last traded (contracts): {last_traded_contracts}")
    print(f"Change (contracts): {traded_change_contracts}")
    print(f"Change (BTC): {traded_change}")

    # The precision-safe method should give a clean result
    expected_change_contracts = round_to(123.45678 - 123.45677, 0.001)  # 0.00001 -> 0.0
    expected_change = float(format_float(expected_change_contracts * 0.01))

    assert traded_change == expected_change, f"Traded change precision mismatch: {traded_change} != {expected_change}"
    print("✓ Traded change precision handling test passed")

    print("\n🎉 All simplified volume conversion tests passed!")


if __name__ == "__main__":
    test_volume_conversion()
