#!/usr/bin/env python
# -*- coding: utf-8 -*-
'''
@File     :  run.py
@Time     :  2025/9/3 16:50
<AUTHOR>  Lance
@Project  :  okx
@Desc     :  None
@Version  :  1.0
'''
# from vnpy.event import EventEngine
# from vnpy.trader.engine import MainEngine
# from vnpy.trader.ui import MainWindow, create_qapp
from vnpy_evo.event import EventEngine
from vnpy_evo.trader.engine import MainEngine
from vnpy_evo.trader.ui import MainWindow, create_qapp

from prod.okx_gateway import OkxGateway


def main() -> None:
    """main entry"""
    qapp = create_qapp()

    event_engine = EventEngine()
    main_engine = MainEngine(event_engine)
    okx_gateway = main_engine.add_gateway(OkxGateway)
    okx_gateway.public_api.enable_log_mode()

    main_window = MainWindow(main_engine, event_engine)
    main_window.showMaximized()

    qapp.exec()


if __name__ == "__main__":
    main()
