2025-09-11T17:48:34.129033+0800 | INFO     | vnpy_algotrading_okx.dispatch_algo:init_redis:265 - Redis连接初始化成功: localhost:6380/0
2025-09-11T17:48:34.139181+0800 | INFO     | vnpy_algotrading_okx.dispatch_algo:init_test_users:161 - 已存在用户数据，跳过测试用户初始化
2025-09-11T17:48:34.139685+0800 | INFO     | vnpy_algotrading_okx.dispatch_algo:start:283 - 测试模式已启用，将自动生成测试订单
2025-09-11T17:48:34.140692+0800 | INFO     | vnpy_algotrading_okx.dispatch_algo:start:286 - 订单分发器启动成功
2025-09-11T17:48:34.142691+0800 | INFO     | vnpy_algotrading_okx.dispatch_algo:start:292 - 分发线程已启动
2025-09-11T17:48:44.365354+0800 | INFO     | vnpy_algotrading_okx.dispatch_algo:_generate_test_orders:422 - 已生成2个测试订单，ref=105, ETHUSDT.BINANCE
2025-09-11T17:56:50.049284+0800 | INFO     | vnpy_algotrading_okx.dispatch_algo:init_redis:265 - Redis连接初始化成功: localhost:6380/0
2025-09-11T17:56:50.052936+0800 | INFO     | vnpy_algotrading_okx.dispatch_algo:init_test_users:161 - 已存在用户数据，跳过测试用户初始化
2025-09-11T17:56:50.053938+0800 | INFO     | vnpy_algotrading_okx.dispatch_algo:start:283 - 测试模式已启用，将自动生成测试订单
2025-09-11T17:56:50.054941+0800 | INFO     | vnpy_algotrading_okx.dispatch_algo:start:286 - 订单分发器启动成功
2025-09-11T17:56:50.054941+0800 | INFO     | vnpy_algotrading_okx.dispatch_algo:start:292 - 分发线程已启动
2025-09-11T17:56:54.217280+0800 | ERROR    | vnpy_algotrading_okx.dispatch_algo:_dispatch_order:531 - 分发订单[9]时发生错误: Error 10061 connecting to localhost:6380. 由于目标计算机积极拒绝，无法连接。.
Traceback (most recent call last):

  File "S:\Envs\evo\Lib\site-packages\redis\connection.py", line 357, in connect
    sock = self.retry.call_with_retry(
           │    │     └ <function Retry.call_with_retry at 0x00000133B4A19E40>
           │    └ <redis.retry.Retry object at 0x00000133B4D680B0>
           └ <redis.connection.Connection(host=localhost,port=6380,db=0)>
  File "S:\Envs\evo\Lib\site-packages\redis\retry.py", line 62, in call_with_retry
    return do()
           └ <function AbstractConnection.connect.<locals>.<lambda> at 0x00000133B49D1D00>
  File "S:\Envs\evo\Lib\site-packages\redis\connection.py", line 358, in <lambda>
    lambda: self._connect(), lambda error: self.disconnect(error)
            │    │                         │    └ <function AbstractConnection.disconnect at 0x00000133B4A1B240>
            │    │                         └ <redis.connection.Connection(host=localhost,port=6380,db=0)>
            │    └ <function Connection._connect at 0x00000133B4A1BC40>
            └ <redis.connection.Connection(host=localhost,port=6380,db=0)>
  File "S:\Envs\evo\Lib\site-packages\redis\connection.py", line 730, in _connect
    raise err
          └ ConnectionRefusedError(10061, '由于目标计算机积极拒绝，无法连接。', None, 10061, None)
  File "S:\Envs\evo\Lib\site-packages\redis\connection.py", line 718, in _connect
    sock.connect(socket_address)
    │    │       └ ('127.0.0.1', 6380)
    │    └ <method 'connect' of '_socket.socket' objects>
    └ <socket.socket [closed] fd=-1, family=2, type=1, proto=0>

ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "S:\Envs\evo\Lib\threading.py", line 1032, in _bootstrap
    self._bootstrap_inner()
    │    └ <function Thread._bootstrap_inner at 0x00000133906C1080>
    └ <Thread(DispatchThread, started daemon 23320)>
  File "S:\Envs\evo\Lib\threading.py", line 1075, in _bootstrap_inner
    self.run()
    │    └ <function Thread.run at 0x00000133906C0D60>
    └ <Thread(DispatchThread, started daemon 23320)>
  File "S:\Envs\evo\Lib\threading.py", line 1012, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {}
    │    │        │    │        └ <Thread(DispatchThread, started daemon 23320)>
    │    │        │    └ ()
    │    │        └ <Thread(DispatchThread, started daemon 23320)>
    │    └ <bound method OrderDispatcher._dispatch_loop of <vnpy_algotrading_okx.dispatch_algo.OrderDispatcher object at 0x00000133B4B69...
    └ <Thread(DispatchThread, started daemon 23320)>

  File "S:\OneDrive - lancely\芷瀚同步\开发\币\okx\vnpy_algotrading_okx\dispatch_algo.py", line 327, in _dispatch_loop
    self._dispatch_pending_orders()
    │    └ <function OrderDispatcher._dispatch_pending_orders at 0x00000133B4B58040>
    └ <vnpy_algotrading_okx.dispatch_algo.OrderDispatcher object at 0x00000133B4B69640>

  File "S:\OneDrive - lancely\芷瀚同步\开发\币\okx\vnpy_algotrading_okx\dispatch_algo.py", line 707, in _dispatch_pending_orders
    self._find_and_dispatch_remaining_order(ref, direction, first_user, True)
    │    │                                  │    │          └ 'zhuser1'
    │    │                                  │    └ '空'
    │    │                                  └ 105
    │    └ <function OrderDispatcher._find_and_dispatch_remaining_order at 0x00000133B4B47100>
    └ <vnpy_algotrading_okx.dispatch_algo.OrderDispatcher object at 0x00000133B4B69640>

  File "S:\OneDrive - lancely\芷瀚同步\开发\币\okx\vnpy_algotrading_okx\dispatch_algo.py", line 492, in _find_and_dispatch_remaining_order
    self._dispatch_order(next_todo)
    │    │               └ <Todo: 9>
    │    └ <function OrderDispatcher._dispatch_order at 0x00000133B4B46E80>
    └ <vnpy_algotrading_okx.dispatch_algo.OrderDispatcher object at 0x00000133B4B69640>

> File "S:\OneDrive - lancely\芷瀚同步\开发\币\okx\vnpy_algotrading_okx\dispatch_algo.py", line 516, in _dispatch_order
    self.redis_client.set(redis_key, json.dumps(order_data))
    │    │            │   │          │    │     └ {'id': 9, 'content': 'ETHUSDT.BINANCE_test_5311', 'vt_symbol': 'ETHUSDT.BINANCE', 'direction': '空', 'offset': '开', 'price': 3...
    │    │            │   │          │    └ <function dumps at 0x00000133923FBEC0>
    │    │            │   │          └ <module 'json' from 'S:\\Envs\\evo\\Lib\\json\\__init__.py'>
    │    │            │   └ 'order:9'
    │    │            └ <function BasicKeyCommands.set at 0x00000133B4AC6160>
    │    └ <redis.client.Redis(<redis.connection.ConnectionPool(<redis.connection.Connection(host=localhost,port=6380,db=0)>)>)>
    └ <vnpy_algotrading_okx.dispatch_algo.OrderDispatcher object at 0x00000133B4B69640>

  File "S:\Envs\evo\Lib\site-packages\redis\commands\core.py", line 2335, in set
    return self.execute_command("SET", *pieces, **options)
           │    │                       │         └ {}
           │    │                       └ ['order:9', '{"id": 9, "content": "ETHUSDT.BINANCE_test_5311", "vt_symbol": "ETHUSDT.BINANCE", "direction": "\\u7a7a", "offse...
           │    └ <function Redis.execute_command at 0x00000133B4AECCC0>
           └ <redis.client.Redis(<redis.connection.ConnectionPool(<redis.connection.Connection(host=localhost,port=6380,db=0)>)>)>
  File "S:\Envs\evo\Lib\site-packages\redis\client.py", line 559, in execute_command
    return self._execute_command(*args, **options)
           │    │                 │       └ {}
           │    │                 └ ('SET', 'order:9', '{"id": 9, "content": "ETHUSDT.BINANCE_test_5311", "vt_symbol": "ETHUSDT.BINANCE", "direction": "\\u7a7a",...
           │    └ <function Redis._execute_command at 0x00000133B4AECD60>
           └ <redis.client.Redis(<redis.connection.ConnectionPool(<redis.connection.Connection(host=localhost,port=6380,db=0)>)>)>
  File "S:\Envs\evo\Lib\site-packages\redis\client.py", line 565, in _execute_command
    conn = self.connection or pool.get_connection(command_name, **options)
           │    │             │    │              │               └ {}
           │    │             │    │              └ 'SET'
           │    │             │    └ <function ConnectionPool.get_connection at 0x00000133B4A21580>
           │    │             └ <redis.connection.ConnectionPool(<redis.connection.Connection(host=localhost,port=6380,db=0)>)>
           │    └ None
           └ <redis.client.Redis(<redis.connection.ConnectionPool(<redis.connection.Connection(host=localhost,port=6380,db=0)>)>)>
  File "S:\Envs\evo\Lib\site-packages\redis\connection.py", line 1422, in get_connection
    connection.connect()
    │          └ <function AbstractConnection.connect at 0x00000133B4A1AF20>
    └ <redis.connection.Connection(host=localhost,port=6380,db=0)>
  File "S:\Envs\evo\Lib\site-packages\redis\connection.py", line 363, in connect
    raise ConnectionError(self._error_message(e))
          │               │    └ <function AbstractConnection._error_message at 0x00000133B4A1B100>
          │               └ <redis.connection.Connection(host=localhost,port=6380,db=0)>
          └ <class 'redis.exceptions.ConnectionError'>

redis.exceptions.ConnectionError: Error 10061 connecting to localhost:6380. 由于目标计算机积极拒绝，无法连接。.
2025-09-11T17:56:59.634003+0800 | INFO     | vnpy_algotrading_okx.dispatch_algo:stop:300 - 订单分发器正在停止...
2025-09-11T17:57:00.051505+0800 | ERROR    | vnpy_algotrading_okx.dispatch_algo:_dispatch_order:531 - 分发订单[9]时发生错误: Error 10061 connecting to localhost:6380. 由于目标计算机积极拒绝，无法连接。.
Traceback (most recent call last):

  File "S:\Envs\evo\Lib\site-packages\redis\connection.py", line 357, in connect
    sock = self.retry.call_with_retry(
           │    │     └ <function Retry.call_with_retry at 0x00000133B4A19E40>
           │    └ <redis.retry.Retry object at 0x00000133B4D680B0>
           └ <redis.connection.Connection(host=localhost,port=6380,db=0)>
  File "S:\Envs\evo\Lib\site-packages\redis\retry.py", line 62, in call_with_retry
    return do()
           └ <function AbstractConnection.connect.<locals>.<lambda> at 0x00000133B3B5F060>
  File "S:\Envs\evo\Lib\site-packages\redis\connection.py", line 358, in <lambda>
    lambda: self._connect(), lambda error: self.disconnect(error)
            │    │                         │    └ <function AbstractConnection.disconnect at 0x00000133B4A1B240>
            │    │                         └ <redis.connection.Connection(host=localhost,port=6380,db=0)>
            │    └ <function Connection._connect at 0x00000133B4A1BC40>
            └ <redis.connection.Connection(host=localhost,port=6380,db=0)>
  File "S:\Envs\evo\Lib\site-packages\redis\connection.py", line 730, in _connect
    raise err
          └ ConnectionRefusedError(10061, '由于目标计算机积极拒绝，无法连接。', None, 10061, None)
  File "S:\Envs\evo\Lib\site-packages\redis\connection.py", line 718, in _connect
    sock.connect(socket_address)
    │    │       └ ('127.0.0.1', 6380)
    │    └ <method 'connect' of '_socket.socket' objects>
    └ <socket.socket [closed] fd=-1, family=2, type=1, proto=0>

ConnectionRefusedError: [WinError 10061] 由于目标计算机积极拒绝，无法连接。


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "S:\Envs\evo\Lib\threading.py", line 1032, in _bootstrap
    self._bootstrap_inner()
    │    └ <function Thread._bootstrap_inner at 0x00000133906C1080>
    └ <Thread(DispatchThread, started daemon 23320)>
  File "S:\Envs\evo\Lib\threading.py", line 1075, in _bootstrap_inner
    self.run()
    │    └ <function Thread.run at 0x00000133906C0D60>
    └ <Thread(DispatchThread, started daemon 23320)>
  File "S:\Envs\evo\Lib\threading.py", line 1012, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {}
    │    │        │    │        └ <Thread(DispatchThread, started daemon 23320)>
    │    │        │    └ ()
    │    │        └ <Thread(DispatchThread, started daemon 23320)>
    │    └ <bound method OrderDispatcher._dispatch_loop of <vnpy_algotrading_okx.dispatch_algo.OrderDispatcher object at 0x00000133B4B69...
    └ <Thread(DispatchThread, started daemon 23320)>

  File "S:\OneDrive - lancely\芷瀚同步\开发\币\okx\vnpy_algotrading_okx\dispatch_algo.py", line 327, in _dispatch_loop
    self._dispatch_pending_orders()
    │    └ <function OrderDispatcher._dispatch_pending_orders at 0x00000133B4B58040>
    └ <vnpy_algotrading_okx.dispatch_algo.OrderDispatcher object at 0x00000133B4B69640>

  File "S:\OneDrive - lancely\芷瀚同步\开发\币\okx\vnpy_algotrading_okx\dispatch_algo.py", line 707, in _dispatch_pending_orders
    self._find_and_dispatch_remaining_order(ref, direction, first_user, True)
    │    │                                  │    │          └ 'zhuser1'
    │    │                                  │    └ '空'
    │    │                                  └ 105
    │    └ <function OrderDispatcher._find_and_dispatch_remaining_order at 0x00000133B4B47100>
    └ <vnpy_algotrading_okx.dispatch_algo.OrderDispatcher object at 0x00000133B4B69640>

  File "S:\OneDrive - lancely\芷瀚同步\开发\币\okx\vnpy_algotrading_okx\dispatch_algo.py", line 492, in _find_and_dispatch_remaining_order
    self._dispatch_order(next_todo)
    │    │               └ <Todo: 9>
    │    └ <function OrderDispatcher._dispatch_order at 0x00000133B4B46E80>
    └ <vnpy_algotrading_okx.dispatch_algo.OrderDispatcher object at 0x00000133B4B69640>

> File "S:\OneDrive - lancely\芷瀚同步\开发\币\okx\vnpy_algotrading_okx\dispatch_algo.py", line 516, in _dispatch_order
    self.redis_client.set(redis_key, json.dumps(order_data))
    │    │            │   │          │    │     └ {'id': 9, 'content': 'ETHUSDT.BINANCE_test_5311', 'vt_symbol': 'ETHUSDT.BINANCE', 'direction': '空', 'offset': '开', 'price': 3...
    │    │            │   │          │    └ <function dumps at 0x00000133923FBEC0>
    │    │            │   │          └ <module 'json' from 'S:\\Envs\\evo\\Lib\\json\\__init__.py'>
    │    │            │   └ 'order:9'
    │    │            └ <function BasicKeyCommands.set at 0x00000133B4AC6160>
    │    └ <redis.client.Redis(<redis.connection.ConnectionPool(<redis.connection.Connection(host=localhost,port=6380,db=0)>)>)>
    └ <vnpy_algotrading_okx.dispatch_algo.OrderDispatcher object at 0x00000133B4B69640>

  File "S:\Envs\evo\Lib\site-packages\redis\commands\core.py", line 2335, in set
    return self.execute_command("SET", *pieces, **options)
           │    │                       │         └ {}
           │    │                       └ ['order:9', '{"id": 9, "content": "ETHUSDT.BINANCE_test_5311", "vt_symbol": "ETHUSDT.BINANCE", "direction": "\\u7a7a", "offse...
           │    └ <function Redis.execute_command at 0x00000133B4AECCC0>
           └ <redis.client.Redis(<redis.connection.ConnectionPool(<redis.connection.Connection(host=localhost,port=6380,db=0)>)>)>
  File "S:\Envs\evo\Lib\site-packages\redis\client.py", line 559, in execute_command
    return self._execute_command(*args, **options)
           │    │                 │       └ {}
           │    │                 └ ('SET', 'order:9', '{"id": 9, "content": "ETHUSDT.BINANCE_test_5311", "vt_symbol": "ETHUSDT.BINANCE", "direction": "\\u7a7a",...
           │    └ <function Redis._execute_command at 0x00000133B4AECD60>
           └ <redis.client.Redis(<redis.connection.ConnectionPool(<redis.connection.Connection(host=localhost,port=6380,db=0)>)>)>
  File "S:\Envs\evo\Lib\site-packages\redis\client.py", line 565, in _execute_command
    conn = self.connection or pool.get_connection(command_name, **options)
           │    │             │    │              │               └ {}
           │    │             │    │              └ 'SET'
           │    │             │    └ <function ConnectionPool.get_connection at 0x00000133B4A21580>
           │    │             └ <redis.connection.ConnectionPool(<redis.connection.Connection(host=localhost,port=6380,db=0)>)>
           │    └ None
           └ <redis.client.Redis(<redis.connection.ConnectionPool(<redis.connection.Connection(host=localhost,port=6380,db=0)>)>)>
  File "S:\Envs\evo\Lib\site-packages\redis\connection.py", line 1422, in get_connection
    connection.connect()
    │          └ <function AbstractConnection.connect at 0x00000133B4A1AF20>
    └ <redis.connection.Connection(host=localhost,port=6380,db=0)>
  File "S:\Envs\evo\Lib\site-packages\redis\connection.py", line 363, in connect
    raise ConnectionError(self._error_message(e))
          │               │    └ <function AbstractConnection._error_message at 0x00000133B4A1B100>
          │               └ <redis.connection.Connection(host=localhost,port=6380,db=0)>
          └ <class 'redis.exceptions.ConnectionError'>

redis.exceptions.ConnectionError: Error 10061 connecting to localhost:6380. 由于目标计算机积极拒绝，无法连接。.
2025-09-11T17:57:00.546995+0800 | INFO     | vnpy_algotrading_okx.dispatch_algo:_generate_test_orders:422 - 已生成2个测试订单，ref=106, ETHUSDT.BINANCE
2025-09-11T17:57:00.716749+0800 | INFO     | vnpy_algotrading_okx.dispatch_algo:stop:308 - 订单分发器已停止
2025-09-11T18:04:42.647384+0800 | INFO     | vnpy_algotrading_okx.dispatch_algo:init_redis:265 - Redis连接初始化成功: localhost:6380/0
2025-09-11T18:04:42.651373+0800 | INFO     | vnpy_algotrading_okx.dispatch_algo:init_test_users:161 - 已存在用户数据，跳过测试用户初始化
2025-09-11T18:04:42.652375+0800 | INFO     | vnpy_algotrading_okx.dispatch_algo:start:283 - 测试模式已启用，将自动生成测试订单
2025-09-11T18:04:42.653375+0800 | INFO     | vnpy_algotrading_okx.dispatch_algo:start:286 - 订单分发器启动成功
2025-09-11T18:04:42.655375+0800 | INFO     | vnpy_algotrading_okx.dispatch_algo:start:292 - 分发线程已启动
2025-09-11T18:04:44.994289+0800 | INFO     | vnpy_algotrading_okx.dispatch_algo:_dispatch_order:520 - [9]分发Redis，用户:zhuser1，组:2，ref:105，ETHUSDT.BINANCE 空
2025-09-11T18:04:45.243104+0800 | INFO     | vnpy_algotrading_okx.dispatch_algo:_dispatch_order:520 - [11]分发Redis，用户:zhuser1，组:2，ref:106，ETHUSDT.BINANCE 多
2025-09-11T18:04:53.236411+0800 | INFO     | vnpy_algotrading_okx.dispatch_algo:_generate_test_orders:422 - 已生成2个测试订单，ref=107, ETHUSDT.BINANCE
2025-09-11T18:04:55.452230+0800 | WARNING  | vnpy_algotrading_okx.dispatch_algo:_check_completed_orders:601 - [9]分发超10秒未接收，跳至下一个
2025-09-11T18:04:56.040081+0800 | WARNING  | vnpy_algotrading_okx.dispatch_algo:_check_completed_orders:601 - [11]分发超10秒未接收，跳至下一个
2025-09-11T18:04:56.500213+0800 | WARNING  | vnpy_algotrading_okx.dispatch_algo:_check_completed_orders:601 - [9]分发超10秒未接收，跳至下一个
2025-09-11T18:04:56.701461+0800 | WARNING  | vnpy_algotrading_okx.dispatch_algo:_check_completed_orders:601 - [11]分发超10秒未接收，跳至下一个
2025-09-11T18:04:57.119522+0800 | WARNING  | vnpy_algotrading_okx.dispatch_algo:_check_completed_orders:601 - [9]分发超10秒未接收，跳至下一个
2025-09-11T18:04:57.243136+0800 | WARNING  | vnpy_algotrading_okx.dispatch_algo:_check_completed_orders:601 - [11]分发超10秒未接收，跳至下一个
2025-09-11T18:04:57.800192+0800 | WARNING  | vnpy_algotrading_okx.dispatch_algo:_check_completed_orders:601 - [9]分发超10秒未接收，跳至下一个
2025-09-11T18:04:58.234396+0800 | WARNING  | vnpy_algotrading_okx.dispatch_algo:_check_completed_orders:601 - [11]分发超10秒未接收，跳至下一个
2025-09-11T18:04:58.619638+0800 | INFO     | vnpy_algotrading_okx.dispatch_algo:stop:300 - 订单分发器正在停止...
2025-09-11T18:04:58.714600+0800 | INFO     | vnpy_algotrading_okx.dispatch_algo:stop:308 - 订单分发器已停止
