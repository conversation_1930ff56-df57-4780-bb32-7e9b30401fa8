from peewee import *
from vnpy.trader.setting import SETTINGS
from peewee import Model, CharField, IntegerField, AutoField, DateTimeField, TextField, MySQLDatabase, FloatField
from peewee import __exception_wrapper__
import datetime

class RetryOperationalError(object):
    def execute_sql(self, sql, params=None, commit=True):
        try:
            cursor = super(RetryOperationalError, self).execute_sql(
                sql, params, commit)
        except OperationalError:
            if not self.is_closed():
                self.close()
            with __exception_wrapper__:
                cursor = self.cursor()
                cursor.execute(sql, params or ())
                if commit and not self.in_transaction():
                    self.commit()
        return cursor
    
class RetryMySQLDatabase(RetryOperationalError, MySQLDatabase):
    pass

db_user_name = SETTINGS["database.user"]
db_passwd = SETTINGS["database.password"]
db_host= SETTINGS["database.host"]
db_port = SETTINGS["database.port"]
db = RetryMySQLDatabase(database='scout',
                   user=db_user_name,
                   password=db_passwd,
                   host=db_host,
                   port=db_port
)
                    
class ModelBase(Model):
    def to_dict(self):
        return self.__data__

class Status(Model):
    id = AutoField()
    content = CharField(unique=True)
    creation_time = DateTimeField()
    # 下载
    download_status = IntegerField(null=True)
    download_updated_time = DateTimeField(null=True)
    
    # 回测
    backtest_status = IntegerField(null=True)
    backtest_updated_time = DateTimeField(null=True)
    
    # 运行
    running_status = IntegerField(null=True)
    running_start_time = DateTimeField(null=True)
    running_updated_time = DateTimeField(null=True)
    
    delete_flag = IntegerField(null=True)
    delete_time = DateTimeField(null=True)
    remarks = CharField(null=True)
    ext1 = CharField(null=True)
    ext2 = CharField(null=True)
        # 新增字段
    main_service = IntegerField(null=True)  # 是否服务main
    hf_service = IntegerField(null=True)    # 是否服务hf

    class Meta:
        database = db
        table_name = 'status2'
        
    
class OrderError(Model):
    symbol = CharField()
    exchange = CharField()
    orderid = CharField()
    create_date = DateTimeField(default=datetime.datetime.now())
    error_code = IntegerField()
    error_msg = CharField()
    username = CharField(null=True)
    todo_id = CharField(null=True)
    fix_date = DateTimeField(null=True)
    remarks = CharField(null=True)
    ext1 = CharField(null=True)
    ext2 = CharField(null=True)

    class Meta:
        database = db
        # indexes = ((('symbol', 'exchange', 'orderid', 'create_date', 'error_code'), True),)

class QRHis(Model):
    id = AutoField()
    symbol = CharField(max_length=255, null=False)
    interval =  IntegerField(null=False)
    max_volume = FloatField(null=False)
    online_time = DateTimeField(null=False)
    offline_time = DateTimeField(null=True)
    status = IntegerField(
        constraints=[Check('status IN (0, 1)')],
        default=0
    )

    class Meta:
        database = db
        table_name = 'qr_his' 
        indexes = (
            (('symbol', 'interval'), True),  # The 'True' makes it unique
        )

# 确保表存在
db.create_tables([OrderError, QRHis, Status], safe=True)