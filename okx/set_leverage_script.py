import sys
import signal
from time import sleep
from logging import INFO

from vnpy.event import EventEngine
from vnpy.trader.engine import MainEngine
from vnpy.trader.setting import SETTINGS
from vnpy.trader.utility import load_json, save_json

# Configure logging settings
SETTINGS["log.active"] = True
SETTINGS["log.level"] = INFO
SETTINGS["log.console"] = True
SETTINGS["log.file"] = True


def signal_handler(signum, frame):
    """Handle exit signals"""
    print("\nReceived signal to exit. Cleaning up...")
    if 'main_engine' in globals():
        main_engine.write_log("Closing connection...")
        main_engine.close()
    sys.exit(0)


def format_symbol(symbol):
    """
    Format symbol to OKX format.
    If input is in Binance format (e.g., BTCUSDT), add _SWAP_OKX suffix.
    If input is already in OKX format (e.g., BTCUSDT_SWAP_OKX), keep as is.
    """
    if symbol.endswith('_SWAP_OKX'):
        return symbol
    else:
        return f"{symbol}_SWAP_OKX"


def set_leverage(symbol, leverage):
    """Set leverage for a specific symbol"""
    global main_engine

    # Register signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # Create event engine
    event_engine = EventEngine()
    main_engine = MainEngine(event_engine)

    # Load connection settings
    connect_filename = 'connect_okx.json'
    okx_setting = load_json(connect_filename)
    if not okx_setting:
        # Create default configuration file
        default_okx_setting = {
            "API Key": "",
            "Secret Key": "",
            "Passphrase": "",
            "Server": "REAL",  # REAL 或 DEMO
            "Proxy Host": "127.0.0.1",
            "Proxy Port": 7890
        }
        
 
        save_json(connect_filename, default_okx_setting)
        print(f"Created default OKX gateway configuration file: {connect_filename}")
        print("Please fill in your API information and run the script again.")
        return False

    # Add gateway
    try:
        okx_gateway = main_engine.add_gateway(OkxGateway)
        main_engine.write_log("OKX Gateway added successfully")
    except Exception as e:
        print(f"Failed to add OKX gateway: {e}")
        return False

    # Connect to OKX
    main_engine.connect(okx_setting, "OKX")
    main_engine.write_log("Connecting to OKX gateway...")

    # Wait for connection
    sleep(5)

    # Format symbol to OKX format
    formatted_symbol = format_symbol(symbol)

    try:
        # Set leverage
        main_engine.get_gateway('OKX').rest_api.set_leverage(formatted_symbol, leverage)
        main_engine.write_log(f"Set leverage for {formatted_symbol} to {leverage}")
        print(f"Successfully set leverage for {formatted_symbol} to {leverage}")

        # Wait a moment for any pending requests to complete
        sleep(2)

        # Close connection properly
        main_engine.close()

        return True
    except Exception as e:
        main_engine.write_log(f"Error setting leverage for {formatted_symbol}: {str(e)}")
        print(f"Error setting leverage for {formatted_symbol}: {str(e)}")

        # Try to close anyway
        try:
            main_engine.close()
        except:
            pass

        return False


if __name__ == "__main__":
    # Add the path to the okx gateway module
    sys.path.append('/opt/external_lib/prod_gateway_okx')
    from okx_gateway import OkxGateway

    # Get input from command line arguments or user input
    if len(sys.argv) == 3:
        symbol = sys.argv[1]
        try:
            leverage = int(sys.argv[2])
        except ValueError:
            print("Leverage must be an integer")
            sys.exit(1)
    else:
        symbol = input("Enter symbol (e.g., BTCUSDT or BTCUSDT_SWAP_OKX): ").strip()
        try:
            leverage = int(input("Enter leverage: ").strip())
        except ValueError:
            print("Leverage must be an integer")
            sys.exit(1)

    # Set leverage
    success = set_leverage(symbol, leverage)

    # Additional sleep to ensure all connections are closed
    sleep(1)

    sys.exit(0 if success else 1)