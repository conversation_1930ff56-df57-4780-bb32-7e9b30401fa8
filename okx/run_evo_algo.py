#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
算法交易启动器：Binance行情 + OKX交易
从数据库读取todo，订阅Binance的行情，跟量也是跟Binance的tick，但发单到OKX
"""

from vnpy_evo.event import EventEngine
from vnpy_evo.trader.engine import MainEngine
from vnpy_evo.trader.utility import load_json, save_json
import time
import signal
import sys
import typer

from vnpy_algotrading_okx.base import EVENT_ALGO_LOG
from prod.recorder_engine import RecorderEngine
from vnpy_algotrading_okx.dispatch_algo import get_dispatcher, logger

app = typer.Typer()

class BinanceOkxAlgoApp:
    def __init__(self, test_mode: bool = False):
        self.test_mode = test_mode
        self.event_engine = EventEngine(interval=0.3)
        self.main_engine = MainEngine(self.event_engine)

        self.binance_gateway = None
        self.okx_gateway = None
        self.algo_engine = None
        self.dispatcher = None  # 订单分发器
        self.register_event()
        self.init_engines()

        self._active = True  # 添加运行状态标记

    def register_event(self) -> None:
        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler) # kill -2
        signal.signal(signal.SIGTERM, self._signal_handler) # kill -15

        # 在Windows平台上signal模块可能不完全支持
        if sys.platform != 'win32':
            # 忽略SIGTSTP信号(Ctrl+Z)，避免进程被挂起
            signal.signal(signal.SIGTSTP, signal.SIG_IGN)
            signal.signal(signal.SIGHUP, self.reload_setting)

    def reload_setting(self, signum, frame):
        if self.algo_engine:
            self.algo_engine.load_setting()

    def _signal_handler(self, signum, frame):
        """
        信号处理函数
        """
        sig_name = signal.Signals(signum).name
        self.main_engine.write_log(f"收到信号: {sig_name}，准备关闭应用...")
        self._active = False

    def init_engines(self):
        # 添加K线记录引擎
        self.recorder_engine = self.main_engine.add_engine(RecorderEngine)
        self.recorder_engine.start(error_only=True)
        self.main_engine.write_log("添加录制引擎")

        # 记录算法引擎日志
        log_engine = self.main_engine.get_engine('log')
        self.event_engine.register(EVENT_ALGO_LOG, log_engine.process_log_event)

        # 添加算法交易引擎
        from vnpy_algotrading_okx import AlgoTradingApp
        self.algo_engine = self.main_engine.add_app(AlgoTradingApp)

        # 初始化订单分发器(测试模式)
        if self.test_mode:
            self.dispatcher = get_dispatcher()
            self.dispatcher.start(test_enabled=self.test_mode)
            logger.info(f"订单分发器已启动，测试模式：{'开启' if self.test_mode else '关闭'}")

        # 添加OKX网关（用于交易）
        from prod.okx_gateway import OkxGateway
        self.okx_gateway = self.main_engine.add_gateway(OkxGateway)

        # 加载OKX网关配置
        okx_setting = load_json("connect_okx.json")
        if not okx_setting:
            # 创建默认配置文件
            default_okx_setting = {
                "API Key": "",
                "Secret Key": "",
                "Passphrase": "",
                "Server": "REAL",  # REAL 或 DEMO
                "Proxy Host": "",
                "Proxy Port": 0
            }
            save_json("connect_okx.json", default_okx_setting)
            self.main_engine.write_log("已创建默认OKX网关配置文件，请填写API信息")
            okx_setting = default_okx_setting

        self.main_engine.connect(okx_setting, "OKX")
        self.main_engine.write_log("OKX网关添加成功")
        
        # 添加Binance网关（用于行情订阅）
        from prod.binance_linear_gateway2 import BinanceLinearGateway
        self.binance_gateway = self.main_engine.add_gateway(BinanceLinearGateway)
        # self.binance_gateway.market_ws_api.enable_log_mode() # test
        
        # 加载Binance网关配置
        binance_setting = load_json("connect_binance.json")
        if not binance_setting:
            # 创建默认配置文件
            default_binance_setting = {
                "API Key": "",
                "API Secret": "",
                "Server": "REAL",  # REAL 或 TESTNET
                "Kline Stream": "True",
                "Proxy Host": "",
                "Proxy Port": 0
            }
            save_json("connect_binance.json", default_binance_setting)
            self.main_engine.write_log("已创建默认Binance网关配置文件，请填写API信息")
            binance_setting = default_binance_setting
        
        self.main_engine.connect(binance_setting, "BINANCE_LINEAR")
        self.main_engine.write_log("Binance网关添加成功")

        # 添加K线生成引擎
        from prod.barGen_redis_engine import BarGenEngine
        bar_gen_engine = self.main_engine.add_engine(BarGenEngine)
        bar_gen_engine.start()
        self.main_engine.write_log("添加Bar生成引擎")

        self.algo_engine.start(
            test_enabled=self.test_mode,  # 根据命令行参数设置测试模式
            allow_multiple_algos=True,    # 默认True
            risk_enabled=False             # 默认True
        )
        self.main_engine.write_log("算法交易引擎创建成功")

    def run(self):
        """运行应用"""
        self.main_engine.write_log("Binance-OKX算法交易应用已启动")
        while self._active:
            time.sleep(1)
        self.main_engine.write_log("应用正在关闭...")
        self.close()

    def close(self):
        """关闭应用"""
        if self.algo_engine:
            self.algo_engine.close()

        if self.okx_gateway:
            self.okx_gateway.cancel_all()

        # 关闭订单分发器
        if self.dispatcher:
            self.dispatcher.stop()
            logger.info("订单分发器已停止")

        if self.binance_gateway:
            self.binance_gateway.close()

        time.sleep(2)

        self.main_engine.close()

@app.command()
def main(
    test: bool = typer.Option(False, "--test", "-t", help="启用测试模式")
):
    """启动Binance-OKX算法交易应用"""
    app = BinanceOkxAlgoApp(test_mode=test)
    app.run()

if __name__ == "__main__":
    app()