import sys
import signal
from datetime import time, datetime
from logging import INFO
from time import sleep
from zoneinfo import ZoneInfo

from vnpy.event import EventEngine
from vnpy.trader.constant import Exchange
from vnpy.trader.engine import MainEngine
from vnpy.trader.object import SubscribeRequest,OrderRequest,Direction,OrderType,Offset,CancelRequest
from vnpy.trader.setting import SETTINGS
from vnpy.trader.utility import load_json, save_json
from vnpy.trader.event import EVENT_TICK
from vnpy_evo.trader.utility import extract_vt_symbol

# sys.path.append('/opt/external_lib/prod_gateway_okx')
# from okx_gateway import OkxGateway
from prod.okx_gateway import OkxGateway
# Configure logging settings
SETTINGS["log.active"] = True
SETTINGS["log.level"] = INFO
SETTINGS["log.console"] = True
SETTINGS["log.file"] = True

# Trading hours configuration
# Configuration file
def process_tick(event):
    """Process tick data update event"""
    tick = event.data
    print(f"Tick received - Symbol: {tick.vt_symbol}, Time: {tick.datetime}, Last Price: {tick.last_price}")

def signal_handler(signum, frame):
    """Handle exit signals"""
    print("\nReceived signal to exit. Cleaning up...")
    if 'main_engine' in globals():
        main_engine.write_log("Closing connection...")
        main_engine.close()
    sys.exit(0)

def run_test():
    """Main test function"""
    global main_engine  # Make it accessible to signal handler
    
    # Create event engine
    event_engine = EventEngine()
    main_engine = MainEngine(event_engine)

    # Register signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Register tick event handler
    # event_engine.register(EVENT_TICK, process_tick)
    # main_engine.write_log("Tick event handler registered")
    
    # connect_filename = 'connect_okx.json'
    connect_filename = 'connect_okx_read.json'
    okx_setting = load_json(connect_filename)
    if not okx_setting:
        # 创建默认配置文件
        default_okx_setting = {
            "API Key": " ",
            "Secret Key": " ",
            "Passphrase": " ",
            "Server": "REAL",  # REAL 或 DEMO
            "Proxy Host": "127.0.0.1",
            "Proxy Port": 7890
        }
        save_json(connect_filename, default_okx_setting)
        print("已创建默认OKX网关配置文件，请填写API信息")
        okx_setting = default_okx_setting
    
    # Add gateway
    okx_gateway = main_engine.add_gateway(OkxGateway)
    okx_gateway.public_api.enable_log_mode()
    main_engine.write_log("okx Gateway added successfully")
    
    # Add BarGen engine
    # main_engine.add_engine(BarGenEngine)
    # main_engine.write_log("BarGen engine added successfully")
    main_engine.write_log("Gateway configuration loaded")
    # 添加OKX网关（用于交易）
    main_engine.connect(okx_setting, "OKX")
    main_engine.write_log("Connected to okx gateway")
    
    # Simple subscription
    vt_symbol = "DOGEUSDT_SWAP_OKX.GLOBAL"
    symbol, exchange = extract_vt_symbol(vt_symbol)

    while not main_engine.get_contract(vt_symbol):
        sleep(1)

    main_engine.write_log("***Querying account and position***")
    main_engine.write_log(str(main_engine.get_all_accounts()))
    main_engine.write_log(str(main_engine.get_all_positions()))

    # 杠杆查询
    main_engine.write_log(f"***Querying leverage***")
    # okx_gateway.query_leverage(symbol)
    okx_gateway.query_symbol_config()
    sleep(2)  # 等待杠杆查询结果

    # 历史数据
    from vnpy.trader.object import HistoryRequest, Interval
    CHINA_TZ: ZoneInfo = ZoneInfo("Asia/Shanghai")
    req = HistoryRequest(
        symbol=symbol,
        exchange=exchange,
        interval=Interval.MINUTE,
        start=datetime(2025, 9, 6, 10, 0, 0, tzinfo=CHINA_TZ),
        end=datetime(2025, 9, 6, 10, 5, 0, tzinfo=CHINA_TZ)
    )
    history = okx_gateway.query_history(req)
    main_engine.write_log(str(history))

    # 订阅
    req = SubscribeRequest(
        symbol=symbol,
        exchange=exchange)
    main_engine.subscribe(req, "OKX")
    main_engine.write_log(f"Subscribed to {vt_symbol}")
    sleep(5)
    main_engine.unsubscribe(req, "OKX")
    sleep(5)
    print('结束')

    # 发单
    # order_req = OrderRequest(
    #     symbol=symbol,
    #     exchange=exchange,
    #     direction=Direction.LONG,
    #     type=OrderType.LIMIT,
    #     volume=10,
    #     price=0.2395,  # 设置远离当前价格避免成交
    #     offset=Offset.OPEN
    # )
    # vt_orderid = main_engine.send_order(order_req,'OKX')
    # full_orderid = vt_orderid.split('.')[1]
    # sleep(5)
    # print(f"订单已发送, ID: {vt_orderid}")
    # # while True:
    # #     sleep(100)
    # cancel_req = CancelRequest(
    #     orderid=full_orderid,  # 使用完整的原始订单ID
    #     symbol=symbol,
    #     exchange=exchange
    # )
    # main_engine.cancel_order(cancel_req,'OKX')

if __name__ == "__main__":
    run_test() 
